@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-slider {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  font-size: 14px;
  width: 100%;
}
.t-slider--disabled .t-slider__dot-value,
.t-slider--disabled .t-slider__range-extreme,
.t-slider--disabled .t-slider__scale-desc,
.t-slider--disabled .t-slider__value {
  color: var(
    --td-slider-disabled-text-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-slider--disabled .t-slider__dot {
  background-color: var(--td-slider-dot-disabled-bg-color, #fff);
  border-color: var(--td-slider-dot-disabled-border-color, #f3f3f3);
}
.t-slider--top {
  padding-top: 40rpx;
}
.t-slider__line {
  background-color: var(
    --td-slider-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-radius: calc(var(--td-slider-bar-height, 8rpx) / 2);
  height: var(--td-slider-bar-height, 8rpx);
  position: absolute;
  top: 0;
}
.t-slider__line--disabled {
  background-color: var(
    --td-slider-disabled-color,
    var(--td-brand-color-disabled, var(--td-primary-color-3, #b5c7ff))
  );
}
.t-slider__line--capsule {
  height: var(--td-slider-capsule-line-heihgt, 36rpx);
}
.t-slider__line--capsule.t-slider__line--single {
  border-bottom-left-radius: calc(
    var(--td-slider-capsule-line-heihgt, 36rpx) / 2
  );
  border-top-left-radius: calc(var(--td-slider-capsule-line-heihgt, 36rpx) / 2);
}
.t-slider__dot {
  background-color: var(--td-slider-dot-bg-color, #fff);
  border: 2rpx solid
    var(
      --td-slider-dot-color,
      var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
    );
  border-radius: 50%;
  box-shadow: var(
    --td-shadow-1,
    0 1px 10px rgba(0, 0, 0, 0.05),
    0 4px 5px rgba(0, 0, 0, 0.08),
    0 2px 4px -1px rgba(0, 0, 0, 0.12)
  );
  box-sizing: border-box;
  height: var(--td-slider-dot-size, 40rpx);
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate3d(50%, -50%, 0);
  width: var(--td-slider-dot-size, 40rpx);
  z-index: 2;
}
.t-slider__dot--left {
  left: 0;
  transform: translate3d(-50%, -50%, 0);
}
.t-slider__dot-value {
  height: 44rpx;
  left: 50%;
  line-height: 44rpx;
  position: relative;
  text-align: center;
  top: -52rpx;
  transform: translateX(-50%);
  width: 96rpx;
}
.t-slider__dot-value,
.t-slider__range-extreme,
.t-slider__value {
  color: var(
    --td-slider-text-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
}
.t-slider__dot-value--sr-only,
.t-slider__range-extreme--sr-only,
.t-slider__value--sr-only {
  clip: rect(0, 0, 0, 0);
  border: 0;
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
.t-slider__dot-slider {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}
.t-slider__value--min {
  margin-left: 32rpx;
}
.t-slider__value--max {
  margin-right: 32rpx;
}
.t-slider__value--right {
  flex-basis: 80rpx;
}
.t-slider__value--right__value--text {
  display: block;
  margin-right: 32rpx;
  text-align: right;
}
.t-slider__bar {
  background-clip: content-box;
  background-color: var(
    --td-slider-default-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
  border-radius: calc(var(--td-slider-bar-height, 8rpx) / 2);
  flex: 10;
  height: var(--td-slider-bar-height, 8rpx);
  margin: 16rpx 32rpx;
  position: relative;
}
.t-slider__bar--capsule {
  background-color: var(
    --td-slider-capsule-bar-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
  border: 6rpx solid
    var(
      --td-slider-capsule-bar-color,
      var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
    );
  border-radius: calc(var(--td-slider-capsule-bar-heihgt, 48rpx) / 2);
  box-sizing: border-box;
  height: var(--td-slider-capsule-bar-heihgt, 48rpx);
}
.t-slider__bar--marks {
  background-color: var(
    --td-slider-default-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
}
.t-slider__bar--disabled {
  background-color: var(
    --td-slider-default-color,
    var(--td-bg-color-component-disabled, var(--td-gray-color-2, #eee))
  );
}
.t-slider__range-extreme--min {
  margin-left: 32rpx;
  text-align: left;
}
.t-slider__range-extreme--max {
  margin-right: 32rpx;
  text-align: right;
}
.t-slider__scale-item {
  background-color: var(
    --td-slider-default-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
  border-radius: 50%;
  height: 16rpx;
  margin-top: -8rpx;
  position: absolute;
  top: 50%;
  width: var(--td-slider-bar-height, 8rpx);
  width: 16rpx;
  z-index: 1;
}
.t-slider__scale-item--active {
  background-color: var(
    --td-slider-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-slider__scale-item--disabled {
  background-color: var(
    --td-slider-default-color,
    var(--td-bg-color-component-disabled, var(--td-gray-color-2, #eee))
  );
}
.t-slider__scale-item--active.t-slider__scale-item--disabled {
  background-color: var(
    --td-slider-disabled-color,
    var(--td-brand-color-disabled, var(--td-primary-color-3, #b5c7ff))
  );
}
.t-slider__scale-item--capsule {
  background-color: var(
    --td-slider-capsule-bar-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
  border-radius: 0;
  height: var(--td-slider-capsule-line-heihgt, 36rpx);
  margin-top: calc(-0.5 * var(--td-slider-capsule-line-heihgt, 36rpx));
  width: 4rpx;
}
.t-slider__scale-item--hidden {
  background-color: initial;
}
.t-slider__scale-desc {
  bottom: 32rpx;
  color: var(
    --td-slider-text-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
}
.t-slider__scale-desc--capsule {
  bottom: 46rpx;
}
.t-slider--vertical {
  --td-slider-bar-height: 400rpx;
  height: var(--td-slider-bar-height, 8rpx);
  justify-content: center;
  position: relative;
}
.t-slider--vertical .t-slider__bar {
  flex: none;
  height: 100%;
  width: var(--td-slider-bar-width, 8rpx);
}
.t-slider--vertical .t-slider__bar--capsule {
  border-radius: calc(var(--td-slider-capsule-bar-width, 48rpx) / 2);
  width: var(--td-slider-capsule-bar-width, 48rpx);
}
.t-slider--vertical .t-slider__line {
  border-radius: calc(var(--td-slider-bar-width, 8rpx) / 2);
  height: unset;
  left: 0;
  width: 100%;
}
.t-slider--vertical .t-slider__line--capsule.t-slider__line--single {
  border-top-left-radius: calc(var(--td-slider-capsule-line-heihgt, 36rpx) / 2);
  border-top-right-radius: calc(
    var(--td-slider-capsule-line-heihgt, 36rpx) / 2
  );
}
.t-slider--vertical .t-slider__dot {
  top: 100%;
}
.t-slider--vertical .t-slider__dot,
.t-slider--vertical .t-slider__dot--left {
  left: 50%;
  transform: translate(-50%, -50%);
}
.t-slider--vertical .t-slider__dot--left {
  top: 0;
}
.t-slider--vertical .t-slider__dot--right {
  left: 50%;
  top: 100%;
  transform: translate(-50%, -50%);
}
.t-slider--vertical .t-slider__dot-value {
  left: 54rpx;
  top: 50%;
  transform: translate(0, -50%);
  width: auto;
}
.t-slider--vertical .t-slider__range-extreme {
  left: 50%;
  margin: 0;
  position: absolute;
  transform: translateX(-50%);
}
.t-slider--vertical .t-slider__range-extreme--min {
  top: 0;
}
.t-slider--vertical .t-slider__range-extreme--max {
  bottom: 0;
}
.t-slider--vertical .t-slider__scale-item {
  left: 50%;
  margin-top: 0;
}
.t-slider--vertical .t-slider__scale-item--capsule {
  height: 4rpx;
  width: var(--td-slider-capsule-line-heihgt, 36rpx);
}
.t-slider--vertical .t-slider__scale-desc {
  bottom: unset;
  left: 38rpx;
  top: 50%;
  transform: translateY(-50%);
}
.t-slider--vertical .t-slider__scale-desc--capsule {
  left: 52rpx;
}
