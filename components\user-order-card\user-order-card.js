Component({
  properties: {
    orderData: {
      type: Object,
      value: {
        order_id: "",
        order_status: "",
        order_time: "",
        accept_time: "",
        cancel_time: null,
        complete_time: null,
        remark: "",
        service: {
          service_id: "",
          name: "",
          thumbnail: "",
          description: "",
          price: "0.00",
        },
        service_provider: {
          user_id: "",
          nickname: "",
          avatar: "",
          contact_info: "",
          auth_status: "",
          neighbor_type: "",
          address: { building: "", unit: "", room: "" },
          order_statistics: { in_progress: 0, completed: 0 },
        },
      },
    },
  },
  methods: {
    onContactCustomer: function () {
      var e = this.properties.orderData.service_provider.contact_info;
      e
        ? wx.makePhoneCall({
            phoneNumber: e,
            fail: function (e) {
              wx.showToast({ title: "拨号失败", icon: "error" });
            },
          })
        : wx.showToast({ title: "暂无联系方式", icon: "none" });
    },
    onNavigateToServiceDetail: function () {
      var e = this.properties.orderData.service.service_id;
      e
        ? wx.navigateTo({
            url: "/pages/service-info/service-info?id=".concat(e),
            fail: function (e) {
              console.error("跳转失败：", e),
                wx.showToast({ title: "跳转失败", icon: "error" });
            },
          })
        : wx.showToast({ title: "服务信息不完整", icon: "none" });
    },
    onNavigateToOrderList: function (e) {
      var r = this.properties.orderData,
        t = r.service.service_id,
        o = r.service.name;
      if (t) {
        var i = e.currentTarget.dataset.status;
        wx.navigateTo({
          url: "/pages/my-order-list/my-order-list?serviceId="
            .concat(t, "&serviceName=")
            .concat(encodeURIComponent(o), "&status=")
            .concat(i),
          fail: function (e) {
            console.error("跳转失败：", e),
              wx.showToast({ title: "跳转失败", icon: "error" });
          },
        });
      } else wx.showToast({ title: "服务信息不完整", icon: "none" });
    },
    onConfirmDelivery: function () {
      this.triggerEvent("confirmDelivery", {
        orderId: this.properties.orderData.order_id,
      });
    },
  },
});
