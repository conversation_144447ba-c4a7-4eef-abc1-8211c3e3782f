__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G[
                "miniprogram_npm/tdesign-miniprogram/action-sheet/template/list"
              ] || {}
            )._,
            (
              G[
                "miniprogram_npm/tdesign-miniprogram/action-sheet/template/grid"
              ] || {}
            )._,
            H
          );
        return S[P];
      };
      var a =
        R["miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            usingCustomNavbar: new Array(1),
            customStyle: new Array(1),
            showOverlay: new Array(1),
            defaultPopUpzIndex: new Array(1),
            defaultPopUpProps: new Array(1),
            visible: new Array(1),
            popupProps: new Array(2),
            style: new Array(1),
          },
          K = U === true,
          g,
          i = (C, T) => {
            C || K || U.description ? T(Y(D.description)) : T();
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.align])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(b).cls)(D.classPrefix + "__description", [D.align])
                    );
                  if (C) O(N, "tabindex", "0");
                },
                i
              );
            }
          },
          j,
          l,
          m = (C, T, E, B, F, S, J) => {
            var $A = I(l);
            if (l && $A)
              $A(
                R,
                C,
                {
                  classPrefix: D.classPrefix,
                  prefix: D.prefix,
                  gridThemeItems: D.gridThemeItems,
                  count: D.count,
                  currentSwiperIndex: D.currentSwiperIndex,
                },
                K ||
                  (U
                    ? {
                        classPrefix: U.classPrefix,
                        prefix: U.prefix,
                        gridThemeItems: U.gridThemeItems,
                        count: U.count,
                        currentSwiperIndex: U.currentSwiperIndex,
                      }
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          o = (C, p, q, r, s, t, T, E, B, F, S, J) => {
            var u,
              v = (C, T, E, B, F, S, J) => {
                var $A = I(u);
                if (u && $A)
                  $A(
                    R,
                    C,
                    {
                      index: q,
                      classPrefix: D.classPrefix,
                      listThemeItemClass: P(X(b).cls)(
                        D.classPrefix + "__list-item",
                        [D.align, [D.disabled, X(p).disabled]]
                      ),
                      item: p,
                    },
                    K ||
                      (U
                        ? {
                            index: s,
                            classPrefix: U.classPrefix,
                            listThemeItemClass:
                              !!(
                                Z(undefined, "cls") ||
                                U.classPrefix ||
                                Q.a([
                                  U.align,
                                  Q.a([U.disabled, Z(r, "disabled")]),
                                ])
                              ) || undefined,
                            item: r,
                          }
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              };
            u = "list";
            B(u, v);
          },
          n = (C, T, E, B, F) => {
            F(D.items, "index", U ? U.items : undefined, [0, "items"], o);
          },
          k = (C, T, E, B, F, S, J) => {
            if (j === 1) {
              l = "grid";
              B(l, m);
            } else if (j === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__list");
                },
                n
              );
            }
          },
          f = (C, T, E, B) => {
            g = D.description ? 1 : 0;
            B(g, h);
            j = X(D.gridThemeItems).length
              ? 1
              : D.items && X(D.items).length
              ? 2
              : 0;
            B(j, k);
          },
          p,
          s = (C) => {},
          t = (C, T) => {
            C || K || !!U.cancelText || undefined
              ? T(Y(D.cancelText || "取消"))
              : T();
          },
          r = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.theme) || undefined)
                  L(N, Y(D.classPrefix) + "__gap-" + Y(D.theme));
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__cancel " +
                      Y(D.prefix) +
                      "-class-cancel"
                  );
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "hover-class", Y(D.classPrefix) + "__cancel--hover");
                if (C) O(N, "hover-stay-time", "70");
                if (C) O(N, "aria-role", "button");
                if (C) R.v(N, "tap", "onCancel", !1, !1, !1, !1);
              },
              t
            );
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__footer");
                },
                r
              );
            }
          },
          e = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([Z(U.gridThemeItems, "length")])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__content", [
                        ["grid", X(D.gridThemeItems).length],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C) O(N, "tabindex", "0");
              },
              f
            );
            S("");
            p = D.showCancel ? 1 : 0;
            B(p, q);
          },
          d = (C, T, E) => {
            E(
              "t-popup",
              {},
              (N, C) => {
                if (C || K || U.visible) O(N, "visible", D.visible);
                A["visible"][0] = (D, E, T) => {
                  O(N, "visible", D.visible);
                  E(N);
                };
                if (C) O(N, "placement", "bottom");
                if (C || K || U.usingCustomNavbar)
                  O(N, "usingCustomNavbar", D.usingCustomNavbar);
                A["usingCustomNavbar"][0] = (D, E, T) => {
                  O(N, "usingCustomNavbar", D.usingCustomNavbar);
                  E(N);
                };
                if (C || K || U.showOverlay)
                  O(N, "show-overlay", D.showOverlay);
                A["showOverlay"][0] = (D, E, T) => {
                  O(N, "show-overlay", D.showOverlay);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(Z(U.popupProps, "zIndex") || U.defaultPopUpzIndex) ||
                  undefined
                )
                  O(
                    N,
                    "z-index",
                    X(D.popupProps).zIndex || D.defaultPopUpzIndex
                  );
                A["popupProps"][0] = A["defaultPopUpzIndex"][0] = (D, E, T) => {
                  O(
                    N,
                    "z-index",
                    X(D.popupProps).zIndex || D.defaultPopUpzIndex
                  );
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(Z(U.popupProps, "overlayProps") || U.defaultPopUpProps) ||
                  undefined
                )
                  O(
                    N,
                    "overlay-props",
                    X(D.popupProps).overlayProps || D.defaultPopUpProps
                  );
                A["popupProps"][1] = A["defaultPopUpProps"][0] = (D, E, T) => {
                  O(
                    N,
                    "overlay-props",
                    X(D.popupProps).overlayProps || D.defaultPopUpProps
                  );
                  E(N);
                };
                if (C)
                  R.v(
                    N,
                    "visible-change",
                    "onPopupVisibleChange",
                    !1,
                    !1,
                    !1,
                    !1
                  );
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
                if (C || K || U.classPrefix) R.i(N, D.classPrefix);
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-action-sheet__content{background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff));border-top-left-radius:var(--td-action-sheet-border-radius,var(--td-radius-extra-large,",
      [0, 24],
      "));border-top-right-radius:var(--td-action-sheet-border-radius,var(--td-radius-extra-large,",
      [0, 24],
      "));color:var(--td-action-sheet-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));overflow:hidden}\n.",
      [1],
      "t-action-sheet__content--grid{padding-top:",
      [0, 16],
      "}\n.",
      [1],
      "t-action-sheet__content:focus{outline:0}\n.",
      [1],
      "t-action-sheet__grid{padding-bottom:",
      [0, 16],
      "}\n.",
      [1],
      "t-action-sheet__grid--swiper{padding-bottom:",
      [0, 48],
      "}\n.",
      [1],
      "t-action-sheet__description{color:var(--td-action-sheet-description-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:",
      [0, 28],
      ";line-height:",
      [0, 44],
      ";padding:",
      [0, 24],
      " ",
      [0, 32],
      ";position:relative;text-align:var(--td-action-sheet-text-align,center)}\n.",
      [1],
      "t-action-sheet__description:focus{outline:0}\n.",
      [1],
      "t-action-sheet__description::after{background-color:var(--td-action-sheet-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
      [1],
      "t-action-sheet__description--left{text-align:left}\n.",
      [1],
      "t-action-sheet__description--left::after{left:",
      [0, 32],
      "}\n.",
      [1],
      "t-action-sheet__list-item{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;height:var(--td-action-sheet-list-item-height,",
      [0, 112],
      ");-webkit-justify-content:center;justify-content:center;padding:0 ",
      [0, 32],
      ";position:relative}\n.",
      [1],
      "t-action-sheet__list-item::after{background-color:var(--td-action-sheet-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
      [1],
      "t-action-sheet__list-item:focus{outline:0}\n.",
      [1],
      "t-action-sheet__list-item--left{-webkit-justify-content:start;justify-content:start}\n.",
      [1],
      "t-action-sheet__list-item--left::after{left:",
      [0, 32],
      "}\n.",
      [1],
      "t-action-sheet__list-item--disabled{color:var(--td-action-sheet-list-item-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
      [1],
      "t-action-sheet__list-item-text{word-wrap:normal;font-size:var(--td-font-size-m,",
      [0, 32],
      ");overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\n.",
      [1],
      "t-action-sheet__list-item-icon{margin-right:",
      [0, 16],
      "}\n.",
      [1],
      "t-action-sheet__list-item-icon--suffix{margin-left:auto}\n.",
      [1],
      "t-action-sheet__swiper-wrap{margin-top:",
      [0, 8],
      ";position:relative}\n.",
      [1],
      "t-action-sheet__footer{background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff))}\n.",
      [1],
      "t-action-sheet__gap-list{background-color:var(--td-action-sheet-gap-color,var(--td-bg-color-page,var(--td-gray-color-1,#f3f3f3)));height:",
      [0, 16],
      "}\n.",
      [1],
      "t-action-sheet__gap-grid{background-color:var(--td-action-sheet-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));height:",
      [0, 1],
      "}\n.",
      [1],
      "t-action-sheet__cancel{-webkit-align-items:center;align-items:center;color:var(--td-action-sheet-cancel-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex;height:var(--td-action-sheet-cancel-height,",
      [0, 96],
      ");-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "t-action-sheet__dots{bottom:",
      [0, 32],
      ";display:-webkit-flex;display:flex;-webkit-flex-direction:row;flex-direction:row;left:50%;position:absolute;-webkit-transform:translateX(-50%);transform:translateX(-50%)}\n.",
      [1],
      "t-action-sheet__dots-item{background-color:#dcdcdc;border-radius:50%;height:",
      [0, 16],
      ";margin:0 ",
      [0, 16],
      ";transition:all .4s ease-in;width:",
      [0, 16],
      "}\n.",
      [1],
      "t-action-sheet__dots-item.",
      [1],
      "t-is-active{background-color:#0052d9}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet.wxss",
    }
  );
}
