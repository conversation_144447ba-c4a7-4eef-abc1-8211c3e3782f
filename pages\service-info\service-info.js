var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator");
Page({
  data: { serviceData: "", loading: !0, showModal: !1, orderRemark: "" },
  safeNavigateBack: function () {
    getCurrentPages().length > 1
      ? wx.navigateBack()
      : wx.switchTab({ url: "/pages/index/index" });
  },
  onLoad: function (e) {
    var t = this;
    console.log("Service Info Page Loaded", e),
      e.id
        ? this.loadServiceDetail(e.id)
        : (wx.showToast({ title: "服务ID不存在", icon: "error" }),
          setTimeout(function () {
            t.safeNavigateBack();
          }, 1500));
  },
  loadServiceDetail: function (a) {
    var r = this;
    return t(
      e().mark(function t() {
        var o, n;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  return (
                    (o = getApp()),
                    r.setData({ loading: !0 }),
                    (e.prev = 2),
                    (e.next = 5),
                    o.call({
                      path: "/api/mp/services/".concat(a),
                      method: "GET",
                    })
                  );
                case 5:
                  if (((n = e.sent), console.log(n), !n || !n.data)) {
                    e.next = 11;
                    break;
                  }
                  r.setData({ serviceData: n.data.service, loading: !1 }),
                    (e.next = 12);
                  break;
                case 11:
                  throw new Error("服务详情获取失败");
                case 12:
                  e.next = 19;
                  break;
                case 14:
                  (e.prev = 14),
                    (e.t0 = e.catch(2)),
                    console.error("获取服务详情失败：", e.t0),
                    r.setData({ loading: !1 }),
                    wx.showToast({ title: "加载失败", icon: "error" });
                case 19:
                case "end":
                  return e.stop();
              }
          },
          t,
          null,
          [[2, 14]]
        );
      })
    )();
  },
  refreshServiceDetail: function () {
    var a = this;
    return t(
      e().mark(function t() {
        var r;
        return e().wrap(function (e) {
          for (;;)
            switch ((e.prev = e.next)) {
              case 0:
                if (!(r = a.data.serviceData.id)) {
                  e.next = 4;
                  break;
                }
                return (e.next = 4), a.loadServiceDetail(r);
              case 4:
              case "end":
                return e.stop();
            }
        }, t);
      })
    )();
  },
  onReady: function () {
    console.log("Service Info Page Ready");
  },
  onShow: function () {
    console.log("Service Info Page Show");
  },
  onHide: function () {
    console.log("Service Info Page Hide");
  },
  onUnload: function () {
    console.log("Service Info Page Unload");
  },
  onPullDownRefresh: function () {
    this.refreshServiceDetail().finally(function () {
      wx.stopPullDownRefresh();
    });
  },
  onReachBottom: function () {
    console.log("Reach Bottom");
  },
  onShareAppMessage: function () {
    var e = this.data.serviceData;
    return (
      console.log("111", e),
      {
        title: e.name || "有邻服务",
        path: "/pages/service-info/service-info?id=".concat(e.id),
        imageUrl: e.thumbnail,
      }
    );
  },
  requestSubscribeMessage: function () {
    var e = this;
    return new Promise(function (t) {
      var a = e.data.subscribeTemplateIds;
      if (0 === a.length)
        return console.log("未配置订阅消息模板ID"), void t(!0);
      wx.requestSubscribeMessage({
        tmplIds: a,
        success: function (e) {
          console.log("订阅消息请求成功:", e);
          var r = !1;
          a.forEach(function (t) {
            var a = e[t];
            switch (a) {
              case "accept":
                console.log("用户同意订阅模板: ".concat(t)), (r = !0);
                break;
              case "reject":
                console.log("用户拒绝订阅模板: ".concat(t));
                break;
              case "ban":
                console.log("模板已被封禁: ".concat(t));
                break;
              case "filter":
                console.log("模板被过滤: ".concat(t));
                break;
              default:
                console.log("未知状态: ".concat(t, " - ").concat(a));
            }
          }),
            wx.setStorageSync("subscribeStatus", e),
            r &&
              wx.showToast({
                title: "订阅成功，将及时通知您",
                icon: "success",
                duration: 1500,
              }),
            t(!0);
        },
        fail: function (e) {
          switch ((console.error("订阅消息请求失败:", e), e.errCode)) {
            case 20004:
              console.log("用户关闭了主开关"),
                wx.showToast({
                  title: "请在设置中开启消息通知",
                  icon: "none",
                  duration: 2e3,
                });
              break;
            case 20005:
              console.log("小程序被禁封");
              break;
            case 10005:
              console.log("无法展示UI，小程序可能在后台");
              break;
            default:
              console.log("其他错误:", e.errMsg);
          }
          t(!0);
        },
        complete: function () {
          console.log("订阅消息请求完成");
        },
      });
    });
  },
  showOrderModal: function () {
    var a = this;
    return t(
      e().mark(function t() {
        var r, o;
        return e().wrap(function (e) {
          for (;;)
            switch ((e.prev = e.next)) {
              case 0:
                if (
                  ((r = a.data.serviceData), (o = getApp()).isUserLoggedIn())
                ) {
                  e.next = 6;
                  break;
                }
                return (
                  wx.showToast({ title: "请先登录", icon: "none" }),
                  setTimeout(function () {
                    wx.navigateTo({ url: "/pages/login/login" });
                  }, 1500),
                  e.abrupt("return")
                );
              case 6:
                if (
                  "incomplete" !==
                  (o.globalData.userInfo.data || {}).auth_status.value
                ) {
                  e.next = 11;
                  break;
                }
                return (
                  wx.showToast({ title: "请先完善认证信息", icon: "none" }),
                  setTimeout(function () {
                    wx.navigateTo({ url: "/pages/auth/auth" });
                  }, 1500),
                  e.abrupt("return")
                );
              case 11:
                if ("approved" === r.audit_status) {
                  e.next = 14;
                  break;
                }
                return (
                  wx.showToast({
                    title: "服务未通过审核，暂不可预约",
                    icon: "none",
                  }),
                  e.abrupt("return")
                );
              case 14:
                if ("online" === r.publish_status) {
                  e.next = 17;
                  break;
                }
                return (
                  wx.showToast({
                    title: "服务已下架，暂不可预约",
                    icon: "none",
                  }),
                  e.abrupt("return")
                );
              case 17:
                o.globalData.needSubscribe.orderStatus &&
                  a.requestOrderStatusSubscribe(),
                  a.setData({ showModal: !0, orderRemark: "" });
              case 19:
              case "end":
                return e.stop();
            }
        }, t);
      })
    )();
  },
  confirmOrder: function () {
    var a = this;
    return t(
      e().mark(function t() {
        var r, o, n, s, i;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  if (
                    ((r = a.data.serviceData),
                    (o = a.data.orderRemark.trim()),
                    (n = getApp()),
                    a.hideOrderModal(),
                    wx.showLoading({ title: "正在预约...", mask: !0 }),
                    (e.prev = 5),
                    (s = n.globalData.userInfo.data),
                    console.log("用户信息:", s),
                    s && s.id)
                  ) {
                    e.next = 10;
                    break;
                  }
                  throw new Error("用户信息不存在，请先登录");
                case 10:
                  return (
                    (e.next = 12),
                    n.call({
                      path: "/api/mp/serviceOrder",
                      method: "POST",
                      data: {
                        customer_id: s.id,
                        service_id: r.id,
                        remark: o || "",
                      },
                    })
                  );
                case 12:
                  if (((i = e.sent), wx.hideLoading(), !i || 0 !== i.code)) {
                    e.next = 20;
                    break;
                  }
                  wx.showToast({
                    title: "预约成功",
                    icon: "success",
                    duration: 2e3,
                  }),
                    console.log("订单创建成功，订单ID:", i.data.order_id),
                    setTimeout(function () {
                      wx.navigateTo({
                        url: "/pages/order-detail/order-detail?orderId=".concat(
                          i.data.order_id
                        ),
                        fail: function () {
                          wx.switchTab({ url: "/pages/my/my" });
                        },
                      });
                    }, 1500),
                    (e.next = 21);
                  break;
                case 20:
                  throw new Error(i.message || "预约失败");
                case 21:
                  e.next = 28;
                  break;
                case 23:
                  (e.prev = 23),
                    (e.t0 = e.catch(5)),
                    wx.hideLoading(),
                    console.error("预约失败：", e.t0),
                    wx.showToast({
                      title: e.t0.message || "预约失败，请重试",
                      icon: "error",
                      duration: 2e3,
                    });
                case 28:
                case "end":
                  return e.stop();
              }
          },
          t,
          null,
          [[5, 23]]
        );
      })
    )();
  },
  hideOrderModal: function () {
    this.setData({ showModal: !1, orderRemark: "" });
  },
  stopPropagation: function () {},
  onRemarkInput: function (e) {
    this.setData({ orderRemark: e.detail.value });
  },
  onOrderClick: function () {
    this.showOrderModal();
  },
  previewImage: function (e) {
    var t = e.currentTarget.dataset.src,
      a = this.data.serviceData.detail_images || [];
    a.length > 0 && wx.previewImage({ current: t, urls: a });
  },
  requestOrderStatusSubscribe: function () {
    var e = require("../../env/index.js").subscriptionTemplates.orderStatus;
    wx.requestSubscribeMessage({
      tmplIds: [e],
      success: function (t) {
        console.log("订单状态提醒订阅请求成功", t);
        var a = getApp();
        "accept" === t[e]
          ? (wx.showToast({
              title: "订阅成功，将及时通知您订单状态",
              icon: "success",
              duration: 2e3,
            }),
            (a.globalData.needSubscribe.orderStatus = !1),
            (a.globalData.subscriptionStatus.orderStatus = "accept"))
          : "reject" === t[e] &&
            ((a.globalData.needSubscribe.orderStatus = !1),
            (a.globalData.subscriptionStatus.orderStatus = "reject"));
      },
      fail: function (e) {
        console.error("订单状态提醒订阅请求失败", e);
      },
    });
  },
});
