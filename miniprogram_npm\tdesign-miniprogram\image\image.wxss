@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-image {
  display: inline-block;
  position: relative;
}
.t-image__img,
.t-image__mask {
  color: var(
    --td-image-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  height: inherit;
  vertical-align: top;
  width: inherit;
}
.t-image__mask {
  align-items: center;
  background-color: var(
    --td-image-loading-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  color: var(
    --td-image-loading-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.t-image--loading-text {
  height: 0;
  width: 0;
}
.t-image__common {
  height: 100%;
  width: 100%;
}
.t-image--lazy {
  left: 0;
  position: absolute;
  top: 0;
  z-index: -1;
}
.t-image--shape-circle {
  border-radius: 50%;
  overflow: hidden;
}
.t-image--shape-round {
  border-radius: var(--td-image-round-radius, var(--td-radius-default, 12rpx));
  overflow: hidden;
}
.t-image--shape-square {
  border-radius: 0;
  overflow: hidden;
}
