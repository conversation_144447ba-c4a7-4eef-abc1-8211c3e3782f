var e = require("@babel/runtime/helpers/regeneratorRuntime"),
  r = require("@babel/runtime/helpers/asyncToGenerator");
require("@babel/runtime/helpers/Arrayincludes");
var t = require("./env/index.js");
App({
  globalData: {
    userInfo: null,
    openid: null,
    isLoggedIn: !1,
    subscriptionStatus: { orderStatus: null, newOrder: null },
    needSubscribe: { orderStatus: !1, newOrder: !1 },
  },
  onError: function (e) {
    e.includes("autofillHideDropdown")
      ? console.warn("忽略微信小程序自动填充相关错误:", e)
      : console.error("应用错误:", e);
  },
  onLaunch: function () {
    var n = this;
    return r(
      e().mark(function r() {
        return e().wrap(function (e) {
          for (;;)
            switch ((e.prev = e.next)) {
              case 0:
                return (
                  console.log("小程序启动"),
                  console.log("resourceEnv:", t.resourceEnv),
                  (e.next = 4),
                  n.userLogin()
                );
              case 4:
              case "end":
                return e.stop();
            }
        }, r);
      })
    )();
  },
  userLogin: function () {
    var t = this;
    return r(
      e().mark(function r() {
        var n, o;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  return (e.prev = 0), (e.next = 3), t.getWxOpenId();
                case 3:
                  if ((n = e.sent)) {
                    e.next = 6;
                    break;
                  }
                  throw new Error("获取微信openID失败");
                case 6:
                  return (
                    (t.globalData.openid = n),
                    console.log("获取到openID:", n),
                    (e.next = 10),
                    t.getUserInfo(n)
                  );
                case 10:
                  (o = e.sent) &&
                    ((t.globalData.userInfo = o),
                    (t.globalData.isLoggedIn = !0),
                    console.log("用户登录成功:", o),
                    t.triggerLoginSuccess(o),
                    t.checkSubscriptionStatus()),
                    (e.next = 19);
                  break;
                case 14:
                  (e.prev = 14),
                    (e.t0 = e.catch(0)),
                    console.error("用户登录失败:", e.t0),
                    (t.globalData.isLoggedIn = !1),
                    wx.showToast({ title: "登录失败", icon: "error" });
                case 19:
                case "end":
                  return e.stop();
              }
          },
          r,
          null,
          [[0, 14]]
        );
      })
    )();
  },
  getWxOpenId: function () {
    var t = this;
    return r(
      e().mark(function r() {
        var n;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  return (
                    (e.prev = 0),
                    (e.next = 3),
                    t.call({ path: "/api/mp/auth/wx_openid", method: "GET" })
                  );
                case 3:
                  if (0 !== (n = e.sent).code) {
                    e.next = 8;
                    break;
                  }
                  return e.abrupt("return", n.data);
                case 8:
                  throw new Error(n.message || "获取openID失败");
                case 9:
                  e.next = 15;
                  break;
                case 11:
                  throw (
                    ((e.prev = 11),
                    (e.t0 = e.catch(0)),
                    console.error("获取微信openID失败:", e.t0),
                    e.t0)
                  );
                case 15:
                case "end":
                  return e.stop();
              }
          },
          r,
          null,
          [[0, 11]]
        );
      })
    )();
  },
  getUserInfo: function (t) {
    var n = this;
    return r(
      e().mark(function r() {
        var o;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  return (
                    (e.prev = 0),
                    (e.next = 3),
                    n.call({
                      path: "/api/mp/auth/user/".concat(t),
                      method: "GET",
                    })
                  );
                case 3:
                  return (
                    (o = e.sent),
                    (n.globalData.userInfo = o),
                    console.log("用户信息获取成功:", o),
                    n.triggerLoginSuccess(),
                    e.abrupt("return", o)
                  );
                case 10:
                  throw (
                    ((e.prev = 10),
                    (e.t0 = e.catch(0)),
                    console.error("获取用户信息失败:", e.t0),
                    e.t0)
                  );
                case 14:
                case "end":
                  return e.stop();
              }
          },
          r,
          null,
          [[0, 10]]
        );
      })
    )();
  },
  triggerLoginSuccess: function () {
    console.log("用户登录成功，可以通知其他页面");
    var e = getCurrentPages(),
      r = e[e.length - 1];
    r && "function" == typeof r.onUserLoginSuccess && r.onUserLoginSuccess();
  },
  getUserData: function () {
    return this.globalData.userInfo;
  },
  isUserLoggedIn: function () {
    return this.globalData.isLoggedIn && this.globalData.userInfo;
  },
  refreshUserInfo: function () {
    var t = this;
    return r(
      e().mark(function r() {
        var n;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  if (!t.globalData.openid) {
                    e.next = 15;
                    break;
                  }
                  return (
                    (e.prev = 1),
                    (e.next = 4),
                    t.getUserInfo(t.globalData.openid)
                  );
                case 4:
                  return (
                    (n = e.sent),
                    (t.globalData.userInfo = n),
                    e.abrupt("return", n)
                  );
                case 9:
                  throw (
                    ((e.prev = 9),
                    (e.t0 = e.catch(1)),
                    console.error("刷新用户信息失败:", e.t0),
                    e.t0)
                  );
                case 13:
                  e.next = 16;
                  break;
                case 15:
                  throw new Error("未获取到openID，无法刷新用户信息");
                case 16:
                case "end":
                  return e.stop();
              }
          },
          r,
          null,
          [[1, 9]]
        );
      })
    )();
  },
  getTempFile: function (n) {
    var o = arguments,
      a = this;
    return r(
      e().mark(function r() {
        var s, c, u, i;
        return e().wrap(function (e) {
          for (;;)
            switch ((e.prev = e.next)) {
              case 0:
                if (
                  ((s = o.length > 1 && void 0 !== o[1] ? o[1] : 86400),
                  null != (c = a).cloud)
                ) {
                  e.next = 7;
                  break;
                }
                return (
                  (u = new wx.cloud.Cloud({
                    resourceAppid: t.resourceAppid,
                    resourceEnv: t.resourceEnv,
                  })),
                  (c.cloud = u),
                  (e.next = 7),
                  c.cloud.init()
                );
              case 7:
                return (
                  (i = ("string" == typeof n ? [n] : n).map(function (e) {
                    return { fileID: e, maxAge: s };
                  })),
                  (e.next = 10),
                  c.cloud.getTempFileURL({ fileList: i })
                );
              case 10:
                return e.abrupt("return", e.sent);
              case 11:
              case "end":
                return e.stop();
            }
        }, r);
      })
    )();
  },
  call: function (n) {
    var o = arguments,
      a = this;
    return r(
      e().mark(function r() {
        var s, c, u, i, l;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  if (
                    ((s = o.length > 1 && void 0 !== o[1] ? o[1] : 0),
                    null != (c = a).cloud)
                  ) {
                    e.next = 7;
                    break;
                  }
                  return (
                    (u = new wx.cloud.Cloud({
                      resourceAppid: t.resourceAppid,
                      resourceEnv: t.resourceEnv,
                    })),
                    (c.cloud = u),
                    (e.next = 7),
                    c.cloud.init()
                  );
                case 7:
                  return (
                    (e.prev = 7),
                    (e.next = 10),
                    c.cloud.callContainer({
                      path: n.path,
                      method: n.method || "GET",
                      header: { "X-WX-SERVICE": t.serviceName },
                      data: n.data,
                    })
                  );
                case 10:
                  return (
                    (i = e.sent),
                    console.log(
                      "微信云托管调用结果"
                        .concat(i.errMsg, " | callid:")
                        .concat(i.callID)
                    ),
                    e.abrupt("return", i.data)
                  );
                case 15:
                  if (
                    ((e.prev = 15),
                    (e.t0 = e.catch(7)),
                    !(
                      -1 !=
                        (l = e.t0.toString()).indexOf(
                          "Cloud API isn't enabled"
                        ) && s < 3
                    ))
                  ) {
                    e.next = 22;
                    break;
                  }
                  return e.abrupt(
                    "return",
                    new Promise(function (e) {
                      setTimeout(function () {
                        e(c.call(n, s + 1));
                      }, 300);
                    })
                  );
                case 22:
                  throw new Error("微信云托管调用失败".concat(l));
                case 23:
                case "end":
                  return e.stop();
              }
          },
          r,
          null,
          [[7, 15]]
        );
      })
    )();
  },
  checkSubscriptionStatus: function () {
    var e = this,
      r = t.subscriptionTemplates;
    wx.getSetting({
      withSubscriptions: !0,
      success: function (t) {
        if ((console.log("获取设置成功：", t), t.subscriptionsSetting)) {
          var n = t.subscriptionsSetting[r.orderStatus];
          n
            ? ((e.globalData.subscriptionStatus.orderStatus = n),
              (e.globalData.needSubscribe.orderStatus =
                "accept" !== n && "reject" !== n))
            : (e.globalData.needSubscribe.orderStatus = !0);
          var o = t.subscriptionsSetting[r.newOrder];
          o
            ? ((e.globalData.subscriptionStatus.newOrder = o),
              (e.globalData.needSubscribe.newOrder =
                "accept" !== o && "reject" !== o))
            : (e.globalData.needSubscribe.newOrder = !0);
        } else
          (e.globalData.needSubscribe.orderStatus = !0),
            (e.globalData.needSubscribe.newOrder = !0);
        console.log("订阅状态检查完成：", e.globalData.subscriptionStatus),
          console.log("需要订阅：", e.globalData.needSubscribe);
      },
      fail: function (r) {
        console.error("获取设置失败：", r),
          (e.globalData.needSubscribe.orderStatus = !0),
          (e.globalData.needSubscribe.newOrder = !0);
      },
    });
  },
});
