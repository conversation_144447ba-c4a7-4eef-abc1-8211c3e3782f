.service_info_container {
  background-color: #fff;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 30rpx;
}
.service_description_section {
  margin-bottom: 40rpx;
}
.section_title {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.description_box {
  background-color: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  border-radius: 15rpx;
  padding: 25rpx;
}
.description_text {
  color: #666;
  display: block;
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 10rpx;
}
.description_text:last-child {
  margin-bottom: 0;
}
.image_section {
  flex: 1;
  margin-bottom: 40rpx;
}
.image_placeholder {
  align-items: center;
  background-color: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  border-radius: 15rpx;
  display: -webkit-flex;
  display: flex;
  height: 300rpx;
  justify-content: center;
  margin-bottom: 20rpx;
  width: 100%;
}
.image_placeholder:last-child {
  margin-bottom: 0;
}
.placeholder_text {
  color: #999;
  font-size: 28rpx;
}
.bottom_actions {
  align-items: center;
  background-color: #fff;
  border-top: 2rpx solid #f0f0f0;
  bottom: 0;
  box-shadow: 0-2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: -webkit-flex;
  display: flex;
  left: 0;
  padding: 20rpx 30rpx;
  position: fixed;
  right: 0;
}
.contact_section {
  flex: 1;
  margin-right: 20rpx;
}
.contact_text {
  color: #195abf;
  font-size: 28rpx;
}
.order_button_section {
  display: -webkit-flex;
  display: flex;
  flex: 1;
  justify-content: flex-end;
}
.custom_order_button {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  height: 80rpx;
  justify-content: center;
  padding: 0 40rpx;
}
.order_button_text {
  border: 5rpx solid #ff4d4f;
  border-radius: 20rpx;
  color: #ff4d4f;
  font-size: 24rpx;
  padding: 8rpx 32rpx;
}
.custom_order_button:active {
  background-color: rgba(255, 77, 79, 0.1);
}
.service_info_container {
  padding-bottom: 120rpx;
}
