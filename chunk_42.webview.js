__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/radio-group/radio-group": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            classPrefix: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
          },
          K = U === true,
          d = (C, e, f, g, h, i, T, E) => {
            var j = (C) => {};
            E(
              "t-radio",
              {},
              (N, C) => {
                if (C || K || !!U.prefix || undefined)
                  L(N, Y(D.prefix) + "-radio-option");
                if (C || K || !!Z(g, "block") || undefined)
                  O(N, "block", X(e).block || true);
                if (C || K || !!Z(g, "label") || undefined)
                  O(N, "label", X(e).label || "");
                if (C || K || Z(g, "value")) O(N, "value", X(e).value);
                if (C || K || !!Z(g, "checked") || undefined)
                  O(N, "checked", X(e).checked || false);
                if (C || K || !!Z(g, "content") || undefined)
                  O(N, "content", X(e).content || "");
                if (
                  C ||
                  K ||
                  !!(Z(g, "allowUncheck") || U.allowUncheck) ||
                  undefined
                )
                  O(N, "allow-uncheck", X(e).allowUncheck || D.allowUncheck);
                if (C || K || !!Z(g, "contentDisabled") || undefined)
                  O(N, "content-disabled", X(e).contentDisabled || false);
                if (C || K || !!Z(g, "readonly") || undefined)
                  O(N, "readonly", X(e).readonly || false);
                if (C || K || !!Z(g, "disabled") || undefined)
                  O(N, "disabled", X(e).disabled || false);
                if (C || K || !!(Z(g, "icon") || U.icon) || undefined)
                  O(N, "icon", X(e).icon || D.icon);
                if (C || K || !!(Z(g, "placement") || U.placement) || undefined)
                  O(N, "placement", X(e).placement || D.placement);
                if (C || K || !!Z(g, "maxContentRow") || undefined)
                  O(N, "max-content-row", X(e).maxContentRow || 5);
                if (C || K || !!Z(g, "maxLabelRow") || undefined)
                  O(N, "max-label-row", X(e).maxLabelRow || 3);
                if (C || K || !!Z(g, "name") || undefined)
                  O(N, "name", X(e).name || "");
                if (C || K || U.borderless) O(N, "borderless", D.borderless);
                if (C || K || h) R.d(N, "index", f);
                if (C || K || Z(g, "value")) R.d(N, "value", X(e).value);
                if (
                  C ||
                  K ||
                  !!(Z(g, "allowUncheck") || U.allowUncheck) ||
                  undefined
                )
                  R.d(N, "allowUncheck", X(e).allowUncheck || D.allowUncheck);
                if (C) R.v(N, "change", "handleRadioChange", !1, !1, !1, !1);
              },
              j
            );
          },
          c = (C, T, E, B, F, S) => {
            S("");
            F(
              D.radioOptions,
              "value",
              U ? U.radioOptions : undefined,
              [0, "radioOptions"],
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["classPrefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-role", "radiogroup");
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/radio/radio": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            block: new Array(1),
            tId: new Array(1),
            tabindex: new Array(1),
            maxContentRow: new Array(1),
            customStyle: new Array(1),
            style: new Array(1),
            maxLabelRow: new Array(1),
          },
          K = U === true,
          e,
          h = (C) => {},
          g = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "-icon__image");
                var $A = D.checked;
                var $B = 0;
                var $C = 1;
                if (
                  C ||
                  K ||
                  !!U.checked ||
                  ($A ? Z(U.iconVal, $B) : Z(U.iconVal, $C))
                )
                  O(N, "src", $A ? X(D.iconVal)[$B] : X(D.iconVal)[$C]);
                if (C) O(N, "webp", true);
              },
              h
            );
          },
          i,
          k = (C) => {},
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__icon-wrap");
                  var $A = D.icon == "circle";
                  if (C || K || !!U.icon || ($A ? undefined : undefined))
                    O(N, "name", $A ? "check-circle-filled" : "check");
                },
                k
              );
            }
          },
          l,
          n = (C) => {},
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      U.icon ||
                      Q.a([Q.a([U._disabled])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__icon-" + D.icon, [
                        ["disabled", D._disabled],
                      ])
                    );
                },
                n
              );
            }
          },
          o,
          q = (C) => {},
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([U._disabled])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__icon-circle", [
                        ["disabled", D._disabled],
                      ])
                    );
                },
                q
              );
            }
          },
          r,
          t = (C) => {},
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "placeholder");
                },
                t
              );
            }
          },
          f = (C, T, E, B, F, S) => {
            if (e === 1) {
              S("icon");
            } else if (e === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__image");
                },
                g
              );
            } else {
              i = D.checked && (D.icon == "circle" || D.icon == "line") ? 1 : 0;
              B(i, j);
              l = D.checked && D.icon == "dot" ? 1 : 0;
              B(l, m);
              o = !D.checked && (D.icon == "circle" || D.icon == "dot") ? 1 : 0;
              B(o, p);
              r = !D.checked && D.icon == "line" ? 1 : 0;
              B(r, s);
            }
          },
          d = (C, T, E, B) => {
            e = D.slotIcon ? 1 : D.customIcon ? 2 : 0;
            B(e, f);
          },
          w,
          x = (C, T) => {
            if (w === 1) {
              C || K || U.label ? T(Y(D.label)) : T();
            }
          },
          v = (C, T, E, B, F, S) => {
            w = D.label ? 1 : 0;
            B(w, x);
            S("");
            S("label");
          },
          z,
          A0 = (C, T) => {
            if (z === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          y = (C, T, E, B, F, S) => {
            z = D.content ? 1 : 0;
            B(z, A0);
            S("content");
          },
          u = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U._disabled]), Q.a([U.checked])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__title", [
                        ["disabled", D._disabled],
                        ["checked", D.checked],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-label"
                  );
                if (C || K || !!U.maxLabelRow || undefined)
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxLabelRow));
                A["maxLabelRow"][0] = (D, E, T) => {
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxLabelRow));
                };
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U._disabled]), Q.a([U.checked])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__description", [
                        ["disabled", D._disabled],
                        ["checked", D.checked],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C || K || !!U.maxContentRow || undefined)
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxContentRow));
                A["maxContentRow"][0] = (D, E, T) => {
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxContentRow));
                };
              },
              y
            );
          },
          B0,
          D0 = (C) => {},
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U._placement]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__border", [D._placement])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-border"
                    );
                },
                D0
              );
            }
          },
          c = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U._placement, Q.a([U.checked]), Q.a([U._disabled])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__icon", [
                        D._placement,
                        ["checked", D.checked],
                        ["disabled", D._disabled],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-icon"
                  );
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__content");
                if (C) R.d(N, "target", "text");
                if (C) R.v(N, "tap", "handleTap", !1, !0, !1, !1);
              },
              u
            );
            B0 = !D.borderless ? 1 : 0;
            B(B0, C0);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U._placement, Q.a([U.block])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D._placement,
                        ["block", D.block],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["block"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D._placement,
                        ["block", D.block],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C || K || U._disabled) O(N, "disabled", D._disabled);
                if (C) O(N, "aria-role", "radio");
                if (C || K || U.checked) O(N, "aria-checked", D.checked);
                if (C || K || !!(U.label || U.content) || undefined)
                  O(N, "aria-label", D.label + D.content);
                if (C || K || U._disabled) O(N, "aria-disabled", D._disabled);
                if (C || K || U.tabindex) O(N, "tabindex", D.tabindex);
                A["tabindex"][0] = (D, E, T) => {
                  O(N, "tabindex", D.tabindex);
                  E(N);
                };
                if (C) R.v(N, "tap", "handleTap", !1, !0, !1, !1);
                if (C || K || U.tId) R.i(N, D.tId);
                A["tId"][0] = (D, E, T) => {
                  R.i(N, D.tId);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/radio-group/radio-group.wxss"
  ] = setCssToHead(
    [[2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"]],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/radio-group/radio-group.wxss",
    }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/radio/radio.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "limit-title-row{-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}\n.",
        [1],
        "t-radio{background:var(--td-radio-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));display:-webkit-inline-flex;display:inline-flex;font-size:var(--td-radio-font-size,",
        [0, 32],
        ");position:relative;vertical-align:middle}\n.",
        [1],
        "t-radio:focus{outline:0}\n.",
        [1],
        "t-radio--block{display:-webkit-flex;display:flex;padding:var(--td-radio-vertical-padding,",
        [0, 32],
        ")}\n.",
        [1],
        "t-radio--right{-webkit-flex-direction:row-reverse;flex-direction:row-reverse}\n.",
        [1],
        "t-radio__icon{color:var(--td-radio-icon-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));font-size:var(--td-radio-icon-size,",
        [0, 48],
        ");height:var(--td-radio-icon-size,",
        [0, 48],
        ");margin-top:calc((var(--td-radio-label-line-height,",
        [0, 48],
        ") - var(--td-radio-icon-size,",
        [0, 48],
        "))/ 2);overflow:hidden;position:relative;width:var(--td-radio-icon-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-radio__icon:empty{display:none}\n.",
        [1],
        "t-radio__icon--left{margin-right:",
        [0, 16],
        "}\n.",
        [1],
        "t-radio__icon--checked{color:var(--td-radio-icon-checked-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-radio__icon--disabled{color:var(--td-radio-icon-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)));cursor:not-allowed}\n.",
        [1],
        "t-radio__icon-circle{box-sizing:border-box;height:var(--td-radio-icon-size,",
        [0, 48],
        ");width:var(--td-radio-icon-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-radio__icon-circle::after{border:3px solid var(--td-radio-icon-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));border-radius:50%;box-sizing:border-box;content:\x22\x22;height:calc(200% - ",
        [0, 6],
        ");left:50%;position:absolute;top:50%;-webkit-transform:translate(-50%,-50%) scale(.5);transform:translate(-50%,-50%) scale(.5);width:calc(200% - ",
        [0, 6],
        ")}\n.",
        [1],
        "t-radio__icon-circle--disabled::after{background:var(--td-radio-icon-disabled-bg-color,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)))}\n.",
        [1],
        "t-radio__icon-line::after,.",
        [1],
        "t-radio__icon-line::before{background:var(--td-radio-icon-checked-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:",
        [0, 2],
        ";content:\x22\x22;display:block;position:absolute;-webkit-transform-origin:top center;transform-origin:top center;width:",
        [0, 5],
        "}\n.",
        [1],
        "t-radio__icon-line::before{height:",
        [0, 16],
        ";left:",
        [0, 8],
        ";top:",
        [0, 22],
        ";-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}\n.",
        [1],
        "t-radio__icon-line::after{height:",
        [0, 26],
        ";right:",
        [0, 8],
        ";top:",
        [0, 14],
        ";-webkit-transform:rotate(45deg);transform:rotate(45deg)}\n.",
        [1],
        "t-radio__icon-line--disabled::after,.",
        [1],
        "t-radio__icon-line--disabled::before{background:var(--td-radio-icon-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-radio__icon-dot{-webkit-align-items:center;align-items:center;border:",
        [0, 6],
        " solid var(--td-radio-icon-checked-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:50%;box-sizing:border-box;display:-webkit-flex;display:flex;height:calc((var(--td-radio-icon-size,",
        [0, 48],
        ") - ",
        [0, 6],
        ") * 2);-webkit-justify-content:center;justify-content:center;left:50%;position:absolute;top:50%;-webkit-transform:translate(-50%,-50%) scale(.5);transform:translate(-50%,-50%) scale(.5);width:calc((var(--td-radio-icon-size,",
        [0, 48],
        ") - ",
        [0, 6],
        ") * 2)}\n.",
        [1],
        "t-radio__icon-dot::after{background:var(--td-radio-icon-checked-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:50%;content:\x22\x22;display:block;height:var(--td-radio-icon-size,",
        [0, 48],
        ");width:var(--td-radio-icon-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-radio__icon-dot--disabled{border-color:var(--td-radio-icon-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-radio__icon-dot--disabled::after{background:var(--td-radio-icon-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-radio__image{line-height:var(--td-radio-icon-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-radio-icon__image{height:var(--td-radio-icon-size,",
        [0, 48],
        ");vertical-align:sub;width:var(--td-radio-icon-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-radio__content{-webkit-flex:1;flex:1}\n.",
        [1],
        "t-radio__content:empty{display:none}\n.",
        [1],
        "t-radio__title{-webkit-box-orient:vertical;color:var(--td-radio-label-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-box;line-height:var(--td-radio-label-line-height,",
        [0, 48],
        ");overflow:hidden}\n.",
        [1],
        "t-radio__title--checked{color:var(--td-radio-label-checked-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-radio__title--disabled{color:var(--td-radio-label-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))));cursor:not-allowed}\n.",
        [1],
        "t-radio__description{-webkit-box-orient:vertical;color:var(--td-radio-content-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))));display:-webkit-box;font-size:var(--td-radio-content-font-size,",
        [0, 28],
        ");line-height:var(--td-radio-content-line-height,",
        [0, 44],
        ");overflow:hidden}\n.",
        [1],
        "t-radio__description--checked{color:var(--td-radio-content-checked-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))))}\n.",
        [1],
        "t-radio__description--disabled{color:var(--td-radio-content-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))));cursor:not-allowed}\n.",
        [1],
        "t-radio__description:empty{display:none}\n.",
        [1],
        "t-radio__title+.",
        [1],
        "t-radio__description{margin-top:",
        [0, 8],
        "}\n.",
        [1],
        "t-radio__border{background:var(--td-radio-border-color,var(--td-component-stroke,var(--td-gray-color-3,#e7e7e7)));bottom:0;height:1px;left:",
        [0, 96],
        ";position:absolute;right:0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
        [1],
        "t-radio__border--right{left:",
        [0, 32],
        "}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/radio/radio.wxss" }
    );
}
