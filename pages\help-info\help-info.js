Page({
  data: {
    serviceData: {
      title: "代买早餐",
      description: ["这是是服务的文字说明", "这是是服务的文字说明"],
      images: [],
    },
  },
  onLoad: function (o) {
    console.log("Service Info Page Loaded", o);
  },
  onReady: function () {
    console.log("Service Info Page Ready");
  },
  onShow: function () {
    console.log("Service Info Page Show");
  },
  onHide: function () {
    console.log("Service Info Page Hide");
  },
  onUnload: function () {
    console.log("Service Info Page Unload");
  },
  onPullDownRefresh: function () {
    setTimeout(function () {
      wx.stopPullDownRefresh();
    }, 1e3);
  },
  onReachBottom: function () {
    console.log("Reach Bottom");
  },
  onShareAppMessage: function () {
    return { title: "代买早餐服务", path: "/pages/service-info/service-info" };
  },
  onOrderClick: function () {
    console.log("Order button clicked"),
      wx.showToast({ title: "立即下单", icon: "success" });
  },
});
