@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-indexes {
  height: 100vh;
  position: relative;
}
.t-indexes__sidebar {
  color: var(
    --td-indexes-sidebar-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-flex;
  display: flex;
  flex-flow: column nowrap;
  font-size: var(--td-indexes-sidebar-font-size, 24rpx);
  line-height: var(--td-indexes-sidebar-line-height, 40rpx);
  position: fixed;
  right: var(--td-indexes-sidebar-right, 16rpx);
  top: 50%;
  transform: translateY(-50%);
  width: var(--td-indexes-sidebar-item-size, 40rpx);
  z-index: 1;
}
.t-indexes__sidebar-item {
  border-radius: 50%;
  position: relative;
  text-align: center;
}
.t-indexes__sidebar-item--active {
  background-color: var(
    --td-indexes-sidebar-active-bg-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  color: var(
    --td-indexes-sidebar-active-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
}
.t-indexes__sidebar-item + .t-indexes__sidebar-item {
  margin-top: 4rpx;
}
.t-indexes__sidebar-tips {
  background-color: var(
    --td-indexes-sidebar-tips-bg-color,
    var(--td-brand-color-light, var(--td-primary-color-1, #f2f3ff))
  );
  border-radius: var(--td-indexes-sidebar-tips-size, 96rpx);
  bottom: 0;
  box-sizing: border-box;
  color: var(
    --td-indexes-sidebar-tips-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  font-size: var(--td-indexes-sidebar-tips-font-size, 40rpx);
  font-weight: 700;
  height: var(--td-indexes-sidebar-tips-size, 96rpx);
  line-height: var(--td-indexes-sidebar-tips-size, 96rpx);
  max-width: 198rpx;
  min-width: var(--td-indexes-sidebar-tips-size, 96rpx);
  overflow: hidden;
  padding: 0 32rpx;
  position: absolute;
  right: var(--td-indexes-sidebar-tips-right, calc(100% + 32rpx));
  text-align: center;
  text-overflow: ellipsis;
  top: 50%;
  transform: translateY(-50%);
  white-space: nowrap;
}
