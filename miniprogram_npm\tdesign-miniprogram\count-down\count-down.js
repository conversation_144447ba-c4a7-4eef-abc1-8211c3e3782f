Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  i = require("../../../@babel/runtime/helpers/inherits"),
  r = require("../../../@babel/runtime/helpers/createSuper"),
  s = require("tslib"),
  a = require("../common/src/index"),
  n = m(require("../common/config")),
  o = m(require("./props")),
  u = require("./utils");
function m(e) {
  return e && e.__esModule ? e : { default: e };
}
var c = n.default.prefix,
  h = "".concat(c, "-count-down"),
  l = (function (s) {
    i(n, s);
    var a = r(n);
    function n() {
      var e;
      return (
        t(this, n),
        ((e = a.apply(this, arguments)).externalClasses = [
          "".concat(c, "-class"),
          "".concat(c, "-class-count"),
          "".concat(c, "-class-split"),
        ]),
        (e.properties = o.default),
        (e.observers = {
          time: function () {
            this.reset();
          },
        }),
        (e.data = {
          prefix: c,
          classPrefix: h,
          timeDataUnit: u.TimeDataUnit,
          timeData: (0, u.parseTimeData)(0),
          formattedTime: "0",
        }),
        (e.timeoutId = null),
        (e.isInitialTime = !1),
        (e.lifetimes = {
          detached: function () {
            this.timeoutId &&
              (clearTimeout(this.timeoutId), (this.timeoutId = null));
          },
        }),
        (e.methods = {
          start: function () {
            this.counting ||
              ((this.counting = !0),
              (this.endTime = Date.now() + this.remain),
              this.doCount());
          },
          pause: function () {
            (this.counting = !1),
              this.timeoutId && clearTimeout(this.timeoutId);
          },
          reset: function () {
            this.pause(),
              (this.remain = this.properties.time),
              this.updateTime(this.remain),
              this.properties.autoStart && this.remain > 0 && this.start(),
              (this.isInitialTime = !0);
          },
          getTime: function () {
            return Math.max(this.endTime - Date.now(), 0);
          },
          updateTime: function (e) {
            var t = this.properties.format;
            this.remain = e;
            var i = (0, u.parseTimeData)(e);
            this.triggerEvent("change", i);
            var r = (0, u.parseFormat)(e, t).timeText,
              s = t.split(":");
            this.setData({
              timeRange: s,
              timeData: i,
              formattedTime: r.replace(/:/g, " : "),
            }),
              0 === e &&
                (this.counting || this.isInitialTime) &&
                (this.pause(),
                this.triggerEvent("finish"),
                (this.counting = !1));
          },
          doCount: function () {
            var e = this;
            this.timeoutId = setTimeout(function () {
              var t = e.getTime();
              e.properties.millisecond
                ? e.updateTime(t)
                : ((0, u.isSameSecond)(t, e.remain) && 0 !== t) ||
                  e.updateTime(t),
                0 !== t && e.doCount();
            }, 33);
          },
        }),
        e
      );
    }
    return e(n);
  })(a.SuperComponent),
  p = (l = (0, s.__decorate)([(0, a.wxComponent)()], l));
exports.default = p;
