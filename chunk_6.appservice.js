__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/back-top/back-top": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            fixed: new Array(1),
            customStyle: new Array(1),
            style: new Array(1),
            hidden: new Array(1),
          },
          K = U === true,
          e,
          g,
          h = (C, T, E, B, F, S, J) => {
            var $A = I(g);
            if (g && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-icon" },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              g = "icon";
              B(g, h);
            }
          },
          d = (C, T, E, B, F, S) => {
            S("icon");
            e = D._icon ? 1 : 0;
            B(e, f);
          },
          i,
          k = (C, T) => {
            C || K || U.text ? T(Y(D.text)) : T();
          },
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(U.classPrefix || U.theme || U.prefix) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__text--" +
                        Y(D.theme) +
                        " " +
                        Y(D.prefix) +
                        "-class-text"
                    );
                },
                k
              );
            }
          },
          c = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon");
                if (C) O(N, "aria-hidden", true);
              },
              d
            );
            i = !!D.text ? 1 : 0;
            B(i, j);
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.fixed]), U.theme])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          ["fixed", D.fixed],
                          D.theme,
                        ])
                      )
                  );
                A["fixed"][0] = (D, E, T) => {
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          ["fixed", D.fixed],
                          D.theme,
                        ])
                      )
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "bindtap", "toTop");
                if (C) O(N, "aria-role", "button");
                if (C || K || U.hidden) O(N, "hidden", D.hidden);
                A["hidden"][0] = (D, E, T) => {
                  O(N, "hidden", D.hidden);
                  E(N);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/back-top/back-top";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/back-top/back-top.js";
define(
  "miniprogram_npm/tdesign-miniprogram/back-top/back-top.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      o = require("tslib"),
      s = require("../common/src/index"),
      n = c(require("../common/config")),
      a = c(require("./props")),
      l = require("../common/utils");
    function c(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = n.default.prefix,
      p = "".concat(u, "-back-top"),
      h = (function (o) {
        r(n, o);
        var s = i(n);
        function n() {
          var e;
          return (
            t(this, n),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-icon"),
              "".concat(u, "-class-text"),
            ]),
            (e.options = { multipleSlots: !0 }),
            (e.properties = a.default),
            (e.relations = {
              "../pull-down-refresh/pull-down-refresh": { type: "ancestor" },
            }),
            (e.data = { prefix: u, classPrefix: p, _icon: null, hidden: !0 }),
            (e.observers = {
              icon: function () {
                this.setIcon();
              },
              scrollTop: function (e) {
                var t = this.properties.visibilityHeight;
                this.setData({ hidden: e < t });
              },
            }),
            (e.lifetimes = {
              ready: function () {
                var e = this.properties.icon;
                this.setIcon(e);
              },
            }),
            (e.methods = {
              setIcon: function (e) {
                this.setData({ _icon: (0, l.calcIcon)(e, "backtop") });
              },
              toTop: function () {
                var e;
                this.triggerEvent("to-top"),
                  this.$parent
                    ? (null === (e = this.$parent) ||
                        void 0 === e ||
                        e.setScrollTop(0),
                      this.setData({ hidden: !0 }))
                    : wx.pageScrollTo({ scrollTop: 0, duration: 300 });
              },
            }),
            e
          );
        }
        return e(n);
      })(s.SuperComponent),
      d = (h = (0, o.__decorate)([(0, s.wxComponent)()], h));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/back-top/back-top.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/back-top/back-top.js");
