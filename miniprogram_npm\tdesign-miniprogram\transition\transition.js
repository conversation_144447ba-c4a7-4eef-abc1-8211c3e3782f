Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  r = require("../../../@babel/runtime/helpers/classCallCheck"),
  t = require("../../../@babel/runtime/helpers/inherits"),
  i = require("../../../@babel/runtime/helpers/createSuper"),
  a = require("tslib"),
  n = require("../common/src/index"),
  s = u(require("../mixins/transition"));
function u(e) {
  return e && e.__esModule ? e : { default: e };
}
var l = u(require("../common/config")).default.prefix,
  o = "".concat(l, "-transition"),
  c = (function (a) {
    t(u, a);
    var n = i(u);
    function u() {
      var e;
      return (
        r(this, u),
        ((e = n.apply(this, arguments)).externalClasses = [
          "".concat(l, "-class"),
        ]),
        (e.behaviors = [(0, s.default)()]),
        (e.data = { classPrefix: o }),
        e
      );
    }
    return e(u);
  })(n.SuperComponent),
  p = (c = (0, a.__decorate)([(0, n.wxComponent)()], c));
exports.default = p;
