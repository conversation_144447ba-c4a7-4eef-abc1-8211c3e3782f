__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/toast/toast": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            showOverlay: new Array(1),
            overlayProps: new Array(12),
            preventScrollThrough: new Array(3),
            usingCustomNavbar: new Array(1),
          },
          K = U === true,
          c,
          g,
          i = (C) => {},
          j,
          k = (C, T, E, B, F, S, J) => {
            var $A = I(j);
            if (j && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    ariaHidden: true,
                    tClass:
                      D.classPrefix +
                      "__icon " +
                      D.classPrefix +
                      "__icon--" +
                      D.direction,
                  },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        {
                          tClass:
                            !!(U.classPrefix || U.classPrefix || U.direction) ||
                            undefined,
                        },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          h = (C, T, E, B, F, S, J) => {
            if (g === 1) {
              E(
                "t-loading",
                {},
                (N, C) => {
                  if (C) O(N, "theme", "circular");
                  var $A = D.direction === "row";
                  if (C || K || !!U.direction || ($A ? undefined : undefined))
                    O(N, "size", $A ? "48rpx" : "64rpx");
                  if (C) O(N, "loading", true);
                  if (C) O(N, "inherit-color", true);
                  if (C) O(N, "layout", "vertical");
                },
                i
              );
            } else if (g === 2) {
              j = "icon";
              B(j, k);
            }
          },
          l = (C, T) => {
            C || K || U.message ? T(Y(D.message)) : T();
          },
          f = (C, T, E, B, F, S) => {
            g = D.isLoading ? 1 : D._icon ? 2 : 0;
            B(g, h);
            S("icon");
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.direction) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__text " +
                      Y(D.classPrefix) +
                      "__text--" +
                      Y(D.direction)
                  );
                if (C) O(N, "aria-role", "alert");
              },
              l
            );
            S("message");
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.direction) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.classPrefix) +
                      "__content--" +
                      Y(D.direction)
                  );
              },
              f
            );
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.direction, U.theme, Q.a([U.message])]) ||
                      U.prefix ||
                      U.transitionClass
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          D.direction,
                          D.theme,
                          ["with-text", D.message],
                        ])
                      ) +
                        " class " +
                        Y(D.prefix) +
                        "-class " +
                        Y(D.transitionClass)
                    );
                  var $A = D.placement === "top";
                  var $B = D.placement === "bottom";
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!(
                          U.placement ||
                          ($A
                            ? undefined
                            : !!U.placement || ($B ? undefined : undefined))
                        ) || undefined,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        "top:" + ($A ? "25%" : $B ? "75%" : "45%"),
                        D.style,
                        D.customStyle,
                      ])
                    );
                  if (C)
                    R.v(N, "transitionend", "onTransitionEnd", !1, !1, !1, !1);
                  if (C) R.v(N, "touchstart", "loop", !0, !1, !1, !1);
                },
                e
              );
            }
          },
          m = (C) => {},
          b = (C, T, E, B) => {
            c = D.realVisible ? 1 : 0;
            B(c, d);
            E(
              "t-overlay",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.overlayProps || Z(U.overlayProps, "style")) ||
                  undefined
                )
                  R.y(N, (D.overlayProps && X(D.overlayProps).style) || "");
                A["overlayProps"][10] = A["overlayProps"][11] = (D, E, T) => {
                  R.y(N, (D.overlayProps && X(D.overlayProps).style) || "");
                };
                if (
                  C ||
                  K ||
                  !!(
                    U.realVisible ||
                    U.showOverlay ||
                    U.preventScrollThrough
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "visible",
                    D.realVisible && (D.showOverlay || D.preventScrollThrough)
                  );
                A["showOverlay"][0] = A["preventScrollThrough"][0] = (
                  D,
                  E,
                  T
                ) => {
                  O(
                    N,
                    "visible",
                    D.realVisible && (D.showOverlay || D.preventScrollThrough)
                  );
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(U.overlayProps || Z(U.overlayProps, "zIndex")) ||
                  undefined
                )
                  O(
                    N,
                    "z-index",
                    (D.overlayProps && X(D.overlayProps).zIndex) || 11000
                  );
                A["overlayProps"][0] = A["overlayProps"][1] = (D, E, T) => {
                  O(
                    N,
                    "z-index",
                    (D.overlayProps && X(D.overlayProps).zIndex) || 11000
                  );
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(U.overlayProps || Z(U.overlayProps, "duration")) ||
                  undefined
                )
                  O(
                    N,
                    "duration",
                    (D.overlayProps && X(D.overlayProps).duration) || 300
                  );
                A["overlayProps"][2] = A["overlayProps"][3] = (D, E, T) => {
                  O(
                    N,
                    "duration",
                    (D.overlayProps && X(D.overlayProps).duration) || 300
                  );
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(
                    U.overlayProps ||
                    Z(U.overlayProps, "usingCustomNavbar") ||
                    U.usingCustomNavbar
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "usingCustomNavbar",
                    (D.overlayProps && X(D.overlayProps).usingCustomNavbar) ||
                      D.usingCustomNavbar
                  );
                A["overlayProps"][4] =
                  A["overlayProps"][5] =
                  A["usingCustomNavbar"][0] =
                    (D, E, T) => {
                      O(
                        N,
                        "usingCustomNavbar",
                        (D.overlayProps &&
                          X(D.overlayProps).usingCustomNavbar) ||
                          D.usingCustomNavbar
                      );
                      E(N);
                    };
                var $A = D.preventScrollThrough;
                if (
                  C ||
                  K ||
                  !!U.preventScrollThrough ||
                  ($A
                    ? undefined
                    : !!(
                        U.overlayProps || Z(U.overlayProps, "backgroundColor")
                      ) || undefined)
                )
                  O(
                    N,
                    "backgroundColor",
                    $A
                      ? "transparent"
                      : (D.overlayProps && X(D.overlayProps).backgroundColor) ||
                          ""
                  );
                A["preventScrollThrough"][1] =
                  A["overlayProps"][6] =
                  A["overlayProps"][7] =
                    (D, E, T) => {
                      var $B = D.preventScrollThrough;
                      O(
                        N,
                        "backgroundColor",
                        $B
                          ? "transparent"
                          : (D.overlayProps &&
                              X(D.overlayProps).backgroundColor) ||
                              ""
                      );
                      E(N);
                    };
                if (
                  C ||
                  K ||
                  !!(
                    U.preventScrollThrough ||
                    U.overlayProps ||
                    Z(U.overlayProps, "preventScrollThrough")
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "preventScrollThrough",
                    D.preventScrollThrough ||
                      (D.overlayProps && X(D.overlayProps).preventScrollThrough)
                  );
                A["preventScrollThrough"][2] =
                  A["overlayProps"][8] =
                  A["overlayProps"][9] =
                    (D, E, T) => {
                      O(
                        N,
                        "preventScrollThrough",
                        D.preventScrollThrough ||
                          (D.overlayProps &&
                            X(D.overlayProps).preventScrollThrough)
                      );
                      E(N);
                    };
              },
              m
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/toast/toast.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-toast{background-color:var(--td-toast-bg-color,var(--td-font-gray-2,rgba(0,0,0,.6)));border-radius:var(--td-toast-radius,",
        [0, 8],
        ");box-sizing:border-box;color:var(--td-toast-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)));font-size:",
        [0, 28],
        ";left:50%;max-width:var(--td-toast-max-width,",
        [0, 374],
        ");opacity:1;position:fixed;right:-50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);transition:opacity .3s ease;width:-webkit-fit-content;width:fit-content;z-index:12001}\n.",
        [1],
        "t-toast--column{-webkit-align-items:center;align-items:center;border-radius:",
        [0, 16],
        ";display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;min-height:",
        [0, 160],
        ";min-width:",
        [0, 160],
        ";padding:",
        [0, 48],
        "}\n.",
        [1],
        "t-toast--loading.",
        [1],
        "t-toast--with-text{min-height:",
        [0, 204],
        ";min-width:",
        [0, 204],
        ";padding-bottom:0;padding-top:0}\n.",
        [1],
        "t-toast__content{-webkit-align-items:center;align-items:center;line-height:",
        [0, 44],
        "}\n.",
        [1],
        "t-toast__content--row{display:-webkit-flex;display:flex;padding:",
        [0, 28],
        " ",
        [0, 44],
        ";text-align:left}\n.",
        [1],
        "t-toast__content--column{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center}\n.",
        [1],
        "t-toast__icon--row{display:-webkit-flex;display:flex;font-size:var(--td-toast-row-icon-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-toast__icon--column{font-size:var(--td-toast-column-icon-size,",
        [0, 64],
        ")}\n.",
        [1],
        "t-toast__text{-webkit-line-clamp:3;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden;text-overflow:ellipsis;white-space:pre-line}\n.",
        [1],
        "t-toast__text--column:not(:empty):not(:only-child){margin-top:",
        [0, 16],
        "}\n.",
        [1],
        "t-toast__text--row:not(:empty):not(:only-child){margin-left:",
        [0, 16],
        "}\n.",
        [1],
        "t-toast.",
        [1],
        "t-fade-enter,.",
        [1],
        "t-toast.",
        [1],
        "t-fade-leave-to{opacity:0}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/toast/toast.wxss" }
    );
}
