__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "components/communityHelp/communityHelp": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { helpData: new Array(8) },
          K = U === true,
          f = (C) => {},
          e = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || Z(U.helpData, "imgSrc"))
                  O(N, "src", X(D.helpData).imgSrc);
                A["helpData"][0] = (D, E, T) => {
                  O(N, "src", X(D.helpData).imgSrc);
                  E(N);
                };
              },
              f
            );
          },
          i = (C, T) => {
            C || K || Z(U.helpData, "title")
              ? T(Y(X(D.helpData).title), (N) => {
                  A["helpData"][1] = (D, E, T) => {
                    T(N, Y(X(D.helpData).title));
                  };
                })
              : T();
          },
          h = (C, T, E) => {
            E("text", {}, (N, C) => {}, i);
          },
          k = (C, T) => {
            C || K || Z(U.helpData, "description")
              ? T(Y(X(D.helpData).description), (N) => {
                  A["helpData"][2] = (D, E, T) => {
                    T(N, Y(X(D.helpData).description));
                  };
                })
              : T();
          },
          j = (C, T, E) => {
            E("text", {}, (N, C) => {}, k);
          },
          m = (C, T) => {
            C || K || !!Z(U.helpData, "overTime") || undefined
              ? T(Y("截止时间:" + Y(X(D.helpData).overTime)), (N) => {
                  A["helpData"][3] = (D, E, T) => {
                    T(N, Y("截止时间:" + Y(X(D.helpData).overTime)));
                  };
                })
              : T();
          },
          l = (C, T, E) => {
            E("text", {}, (N, C) => {}, m);
          },
          o = (C, T) => {
            C || K || !!Z(U.helpData, "userLimit") || undefined
              ? T(Y("接单限制：" + Y(X(D.helpData).userLimit)), (N) => {
                  A["helpData"][4] = (D, E, T) => {
                    T(N, Y("接单限制：" + Y(X(D.helpData).userLimit)));
                  };
                })
              : T();
          },
          n = (C, T, E) => {
            E("text", {}, (N, C) => {}, o);
          },
          g = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_title");
              },
              h
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_ms");
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_time");
              },
              l
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_fw");
              },
              n
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_img");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info");
              },
              g
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top");
              },
              d
            );
          },
          s = (C) => {},
          r = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || Z(U.helpData, "providerAvatar"))
                  O(N, "src", X(D.helpData).providerAvatar);
                A["helpData"][5] = (D, E, T) => {
                  O(N, "src", X(D.helpData).providerAvatar);
                  E(N);
                };
              },
              s
            );
          },
          t = (C, T) => {
            C || K || Z(U.helpData, "providerName")
              ? T(Y(X(D.helpData).providerName), (N) => {
                  A["helpData"][6] = (D, E, T) => {
                    T(N, Y(X(D.helpData).providerName));
                  };
                })
              : T();
          },
          q = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_top_img");
              },
              r
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_top_username");
              },
              t
            );
          },
          w = (C, T) => {
            C ? T("报酬：") : T();
          },
          x = (C, T) => {
            C || K || !!Z(U.helpData, "price") || undefined
              ? T(Y("￥" + Y(X(D.helpData).price)), (N) => {
                  A["helpData"][7] = (D, E, T) => {
                    T(N, Y("￥" + Y(X(D.helpData).price)));
                  };
                })
              : T();
          },
          v = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "price-label");
              },
              w
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "price-value");
              },
              x
            );
          },
          u = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_middle_price");
              },
              v
            );
          },
          p = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_top");
              },
              q
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_middle");
              },
              u
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left");
              },
              c
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right");
              },
              p
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fuwu_list");
                if (C) O(N, "bindtap", "onHelpTap");
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/notice-bar/notice-bar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          g,
          i,
          j = (C, T, E, B, F, S, J) => {
            var $A = I(i);
            if (i && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-prefix-icon" },
                  X(D._prefixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._prefixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._prefixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          h = (C, T, E, B, F, S, J) => {
            if (g === 1) {
              i = "icon";
              B(i, j);
            }
          },
          f = (C, T, E, B, F, S) => {
            S("prefix-icon");
            g = D._prefixIcon ? 1 : 0;
            B(g, h);
          },
          l,
          p = (C, q, r, s, t, u, T, E) => {
            var w = (C, T) => {
                C || K || s ? T(Y(q)) : T();
              },
              v = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!U.classPrefix || undefined)
                      L(N, Y(D.classPrefix) + "__content--vertical-item");
                  },
                  w
                );
              };
            E("swiper-item", {}, (N, C) => {}, v);
          },
          o = (C, T, E, B, F) => {
            F(D.content, "index", U ? U.content : undefined, [0, "content"], p);
          },
          n = (C, T, E) => {
            E(
              "swiper",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.classPrefix) +
                      "__content--vertical"
                  );
                if (C) O(N, "autoplay", "true");
                if (C) O(N, "vertical", "true");
                if (C) O(N, "circular", "true");
                if (C || K || U.interval) O(N, "interval", D.interval);
                if (C) O(N, "display-multiple-items", "1");
                if (C) O(N, "bindchange", "onChange");
              },
              o
            );
          },
          r,
          s = (C, T) => {
            if (r === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          u,
          v = (C, T) => {
            if (u === 1) {
              C || K || U.operation ? T(Y(D.operation)) : T();
            }
          },
          t = (C, T, E, B, F, S) => {
            u = D.operation ? 1 : 0;
            B(u, v);
            S("operation");
          },
          q = (C, T, E, B, F, S) => {
            r = D.content ? 1 : 0;
            B(r, s);
            S("content");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__operation " +
                      Y(D.prefix) +
                      "-class-operation"
                  );
                if (C) R.v(N, "tap", "clickOperation", !0, !1, !1, !1);
              },
              t
            );
          },
          m = (C, T, E) => {
            if (l === 1) {
              E("view", {}, (N, C) => {}, n);
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = !D.marquee;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.prefix ||
                      U.marquee ||
                      ($A ? !!U.classPrefix || undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__content " +
                        Y(D.prefix) +
                        "-class-content " +
                        Y($A ? D.classPrefix + "__content-wrapable" : "")
                    );
                  if (C || K || U.animationData)
                    O(N, "animation", D.animationData);
                },
                q
              );
            }
          },
          k = (C, T, E, B) => {
            l =
              D.direction === "vertical" && P(X(a).isArray)(D.content) ? 1 : 0;
            B(l, m);
          },
          x,
          z,
          A0 = (C, T, E, B, F, S, J) => {
            var $A = I(z);
            if (z && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-suffix-icon" },
                  X(D._suffixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._suffixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._suffixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          y = (C, T, E, B, F, S, J) => {
            if (x === 1) {
              z = "icon";
              B(z, A0);
            }
          },
          w = (C, T, E, B, F, S) => {
            S("suffix-icon");
            x = D._suffixIcon ? 1 : 0;
            B(x, y);
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__prefix-icon");
                if (C) R.v(N, "tap", "clickPrefixIcon", !1, !1, !1, !1);
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__content-wrap");
                if (C) R.v(N, "tap", "clickContent", !1, !1, !1, !1);
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__suffix-icon");
                if (C) R.v(N, "tap", "clickSuffixIcon", !1, !1, !1, !1);
              },
              w
            );
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(U.classPrefix || U.classPrefix || U.theme || U.prefix) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        " " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.theme) +
                        " class " +
                        Y(D.prefix) +
                        "-class"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                },
                e
              );
            }
          },
          b = (C, T, E, B) => {
            c = D.visible ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/swiper-nav/swiper-nav": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          f = (C) => {},
          g = (C) => {},
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__btn--prev");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "上一张");
                if (C) R.d(N, "dir", "prev");
                if (C) R.v(N, "tap", "nav", !1, !1, !1, !1);
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__btn--next");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "下一张");
                if (C) R.d(N, "dir", "next");
                if (C) R.v(N, "tap", "nav", !1, !1, !1, !1);
              },
              g
            );
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                    L(
                      N,
                      "class " +
                        Y(D.prefix) +
                        "-class " +
                        Y(D.classPrefix) +
                        "__btn"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                },
                e
              );
            }
          },
          h,
          k,
          m = (C, n, o, p, q, r, T, E) => {
            var s = (C) => {};
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    U.type ||
                    Q.a([Q.a([!!(U.current || q) || undefined]), U.direction])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__" + D.type + "-item", [
                      ["active", D.current === o],
                      D.direction,
                    ])
                  );
              },
              s
            );
          },
          l = (C, T, E, B, F) => {
            if (k === 1) {
              F(D.total, "idx", U ? U.total : undefined, [0, "total"], m);
            }
          },
          n,
          o = (C, T) => {
            if (n === 1) {
              C || K || !!(U.current || U.total) || undefined
                ? T(Y(Y(D.current + 1) + "/" + Y(D.total)))
                : T();
            }
          },
          j = (C, T, E, B) => {
            k = D.type === "dots" || D.type === "dots-bar" ? 1 : 0;
            B(k, l);
            n = D.type === "fraction" ? 1 : 0;
            B(n, o);
          },
          i = (C, T, E) => {
            if (h === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.prefix ||
                      U.classPrefix ||
                      U.classPrefix ||
                      U.direction ||
                      U.classPrefix ||
                      U.type ||
                      U.classPrefix ||
                      U.paginationPosition
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      "class " +
                        Y(D.prefix) +
                        "-class " +
                        Y(D.classPrefix) +
                        " " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.direction) +
                        " " +
                        Y(D.classPrefix) +
                        "__" +
                        Y(D.type) +
                        " " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.paginationPosition)
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                },
                j
              );
            }
          },
          b = (C, T, E, B) => {
            c = D.showControls ? 1 : 0;
            B(c, d);
            h = D.total >= D.minShowNum ? 1 : 0;
            B(h, i);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/swiper/swiper": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/image"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/swiper/index"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            current: new Array(1),
            snapToEdge: new Array(1),
            loop: new Array(1),
            nextMargin: new Array(1),
            interval: new Array(1),
            duration: new Array(1),
            style: new Array(1),
            easingFunction: new Array(1),
            previousMargin: new Array(1),
            displayMultipleItems: new Array(1),
            customStyle: new Array(1),
            autoplay: new Array(1),
          },
          K = U === true,
          f = (C, g, h, i, j, k, T, E) => {
            var m,
              n = (C, T, E, B, F, S, J) => {
                var $A = I(m);
                var $B = P(X(b).isObject)(g);
                if (m && $A)
                  $A(
                    R,
                    C,
                    Object.assign(
                      {
                        tClass: P(X(a).getImageClass)(
                          D.prefix,
                          D.navCurrent,
                          h,
                          D.list
                        ),
                        style: "height: " + P(X(b).addUnit)(D.height),
                        src: $B ? X(g).value : g,
                        mode: "aspectFill",
                        dataset: h,
                      },
                      X(D.imageProps),
                      { bindload: "onImageLoad" }
                    ),
                    K ||
                      (U
                        ? U.imageProps === true ||
                          Object.assign(
                            {
                              tClass:
                                !!(
                                  Z(undefined, "getImageClass") ||
                                  U.prefix ||
                                  U.navCurrent ||
                                  j ||
                                  U.list
                                ) || undefined,
                              style:
                                !!(Z(undefined, "addUnit") || U.height) ||
                                undefined,
                              src:
                                !!(Z(undefined, "isObject") || i) ||
                                ($B ? Z(i, "value") : i),
                              dataset: j,
                            },
                            X(U.imageProps),
                            {}
                          )
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              },
              l = (C, T, E, B, F, S, J) => {
                m = "image";
                B(m, n);
              };
            E(
              "swiper-item",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([
                        !!(
                          Z(undefined, "isPrev") ||
                          U.navCurrent ||
                          j ||
                          U.list
                        ) || undefined,
                      ]),
                      Q.a([
                        !!(
                          Z(undefined, "isNext") ||
                          U.navCurrent ||
                          j ||
                          U.list
                        ) || undefined,
                      ]),
                    ])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__item", [
                      ["preview", P(X(a).isPrev)(D.navCurrent, h, D.list)],
                      ["next", P(X(a).isNext)(D.navCurrent, h, D.list)],
                    ])
                  );
                if (C || K || !!(U.navCurrent || j) || undefined)
                  O(N, "aria-hidden", D.navCurrent !== h);
                if (C) O(N, "aria-role", "image");
                var $A = P(X(b).isObject)(g);
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "isObject") || i) ||
                  ($A ? Z(i, "ariaLabel") : undefined)
                )
                  O(N, "aria-label", $A ? X(g).ariaLabel : "");
                if (C || K || j) R.d(N, "index", h);
                if (C) R.v(N, "tap", "onTap", !1, !1, !1, !1);
              },
              l
            );
          },
          e = (C, T, E, B, F) => {
            F(D.list, "index", U ? U.list : undefined, [0, "list"], f);
          },
          g,
          i = (C) => {},
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "t-swiper-nav",
                {},
                (N, C) => {
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class", Y(D.prefix) + "-class-nav");
                  if (C || K || !!Z(U.navigation, "type") || undefined)
                    O(N, "type", X(D.navigation).type || "dots");
                  if (C || K || !!U.navCurrent || undefined)
                    O(N, "current", D.navCurrent || 0);
                  if (C || K || !!Z(U.list, "length") || undefined)
                    O(N, "total", X(D.list).length || 0);
                  if (C || K || !!U.direction || undefined)
                    O(N, "direction", D.direction || "horizontal");
                  if (C || K || !!U.paginationPosition || undefined)
                    O(
                      N,
                      "pagination-position",
                      D.paginationPosition || "bottom"
                    );
                  if (C || K || !!Z(U.navigation, "minShowNum") || undefined)
                    O(N, "min-show-num", X(D.navigation).minShowNum || 2);
                  if (C || K || !!Z(U.navigation, "showControls") || undefined)
                    O(
                      N,
                      "show-controls",
                      X(D.navigation).showControls || false
                    );
                  if (C)
                    R.v(N, "nav-btn-change", "onNavBtnChange", !1, !1, !1, !1);
                },
                i
              );
            }
          },
          d = (C, T, E, B, F, S) => {
            E(
              "swiper",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "-host");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "addUnit") || U.height) ||
                  undefined
                )
                  R.y(N, "height:" + Y(P(X(b).addUnit)(D.height)));
                if (C || K || U.autoplay) O(N, "autoplay", D.autoplay);
                A["autoplay"][0] = (D, E, T) => {
                  O(N, "autoplay", D.autoplay);
                  E(N);
                };
                if (C || K || U.current) O(N, "current", D.current);
                A["current"][0] = (D, E, T) => {
                  O(N, "current", D.current);
                  E(N);
                };
                if (C || K || U.interval) O(N, "interval", D.interval);
                A["interval"][0] = (D, E, T) => {
                  O(N, "interval", D.interval);
                  E(N);
                };
                if (C || K || U.duration) O(N, "duration", D.duration);
                A["duration"][0] = (D, E, T) => {
                  O(N, "duration", D.duration);
                  E(N);
                };
                if (C || K || U.loop) O(N, "circular", D.loop);
                A["loop"][0] = (D, E, T) => {
                  O(N, "circular", D.loop);
                  E(N);
                };
                if (C || K || !!U.direction || undefined)
                  O(N, "vertical", D.direction == "vertical");
                if (C || K || U.easingFunction)
                  O(N, "easing-function", D.easingFunction);
                A["easingFunction"][0] = (D, E, T) => {
                  O(N, "easing-function", D.easingFunction);
                  E(N);
                };
                if (C || K || U.previousMargin)
                  O(N, "previous-margin", D.previousMargin);
                A["previousMargin"][0] = (D, E, T) => {
                  O(N, "previous-margin", D.previousMargin);
                  E(N);
                };
                if (C || K || U.nextMargin) O(N, "next-margin", D.nextMargin);
                A["nextMargin"][0] = (D, E, T) => {
                  O(N, "next-margin", D.nextMargin);
                  E(N);
                };
                if (C || K || U.snapToEdge) O(N, "snap-to-edge", D.snapToEdge);
                A["snapToEdge"][0] = (D, E, T) => {
                  O(N, "snap-to-edge", D.snapToEdge);
                  E(N);
                };
                if (C || K || U.displayMultipleItems)
                  O(N, "display-multiple-items", D.displayMultipleItems);
                A["displayMultipleItems"][0] = (D, E, T) => {
                  O(N, "display-multiple-items", D.displayMultipleItems);
                  E(N);
                };
                if (C) O(N, "bindchange", "onChange");
              },
              e
            );
            g = D.navigation ? 1 : 0;
            B(g, h);
            S("navigation");
            S("nav");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "pages/index/index": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            current: new Array(1),
            autoplay: new Array(1),
            interval: new Array(1),
            appName: new Array(1),
            duration: new Array(1),
            showIncompleteNotice: new Array(1),
            swiperList: new Array(1),
            height: new Array(1),
          },
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || U.appName) O(N, "title", D.appName);
                A["appName"][0] = (D, E, T) => {
                  O(N, "title", D.appName);
                  E(N);
                };
                if (C || K || undefined) O(N, "back", false);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          f = (C) => {},
          e = (C, T, E) => {
            E(
              "t-swiper",
              {},
              (N, C) => {
                if (C || K || U.current) O(N, "current", D.current);
                A["current"][0] = (D, E, T) => {
                  O(N, "current", D.current);
                  E(N);
                };
                if (C || K || U.autoplay) O(N, "autoplay", D.autoplay);
                A["autoplay"][0] = (D, E, T) => {
                  O(N, "autoplay", D.autoplay);
                  E(N);
                };
                if (C || K || U.duration) O(N, "duration", D.duration);
                A["duration"][0] = (D, E, T) => {
                  O(N, "duration", D.duration);
                  E(N);
                };
                if (C || K || U.interval) O(N, "interval", D.interval);
                A["interval"][0] = (D, E, T) => {
                  O(N, "interval", D.interval);
                  E(N);
                };
                if (C || K || U.height) O(N, "height", D.height);
                A["height"][0] = (D, E, T) => {
                  O(N, "height", D.height);
                  E(N);
                };
                if (C || K || U.swiperList) O(N, "list", D.swiperList);
                A["swiperList"][0] = (D, E, T) => {
                  O(N, "list", D.swiperList);
                  E(N);
                };
                if (C || K || Q.b({})) O(N, "navigation", { type: "dots-bar" });
                if (C) R.v(N, "click", "onSwiperClick", !1, !1, !1, !1);
              },
              f
            );
          },
          i,
          l = (C) => {},
          k = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "32rpx");
                if (C) O(N, "text", "加载中...");
              },
              l
            );
          },
          n = (C, o, p, q, r, s, T, E) => {
            var t = (C) => {};
            E(
              "t-grid-item",
              {},
              (N, C) => {
                if (C || K || Z(q, "name")) O(N, "text", X(o).name);
                if (C || K || Z(q, "thumbnail")) O(N, "image", X(o).thumbnail);
                if (C) O(N, "t-class", "category-item");
                if (C) O(N, "t-class-image", "category-image");
                if (C) O(N, "bindtap", "onCategoryTap");
                if (C || K || q) R.d(N, "category", o);
              },
              t
            );
          },
          m = (C, T, E, B, F) => {
            F(
              D.categoryList,
              "id",
              U ? U.categoryList : undefined,
              [0, "categoryList"],
              n
            );
          },
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                k
              );
            } else {
              E(
                "t-grid",
                {},
                (N, C) => {
                  if (C || K || undefined) O(N, "column", 3);
                  if (C || K || undefined) O(N, "border", false);
                },
                m
              );
            }
          },
          o,
          r = (C) => {},
          q = (C, T, E) => {
            E(
              "t-empty",
              {},
              (N, C) => {
                if (C) O(N, "icon", "service");
                if (C) O(N, "description", "暂无分类");
              },
              r
            );
          },
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty-category");
                },
                q
              );
            }
          },
          h = (C, T, E, B) => {
            i = D.categoryLoading ? 1 : 0;
            B(i, j);
            o = X(D.categoryList).length === 0 && !D.categoryLoading ? 1 : 0;
            B(o, p);
          },
          g = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "category-grid");
              },
              h
            );
          },
          t = (C) => {},
          s = (C, T, E) => {
            E(
              "t-notice-bar",
              {},
              (N, C) => {
                if (C || K || U.showIncompleteNotice)
                  O(N, "visible", D.showIncompleteNotice);
                A["showIncompleteNotice"][0] = (D, E, T) => {
                  O(N, "visible", D.showIncompleteNotice);
                  E(N);
                };
                if (C) O(N, "theme", "warning");
                if (C)
                  O(N, "content", "需要补充认证信息后，才可以发布服务或求助");
                if (C) O(N, "bindtap", "goToEditProfile");
              },
              t
            );
          },
          w = (C) => {},
          v = (C, T, E) => {
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "value", "service");
                if (C) O(N, "label", "热门服务");
              },
              w
            );
          },
          u = (C, T, E) => {
            E(
              "t-tabs",
              {},
              (N, C) => {
                if (C || K || U.activeTab) O(N, "value", D.activeTab);
                if (C) O(N, "theme", "line");
                if (C || K || undefined) O(N, "space-evenly", false);
                if (C || K || undefined) O(N, "sticky", false);
                if (C) O(N, "t-class", "custom-tabs");
                if (C) O(N, "t-class-track", "custom-track");
                if (C) R.v(N, "change", "onTabChange", !1, !1, !1, !1);
              },
              v
            );
          },
          x,
          A0,
          D0 = (C) => {},
          C0 = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "40rpx");
                if (C) O(N, "text", "加载中...");
              },
              D0
            );
          },
          F0 = (C, G0, H0, I0, J0, K0, T, E) => {
            var L0 = (C) => {};
            E(
              "service-card",
              {},
              (N, C) => {
                if (C || K || I0) O(N, "service", G0);
                if (C) R.v(N, "contact", "handleContact", !1, !1, !1, !1);
                if (C) R.v(N, "confirm", "handleConfirm", !1, !1, !1, !1);
              },
              L0
            );
          },
          G0,
          J0,
          M0 = (C) => {},
          L0 = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "32rpx");
                if (C) O(N, "text", "加载更多...");
              },
              M0
            );
          },
          N0 = (C, T) => {
            C ? T("点击加载更多") : T();
          },
          K0 = (C, T, E) => {
            if (J0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-more");
                },
                L0
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "load-more-text");
                },
                N0
              );
            }
          },
          I0 = (C, T, E, B) => {
            J0 = D.serviceLoading ? 1 : 0;
            B(J0, K0);
          },
          H0 = (C, T, E) => {
            if (G0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "load-more");
                  if (C) O(N, "bindtap", "loadMoreServices");
                },
                I0
              );
            }
          },
          O0,
          Q0 = (C, T) => {
            C ? T("没有更多服务了") : T();
          },
          P0 = (C, T, E) => {
            if (O0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "no-more");
                },
                Q0
              );
            }
          },
          R0,
          U0 = (C) => {},
          T0 = (C, T, E) => {
            E(
              "t-empty",
              {},
              (N, C) => {
                if (C) O(N, "icon", "service");
                if (C) O(N, "description", "暂无服务");
              },
              U0
            );
          },
          S0 = (C, T, E) => {
            if (R0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty-state");
                },
                T0
              );
            }
          },
          E0 = (C, T, E, B, F) => {
            F(
              D.serviceList,
              "id",
              U ? U.serviceList : undefined,
              [0, "serviceList"],
              F0
            );
            G0 = D.serviceHasMore ? 1 : 0;
            B(G0, H0);
            O0 = !D.serviceHasMore && X(D.serviceList).length > 0 ? 1 : 0;
            B(O0, P0);
            R0 = X(D.serviceList).length === 0 && !D.serviceLoading ? 1 : 0;
            B(R0, S0);
          },
          B0 = (C, T, E) => {
            if (A0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                C0
              );
            } else {
              E("view", {}, (N, C) => {}, E0);
            }
          },
          z = (C, T, E, B) => {
            A0 = D.serviceLoading && X(D.serviceList).length === 0 ? 1 : 0;
            B(A0, B0);
          },
          y = (C, T, E) => {
            if (x === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                z
              );
            }
          },
          d = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "lb_box");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "nr_box");
              },
              g
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "nr_box");
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fl_box");
              },
              u
            );
            x = D.activeTab === "service" ? 1 : 0;
            B(x, y);
          },
          a = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "components/communityHelp/communityHelp";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "components/communityHelp/communityHelp.js";
define(
  "components/communityHelp/communityHelp.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Component({
      properties: {
        helpData: {
          type: Object,
          value: {
            imgSrc: "/assets/img/汉堡包.png",
            title: "默认标题",
            description: "默认描述",
            overTime: "5月21日 10:00",
            userLimit: "认证用户",
            providerAvatar: "/assets/tx/实习生女孩.png",
            providerName: "默认名称",
            price: "0.00",
          },
        },
      },
      data: {},
      methods: {
        onHelpTap: function () {
          wx.navigateTo({ url: "/pages/help-info/help-info" });
        },
      },
    });
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "components/communityHelp/communityHelp.js",
  }
);
require("components/communityHelp/communityHelp.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/notice-bar/notice-bar";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/notice-bar/notice-bar.js";
define(
  "miniprogram_npm/tdesign-miniprogram/notice-bar/notice-bar.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var t = require("../../../@babel/runtime/helpers/slicedToArray"),
      i = require("../../../@babel/runtime/helpers/createClass"),
      e = require("../../../@babel/runtime/helpers/classCallCheck"),
      n = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      o = require("../common/src/index"),
      c = require("../common/utils"),
      s = l(require("./props"));
    function l(t) {
      return t && t.__esModule ? t : { default: t };
    }
    var u = l(require("../common/config")).default.prefix,
      h = "".concat(u, "-notice-bar"),
      f = {
        info: "info-circle-filled",
        success: "check-circle-filled",
        warning: "info-circle-filled",
        error: "error-circle-filled",
      },
      m = (function (a) {
        n(l, a);
        var o = r(l);
        function l() {
          var i;
          return (
            e(this, l),
            ((i = o.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-content"),
              "".concat(u, "-class-prefix-icon"),
              "".concat(u, "-class-operation"),
              "".concat(u, "-class-suffix-icon"),
            ]),
            (i.options = { multipleSlots: !0, pureDataPattern: /^__/ }),
            (i.properties = s.default),
            (i.data = { prefix: u, classPrefix: h, loop: -1, __ready: !1 }),
            (i.observers = {
              marquee: function (t) {
                ("{}" !== JSON.stringify(t) && "true" !== JSON.stringify(t)) ||
                  this.setData({ marquee: { speed: 50, loop: -1, delay: 0 } });
              },
              visible: function (t) {
                this.data.__ready &&
                  (t ? this.show() : this.clearNoticeBarAnimation());
              },
              prefixIcon: function (t) {
                this.setPrefixIcon(t);
              },
              suffixIcon: function (t) {
                this.setData({ _suffixIcon: (0, c.calcIcon)(t) });
              },
              content: function () {
                this.data.__ready &&
                  (this.clearNoticeBarAnimation(), this.initAnimation());
              },
            }),
            (i.lifetimes = {
              created: function () {
                this.resetAnimation = wx.createAnimation({
                  duration: 0,
                  timingFunction: "linear",
                });
              },
              detached: function () {
                this.clearNoticeBarAnimation();
              },
              ready: function () {
                this.show(), this.setData({ __ready: !0 });
              },
            }),
            (i.methods = {
              initAnimation: function () {
                var i = this,
                  e = ".".concat(h, "__content-wrap"),
                  n = ".".concat(h, "__content");
                (0, c.getAnimationFrame)(this, function () {
                  Promise.all([(0, c.getRect)(i, n), (0, c.getRect)(i, e)])
                    .then(function (e) {
                      var n = t(e, 2),
                        r = n[0],
                        a = n[1],
                        o = i.properties.marquee;
                      if (
                        null != r &&
                        null != a &&
                        r.width &&
                        a.width &&
                        !1 !== o &&
                        (o || a.width < r.width)
                      ) {
                        var c = o.speed || 50,
                          s = o.delay || 0,
                          l = ((a.width + r.width) / c) * 1e3,
                          u = (r.width / c) * 1e3;
                        i.setData({
                          wrapWidth: Number(a.width),
                          nodeWidth: Number(r.width),
                          animationDuration: l,
                          delay: s,
                          loop: o.loop - 1,
                          firstAnimationDuration: u,
                        }),
                          0 !== o.loop && i.startScrollAnimation(!0);
                      }
                    })
                    .catch(function () {});
                });
              },
              startScrollAnimation: function () {
                var t = this,
                  i =
                    arguments.length > 0 &&
                    void 0 !== arguments[0] &&
                    arguments[0];
                this.clearNoticeBarAnimation();
                var e = this.data,
                  n = e.wrapWidth,
                  r = e.nodeWidth,
                  a = e.firstAnimationDuration,
                  o = e.animationDuration,
                  s = e.delay,
                  l = i ? s : 0,
                  u = i ? a : o;
                this.setData({
                  animationData: this.resetAnimation
                    .translateX(i ? 0 : n)
                    .step()
                    .export(),
                }),
                  (0, c.getAnimationFrame)(this, function () {
                    t.setData({
                      animationData: wx
                        .createAnimation({
                          duration: u,
                          timingFunction: "linear",
                          delay: l,
                        })
                        .translateX(-r)
                        .step()
                        .export(),
                    });
                  }),
                  (this.nextAnimationContext = setTimeout(function () {
                    t.data.loop > 0
                      ? ((t.data.loop -= 1), t.startScrollAnimation())
                      : 0 === t.data.loop
                      ? t.setData({
                          animationData: t.resetAnimation
                            .translateX(0)
                            .step()
                            .export(),
                        })
                      : t.data.loop < 0 && t.startScrollAnimation();
                  }, u + l));
              },
              show: function () {
                this.clearNoticeBarAnimation(),
                  this.setPrefixIcon(this.properties.prefixIcon),
                  this.initAnimation();
              },
              clearNoticeBarAnimation: function () {
                this.nextAnimationContext &&
                  clearTimeout(this.nextAnimationContext),
                  (this.nextAnimationContext = null);
              },
              setPrefixIcon: function (t) {
                var i = this.properties.theme;
                this.setData({ _prefixIcon: (0, c.calcIcon)(t, f[i]) });
              },
              onChange: function (t) {
                var i = t.detail,
                  e = i.current,
                  n = i.source;
                this.triggerEvent("change", { current: e, source: n });
              },
              clickPrefixIcon: function () {
                this.triggerEvent("click", { trigger: "prefix-icon" });
              },
              clickContent: function () {
                this.triggerEvent("click", { trigger: "content" });
              },
              clickSuffixIcon: function () {
                this.triggerEvent("click", { trigger: "suffix-icon" });
              },
              clickOperation: function () {
                this.triggerEvent("click", { trigger: "operation" });
              },
            }),
            i
          );
        }
        return i(l);
      })(o.SuperComponent),
      d = (m = (0, a.__decorate)([(0, o.wxComponent)()], m));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/notice-bar/notice-bar.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/notice-bar/notice-bar.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/swiper-nav/swiper-nav";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/swiper-nav/swiper-nav.js";
define(
  "miniprogram_npm/tdesign-miniprogram/swiper-nav/swiper-nav.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e,
      r = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      o = require("../common/src/index");
    var s = (
        (e = require("../common/config")) && e.__esModule ? e : { default: e }
      ).default.prefix,
      u = "".concat(s, "-swiper-nav"),
      l = (function (e) {
        a(o, e);
        var i = n(o);
        function o() {
          var e;
          return (
            t(this, o),
            ((e = i.apply(this, arguments)).externalClasses = [
              "".concat(s, "-class"),
            ]),
            (e.properties = {
              current: { type: Number, value: 0 },
              total: { type: Number, value: 0 },
              type: { type: String, value: "dots" },
              minShowNum: { type: Number, value: 2 },
              showControls: { type: Boolean, value: !1 },
              direction: { type: String, value: "horizontal" },
              paginationPosition: { type: String, value: "bottom" },
            }),
            (e.relations = { "../swiper/swiper": { type: "parent" } }),
            (e.data = { prefix: s, classPrefix: u }),
            (e.methods = {
              nav: function (e) {
                var r,
                  t = e.target.dataset.dir;
                this.triggerEvent("nav-btn-change", { dir: t, source: "nav" }),
                  this.$parent &&
                    (null === (r = this.$parent) ||
                      void 0 === r ||
                      r.doNavBtnChange(t, "nav"));
              },
            }),
            e
          );
        }
        return r(o);
      })(o.SuperComponent),
      p = (l = (0, i.__decorate)([(0, o.wxComponent)()], l));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/swiper-nav/swiper-nav.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/swiper-nav/swiper-nav.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/swiper/swiper";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/swiper/swiper.js";
define(
  "miniprogram_npm/tdesign-miniprogram/swiper/swiper.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      i = require("../common/src/index"),
      s = c(require("../common/config")),
      o = c(require("./props"));
    function c(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = s.default.prefix,
      l = "".concat(u, "-swiper"),
      p = (function (a) {
        r(s, a);
        var i = n(s);
        function s() {
          var e;
          return (
            t(this, s),
            ((e = i.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-nav"),
              "".concat(u, "-class-image"),
              "".concat(u, "-class-prev-image"),
              "".concat(u, "-class-next-image"),
            ]),
            (e.options = { multipleSlots: !0 }),
            (e.properties = o.default),
            (e.observers = {
              navCurrent: function (e) {
                this.updateNav(e);
              },
            }),
            (e.$nav = null),
            (e.relations = { "../swiper-nav/swiper-nav": { type: "child" } }),
            (e.data = { prefix: u, classPrefix: l }),
            (e.lifetimes = {
              ready: function () {
                var e = this.properties.current;
                this.setData({ navCurrent: e });
              },
            }),
            (e.methods = {
              updateNav: function (e) {
                var t;
                if (!this.data.navigation) {
                  var r =
                    null === (t = this.getRelationNodes("./swiper-nav")) ||
                    void 0 === t
                      ? void 0
                      : t[0];
                  if (r) {
                    var n = this.properties,
                      a = n.direction,
                      i = n.paginationPosition,
                      s = n.list;
                    r.setData({
                      current: e,
                      total: s.length,
                      direction: a,
                      paginationPosition: i,
                    });
                  }
                }
              },
              onTap: function (e) {
                var t = e.currentTarget.dataset.index;
                this.triggerEvent("click", { index: t });
              },
              onChange: function (e) {
                var t = e.detail,
                  r = t.current,
                  n = t.source;
                this.setData({ navCurrent: r }),
                  this.triggerEvent("change", { current: r, source: n });
              },
              onNavBtnChange: function (e) {
                var t = e.detail,
                  r = t.dir,
                  n = t.source;
                this.doNavBtnChange(r, n);
              },
              doNavBtnChange: function (e, t) {
                var r = this.data,
                  n = r.current,
                  a = r.list,
                  i = r.loop,
                  s = a.length,
                  o = "next" === e ? n + 1 : n - 1;
                (o = i
                  ? "next" === e
                    ? (n + 1) % s
                    : (n - 1 + s) % s
                  : o < 0 || o >= s
                  ? n
                  : o) !== n &&
                  (this.setData({ current: o }),
                  this.triggerEvent("change", { current: o, source: t }));
              },
              onImageLoad: function (e) {
                this.triggerEvent("image-load", {
                  index: e.target.dataset.custom,
                });
              },
            }),
            e
          );
        }
        return e(s);
      })(i.SuperComponent),
      d = (p = (0, a.__decorate)([(0, i.wxComponent)()], p));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/swiper/swiper.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/swiper/swiper.js");
__wxRoute = "pages/index/index";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/index/index.js";
define(
  "pages/index/index.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      t = require("../../@babel/runtime/helpers/toConsumableArray"),
      a = require("../../@babel/runtime/helpers/asyncToGenerator"),
      r = require("../../env/index.js");
    Page({
      data: {
        appName: r.appName,
        current: 1,
        autoplay: !0,
        duration: 500,
        interval: 5e3,
        height: 210,
        categoryList: [],
        categoryLoading: !1,
        swiperList: [],
        showIncompleteNotice: !1,
        activeTab: "service",
        img1: "/assets/icon/025-map point.svg",
        img2: "/assets/icon/026-wallet.svg",
        serviceList: [],
        serviceLoading: !1,
        servicePage: 1,
        serviceLimit: 10,
        serviceHasMore: !0,
        orderData: {
          userInfo: {
            avatar: "/assets/tx/时尚职业男.png",
            name: "小美",
            id: "18-2-906",
          },
          serviceInfo: {
            icon: "/assets/img/鱼生套餐.png",
            title: "试卷打印",
            attachments: [1, 2],
            price: 0.5,
            quantity: 1,
            totalAmount: 1,
          },
        },
        helpItem: {
          imgSrc: "/assets/img/男人拿喇叭.png",
          title: "帮忙开窗通风每天按时开窗通风每天按时开窗通风",
          description: "每天按时开窗通风每天按时开窗通风每天按时开窗通风",
          overTime: "5月21日 10:00",
          userLimit: "认证用户",
          providerAvatar: "/assets/tx/商务职业男.png",
          providerName: "张三",
          price: "0.00",
        },
        userCardInfo: {
          avatar: "/assets/tx/眼镜职业女.png",
          name: "小美",
          serviceCount: 123,
          rating: 9.5,
          isVerified: !1,
        },
        userServices: [
          {
            image: "/assets/img/鱼生套餐.png",
            title: "现煮咖啡现煮咖啡现煮咖啡现煮咖啡现煮咖啡现煮咖啡",
            price: "9.9",
          },
          { image: "/assets/img/鱼生套餐.png", title: "打扫卫生", price: "30" },
          {
            image: "/assets/img/鱼生套餐.png",
            title: "试卷打印",
            price: "0.5",
          },
        ],
      },
      fetchServiceList: function () {
        var r = arguments,
          s = this;
        return a(
          e().mark(function a() {
            var i, n, c, o, u, l;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      if (
                        ((i = r.length > 0 && void 0 !== r[0] && r[0]),
                        !s.data.serviceLoading)
                      ) {
                        e.next = 3;
                        break;
                      }
                      return e.abrupt("return");
                    case 3:
                      return (
                        (n = getApp()),
                        s.setData({ serviceLoading: !0 }),
                        (e.prev = 5),
                        (c = i ? 1 : s.data.servicePage),
                        (e.next = 9),
                        n.call({
                          path: "/api/mp/services?is_recommended=true",
                          method: "GET",
                          data: {
                            is_recommended: "true",
                            page: c,
                            limit: s.data.serviceLimit,
                          },
                        })
                      );
                    case 9:
                      if (0 !== (o = e.sent).code) {
                        e.next = 17;
                        break;
                      }
                      (u = o.data.services || []),
                        (l = i ? u : [].concat(t(s.data.serviceList), t(u))),
                        s.setData({
                          serviceList: l,
                          servicePage: c + 1,
                          serviceHasMore: o.data.pagination.has_next,
                          serviceLoading: !1,
                        }),
                        console.log("获取服务列表成功:", l),
                        (e.next = 18);
                      break;
                    case 17:
                      throw new Error(o.message || "获取服务列表失败");
                    case 18:
                      e.next = 25;
                      break;
                    case 20:
                      (e.prev = 20),
                        (e.t0 = e.catch(5)),
                        console.error("获取服务列表失败:", e.t0),
                        s.setData({ serviceLoading: !1 }),
                        wx.showToast({ title: "加载失败", icon: "error" });
                    case 25:
                    case "end":
                      return e.stop();
                  }
              },
              a,
              null,
              [[5, 20]]
            );
          })
        )();
      },
      refreshServiceList: function () {
        var t = this;
        return a(
          e().mark(function a() {
            return e().wrap(function (e) {
              for (;;)
                switch ((e.prev = e.next)) {
                  case 0:
                    return (
                      console.log("开始刷新服务列表"),
                      t.setData({
                        servicePage: 1,
                        serviceHasMore: !0,
                        serviceList: [],
                      }),
                      (e.next = 4),
                      t.fetchServiceList(!0)
                    );
                  case 4:
                  case "end":
                    return e.stop();
                }
            }, a);
          })
        )();
      },
      loadMoreServices: function () {
        var t = this;
        return a(
          e().mark(function a() {
            return e().wrap(function (e) {
              for (;;)
                switch ((e.prev = e.next)) {
                  case 0:
                    if (!t.data.serviceHasMore || t.data.serviceLoading) {
                      e.next = 3;
                      break;
                    }
                    return (e.next = 3), t.fetchServiceList(!1);
                  case 3:
                  case "end":
                    return e.stop();
                }
            }, a);
          })
        )();
      },
      switchTab: function (e) {
        var t = e.currentTarget.dataset.tab;
        this.setData({ activeTab: t }),
          "service" === t &&
            0 === this.data.serviceList.length &&
            this.fetchServiceList(!0);
      },
      goToEditProfile: function () {
        this.data.showIncompleteNotice &&
          wx.navigateTo({ url: "/pages/auth/auth?from=index" });
      },
      checkUserAuthStatus: function () {
        var e = getApp().globalData.userInfo;
        console.log("检查用户认证状态:", e),
          e && "incomplete" === e.data.auth_status.value
            ? this.setData({ showIncompleteNotice: !0 })
            : this.setData({ showIncompleteNotice: !1 });
      },
      onLoad: function () {
        var e = this;
        console.log("index页面onLoad被调用"),
          this.fetchArticleBanners(),
          this.fetchCategoryList(),
          setTimeout(function () {
            e.checkUserAuthStatus();
          }, 500),
          "service" === this.data.activeTab && this.fetchServiceList(!0);
      },
      onShow: function () {
        "function" == typeof this.getTabBar &&
          this.getTabBar() &&
          this.getTabBar().setData({ selected: 0 }),
          this.checkUserAuthStatus();
      },
      onUserLoginSuccess: function () {
        console.log("收到用户登录完成通知，重新检查用户状态"),
          this.checkUserAuthStatus();
      },
      onPullDownRefresh: function () {
        var t = this;
        return a(
          e().mark(function a() {
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return (
                        console.log("用户触发下拉刷新"),
                        (e.prev = 1),
                        (e.next = 4),
                        t.fetchArticleBanners()
                      );
                    case 4:
                      return (e.next = 6), t.fetchCategoryList();
                    case 6:
                      if ("service" !== t.data.activeTab) {
                        e.next = 11;
                        break;
                      }
                      return (e.next = 9), t.refreshServiceList();
                    case 9:
                      e.next = 12;
                      break;
                    case 11:
                      "help" === t.data.activeTab &&
                        console.log("求助列表刷新逻辑待实现");
                    case 12:
                      t.checkUserAuthStatus(),
                        wx.showToast({
                          title: "刷新成功",
                          icon: "success",
                          duration: 1500,
                        }),
                        (e.next = 20);
                      break;
                    case 16:
                      (e.prev = 16),
                        (e.t0 = e.catch(1)),
                        console.error("下拉刷新失败:", e.t0),
                        wx.showToast({
                          title: "刷新失败",
                          icon: "error",
                          duration: 1500,
                        });
                    case 20:
                      return (
                        (e.prev = 20), wx.stopPullDownRefresh(), e.finish(20)
                      );
                    case 23:
                    case "end":
                      return e.stop();
                  }
              },
              a,
              null,
              [[1, 16, 20, 23]]
            );
          })
        )();
      },
      fetchArticleBanners: function () {
        var t = this;
        return a(
          e().mark(function a() {
            var r, s, i;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return (
                        (r = getApp()),
                        (e.prev = 1),
                        (e.next = 4),
                        r.call({
                          path: "/api/mp/articles/banners",
                          method: "GET",
                        })
                      );
                    case 4:
                      if (0 !== (s = e.sent).code) {
                        e.next = 11;
                        break;
                      }
                      (i = s.data.map(function (e) {
                        return {
                          value: e.banner_image,
                          ariaLabel: e.title,
                          articleId: e.article_id,
                          title: e.title,
                        };
                      })),
                        t.setData({ swiperList: i }),
                        console.log("获取文章轮播图成功:", i),
                        (e.next = 12);
                      break;
                    case 11:
                      throw new Error(s.message || "获取文章轮播图失败");
                    case 12:
                      e.next = 18;
                      break;
                    case 14:
                      (e.prev = 14),
                        (e.t0 = e.catch(1)),
                        console.error("获取文章轮播图失败:", e.t0),
                        t.setData({
                          swiperList: [
                            {
                              value: "/assets/img/ms.png",
                              ariaLabel: "默认轮播图1",
                            },
                            {
                              value: "/assets/img/sfc.png",
                              ariaLabel: "默认轮播图2",
                            },
                          ],
                        });
                    case 18:
                    case "end":
                      return e.stop();
                  }
              },
              a,
              null,
              [[1, 14]]
            );
          })
        )();
      },
      onSwiperClick: function (e) {
        var t = e.detail.index,
          a = this.data.swiperList[t];
        a &&
          a.articleId &&
          wx.navigateTo({
            url: "/pages/articles-info/articles-info?id=".concat(a.articleId),
          });
      },
      fetchCategoryList: function () {
        var t = this;
        return a(
          e().mark(function a() {
            var r, s, i;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      if (!t.data.categoryLoading) {
                        e.next = 2;
                        break;
                      }
                      return e.abrupt("return");
                    case 2:
                      return (
                        (r = getApp()),
                        t.setData({ categoryLoading: !0 }),
                        (e.prev = 4),
                        (e.next = 7),
                        r.call({
                          path: "/api/mp/services/categories",
                          method: "GET",
                          data: { status: "enabled" },
                        })
                      );
                    case 7:
                      if (0 !== (s = e.sent).code) {
                        e.next = 14;
                        break;
                      }
                      (i = (s.data || []).sort(function (e, t) {
                        return e.sort_order - t.sort_order;
                      })),
                        t.setData({ categoryList: i, categoryLoading: !1 }),
                        console.log("获取服务分类成功:", i),
                        (e.next = 15);
                      break;
                    case 14:
                      throw new Error(s.message || "获取服务分类失败");
                    case 15:
                      e.next = 22;
                      break;
                    case 17:
                      (e.prev = 17),
                        (e.t0 = e.catch(4)),
                        console.error("获取服务分类失败:", e.t0),
                        t.setData({ categoryLoading: !1 }),
                        wx.showToast({ title: "加载分类失败", icon: "error" });
                    case 22:
                    case "end":
                      return e.stop();
                  }
              },
              a,
              null,
              [[4, 17]]
            );
          })
        )();
      },
      onCategoryTap: function (e) {
        var t = e.currentTarget.dataset.category;
        console.log("点击分类:", t),
          wx.navigateTo({
            url: "/pages/service-list/service-list?categoryId="
              .concat(t.id, "&categoryName=")
              .concat(t.name),
          });
      },
      onShareAppMessage: function () {
        return {
          title: r.appName,
          path: "/pages/index/index",
          imageUrl: "/assets/icon/login.png",
        };
      },
    });
  },
  { isPage: true, isComponent: true, currentFile: "pages/index/index.js" }
);
require("pages/index/index.js");
