__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/edit-service/edit-service": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            noteLength: new Array(1),
            titleLength: new Array(1),
            serviceCategoryIndex: new Array(2),
            isEdit: new Array(2),
            serviceCategoryList: new Array(2),
          },
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                var $A = D.isEdit;
                if (C || K || !!U.isEdit || ($A ? undefined : undefined))
                  O(N, "title", $A ? "编辑-邻里服务" : "发布-邻里服务");
                A["isEdit"][0] = (D, E, T) => {
                  var $B = D.isEdit;
                  O(N, "title", $B ? "编辑-邻里服务" : "发布-邻里服务");
                  E(N);
                };
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
                if (C || K || undefined) O(N, "show-back", true);
              },
              c
            );
          },
          g = (C, T) => {
            C ? T("服务名称") : T();
          },
          i = (C) => {},
          j = (C, T) => {
            C || K || !!U.titleLength || undefined
              ? T(Y(Y(D.titleLength) + "/15"), (N) => {
                  A["titleLength"][0] = (D, E, T) => {
                    T(N, Y(Y(D.titleLength) + "/15"));
                  };
                })
              : T();
          },
          h = (C, T, E) => {
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请输入服务名称");
                if (C || K || Z(U.formData, "title"))
                  O(N, "value", X(D.formData).title);
                if (C) O(N, "bindinput", "onTitleInput");
                if (C) O(N, "maxlength", "15");
              },
              i
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "char-count-inner");
              },
              j
            );
          },
          f = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              g
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "input-wrapper");
              },
              h
            );
          },
          l = (C, T) => {
            C ? T("价格") : T();
          },
          m = (C) => {},
          k = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              l
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请写服务价格");
                if (C || K || Z(U.formData, "price"))
                  O(N, "value", X(D.formData).price);
                if (C) O(N, "bindinput", "onPriceInput");
                if (C) O(N, "type", "digit");
              },
              m
            );
          },
          o = (C, T) => {
            C ? T("服务类目") : T();
          },
          q = (C, T) => {
            var $A = D.serviceCategoryIndex;
            C ||
            K ||
            !!(U.serviceCategoryIndex || Z(U.serviceCategoryList, $A)) ||
            undefined
              ? T(Y(X(D.serviceCategoryList)[$A] || "餐饮美食"), (N) => {
                  A["serviceCategoryList"][1] = A["serviceCategoryIndex"][1] = (
                    D,
                    E,
                    T
                  ) => {
                    var $B = D.serviceCategoryIndex;
                    T(N, Y(X(D.serviceCategoryList)[$B] || "餐饮美食"));
                  };
                })
              : T();
          },
          p = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-input picker-input");
              },
              q
            );
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              o
            );
            E(
              "picker",
              {},
              (N, C) => {
                if (C) O(N, "mode", "selector");
                if (C || K || U.serviceCategoryList)
                  O(N, "range", D.serviceCategoryList);
                A["serviceCategoryList"][0] = (D, E, T) => {
                  O(N, "range", D.serviceCategoryList);
                  E(N);
                };
                if (C || K || U.serviceCategoryIndex)
                  O(N, "value", D.serviceCategoryIndex);
                A["serviceCategoryIndex"][0] = (D, E, T) => {
                  O(N, "value", D.serviceCategoryIndex);
                  E(N);
                };
                if (C) O(N, "bindchange", "onServiceCategoryChange");
              },
              p
            );
          },
          s = (C, T) => {
            C ? T("缩略图") : T();
          },
          v,
          x = (C) => {},
          z = (C, T) => {
            C ? T("点击选择缩略图") : T();
          },
          y = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder-text");
              },
              z
            );
          },
          w = (C, T, E) => {
            if (v === 1) {
              E(
                "image",
                {},
                (N, C) => {
                  if (C) L(N, "selected-thumbnail");
                  if (C || K || Z(U.formData, "thumbnail"))
                    O(N, "src", X(D.formData).thumbnail);
                  if (C) O(N, "mode", "aspectFill");
                },
                x
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "thumbnail-placeholder");
                },
                y
              );
            }
          },
          u = (C, T, E, B) => {
            v = X(D.formData).thumbnail ? 1 : 0;
            B(v, w);
          },
          t = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-preview");
                if (C) O(N, "bindtap", "showThumbnailSelector");
              },
              u
            );
          },
          r = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-section");
              },
              t
            );
          },
          B0 = (C, T) => {
            C ? T("详情页图片") : T();
          },
          D0,
          G0 = (C, H0, I0, J0, K0, L0, T, E) => {
            var N0 = (C) => {},
              O0 = (C, T) => {
                C ? T("×") : T();
              },
              M0 = (C, T, E) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "uploaded-image");
                    if (C || K || Z(J0, "tempUrl")) O(N, "src", X(H0).tempUrl);
                    if (C) O(N, "mode", "aspectFill");
                  },
                  N0
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "delete-btn");
                    if (C) O(N, "bindtap", "deleteImage");
                    if (C || K || K0) R.d(N, "index", I0);
                  },
                  O0
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image-item");
              },
              M0
            );
          },
          F0 = (C, T, E, B, F) => {
            F(
              X(D.formData).serviceImages,
              "index",
              U ? Z(U.formData, "serviceImages") : undefined,
              [0, "formData", "serviceImages"],
              G0
            );
          },
          E0 = (C, T, E) => {
            if (D0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "uploaded-images");
                },
                F0
              );
            }
          },
          H0,
          K0 = (C, T) => {
            C ? T("+") : T();
          },
          L0 = (C, T) => {
            C ? T("添加图片") : T();
          },
          J0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "upload-text");
              },
              K0
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "upload-desc");
              },
              L0
            );
          },
          I0 = (C, T, E) => {
            if (H0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "upload-btn");
                  if (C) O(N, "bindtap", "chooseImage");
                },
                J0
              );
            }
          },
          N0 = (C, T) => {
            C ? T("最多可上传6张图片，建议尺寸750x750像素") : T();
          },
          M0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, N0);
          },
          C0 = (C, T, E, B) => {
            D0 = X(X(D.formData).serviceImages).length > 0 ? 1 : 0;
            B(D0, E0);
            H0 = X(X(D.formData).serviceImages).length < 6 ? 1 : 0;
            B(H0, I0);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "upload-tip");
              },
              M0
            );
          },
          A0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              B0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image-upload-section");
              },
              C0
            );
          },
          P0 = (C, T) => {
            C ? T("服务说明") : T();
          },
          R0 = (C) => {},
          S0 = (C, T) => {
            C || K || !!U.noteLength || undefined
              ? T(Y(Y(D.noteLength) + "/200"), (N) => {
                  A["noteLength"][0] = (D, E, T) => {
                    T(N, Y(Y(D.noteLength) + "/200"));
                  };
                })
              : T();
          },
          Q0 = (C, T, E) => {
            E(
              "textarea",
              {},
              (N, C) => {
                if (C) L(N, "form-textarea");
                if (C)
                  O(
                    N,
                    "placeholder",
                    "例如：接单时间，接单数量，服务范围（全小区还是本栋楼）"
                  );
                if (C || K || Z(U.formData, "note"))
                  O(N, "value", X(D.formData).note);
                if (C) O(N, "bindinput", "onNoteInput");
                if (C) O(N, "maxlength", "200");
              },
              R0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "char-count-inner textarea-count");
              },
              S0
            );
          },
          O0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              P0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "textarea-wrapper");
              },
              Q0
            );
          },
          U0 = (C, T) => {
            C ? T("取消") : T();
          },
          V0 = (C, T) => {
            var $A = D.isEdit;
            C || K || !!U.isEdit || ($A ? undefined : undefined)
              ? T(Y($A ? "保存" : "发布"), (N) => {
                  A["isEdit"][1] = (D, E, T) => {
                    var $B = D.isEdit;
                    T(N, Y($B ? "保存" : "发布"));
                  };
                })
              : T();
          },
          T0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "onCancel");
              },
              U0
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-publish");
                if (C) O(N, "bindtap", "onPublish");
              },
              V0
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              n
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              r
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              A0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              O0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "button-group");
              },
              T0
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-container");
              },
              e
            );
          },
          W0,
          Z0 = (C) => {},
          c0 = (C, T) => {
            C ? T("选择缩略图") : T();
          },
          b0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "modal-title");
              },
              c0
            );
          },
          e0 = (C, f0, g0, h0, i0, j0, T, E) => {
            var l0 = (C) => {},
              m0,
              o0 = (C, T) => {
                C ? T("✓") : T();
              },
              n0 = (C, T, E) => {
                if (m0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "selected-mark");
                    },
                    o0
                  );
                }
              },
              k0 = (C, T, E, B) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "thumbnail-img");
                    if (C || K || Z(h0, "src")) O(N, "src", X(f0).src);
                    if (C) O(N, "mode", "aspectFill");
                  },
                  l0
                );
                m0 = X(D.formData).thumbnail === X(f0).src ? 1 : 0;
                B(m0, n0);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-item");
                if (C) O(N, "bindtap", "selectThumbnail");
                if (C || K || Z(h0, "src")) R.d(N, "src", X(f0).src);
                if (C || K || Z(h0, "file_id")) R.d(N, "fileId", X(f0).file_id);
              },
              k0
            );
          },
          d0 = (C, T, E, B, F) => {
            F(
              D.thumbnailList,
              "index",
              U ? U.thumbnailList : undefined,
              [0, "thumbnailList"],
              e0
            );
          },
          g0 = (C, T) => {
            C ? T("取消") : T();
          },
          f0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "hideThumbnailSelector");
              },
              g0
            );
          },
          a0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-header");
              },
              b0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-grid");
              },
              d0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-footer");
              },
              f0
            );
          },
          Y0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-mask");
                if (C) O(N, "bindtap", "hideThumbnailSelector");
              },
              Z0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-content");
              },
              a0
            );
          },
          X0 = (C, T, E) => {
            if (W0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "thumbnail-modal");
                },
                Y0
              );
            }
          },
          a = (C, T, E, B) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
            W0 = D.showThumbnailModal ? 1 : 0;
            B(W0, X0);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/edit-service/edit-service.wxss"] = setCssToHead(
    [
      ".",
      [1],
      "container{background-color:#fff;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;min-height:100vh;padding:",
      [0, 180],
      " ",
      [0, 30],
      " ",
      [0, 30],
      "}\n.",
      [1],
      "form-container{background-color:#fff;border-radius:",
      [0, 20],
      ";margin:",
      [0, 20],
      " auto;padding:",
      [0, 40],
      " ",
      [0, 30],
      ";width:95%}\n.",
      [1],
      "form-item{margin-bottom:",
      [0, 40],
      "}\n.",
      [1],
      "form-label{color:#333;font-size:",
      [0, 28],
      ";font-weight:500;margin-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "form-label.",
      [1],
      "required::before{color:#ff4d4f;content:\x22*\x22;margin-right:",
      [0, 8],
      "}\n.",
      [1],
      "input-wrapper{position:relative}\n.",
      [1],
      "form-input{background-color:#fff;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 12],
      ";box-sizing:border-box;color:#333;font-size:",
      [0, 28],
      ";height:",
      [0, 80],
      ";padding:0 ",
      [0, 20],
      ";width:100%}\n.",
      [1],
      "form-input:focus{border-color:#195abf}\n.",
      [1],
      "form-input.",
      [1],
      "picker-input{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;line-height:",
      [0, 80],
      ";position:relative}\n.",
      [1],
      "form-input.",
      [1],
      "picker-input::after{border-left:",
      [0, 8],
      " solid transparent;border-right:",
      [0, 8],
      " solid transparent;border-top:",
      [0, 8],
      " solid #999;content:\x22\x22;height:0;width:0}\n.",
      [1],
      "char-count-inner,.",
      [1],
      "form-input.",
      [1],
      "picker-input::after{position:absolute;right:",
      [0, 20],
      ";top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.",
      [1],
      "char-count-inner{color:#999;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "textarea-wrapper{position:relative}\n.",
      [1],
      "form-textarea{background-color:#fff;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 12],
      ";box-sizing:border-box;color:#333;font-size:",
      [0, 28],
      ";min-height:",
      [0, 120],
      ";padding:",
      [0, 20],
      ";width:100%}\n.",
      [1],
      "form-textarea.",
      [1],
      "contact-textarea{min-height:",
      [0, 80],
      "}\n.",
      [1],
      "form-textarea:focus{border-color:#195abf}\n.",
      [1],
      "textarea-count{bottom:",
      [0, 20],
      ";position:absolute;right:",
      [0, 20],
      ";top:auto;-webkit-transform:none;transform:none}\n.",
      [1],
      "thumbnail-section{margin-top:",
      [0, 15],
      "}\n.",
      [1],
      "thumbnail-preview{-webkit-align-items:center;align-items:center;background-color:#f8f9fa;border:",
      [0, 2],
      " dashed #e0e0e0;border-radius:",
      [0, 10],
      ";display:-webkit-flex;display:flex;height:",
      [0, 200],
      ";-webkit-justify-content:center;justify-content:center;overflow:hidden;position:relative;width:",
      [0, 200],
      "}\n.",
      [1],
      "selected-thumbnail{border-radius:",
      [0, 8],
      ";height:100%;width:100%}\n.",
      [1],
      "thumbnail-placeholder{-webkit-align-items:center;align-items:center;color:#999;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "placeholder-text{color:#999;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "modal-content{max-height:80vh;overflow-y:auto}\n.",
      [1],
      "thumbnail-grid{-webkit-flex-wrap:wrap;flex-wrap:wrap;gap:",
      [0, 15],
      ";-webkit-justify-content:flex-start;justify-content:flex-start;margin-bottom:",
      [0, 30],
      "}\n.",
      [1],
      "thumbnail-grid,.",
      [1],
      "thumbnail-item{display:-webkit-flex;display:flex}\n.",
      [1],
      "thumbnail-item{-webkit-align-items:center;align-items:center;border-radius:",
      [0, 10],
      ";box-sizing:border-box;-webkit-flex-direction:column;flex-direction:column;padding:",
      [0, 8],
      ";position:relative;transition:all .3s ease;width:calc(25% - ",
      [0, 15],
      ")}\n.",
      [1],
      "thumbnail-img{border-radius:",
      [0, 8],
      ";height:",
      [0, 80],
      ";width:",
      [0, 80],
      "}\n.",
      [1],
      "thumbnail-name{font-size:",
      [0, 22],
      ";line-height:1.2;margin-top:",
      [0, 8],
      ";text-align:center}\n.",
      [1],
      "selected-mark{-webkit-align-items:center;align-items:center;background-color:#195abf;border-radius:50%;color:#fff;display:-webkit-flex;display:flex;font-size:",
      [0, 20],
      ";font-weight:700;height:",
      [0, 30],
      ";-webkit-justify-content:center;justify-content:center;position:absolute;right:",
      [0, 3],
      ";top:",
      [0, 3],
      ";width:",
      [0, 30],
      "}\n.",
      [1],
      "thumbnail-name{color:#666;font-size:",
      [0, 26],
      "}\n.",
      [1],
      "radio-options{display:-webkit-flex;display:flex;-webkit-flex-direction:row;flex-direction:row;gap:",
      [0, 40],
      ";margin-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "radio-item{color:#333;font-size:",
      [0, 28],
      ";gap:",
      [0, 12],
      ";margin-right:",
      [0, 25],
      "}\n.",
      [1],
      "radio-item wx-radio{-webkit-transform:scale(.8);transform:scale(.8)}\n.",
      [1],
      "button-group{gap:",
      [0, 20],
      ";-webkit-justify-content:space-between;justify-content:space-between;margin-top:",
      [0, 60],
      "}\n.",
      [1],
      "btn,.",
      [1],
      "button-group{display:-webkit-flex;display:flex}\n.",
      [1],
      "btn{-webkit-align-items:center;align-items:center;border:none;border-radius:",
      [0, 20],
      ";-webkit-flex:1;flex:1;font-size:",
      [0, 28],
      ";height:",
      [0, 80],
      ";-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "btn-cancel{background-color:#fff;border:",
      [0, 3],
      " solid #1e1e1e;color:#666}\n.",
      [1],
      "btn-publish{background-color:#a5d8ff;border:",
      [0, 3],
      " solid #1971c2;color:#1971c2}\n.",
      [1],
      "btn:active{opacity:.8}\n.",
      [1],
      "thumbnail-modal{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;position:fixed;z-index:9999}\n.",
      [1],
      "modal-mask,.",
      [1],
      "thumbnail-modal{bottom:0;left:0;right:0;top:0}\n.",
      [1],
      "modal-mask{background-color:rgba(0,0,0,.5);position:absolute}\n.",
      [1],
      "modal-content{background-color:#fff;border-radius:",
      [0, 20],
      ";max-width:",
      [0, 600],
      ";padding:",
      [0, 40],
      ";position:relative;width:90%}\n.",
      [1],
      "modal-header{margin-bottom:",
      [0, 30],
      ";text-align:center}\n.",
      [1],
      "modal-title{color:#333;font-size:",
      [0, 32],
      ";font-weight:500}\n.",
      [1],
      "modal-footer{display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;margin:0 auto;width:50%}\n.",
      [1],
      "image-upload-section{margin-top:",
      [0, 15],
      "}\n.",
      [1],
      "uploaded-images{display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;gap:",
      [0, 20],
      ";margin-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "image-item{height:",
      [0, 200],
      ";position:relative;width:",
      [0, 200],
      "}\n.",
      [1],
      "uploaded-image{border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 10],
      ";height:100%;width:100%}\n.",
      [1],
      "delete-btn{background-color:#ff4d4f;border-radius:50%;box-shadow:0 ",
      [0, 2],
      " ",
      [0, 8],
      " rgba(0,0,0,.15);color:#fff;font-size:",
      [0, 24],
      ";font-weight:700;height:",
      [0, 40],
      ";position:absolute;right:",
      [0, -10],
      ";top:",
      [0, -10],
      ";width:",
      [0, 40],
      "}\n.",
      [1],
      "delete-btn,.",
      [1],
      "upload-btn{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "upload-btn{background-color:#f8f9fa;border:",
      [0, 2],
      " dashed #e0e0e0;border-radius:",
      [0, 10],
      ";color:#999;-webkit-flex-direction:column;flex-direction:column;height:",
      [0, 200],
      ";width:",
      [0, 200],
      "}\n.",
      [1],
      "upload-btn:active{background-color:#f0f0f0}\n.",
      [1],
      "upload-text{font-size:",
      [0, 48],
      ";font-weight:300;margin-bottom:",
      [0, 10],
      "}\n.",
      [1],
      "upload-desc,.",
      [1],
      "upload-tip{font-size:",
      [0, 24],
      "}\n.",
      [1],
      "upload-tip{color:#999;margin-top:",
      [0, 15],
      ";text-align:left}\n.",
      [1],
      "selected-buildings-display{-webkit-align-items:center;align-items:center;background-color:#fff;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 12],
      ";display:-webkit-flex;display:flex;margin-bottom:",
      [0, 20],
      ";min-height:",
      [0, 80],
      ";padding:",
      [0, 20],
      ";position:relative}\n.",
      [1],
      "selected-buildings-display:active{background-color:#f5f5f5}\n.",
      [1],
      "selected-buildings-label{color:#666;font-size:",
      [0, 26],
      ";margin-right:",
      [0, 15],
      ";white-space:nowrap}\n.",
      [1],
      "selected-buildings-text{color:#333;-webkit-flex:1;flex:1;font-size:",
      [0, 28],
      ";line-height:1.4}\n.",
      [1],
      "selected-buildings-text .",
      [1],
      "placeholder-text{color:#999}\n.",
      [1],
      "arrow-icon{color:#999;font-size:",
      [0, 28],
      ";margin-left:",
      [0, 15],
      "}\n.",
      [1],
      "building-picker-modal{-webkit-align-items:center;align-items:center;bottom:0;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;left:0;position:fixed;right:0;top:0;z-index:9999}\n.",
      [1],
      "building-picker-modal .",
      [1],
      "modal-mask{background-color:rgba(0,0,0,.5);bottom:0;left:0;position:absolute;right:0;top:0}\n.",
      [1],
      "building-picker-modal .",
      [1],
      "modal-content{background-color:#fff;border-radius:",
      [0, 20],
      ";max-height:70vh;max-width:",
      [0, 600],
      ";overflow-y:auto;padding:",
      [0, 40],
      ";position:relative;width:90%}\n.",
      [1],
      "building-picker-modal .",
      [1],
      "modal-header{-webkit-align-items:center;align-items:center;border-bottom:",
      [0, 2],
      " solid #f0f0f0;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between;margin-bottom:",
      [0, 30],
      ";padding-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "building-picker-modal .",
      [1],
      "modal-title{color:#333;font-size:",
      [0, 32],
      ";font-weight:500}\n.",
      [1],
      "building-picker-modal .",
      [1],
      "modal-footer{border-top:",
      [0, 2],
      " solid #f0f0f0;margin-top:",
      [0, 30],
      ";padding-top:",
      [0, 20],
      "}\n.",
      [1],
      "building-picker-modal .",
      [1],
      "btn-confirm{background-color:#195abf;border:none;border-radius:",
      [0, 12],
      ";color:#fff;font-size:",
      [0, 28],
      ";height:",
      [0, 80],
      ";width:100%}\n.",
      [1],
      "building-item{-webkit-align-items:center;align-items:center;background-color:#fff;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 8],
      ";box-shadow:0 ",
      [0, 2],
      " ",
      [0, 4],
      " rgba(0,0,0,.05);color:#333;display:-webkit-flex;display:flex;font-size:",
      [0, 26],
      ";-webkit-justify-content:center;justify-content:center;min-width:",
      [0, 120],
      ";padding:",
      [0, 15],
      " ",
      [0, 25],
      ";position:relative;transition:all .3s ease}\n.",
      [1],
      "building-item.",
      [1],
      "unselected{background-color:#fff;border-color:#e0e0e0;color:#333}\n.",
      [1],
      "building-item.",
      [1],
      "unselected:hover{background-color:#f8fbff;border-color:#195abf}\n.",
      [1],
      "building-item.",
      [1],
      "selected{background-color:#e6f3ff;border-color:#195abf;box-shadow:0 ",
      [0, 4],
      " ",
      [0, 8],
      " rgba(25,90,191,.15);color:#195abf;font-weight:500}\n.",
      [1],
      "building-item:active{-webkit-transform:scale(.95);transform:scale(.95)}\n.",
      [1],
      "check-mark{-webkit-align-items:center;align-items:center;background-color:#195abf;border-radius:50%;box-shadow:0 ",
      [0, 2],
      " ",
      [0, 4],
      " rgba(25,90,191,.3);color:#fff;display:-webkit-flex;display:flex;font-size:",
      [0, 20],
      ";font-weight:700;height:",
      [0, 30],
      ";-webkit-justify-content:center;justify-content:center;position:absolute;right:",
      [0, -5],
      ";top:",
      [0, -5],
      ";width:",
      [0, 30],
      "}\n",
    ],
    "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/edit-service/edit-service.wxss:1:3422)",
    { path: "./pages/edit-service/edit-service.wxss" }
  );
}
