@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-avatar {
  align-items: center;
  background-color: var(
    --td-avatar-bg-color,
    var(--td-brand-color-light-active, var(--td-primary-color-2, #d9e1ff))
  );
  box-sizing: border-box;
  color: var(
    --td-avatar-content-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.t-avatar__wrapper {
  display: -webkit-inline-flex;
  display: inline-flex;
  margin-left: var(--td-avatar-margin-left, 0);
  position: relative;
  vertical-align: top;
}
.t-avatar--large {
  font-size: var(
    --td-avatar-text-large-font-size,
    var(--td-font-size-xl, 40rpx)
  );
  height: var(--td-avatar-large-width, 128rpx);
  width: var(--td-avatar-large-width, 128rpx);
}
.t-avatar--large .t-avatar__icon {
  font-size: var(--td-avatar-icon-large-font-size, 64rpx);
}
.t-avatar--medium {
  font-size: var(
    --td-avatar-text-medium-font-size,
    var(--td-font-size-m, 32rpx)
  );
  height: var(--td-avatar-medium-width, 96rpx);
  width: var(--td-avatar-medium-width, 96rpx);
}
.t-avatar--medium .t-avatar__icon {
  font-size: var(--td-avatar-icon-medium-font-size, 48rpx);
}
.t-avatar--small {
  font-size: var(
    --td-avatar-text-small-font-size,
    var(--td-font-size-base, 28rpx)
  );
  height: var(--td-avatar-small-width, 80rpx);
  width: var(--td-avatar-small-width, 80rpx);
}
.t-avatar--small .t-avatar__icon {
  font-size: var(--td-avatar-icon-small-font-size, 40rpx);
}
.t-avatar .t-image,
.t-avatar__image {
  height: 100%;
  width: 100%;
}
.t-avatar--circle {
  border-radius: var(
    --td-avatar-circle-border-radius,
    var(--td-radius-circle, 50%)
  );
  overflow: hidden;
}
.t-avatar--round {
  border-radius: var(
    --td-avatar-round-border-radius,
    var(--td-radius-default, 12rpx)
  );
  overflow: hidden;
}
.t-avatar__icon,
.t-avatar__text {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  height: 100%;
  justify-content: center;
  width: 100%;
}
.t-avatar__icon:empty,
.t-avatar__text:empty {
  height: 0;
  width: 0;
}
.t-avatar--border {
  border-color: var(--td-avatar-border-color, #fff);
  border-style: solid;
}
.t-avatar--border-small {
  border-width: var(--td-avatar-border-width-small, 2rpx);
}
.t-avatar--border-medium {
  border-width: var(--td-avatar-border-width-medium, 4rpx);
}
.t-avatar--border-large {
  border-width: var(--td-avatar-border-width-large, 6rpx);
}
