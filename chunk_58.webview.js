__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/textarea/textarea": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/textarea/textarea"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            selectionEnd: new Array(1),
            placeholderClass: new Array(1),
            bordered: new Array(1),
            showConfirmBar: new Array(1),
            placeholder: new Array(1),
            autofocus: new Array(1),
            cursor: new Array(1),
            confirmHold: new Array(1),
            selectionStart: new Array(1),
            allowInputOverMax: new Array(1),
            placeholderStyle: new Array(1),
            style: new Array(1),
            cursorSpacing: new Array(1),
            holdKeyboard: new Array(1),
            adjustPosition: new Array(1),
            confirmType: new Array(1),
            autosize: new Array(2),
            disabled: new Array(2),
            disableDefaultPadding: new Array(1),
            fixed: new Array(1),
            readonly: new Array(1),
            customStyle: new Array(1),
            value: new Array(1),
            focus: new Array(1),
          },
          K = U === true,
          f,
          g = (C, T) => {
            if (f === 1) {
              C || K || U.label ? T(Y(D.label)) : T();
            }
          },
          e = (C, T, E, B, F, S) => {
            f = D.label ? 1 : 0;
            B(f, g);
            S("label");
          },
          i = (C) => {},
          j,
          l = (C, T) => {
            C || K || !!(U.count || U.maxcharacter || U.maxlength) || undefined
              ? T(Y(Y(D.count) + " / " + Y(D.maxcharacter || D.maxlength)))
              : T();
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__indicator " +
                        Y(D.prefix) +
                        "-class-indicator"
                    );
                },
                l
              );
            }
          },
          h = (C, T, E, B) => {
            E(
              "textarea",
              {},
              (N, C) => {
                var $A = D.disabled;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.disabled ||
                    ($A ? !!U.prefix || undefined : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__wrapper-inner " +
                      Y($A ? D.prefix + "-is-disabled" : "") +
                      " " +
                      Y(D.prefix) +
                      "-class-textarea"
                  );
                A["disabled"][1] = (D, E, T) => {
                  var $B = D.disabled;
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__wrapper-inner " +
                      Y($B ? D.prefix + "-is-disabled" : "") +
                      " " +
                      Y(D.prefix) +
                      "-class-textarea"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "textareaStyle") || U.autosize) ||
                  undefined
                )
                  R.y(N, P(X(b).textareaStyle)(D.autosize));
                A["autosize"][1] = (D, E, T) => {
                  R.y(N, P(X(b).textareaStyle)(D.autosize));
                };
                var $B = D.allowInputOverMax;
                if (
                  C ||
                  K ||
                  !!U.allowInputOverMax ||
                  ($B ? undefined : U.maxlength)
                )
                  O(N, "maxlength", $B ? -1 : D.maxlength);
                A["allowInputOverMax"][0] = (D, E, T) => {
                  var $C = D.allowInputOverMax;
                  O(N, "maxlength", $C ? -1 : D.maxlength);
                  E(N);
                };
                if (C || K || !!(U.disabled || U.readonly) || undefined)
                  O(N, "disabled", D.disabled || D.readonly);
                A["disabled"][0] = A["readonly"][0] = (D, E, T) => {
                  O(N, "disabled", D.disabled || D.readonly);
                  E(N);
                };
                if (C || K || U.placeholder) O(N, "placeholder", D.placeholder);
                A["placeholder"][0] = (D, E, T) => {
                  O(N, "placeholder", D.placeholder);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.placeholderClass) ||
                  undefined
                )
                  O(
                    N,
                    "placeholder-class",
                    Y(D.classPrefix) + "__placeholder " + Y(D.placeholderClass)
                  );
                A["placeholderClass"][0] = (D, E, T) => {
                  O(
                    N,
                    "placeholder-class",
                    Y(D.classPrefix) + "__placeholder " + Y(D.placeholderClass)
                  );
                  E(N);
                };
                if (C || K || U.placeholderStyle)
                  O(N, "placeholder-style", D.placeholderStyle);
                A["placeholderStyle"][0] = (D, E, T) => {
                  O(N, "placeholder-style", D.placeholderStyle);
                  E(N);
                };
                if (C || K || U.value) O(N, "value", D.value);
                A["value"][0] = (D, E, T) => {
                  O(N, "value", D.value);
                  E(N);
                };
                if (C || K || !!U.autosize || undefined)
                  O(N, "auto-height", !!D.autosize);
                A["autosize"][0] = (D, E, T) => {
                  O(N, "auto-height", !!D.autosize);
                  E(N);
                };
                if (C || K || U.autofocus) O(N, "auto-focus", D.autofocus);
                A["autofocus"][0] = (D, E, T) => {
                  O(N, "auto-focus", D.autofocus);
                  E(N);
                };
                if (C || K || U.fixed) O(N, "fixed", D.fixed);
                A["fixed"][0] = (D, E, T) => {
                  O(N, "fixed", D.fixed);
                  E(N);
                };
                if (C || K || U.focus) O(N, "focus", D.focus);
                A["focus"][0] = (D, E, T) => {
                  O(N, "focus", D.focus);
                  E(N);
                };
                if (C || K || U.cursor) O(N, "cursor", D.cursor);
                A["cursor"][0] = (D, E, T) => {
                  O(N, "cursor", D.cursor);
                  E(N);
                };
                if (C || K || U.cursorSpacing)
                  O(N, "cursor-spacing", D.cursorSpacing);
                A["cursorSpacing"][0] = (D, E, T) => {
                  O(N, "cursor-spacing", D.cursorSpacing);
                  E(N);
                };
                if (C || K || U.adjustPosition)
                  O(N, "adjust-position", D.adjustPosition);
                A["adjustPosition"][0] = (D, E, T) => {
                  O(N, "adjust-position", D.adjustPosition);
                  E(N);
                };
                if (C || K || U.confirmType)
                  O(N, "confirm-type", D.confirmType);
                A["confirmType"][0] = (D, E, T) => {
                  O(N, "confirm-type", D.confirmType);
                  E(N);
                };
                if (C || K || U.confirmHold)
                  O(N, "confirm-hold", D.confirmHold);
                A["confirmHold"][0] = (D, E, T) => {
                  O(N, "confirm-hold", D.confirmHold);
                  E(N);
                };
                if (C || K || U.disableDefaultPadding)
                  O(N, "disable-default-padding", D.disableDefaultPadding);
                A["disableDefaultPadding"][0] = (D, E, T) => {
                  O(N, "disable-default-padding", D.disableDefaultPadding);
                  E(N);
                };
                if (C || K || U.showConfirmBar)
                  O(N, "show-confirm-bar", D.showConfirmBar);
                A["showConfirmBar"][0] = (D, E, T) => {
                  O(N, "show-confirm-bar", D.showConfirmBar);
                  E(N);
                };
                if (C || K || U.selectionStart)
                  O(N, "selection-start", D.selectionStart);
                A["selectionStart"][0] = (D, E, T) => {
                  O(N, "selection-start", D.selectionStart);
                  E(N);
                };
                if (C || K || U.selectionEnd)
                  O(N, "selection-end", D.selectionEnd);
                A["selectionEnd"][0] = (D, E, T) => {
                  O(N, "selection-end", D.selectionEnd);
                  E(N);
                };
                if (C || K || U.holdKeyboard)
                  O(N, "hold-keyboard", D.holdKeyboard);
                A["holdKeyboard"][0] = (D, E, T) => {
                  O(N, "hold-keyboard", D.holdKeyboard);
                  E(N);
                };
                if (C) O(N, "bindinput", "onInput");
                if (C) O(N, "bindfocus", "onFocus");
                if (C) O(N, "bindblur", "onBlur");
                if (C) O(N, "bindconfirm", "onConfirm");
                if (C) O(N, "bindlinechange", "onLineChange");
                if (C)
                  R.v(
                    N,
                    "keyboardheightchange",
                    "onKeyboardHeightChange",
                    !1,
                    !1,
                    !1,
                    !1
                  );
              },
              i
            );
            j = D.indicator && (D.maxcharacter > 0 || D.maxlength > 0) ? 1 : 0;
            B(j, k);
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__label " + Y(D.prefix) + "-class-label"
                  );
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrapper");
              },
              h
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.bordered;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.bordered ||
                    ($A ? !!U.classPrefix || undefined : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y($A ? D.classPrefix + "--border" : "") +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["bordered"][0] = (D, E, T) => {
                  var $B = D.bordered;
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y($B ? D.classPrefix + "--border" : "") +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/textarea/textarea.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-textarea{background-color:var(--td-textarea-background-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));box-sizing:border-box;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;padding:",
        [0, 32],
        "}\n.",
        [1],
        "t-textarea__label:not(:empty){color:var(--td-textarea-label-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));-webkit-flex-shrink:0;flex-shrink:0;font-size:var(--td-font-size-base,",
        [0, 28],
        ");line-height:",
        [0, 44],
        ";overflow:hidden;padding-bottom:var(--td-spacer,",
        [0, 16],
        ");text-overflow:ellipsis;white-space:nowrap}\n.",
        [1],
        "t-textarea__wrapper{display:-webkit-flex;display:flex;-webkit-flex:1 1 auto;flex:1 1 auto;-webkit-flex-direction:column;flex-direction:column;overflow:hidden;width:100%}\n.",
        [1],
        "t-textarea__wrapper-inner{background-color:initial;border:0;box-sizing:border-box;color:var(--td-textarea-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));-webkit-flex:1 1 auto;flex:1 1 auto;font-size:var(--td-font-size-m,",
        [0, 32],
        ");line-height:",
        [0, 48],
        ";margin:0;min-height:20px;min-width:0;padding:0;resize:none;text-align:left;width:inherit}\n.",
        [1],
        "t-textarea__placeholder{color:var(--td-textarea-placeholder-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-font-size-m,",
        [0, 32],
        ")}\n.",
        [1],
        "t-textarea__indicator:not(:empty){color:var(--td-textarea-indicator-text-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));-webkit-flex-shrink:0;flex-shrink:0;font-size:var(--td-spacer-1,",
        [0, 24],
        ");line-height:",
        [0, 40],
        ";padding-top:var(--td-spacer,",
        [0, 16],
        ");text-align:right}\n.",
        [1],
        "t-textarea--border{border:",
        [0, 2],
        " solid var(--td-textarea-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));border-radius:var(--td-textarea-border-radius,var(--td-radius-default,",
        [0, 12],
        "))}\n.",
        [1],
        "t-textarea .",
        [1],
        "t-is-disabled{color:var(--td-textarea-disabled-text-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/textarea/textarea.wxss" }
    );
}
