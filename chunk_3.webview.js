__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "components/service-card/service-card": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          e = (C) => {},
          f = (C, T) => {
            C || K || Z(Z(U.service, "publisher"), "nickname")
              ? T(Y(X(X(D.service).publisher).nickname))
              : T();
          },
          d = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) L(N, "avatar");
                if (C || K || Z(Z(U.service, "publisher"), "avatar"))
                  O(N, "src", X(X(D.service).publisher).avatar);
              },
              e
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "name");
              },
              f
            );
          },
          h,
          j = (C, T) => {
            C || K || Z(Z(Z(U.service, "publisher"), "auth_status"), "label")
              ? T(Y(X(X(X(D.service).publisher).auth_status).label))
              : T();
          },
          i = (C, T, E) => {
            if (h === 1) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  if (C) O(N, "theme", "default");
                },
                j
              );
            }
          },
          k,
          m = (C, T) => {
            C || K || Z(Z(Z(U.service, "publisher"), "neighbor_type"), "label")
              ? T(Y(X(X(X(D.service).publisher).neighbor_type).label))
              : T();
          },
          l = (C, T, E) => {
            if (k === 1) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  if (C) O(N, "theme", "primary");
                },
                m
              );
            }
          },
          g = (C, T, E, B) => {
            h =
              X(X(X(D.service).publisher).auth_status).value !== "verified"
                ? 1
                : 0;
            B(h, i);
            k =
              X(X(X(D.service).publisher).auth_status).value === "verified"
                ? 1
                : 0;
            B(k, l);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top_left");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top_right");
              },
              g
            );
          },
          p = (C) => {},
          o = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) L(N, "service-icon");
                if (C || K || Z(U.service, "thumbnail"))
                  O(N, "src", X(D.service).thumbnail);
              },
              p
            );
          },
          r = (C, T) => {
            C || K || Z(U.service, "name") ? T(Y(X(D.service).name)) : T();
          },
          s = (C, T) => {
            C || K || Z(U.service, "description")
              ? T(Y(X(D.service).description))
              : T();
          },
          q = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service-title");
              },
              r
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service-description");
              },
              s
            );
          },
          u,
          x = (C, T) => {
            C || K || Z(U.service, "price") ? T(Y(X(D.service).price)) : T();
          },
          w = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "price-value");
              },
              x
            );
          },
          z = (C, T) => {
            C ? T("面议") : T();
          },
          y = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "price-value");
              },
              z
            );
          },
          v = (C, T, E) => {
            if (u === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "price-box");
                },
                w
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "price-box");
                },
                y
              );
            }
          },
          t = (C, T, E, B) => {
            u = X(D.service).price ? 1 : 0;
            B(u, v);
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_left");
              },
              o
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_midle");
              },
              q
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_right");
              },
              t
            );
          },
          C0 = (C, T) => {
            C || K || !!Z(U.service, "order_count") || undefined
              ? T(Y("订单数量：" + Y(X(D.service).order_count)))
              : T();
          },
          B0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "order-count");
              },
              C0
            );
          },
          E0 = (C, T) => {
            C || K || Z(Z(U.service, "category"), "name")
              ? T(Y(X(X(D.service).category).name))
              : T();
          },
          D0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "category");
              },
              E0
            );
          },
          A0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service-stats");
              },
              B0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service-stats");
              },
              D0
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top");
              },
              c
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info");
                if (C) R.v(N, "tap", "onServiceClick", !1, !1, !1, !1);
              },
              n
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_bottom");
              },
              A0
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list");
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/empty/empty": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { customStyle: new Array(1), style: new Array(1) },
          K = U === true,
          e,
          g = (C) => {},
          h,
          i = (C, T, E, B, F, S, J) => {
            var $A = I(h);
            if (h && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.classPrefix + "__icon", name: D.iconName },
                  X(D.iconData),
                  {}
                ),
                K ||
                  (U
                    ? U.iconData === true ||
                      Object.assign(
                        {
                          tClass: !!U.classPrefix || undefined,
                          name: U.iconName,
                        },
                        X(U.iconData),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class", Y(D.prefix) + "-class-image");
                  if (C || K || U.image) O(N, "src", D.image);
                  if (C) O(N, "mode", "aspectFit");
                },
                g
              );
            } else if (e === 2) {
              h = "icon";
              B(h, i);
            } else {
              S("image");
            }
          },
          d = (C, T, E, B) => {
            e = D.image
              ? 1
              : D.iconName || P(X(a).isNoEmptyObj)(D.iconData)
              ? 2
              : 0;
            B(e, f);
          },
          k,
          l = (C, T) => {
            if (k === 1) {
              C || K || U.description ? T(Y(D.description)) : T();
            }
          },
          j = (C, T, E, B, F, S) => {
            k = D.description ? 1 : 0;
            B(k, l);
            S("description");
          },
          m = (C, T, E, B, F, S) => {
            S("action");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__thumb");
                if (C) O(N, "aria-hidden", "true");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__description " +
                      Y(D.prefix) +
                      "-class-description"
                  );
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__actions " +
                      Y(D.prefix) +
                      "-class-actions"
                  );
              },
              m
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["components/service-card/service-card.wxss"] = setCssToHead(
    [
      ".",
      [1],
      "fwdd_list{background:#fff;border-radius:",
      [0, 24],
      ";margin:",
      [0, 20],
      " auto;padding:",
      [0, 24],
      ";width:95%}\n.",
      [1],
      "fwdd_list_top{-webkit-justify-content:space-between;justify-content:space-between;margin-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "fwdd_list_top,.",
      [1],
      "fwdd_list_top_left{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
      [1],
      "fwdd_list_top_left{gap:",
      [0, 12],
      "}\n.",
      [1],
      "avatar{border-radius:50%;height:",
      [0, 36],
      ";width:",
      [0, 36],
      "}\n.",
      [1],
      "name{color:#333;font-size:",
      [0, 28],
      "}\n.",
      [1],
      "id{color:#999;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "fwdd_list_top_right{display:-webkit-flex;display:flex;gap:",
      [0, 12],
      "}\n.",
      [1],
      "btn-delivery,.",
      [1],
      "btn-service{border-radius:",
      [0, 8],
      ";font-size:",
      [0, 24],
      ";padding:",
      [0, 4],
      " ",
      [0, 16],
      "}\n.",
      [1],
      "btn-service{border:",
      [0, 2],
      " solid #ff4d4f;color:#ff4d4f}\n.",
      [1],
      "btn-delivery{border:",
      [0, 2],
      " solid #ddd;color:#666}\n.",
      [1],
      "fwdd_list_info{border-bottom:",
      [0, 2],
      " solid #f5f5f5;border-top:",
      [0, 2],
      " solid #f5f5f5;padding:",
      [0, 20],
      " 0}\n.",
      [1],
      "fwdd_list_info,.",
      [1],
      "fwdd_list_info_left{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
      [1],
      "fwdd_list_info_left{background-color:#eff2f5;border-radius:",
      [0, 15],
      ";padding:",
      [0, 5],
      "}\n.",
      [1],
      "service-icon{border-radius:",
      [0, 16],
      ";height:",
      [0, 160],
      ";width:",
      [0, 160],
      "}\n.",
      [1],
      "fwdd_list_info_midle{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center;margin-left:",
      [0, 35],
      "}\n.",
      [1],
      "service-title{color:#1e1e1e;font-size:",
      [0, 30],
      ";font-weight:700}\n.",
      [1],
      "attachment-box{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;margin:",
      [0, 8],
      " 0}\n.",
      [1],
      "attachment-label{color:#2b5ee3;font-size:",
      [0, 26],
      "}\n.",
      [1],
      "attachment-count{color:#666;font-size:",
      [0, 26],
      "}\n.",
      [1],
      "download-hint{color:#999;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "fwdd_list_info_right{-webkit-align-items:flex-end;align-items:flex-end;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center;min-width:",
      [0, 120],
      ";text-align:right}\n.",
      [1],
      "fwdd_list_info_right,.",
      [1],
      "price-box{display:-webkit-flex;display:flex}\n.",
      [1],
      "price-box{-webkit-align-items:baseline;align-items:baseline}\n.",
      [1],
      "price-symbol{color:#ff4d4f;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "price-value{color:#ff4d4f;font-size:",
      [0, 32],
      ";font-weight:500}\n.",
      [1],
      "quantity{color:#999;font-size:",
      [0, 24],
      ";margin:",
      [0, 8],
      " 0}\n.",
      [1],
      "total-price{color:#666;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "amount{color:#ff4d4f}\n.",
      [1],
      "fwdd_list_bottom{margin-top:",
      [0, 20],
      "}\n.",
      [1],
      "contact-btn{color:#2b5ee3;font-size:",
      [0, 28],
      ";margin-left:",
      [0, 20],
      "}\n.",
      [1],
      "confirm-btn{border:",
      [0, 5],
      " solid #ff4d4f;border-radius:",
      [0, 20],
      ";color:#ff4d4f;font-size:",
      [0, 24],
      ";padding:",
      [0, 8],
      " ",
      [0, 32],
      "}\n.",
      [1],
      "service-description{color:#666;font-size:",
      [0, 22],
      ";line-height:1.8;margin:",
      [0, 8],
      " 0}\n.",
      [1],
      "service-stats{display:block;margin:",
      [0, 4],
      " 0}\n.",
      [1],
      "category{margin-top:",
      [0, 8],
      "}\n.",
      [1],
      "service-time{color:#999;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "action-buttons{display:-webkit-flex;display:flex;gap:",
      [0, 20],
      "}\n.",
      [1],
      "fwdd_list_bottom{gap:",
      [0, 80],
      ";-webkit-justify-content:space-between;justify-content:space-between}\n.",
      [1],
      "fwdd_list_bottom,.",
      [1],
      "service-stats{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
      [1],
      "service-stats{height:",
      [0, 40],
      ";-webkit-justify-content:flex-start;justify-content:flex-start}\n.",
      [1],
      "service-stats:last-child{-webkit-justify-content:flex-end;justify-content:flex-end}\n.",
      [1],
      "order-count,.",
      [1],
      "positive-rate{color:#999;font-size:",
      [0, 24],
      ";line-height:1}\n.",
      [1],
      "order-count{margin-left:",
      [0, 10],
      "}\n.",
      [1],
      "category{color:#666;font-size:",
      [0, 24],
      "}\n",
    ],
    undefined,
    { path: "./components/service-card/service-card.wxss" }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/empty/empty.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-empty{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column}\n.",
        [1],
        "t-empty__icon{color:var(--td-empty-icon-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:",
        [0, 192],
        "}\n.",
        [1],
        "t-empty__thumb+.",
        [1],
        "t-empty__description:not(:empty){margin-top:var(--td-empty-description-margin-top,var(--td-spacer-2,",
        [0, 32],
        "))}\n.",
        [1],
        "t-empty__description{color:var(--td-empty-description-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-empty-description-font-size,var(--td-font-size-base,",
        [0, 28],
        "));line-height:var(--td-empty-description-line-height,",
        [0, 44],
        ");text-align:center;white-space:pre-wrap}\n.",
        [1],
        "t-empty__description+.",
        [1],
        "t-empty__actions:not(:empty){margin-top:var(--td-empty-action-margin-top,var(--td-spacer-4,",
        [0, 64],
        "))}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/empty/empty.wxss" }
    );
}
