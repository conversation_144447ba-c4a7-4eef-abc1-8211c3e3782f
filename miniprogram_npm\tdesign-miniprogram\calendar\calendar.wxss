@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-calendar {
  background: var(
    --td-calendar-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  overflow-x: hidden;
  position: relative;
  width: inherit;
  z-index: 9999;
}
.t-calendar--popup {
  border-top-left-radius: var(--td-calendar-radius, 24rpx);
  border-top-right-radius: var(--td-calendar-radius, 24rpx);
}
.t-calendar__title {
  align-items: center;
  color: var(
    --td-calendar-title-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-flex;
  display: flex;
  font-size: var(--td-calendar-title-font-size, 18px);
  font-weight: 600;
  height: 52rpx;
  justify-content: center;
  padding: 32rpx;
}
.t-calendar__title:focus {
  outline: 0;
}
.t-calendar__close-btn {
  color: var(
    --td-calendar-title-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  margin: -24rpx;
  padding: 24rpx;
  position: absolute;
  right: 32rpx;
  top: 32rpx;
}
.t-calendar__days {
  grid-column-gap: 8rpx;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  line-height: 92rpx;
  padding: 0 32rpx;
  text-align: center;
}
.t-calendar__days-item {
  color: var(
    --td-calendar-days-color,
    var(--td-text-color-secondary, var(--td-font-gray-2, rgba(0, 0, 0, 0.6)))
  );
  font-size: 28rpx;
  height: 92rpx;
}
.t-calendar__content {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  min-height: 400rpx;
}
.t-calendar__month {
  color: var(
    --td-calendar-title-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  font-size: 28rpx;
  font-weight: 600;
  padding: 32rpx 0 0;
}
.t-calendar__months {
  box-sizing: border-box;
  height: 712rpx;
  padding: 0 32rpx 32rpx;
}
.t-calendar__months::-webkit-scrollbar {
  display: none;
}
.t-calendar__dates {
  grid-column-gap: 8rpx;
  display: grid;
  flex: 1;
  grid-template-columns: repeat(7, 1fr);
}
.t-calendar__dates-item {
  -webkit-tap-highlight-color: transparent;
  align-items: center;
  border-radius: var(
    --td-calendar-selected-border-radius,
    var(--td-radius-default, 12rpx)
  );
  color: var(
    --td-calendar-title-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  cursor: pointer;
  display: -webkit-flex;
  display: flex;
  font-size: 32rpx;
  font-weight: 600;
  height: 120rpx;
  justify-content: center;
  line-height: 48rpx;
  margin-top: 16rpx;
  position: relative;
  user-select: none;
}
.t-calendar__dates-item-prefix,
.t-calendar__dates-item-suffix {
  font-size: 20rpx;
  font-weight: 400;
  line-height: 32rpx;
  position: absolute;
  text-align: center;
  width: 100%;
}
.t-calendar__dates-item-prefix {
  top: 8rpx;
}
.t-calendar__dates-item-suffix {
  bottom: 8rpx;
  color: var(
    --td-calendar-item-suffix-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
}
.t-calendar__dates-item-suffix--end,
.t-calendar__dates-item-suffix--selected,
.t-calendar__dates-item-suffix--start {
  color: var(
    --td-calendar-selected-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
}
.t-calendar__dates-item-suffix--disabled {
  color: var(
    --td-calendar-item-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-calendar__dates-item--end,
.t-calendar__dates-item--selected,
.t-calendar__dates-item--start {
  background: var(
    --td-calendar-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-radius: var(
    --td-calendar-selected-border-radius,
    var(--td-radius-default, 12rpx)
  );
  color: var(
    --td-calendar-selected-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
}
.t-calendar__dates-item--start {
  border-radius: var(
      --td-calendar-selected-border-radius,
      var(--td-radius-default, 12rpx)
    )
    0 0
    var(--td-calendar-selected-border-radius, var(--td-radius-default, 12rpx));
}
.t-calendar__dates-item--end {
  border-radius: 0
    var(--td-calendar-selected-border-radius, var(--td-radius-default, 12rpx))
    var(--td-calendar-selected-border-radius, var(--td-radius-default, 12rpx)) 0;
}
.t-calendar__dates-item--start + .t-calendar__dates-item--end::before {
  background: var(
    --td-calendar-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  top: 0;
  width: 8rpx;
}
.t-calendar__dates-item--start + .t-calendar__dates-item--end:before {
  left: -8rpx;
}
.t-calendar__dates-item--centre {
  border-radius: 0;
}
.t-calendar__dates-item--centre,
.t-calendar__dates-item--centre::after,
.t-calendar__dates-item--centre::before {
  background-color: var(
    --td-calendar-item-centre-color,
    var(--td-brand-color-light, var(--td-primary-color-1, #f2f3ff))
  );
}
.t-calendar__dates-item--centre::after,
.t-calendar__dates-item--centre::before {
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  top: 0;
  width: 8rpx;
}
.t-calendar__dates-item--centre:before {
  left: -8rpx;
}
.t-calendar__dates-item--centre:after {
  right: -8rpx;
}
.t-calendar__dates-item--disabled {
  color: var(
    --td-calendar-item-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
  cursor: default;
}
.t-calendar__footer {
  padding: 32rpx;
}
.t-calendar-switch-mode--none > .t-calendar__months {
  height: 60vh;
}
.t-calendar-header {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  line-height: 44rpx;
}
.t-calendar-header__with-action {
  box-sizing: border-box;
  padding: 0rpx 32rpx 16rpx;
  position: relative;
}
.t-calendar-header__with-action::after {
  background-color: var(--td-border-color, var(--td-gray-color-3, #e7e7e7));
  bottom: 0;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: unset;
  transform: scaleY(0.5);
}
.t-calendar-header__with-action .t-calendar-header__title {
  flex: 1;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
}
.t-calendar-header__action {
  color: var(
    --td-calendar-switch-mode-icon-color,
    var(--td-text-color-secondary, var(--td-font-gray-2, rgba(0, 0, 0, 0.6)))
  );
  display: -webkit-flex;
  display: flex;
  font-size: 40rpx;
}
.t-calendar-header__icon {
  padding: 16rpx;
}
.t-calendar-header__icon--disabled {
  color: var(
    --td-calendar-switch-mode-icon-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-calendar-header__title {
  text-align: left;
}
