__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/auth/auth": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { formData: new Array(4), showBackButton: new Array(1) },
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "填写认证信息");
                if (C || K || U.showBackButton) O(N, "back", D.showBackButton);
                A["showBackButton"][0] = (D, E, T) => {
                  O(N, "back", D.showBackButton);
                  E(N);
                };
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          f = (C, T) => {
            C ? T("怎么认证") : T();
          },
          g = (C, T) => {
            C
              ? T(
                  "提交后管理员会在1个工作日内主动联系您进行认证\\n管理员微信：bcxtd001"
                )
              : T();
          },
          h = (C, T) => {
            C ? T("隐私保护") : T();
          },
          i = (C, T) => {
            C
              ? T(
                  "您的认证信息严格保密，仅在下单时给服务者，或发布求助时给您选定的接单人查看"
                )
              : T();
          },
          j = (C, T) => {
            C
              ? T("如果您填写认证信息，将默认您同意授权向服务者展示认证信息")
              : T();
          },
          e = (C, T, E) => {
            E("text", {}, (N, C) => {}, f);
            E("text", {}, (N, C) => {}, g);
            E("text", {}, (N, C) => {}, h);
            E("text", {}, (N, C) => {}, i);
            E("text", {}, (N, C) => {}, j);
          },
          m = (C, T) => {
            C ? T("手机号") : T();
          },
          n = (C) => {},
          l = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              m
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请填写手机号");
                if (C || K || Z(U.formData, "phone"))
                  O(N, "value", X(D.formData).phone);
                A["formData"][0] = (D, E, T) => {
                  O(N, "value", X(D.formData).phone);
                  E(N);
                };
                if (C) O(N, "bindinput", "onPhoneInput");
                if (C) O(N, "type", "number");
                if (C) O(N, "maxlength", "11");
              },
              n
            );
          },
          p = (C, T) => {
            C ? T("楼栋号") : T();
          },
          q = (C) => {},
          o = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              p
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请输入楼栋号");
                if (C || K || Z(U.formData, "building"))
                  O(N, "value", X(D.formData).building);
                A["formData"][1] = (D, E, T) => {
                  O(N, "value", X(D.formData).building);
                  E(N);
                };
                if (C) O(N, "bindinput", "onBuildingInput");
                if (C) O(N, "type", "number");
              },
              q
            );
          },
          s = (C, T) => {
            C ? T("单元号") : T();
          },
          t = (C) => {},
          r = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              s
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请输入单元号");
                if (C || K || Z(U.formData, "unit"))
                  O(N, "value", X(D.formData).unit);
                A["formData"][2] = (D, E, T) => {
                  O(N, "value", X(D.formData).unit);
                  E(N);
                };
                if (C) O(N, "bindinput", "onUnitInput");
                if (C) O(N, "type", "number");
              },
              t
            );
          },
          v = (C, T) => {
            C ? T("门牌号") : T();
          },
          w = (C) => {},
          u = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              v
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请填写");
                if (C || K || Z(U.formData, "roomNumber"))
                  O(N, "value", X(D.formData).roomNumber);
                A["formData"][3] = (D, E, T) => {
                  O(N, "value", X(D.formData).roomNumber);
                  E(N);
                };
                if (C) O(N, "bindinput", "onRoomNumberInput");
                if (C) O(N, "type", "number");
              },
              w
            );
          },
          y,
          A0 = (C, T) => {
            C ? T("取消") : T();
          },
          z = (C, T, E) => {
            if (y === 1) {
              E(
                "button",
                {},
                (N, C) => {
                  if (C) L(N, "btn btn-cancel");
                  if (C) O(N, "bindtap", "onCancel");
                },
                A0
              );
            }
          },
          B0,
          D0 = (C, T) => {
            C ? T("跳过") : T();
          },
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "button",
                {},
                (N, C) => {
                  if (C) L(N, "btn btn-cancel");
                  if (C) O(N, "bindtap", "onSkip");
                },
                D0
              );
            }
          },
          E0 = (C, T) => {
            C ? T("提交认证") : T();
          },
          x = (C, T, E, B) => {
            y = D.showCancelButton ? 1 : 0;
            B(y, z);
            B0 = D.showSkipButton ? 1 : 0;
            B(B0, C0);
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-submit");
                if (C) O(N, "bindtap", "onSubmit");
              },
              E0
            );
          },
          k = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              l
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              o
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              r
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              u
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "button-group");
              },
              x
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "shuoming_box");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-container");
              },
              k
            );
          },
          a = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/auth/auth";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/auth/auth.js";
define(
  "pages/auth/auth.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var t,
      e = require("../../@babel/runtime/helpers/defineProperty"),
      a = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      n = require("../../@babel/runtime/helpers/asyncToGenerator");
    Page(
      (e(
        (t = {
          data: {
            formData: { phone: "", building: "", unit: "", roomNumber: "" },
            isSubmitting: !1,
            showBackButton: !0,
            showCancelButton: !0,
            showSkipButton: !1,
            fromPage: "",
          },
          onBuildingInput: function (t) {
            var e = t.detail.value.replace(/\D/g, "");
            this.setData({ "formData.building": e });
          },
          onUnitInput: function (t) {
            var e = t.detail.value.replace(/\D/g, "");
            this.setData({ "formData.unit": e });
          },
          onRoomNumberInput: function (t) {
            var e = t.detail.value.replace(/\D/g, "");
            this.setData({ "formData.roomNumber": e });
          },
          onLoad: function (t) {
            var e = t.from || "";
            console.log("认证页面来源:", e),
              this.setData({ fromPage: e }),
              "login" === e &&
                this.setData({
                  showBackButton: !1,
                  showCancelButton: !1,
                  showSkipButton: !0,
                });
          },
          onPhoneInput: function (t) {
            this.setData({ "formData.phone": t.detail.value });
          },
          onBuildingChange: function (t) {
            this.setData({ "formData.buildingIndex": t.detail.value });
          },
          onUnitChange: function (t) {
            this.setData({ "formData.unitIndex": t.detail.value });
          },
        }),
        "onRoomNumberInput",
        function (t) {
          this.setData({ "formData.roomNumber": t.detail.value });
        }
      ),
      e(t, "validateForm", function () {
        var t = this.data.formData,
          e = t.phone,
          a = t.building,
          n = t.unit,
          o = t.roomNumber;
        return e
          ? /^1\d{10}$/.test(e)
            ? a
              ? n
                ? !!o ||
                  (wx.showToast({ title: "请填写门牌号", icon: "error" }), !1)
                : (wx.showToast({ title: "请输入单元号", icon: "error" }), !1)
              : (wx.showToast({ title: "请输入楼栋号", icon: "error" }), !1)
            : (wx.showToast({ title: "手机号格式不正确", icon: "error" }), !1)
          : (wx.showToast({ title: "请填写手机号", icon: "error" }), !1);
      }),
      e(t, "onSubmit", function () {
        var t = this;
        return n(
          a().mark(function e() {
            var n, o, r, i, s, u, l;
            return a().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      if (!t.data.isSubmitting) {
                        e.next = 2;
                        break;
                      }
                      return e.abrupt("return");
                    case 2:
                      if (t.validateForm()) {
                        e.next = 4;
                        break;
                      }
                      return e.abrupt("return");
                    case 4:
                      if (
                        (t.setData({ isSubmitting: !0 }),
                        wx.showLoading({ title: "提交中..." }),
                        (e.prev = 6),
                        (n = getApp()),
                        (o = t.data.formData),
                        (r = o.phone),
                        (i = o.building),
                        (s = o.unit),
                        (u = o.roomNumber),
                        n.globalData.openid)
                      ) {
                        e.next = 11;
                        break;
                      }
                      throw new Error("未获取到用户openid");
                    case 11:
                      return (
                        (e.next = 13),
                        n.call({
                          path: "/api/mp/auth/bind-address",
                          method: "POST",
                          data: {
                            openid: n.globalData.openid,
                            phone: r,
                            building: i + "栋",
                            unit: s + "单元",
                            room: u,
                          },
                        })
                      );
                    case 13:
                      if (0 !== (l = e.sent).code) {
                        e.next = 21;
                        break;
                      }
                      return (
                        wx.showToast({ title: "提交成功", icon: "success" }),
                        (e.next = 18),
                        n.refreshUserInfo()
                      );
                    case 18:
                      setTimeout(function () {
                        "login" === t.data.fromPage
                          ? wx.switchTab({ url: "/pages/index/index" })
                          : wx.navigateBack();
                      }, 1500),
                        (e.next = 22);
                      break;
                    case 21:
                      throw new Error(l.message || "提交失败");
                    case 22:
                      e.next = 28;
                      break;
                    case 24:
                      (e.prev = 24),
                        (e.t0 = e.catch(6)),
                        console.error("绑定地址失败:", e.t0),
                        wx.showToast({
                          title: e.t0.message || "提交失败",
                          icon: "error",
                        });
                    case 28:
                      return (
                        (e.prev = 28),
                        t.setData({ isSubmitting: !1 }),
                        wx.hideLoading(),
                        e.finish(28)
                      );
                    case 32:
                    case "end":
                      return e.stop();
                  }
              },
              e,
              null,
              [[6, 24, 28, 32]]
            );
          })
        )();
      }),
      e(t, "onCancel", function () {
        wx.navigateBack();
      }),
      e(t, "onSkip", function () {
        wx.switchTab({ url: "/pages/index/index" });
      }),
      t)
    );
  },
  { isPage: true, isComponent: true, currentFile: "pages/auth/auth.js" }
);
require("pages/auth/auth.js");
