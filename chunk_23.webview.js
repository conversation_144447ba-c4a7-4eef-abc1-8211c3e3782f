__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/drawer/drawer": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          g,
          i = (C, T) => {
            C || K || U.title ? T(Y(D.title)) : T();
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__title");
                },
                i
              );
            }
          },
          k = (C, l, m, n, o, p, T, E) => {
            var r,
              u = (C) => {},
              t = (C, T, E) => {
                E(
                  "t-icon",
                  {},
                  (N, C) => {
                    if (C || K || Z(n, "icon")) O(N, "name", X(l).icon);
                  },
                  u
                );
              },
              s = (C, T, E) => {
                if (r === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__sidebar-item-icon");
                      if (C || K || undefined) O(N, "aria-hidden", true);
                    },
                    t
                  );
                }
              },
              v = (C, T) => {
                C || K || Z(n, "title") ? T(Y(X(l).title)) : T();
              },
              q = (C, T, E, B) => {
                r = X(l).icon ? 1 : 0;
                B(r, s);
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!U.classPrefix || undefined)
                      L(N, Y(D.classPrefix) + "__sidebar-item-title");
                  },
                  v
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__sidebar-item");
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "hover-class", Y(D.classPrefix) + "--hover");
                if (C || K || undefined) O(N, "hover-start-time", 0);
                if (C || K || undefined) O(N, "hover-stay-time", 100);
                if (C) O(N, "bindtap", "itemClick");
                if (C || K || !!U.ariaRole || undefined)
                  O(N, "aria-role", D.ariaRole || "button");
                if (C || K || Z(n, "title")) O(N, "aria-label", X(l).title);
                if (C || K || n) R.d(N, "item", l);
                if (C || K || o) R.d(N, "index", m);
              },
              q
            );
          },
          j = (C, T, E, B, F) => {
            F(D.items, "index", U ? U.items : undefined, [0, "items"], k);
          },
          l = (C, T, E, B, F, S) => {
            S("");
            S("footer");
          },
          f = (C, T, E, B, F, S) => {
            S("title");
            g = D.title ? 1 : 0;
            B(g, h);
            E(
              "scroll-view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__sidebar");
                if (C) O(N, "scroll-y", true);
                if (C) O(N, "type", "list");
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__footer");
              },
              l
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || U.classPrefix) L(N, D.classPrefix);
              },
              f
            );
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "t-popup",
                {},
                (N, C) => {
                  if (C) L(N, "class");
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C || K || U.zIndex) O(N, "zIndex", D.zIndex);
                  if (C || K || U.usingCustomNavbar)
                    O(N, "usingCustomNavbar", D.usingCustomNavbar);
                  var $A = D.placement == "right";
                  if (C || K || !!U.placement || ($A ? undefined : undefined))
                    O(N, "placement", $A ? "right" : "left");
                  if (C || K || U.showOverlay)
                    O(N, "showOverlay", D.showOverlay);
                  if (C || K || U.closeOnOverlayClick)
                    O(N, "closeOnOverlayClick", D.closeOnOverlayClick);
                  if (C)
                    R.v(N, "visible-change", "visibleChange", !1, !1, !1, !1);
                },
                e
              );
            }
          },
          b = (C, T, E, B) => {
            c = !D.destroyOnClose || D.visible ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/drawer/drawer.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-drawer{background:var(--td-drawer-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;height:100%;width:var(--td-drawer-width,",
        [0, 560],
        ")}\n.",
        [1],
        "t-drawer--hover{background-color:var(--td-drawer-hover-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)))}\n.",
        [1],
        "t-drawer__title{color:var(--td-drawer-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:var(--td-drawer-title-font-size,",
        [0, 36],
        ");font-weight:600;padding:",
        [0, 48],
        " ",
        [0, 32],
        " ",
        [0, 16],
        "}\n.",
        [1],
        "t-drawer__sidebar{height:var(--td-drawer-sidebar-height,70vh)}\n.",
        [1],
        "t-drawer__sidebar-item{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;line-height:var(--td-drawer-item-height,",
        [0, 48],
        ");padding-bottom:var(--td-drawer-item-padding,",
        [0, 32],
        ");padding-left:var(--td-drawer-item-padding,",
        [0, 32],
        ");padding-right:0;padding-top:var(--td-drawer-item-padding,",
        [0, 32],
        ");position:relative}\n.",
        [1],
        "t-drawer__sidebar-item::after{background-color:var(--td-drawer-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;left:var(--td-drawer-item-padding,",
        [0, 32],
        ");position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
        [1],
        "t-drawer__sidebar-item-title{color:var(--td-drawer-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));-webkit-flex:1;flex:1}\n.",
        [1],
        "t-drawer__sidebar-item-icon{color:var(--td-drawer-item-icon-color,var(--td-drawer-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9)))));font-size:var(--td-drawer-item-icon-size,",
        [0, 48],
        ");padding-right:",
        [0, 16],
        "}\n.",
        [1],
        "t-drawer__footer{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-flex-direction:column;flex-direction:column;padding-bottom:var(--td-drawer-footer-padding-bottom,",
        [0, 40],
        ")}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/drawer/drawer.wxss" }
    );
}
