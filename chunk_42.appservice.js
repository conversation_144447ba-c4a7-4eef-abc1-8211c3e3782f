__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/radio-group/radio-group": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            classPrefix: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
          },
          K = U === true,
          d = (C, e, f, g, h, i, T, E) => {
            var j = (C) => {};
            E(
              "t-radio",
              {},
              (N, C) => {
                if (C || K || !!U.prefix || undefined)
                  L(N, Y(D.prefix) + "-radio-option");
                if (C || K || !!Z(g, "block") || undefined)
                  O(N, "block", X(e).block || true);
                if (C || K || !!Z(g, "label") || undefined)
                  O(N, "label", X(e).label || "");
                if (C || K || Z(g, "value")) O(N, "value", X(e).value);
                if (C || K || !!Z(g, "checked") || undefined)
                  O(N, "checked", X(e).checked || false);
                if (C || K || !!Z(g, "content") || undefined)
                  O(N, "content", X(e).content || "");
                if (
                  C ||
                  K ||
                  !!(Z(g, "allowUncheck") || U.allowUncheck) ||
                  undefined
                )
                  O(N, "allow-uncheck", X(e).allowUncheck || D.allowUncheck);
                if (C || K || !!Z(g, "contentDisabled") || undefined)
                  O(N, "content-disabled", X(e).contentDisabled || false);
                if (C || K || !!Z(g, "readonly") || undefined)
                  O(N, "readonly", X(e).readonly || false);
                if (C || K || !!Z(g, "disabled") || undefined)
                  O(N, "disabled", X(e).disabled || false);
                if (C || K || !!(Z(g, "icon") || U.icon) || undefined)
                  O(N, "icon", X(e).icon || D.icon);
                if (C || K || !!(Z(g, "placement") || U.placement) || undefined)
                  O(N, "placement", X(e).placement || D.placement);
                if (C || K || !!Z(g, "maxContentRow") || undefined)
                  O(N, "max-content-row", X(e).maxContentRow || 5);
                if (C || K || !!Z(g, "maxLabelRow") || undefined)
                  O(N, "max-label-row", X(e).maxLabelRow || 3);
                if (C || K || !!Z(g, "name") || undefined)
                  O(N, "name", X(e).name || "");
                if (C || K || U.borderless) O(N, "borderless", D.borderless);
                if (C || K || h) R.d(N, "index", f);
                if (C || K || Z(g, "value")) R.d(N, "value", X(e).value);
                if (
                  C ||
                  K ||
                  !!(Z(g, "allowUncheck") || U.allowUncheck) ||
                  undefined
                )
                  R.d(N, "allowUncheck", X(e).allowUncheck || D.allowUncheck);
                if (C) R.v(N, "change", "handleRadioChange", !1, !1, !1, !1);
              },
              j
            );
          },
          c = (C, T, E, B, F, S) => {
            S("");
            F(
              D.radioOptions,
              "value",
              U ? U.radioOptions : undefined,
              [0, "radioOptions"],
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["classPrefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-role", "radiogroup");
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/radio/radio": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            block: new Array(1),
            tId: new Array(1),
            tabindex: new Array(1),
            maxContentRow: new Array(1),
            customStyle: new Array(1),
            style: new Array(1),
            maxLabelRow: new Array(1),
          },
          K = U === true,
          e,
          h = (C) => {},
          g = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "-icon__image");
                var $A = D.checked;
                var $B = 0;
                var $C = 1;
                if (
                  C ||
                  K ||
                  !!U.checked ||
                  ($A ? Z(U.iconVal, $B) : Z(U.iconVal, $C))
                )
                  O(N, "src", $A ? X(D.iconVal)[$B] : X(D.iconVal)[$C]);
                if (C) O(N, "webp", true);
              },
              h
            );
          },
          i,
          k = (C) => {},
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__icon-wrap");
                  var $A = D.icon == "circle";
                  if (C || K || !!U.icon || ($A ? undefined : undefined))
                    O(N, "name", $A ? "check-circle-filled" : "check");
                },
                k
              );
            }
          },
          l,
          n = (C) => {},
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      U.icon ||
                      Q.a([Q.a([U._disabled])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__icon-" + D.icon, [
                        ["disabled", D._disabled],
                      ])
                    );
                },
                n
              );
            }
          },
          o,
          q = (C) => {},
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([U._disabled])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__icon-circle", [
                        ["disabled", D._disabled],
                      ])
                    );
                },
                q
              );
            }
          },
          r,
          t = (C) => {},
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "placeholder");
                },
                t
              );
            }
          },
          f = (C, T, E, B, F, S) => {
            if (e === 1) {
              S("icon");
            } else if (e === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__image");
                },
                g
              );
            } else {
              i = D.checked && (D.icon == "circle" || D.icon == "line") ? 1 : 0;
              B(i, j);
              l = D.checked && D.icon == "dot" ? 1 : 0;
              B(l, m);
              o = !D.checked && (D.icon == "circle" || D.icon == "dot") ? 1 : 0;
              B(o, p);
              r = !D.checked && D.icon == "line" ? 1 : 0;
              B(r, s);
            }
          },
          d = (C, T, E, B) => {
            e = D.slotIcon ? 1 : D.customIcon ? 2 : 0;
            B(e, f);
          },
          w,
          x = (C, T) => {
            if (w === 1) {
              C || K || U.label ? T(Y(D.label)) : T();
            }
          },
          v = (C, T, E, B, F, S) => {
            w = D.label ? 1 : 0;
            B(w, x);
            S("");
            S("label");
          },
          z,
          A0 = (C, T) => {
            if (z === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          y = (C, T, E, B, F, S) => {
            z = D.content ? 1 : 0;
            B(z, A0);
            S("content");
          },
          u = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U._disabled]), Q.a([U.checked])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__title", [
                        ["disabled", D._disabled],
                        ["checked", D.checked],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-label"
                  );
                if (C || K || !!U.maxLabelRow || undefined)
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxLabelRow));
                A["maxLabelRow"][0] = (D, E, T) => {
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxLabelRow));
                };
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U._disabled]), Q.a([U.checked])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__description", [
                        ["disabled", D._disabled],
                        ["checked", D.checked],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C || K || !!U.maxContentRow || undefined)
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxContentRow));
                A["maxContentRow"][0] = (D, E, T) => {
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxContentRow));
                };
              },
              y
            );
          },
          B0,
          D0 = (C) => {},
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U._placement]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__border", [D._placement])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-border"
                    );
                },
                D0
              );
            }
          },
          c = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U._placement, Q.a([U.checked]), Q.a([U._disabled])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__icon", [
                        D._placement,
                        ["checked", D.checked],
                        ["disabled", D._disabled],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-icon"
                  );
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__content");
                if (C) R.d(N, "target", "text");
                if (C) R.v(N, "tap", "handleTap", !1, !0, !1, !1);
              },
              u
            );
            B0 = !D.borderless ? 1 : 0;
            B(B0, C0);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U._placement, Q.a([U.block])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D._placement,
                        ["block", D.block],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["block"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D._placement,
                        ["block", D.block],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C || K || U._disabled) O(N, "disabled", D._disabled);
                if (C) O(N, "aria-role", "radio");
                if (C || K || U.checked) O(N, "aria-checked", D.checked);
                if (C || K || !!(U.label || U.content) || undefined)
                  O(N, "aria-label", D.label + D.content);
                if (C || K || U._disabled) O(N, "aria-disabled", D._disabled);
                if (C || K || U.tabindex) O(N, "tabindex", D.tabindex);
                A["tabindex"][0] = (D, E, T) => {
                  O(N, "tabindex", D.tabindex);
                  E(N);
                };
                if (C) R.v(N, "tap", "handleTap", !1, !0, !1, !1);
                if (C || K || U.tId) R.i(N, D.tId);
                A["tId"][0] = (D, E, T) => {
                  R.i(N, D.tId);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/radio-group/radio-group";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/radio-group/radio-group.js";
define(
  "miniprogram_npm/tdesign-miniprogram/radio-group/radio-group.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/typeof"),
      t = require("../../../@babel/runtime/helpers/createClass"),
      a = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      l = d(require("../common/config")),
      o = require("../common/src/index"),
      s = d(require("./props"));
    function d(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = l.default.prefix,
      c = "".concat(u, "-radio-group"),
      h = (function (n) {
        i(o, n);
        var l = r(o);
        function o() {
          var t;
          return (
            a(this, o),
            ((t = l.apply(this, arguments)).behaviors = ["wx://form-field"]),
            (t.externalClasses = ["".concat(u, "-class")]),
            (t.data = { prefix: u, classPrefix: c, radioOptions: [] }),
            (t.relations = {
              "../radio/radio": {
                type: "descendant",
                linked: function (e) {
                  var t = this.data,
                    a = t.value,
                    i = t.disabled,
                    r = t.readonly;
                  e.setData({ checked: a === e.data.value }),
                    e.setDisabled(i),
                    e.setReadonly(r);
                },
              },
            }),
            (t.properties = s.default),
            (t.controlledProps = [{ key: "value", event: "change" }]),
            (t.observers = {
              value: function (e) {
                this.getChildren().forEach(function (t) {
                  t.setData({ checked: e === t.data.value });
                });
              },
              options: function () {
                this.initWithOptions();
              },
              disabled: function (e) {
                var t;
                (
                  null === (t = this.data.options) || void 0 === t
                    ? void 0
                    : t.length
                )
                  ? this.initWithOptions()
                  : this.getChildren().forEach(function (t) {
                      t.setDisabled(e);
                    });
              },
            }),
            (t.methods = {
              getChildren: function () {
                var e = this.$children;
                return (
                  (null == e ? void 0 : e.length) ||
                    (e = this.selectAllComponents(
                      ".".concat(u, "-radio-option")
                    )),
                  e
                );
              },
              updateValue: function (e) {
                this._trigger("change", { value: e });
              },
              handleRadioChange: function (e) {
                var t = e.detail.checked,
                  a = e.target.dataset,
                  i = a.value,
                  r = a.index,
                  n = a.allowUncheck;
                this._trigger(
                  "change",
                  !1 === t && n
                    ? { value: null, index: r }
                    : { value: i, index: r }
                );
              },
              initWithOptions: function () {
                var t = this.data,
                  a = t.options,
                  i = t.value,
                  r = t.keys,
                  n = t.disabled,
                  l = t.readonly;
                if ((null == a ? void 0 : a.length) && Array.isArray(a)) {
                  var o = [];
                  try {
                    a.forEach(function (t) {
                      var a,
                        s,
                        d,
                        u = e(t);
                      "number" === u || "string" === u
                        ? o.push({
                            label: "".concat(t),
                            value: t,
                            checked: i === t,
                            disabled: n,
                            readonly: l,
                          })
                        : "object" === u &&
                          o.push(
                            Object.assign(Object.assign({}, t), {
                              label:
                                t[
                                  null !== (a = null == r ? void 0 : r.label) &&
                                  void 0 !== a
                                    ? a
                                    : "label"
                                ],
                              value:
                                t[
                                  null !== (s = null == r ? void 0 : r.value) &&
                                  void 0 !== s
                                    ? s
                                    : "value"
                                ],
                              checked:
                                i ===
                                t[
                                  null !== (d = null == r ? void 0 : r.value) &&
                                  void 0 !== d
                                    ? d
                                    : "value"
                                ],
                              disabled: t.disabled || n,
                              readonly: t.readonly || l,
                            })
                          );
                    }),
                      this.setData({ radioOptions: o });
                  } catch (a) {
                    console.error("error", a);
                  }
                } else this.setData({ radioOptions: [] });
              },
            }),
            t
          );
        }
        return t(o);
      })(o.SuperComponent),
      v = (h = (0, n.__decorate)([(0, o.wxComponent)()], h));
    exports.default = v;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/radio-group/radio-group.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/radio-group/radio-group.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/radio/radio";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/radio/radio.js";
define(
  "miniprogram_npm/tdesign-miniprogram/radio/radio.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      o = l(require("../common/config")),
      r = require("../common/src/index"),
      s = l(require("./props"));
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var c = o.default.prefix,
      d = "".concat(c, "-radio"),
      u = (function (i) {
        a(r, i);
        var o = n(r);
        function r() {
          var e;
          return (
            t(this, r),
            ((e = o.apply(this, arguments)).externalClasses = [
              "".concat(c, "-class"),
              "".concat(c, "-class-label"),
              "".concat(c, "-class-icon"),
              "".concat(c, "-class-content"),
              "".concat(c, "-class-border"),
            ]),
            (e.behaviors = ["wx://form-field"]),
            (e.relations = {
              "../radio-group/radio-group": {
                type: "ancestor",
                linked: function (e) {
                  e.data.borderless && this.setData({ borderless: !0 });
                },
              },
            }),
            (e.options = { multipleSlots: !0 }),
            (e.lifetimes = {
              attached: function () {
                this.init();
              },
            }),
            (e.properties = Object.assign(Object.assign({}, s.default), {
              borderless: { type: Boolean, value: !1 },
              tId: { type: String },
            })),
            (e.controlledProps = [{ key: "checked", event: "change" }]),
            (e.data = {
              prefix: c,
              classPrefix: d,
              customIcon: !1,
              slotIcon: !1,
              optionLinked: !1,
              iconVal: [],
              _placement: "",
              _disabled: !1,
              _readonly: !1,
            }),
            (e.observers = {
              disabled: function (e) {
                this.setData({ _disabled: e });
              },
              readonly: function (e) {
                this.setData({ _readonly: e });
              },
            }),
            (e.methods = {
              handleTap: function (e) {
                var t = this.data,
                  a = t._disabled,
                  n = t._readonly,
                  i = t.contentDisabled,
                  o = e.currentTarget.dataset.target;
                a || n || ("text" === o && i) || this.doChange();
              },
              doChange: function () {
                var e,
                  t = this.data,
                  a = t.value,
                  n = t.checked,
                  i = t.allowUncheck,
                  o = Boolean(
                    i ||
                      (null === (e = this.$parent) || void 0 === e
                        ? void 0
                        : e.data.allowUncheck)
                  );
                this.$parent
                  ? this.$parent.updateValue(n && o ? null : a)
                  : this._trigger("change", { checked: !o || !n });
              },
              init: function () {
                var e,
                  t,
                  a,
                  n,
                  i = this.data.icon,
                  o = Array.isArray(
                    (null === (e = this.$parent) || void 0 === e
                      ? void 0
                      : e.icon) || i
                  );
                this.setData({
                  customIcon: o,
                  slotIcon: "slot" === i,
                  iconVal: o
                    ? (null === (t = this.$parent) || void 0 === t
                        ? void 0
                        : t.icon) || i
                    : [],
                  _placement:
                    this.data.placement ||
                    (null ===
                      (n =
                        null === (a = this.$parent) || void 0 === a
                          ? void 0
                          : a.data) || void 0 === n
                      ? void 0
                      : n.placement) ||
                    "left",
                });
              },
              setDisabled: function (e) {
                this.setData({ _disabled: this.data.disabled || e });
              },
              setReadonly: function (e) {
                this.setData({ _readonly: this.data.readonly || e });
              },
            }),
            e
          );
        }
        return e(r);
      })(r.SuperComponent),
      h = (u = (0, i.__decorate)([(0, r.wxComponent)()], u));
    exports.default = h;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/radio/radio.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/radio/radio.js");
