__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/cell/cell": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            arrow: new Array(1),
            ariaLabel: new Array(1),
            bordered: new Array(1),
            align: new Array(1),
            customStyle: new Array(1),
            isLastChild: new Array(1),
            style: new Array(1),
            hover: new Array(1),
            ariaRole: new Array(1),
          },
          K = U === true,
          e,
          g,
          h = (C, T, E, B, F, S, J) => {
            var $A = I(g);
            if (g && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix +
                      "__left-icon " +
                      D.prefix +
                      "-class-left-icon",
                  },
                  X(D._leftIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._leftIcon === true ||
                      Object.assign(
                        { tClass: !!(U.classPrefix || U.prefix) || undefined },
                        X(U._leftIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              g = "icon";
              B(g, h);
            }
          },
          i,
          k = (C) => {},
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (C) O(N, "shape", "round");
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    O(
                      N,
                      "t-class",
                      Y(D.classPrefix) +
                        "__left-image " +
                        Y(D.prefix) +
                        "-class-image"
                    );
                  if (C || K || U.image) O(N, "src", D.image);
                },
                k
              );
            }
          },
          d = (C, T, E, B, F, S) => {
            e = D._leftIcon ? 1 : 0;
            B(e, f);
            S("left-icon");
            i = D.image ? 1 : 0;
            B(i, j);
            S("image");
          },
          n,
          o = (C, T) => {
            if (n === 1) {
              C || K || U.title ? T(Y(D.title)) : T();
            }
          },
          p,
          r = (C, T) => {
            C ? T("\u{a0}*") : T();
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "--required");
                  if (C) O(N, "decode", true);
                },
                r
              );
            }
          },
          m = (C, T, E, B, F, S) => {
            n = D.title ? 1 : 0;
            B(n, o);
            S("title");
            p = D.required ? 1 : 0;
            B(p, q);
          },
          t,
          v = (C, T) => {
            C || K || U.description ? T(Y(D.description)) : T();
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__description-text");
                },
                v
              );
            }
          },
          s = (C, T, E, B, F, S) => {
            t = D.description ? 1 : 0;
            B(t, u);
            S("description");
          },
          l = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__title-text " +
                      Y(D.prefix) +
                      "-class-title"
                  );
              },
              m
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__description " +
                      Y(D.prefix) +
                      "-class-description"
                  );
              },
              s
            );
          },
          x,
          z = (C, T) => {
            C || K || U.note ? T(Y(D.note)) : T();
          },
          y = (C, T, E) => {
            if (x === 1) {
              E("text", {}, (N, C) => {}, z);
            }
          },
          w = (C, T, E, B, F, S) => {
            x = D.note ? 1 : 0;
            B(x, y);
            S("note");
          },
          B0,
          D0,
          E0 = (C, T, E, B, F, S, J) => {
            var $A = I(D0);
            if (D0 && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix +
                      "__right-icon " +
                      D.prefix +
                      "-class-right-icon",
                  },
                  X(D._arrow),
                  {}
                ),
                K ||
                  (U
                    ? U._arrow === true ||
                      Object.assign(
                        { tClass: !!(U.classPrefix || U.prefix) || undefined },
                        X(U._arrow),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          F0,
          H0,
          I0 = (C, T, E, B, F, S, J) => {
            var $A = I(H0);
            if (H0 && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix +
                      "__right-icon " +
                      D.prefix +
                      "-class-right-icon",
                  },
                  X(D._rightIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._rightIcon === true ||
                      Object.assign(
                        { tClass: !!(U.classPrefix || U.prefix) || undefined },
                        X(U._rightIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          G0 = (C, T, E, B, F, S, J) => {
            if (F0 === 1) {
              H0 = "icon";
              B(H0, I0);
            }
          },
          C0 = (C, T, E, B, F, S, J) => {
            if (B0 === 1) {
              D0 = "icon";
              B(D0, E0);
            } else {
              F0 = D._rightIcon ? 1 : 0;
              B(F0, G0);
              S("right-icon");
            }
          },
          A0 = (C, T, E, B) => {
            B0 = D._arrow ? 1 : 0;
            B(B0, C0);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__left " + Y(D.prefix) + "-class-left"
                  );
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__title " +
                      Y(D.prefix) +
                      "-class-center"
                  );
              },
              l
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__note " + Y(D.prefix) + "-class-note"
                  );
              },
              w
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.align]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__right", [D.align])) +
                      " " +
                      Y(D.prefix) +
                      "-class-right"
                  );
                A["align"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__right", [D.align])) +
                      " " +
                      Y(D.prefix) +
                      "-class-right"
                  );
                };
              },
              A0
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!(U.bordered || U.isLastChild) || undefined])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          ["borderless", !D.bordered || D.isLastChild],
                        ])
                      )
                  );
                A["bordered"][0] = A["isLastChild"][0] = (D, E, T) => {
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          ["borderless", !D.bordered || D.isLastChild],
                        ])
                      )
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                var $A = D.hover;
                if (
                  C ||
                  K ||
                  !!U.hover ||
                  ($A ? !!U.classPrefix || undefined : undefined)
                )
                  O(N, "hover-class", $A ? D.classPrefix + "--hover" : "");
                A["hover"][0] = (D, E, T) => {
                  var $B = D.hover;
                  O(N, "hover-class", $B ? D.classPrefix + "--hover" : "");
                  E(N);
                };
                if (C) O(N, "hover-stay-time", "70");
                var $B = D.arrow;
                if (
                  C ||
                  K ||
                  !!(U.ariaRole || U.arrow || ($B ? undefined : undefined)) ||
                  undefined
                )
                  O(N, "aria-role", D.ariaRole || ($B ? "button" : ""));
                A["ariaRole"][0] = A["arrow"][0] = (D, E, T) => {
                  var $C = D.arrow;
                  O(N, "aria-role", D.ariaRole || ($C ? "button" : ""));
                  E(N);
                };
                if (C || K || U.ariaLabel) O(N, "aria-label", D.ariaLabel);
                A["ariaLabel"][0] = (D, E, T) => {
                  O(N, "aria-label", D.ariaLabel);
                  E(N);
                };
                if (C) R.v(N, "tap", "onClick", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/cell/cell.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-cell{background-color:var(--td-cell-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));box-sizing:border-box;display:-webkit-flex;display:flex;height:var(--td-cell-height,auto);line-height:var(--td-cell-line-height,",
        [0, 48],
        ");padding:var(--td-cell-vertical-padding,",
        [0, 32],
        ") var(--td-cell-horizontal-padding,",
        [0, 32],
        ");position:relative;width:100%}\n.",
        [1],
        "t-cell::after{border-bottom:var(--td-cell-border-width,1px) solid var(--td-cell-border-color,var(--td-component-stroke,var(--td-gray-color-3,#e7e7e7)));bottom:0;box-sizing:border-box;content:\x22 \x22;left:0;left:var(--td-cell-border-left-space,var(--td-cell-horizontal-padding,",
        [0, 32],
        "));pointer-events:none;position:absolute;right:0;right:var(--td-cell-border-right-space,0);-webkit-transform:scaleY(.5);transform:scaleY(.5);-webkit-transform-origin:bottom;transform-origin:bottom}\n.",
        [1],
        "t-cell--borderless::after{display:none}\n.",
        [1],
        "t-cell__description{color:var(--td-cell-description-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))));font-size:var(--td-cell-description-font-size,var(--td-font-size-base,",
        [0, 28],
        "));line-height:var(--td-cell-description-line-height,",
        [0, 44],
        ")}\n.",
        [1],
        "t-cell__description-text{margin-top:calc(var(--td-spacer,",
        [0, 16],
        ")/ 2)}\n.",
        [1],
        "t-cell__note{-webkit-align-items:center;align-items:center;color:var(--td-cell-note-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));display:-webkit-flex;display:flex;font-size:var(--td-cell-note-font-size,var(--td-font-size-m,",
        [0, 32],
        "));-webkit-justify-content:flex-end;justify-content:flex-end}\n.",
        [1],
        "t-cell__title{margin-right:var(--td-spacer-2,",
        [0, 32],
        ")}\n.",
        [1],
        "t-cell__note,.",
        [1],
        "t-cell__title{-webkit-flex:1 1 auto;flex:1 1 auto}\n.",
        [1],
        "t-cell__note:empty,.",
        [1],
        "t-cell__title:empty{display:none}\n.",
        [1],
        "t-cell__title-text{color:var(--td-cell-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex;font-size:var(--td-cell-title-font-size,var(--td-font-size-m,",
        [0, 32],
        "));font-weight:400}\n.",
        [1],
        "t-cell__left,.",
        [1],
        "t-cell__right{-webkit-align-self:stretch;align-self:stretch}\n.",
        [1],
        "t-cell__left:not(:empty){margin-right:var(--td-spacer-1,",
        [0, 24],
        ")}\n.",
        [1],
        "t-cell__left-icon{color:var(--td-cell-left-icon-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));font-size:var(--td-cell-left-icon-font-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-cell__left-image{height:var(--td-cell-image-height,",
        [0, 96],
        ");width:var(--td-cell-image-width,",
        [0, 96],
        ")}\n.",
        [1],
        "t-cell__note:not(:empty)+.",
        [1],
        "t-cell__right{margin-left:calc(var(--td-spacer,",
        [0, 16],
        ")/ 2)}\n.",
        [1],
        "t-cell__right{display:-webkit-flex;display:flex}\n.",
        [1],
        "t-cell__right-icon{color:var(--td-cell-right-icon-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-cell-right-icon-font-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-cell__right--middle{-webkit-align-items:center;align-items:center}\n.",
        [1],
        "t-cell__right--top{-webkit-align-items:flex-start;align-items:flex-start}\n.",
        [1],
        "t-cell__right--bottom{-webkit-align-items:flex-end;align-items:flex-end}\n.",
        [1],
        "t-cell--hover{background-color:var(--td-cell-hover-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)))}\n.",
        [1],
        "t-cell--required{color:var(--td-cell-required-color,var(--td-error-color,var(--td-error-color-6,#d54941)));font-size:var(--td-cell-required-font-size,var(--td-font-size-m,",
        [0, 32],
        "))}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/cell/cell.wxss" }
    );
}
