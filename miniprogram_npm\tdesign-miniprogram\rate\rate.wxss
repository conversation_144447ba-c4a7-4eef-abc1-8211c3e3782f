@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-rate {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: flex-start;
  position: relative;
}
.t-rate__wrapper {
  display: -webkit-inline-flex;
  display: inline-flex;
  line-height: 1em;
}
.t-rate__icon {
  display: block;
  line-height: 1em;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  width: 1em;
}
.t-rate__icon--current {
  transform: scale(var(--td-rate-icon-scale, 1.33));
}
.t-rate__icon--selected {
  color: var(
    --td-rate-selected-color,
    var(--td-warning-color, var(--td-warning-color-5, #e37318))
  );
}
.t-rate__icon--selected-half {
  background: linear-gradient(
    to right,
    var(
        --td-rate-selected-color,
        var(--td-warning-color, var(--td-warning-color-5, #e37318))
      )
      0,
    var(
        --td-rate-selected-color,
        var(--td-warning-color, var(--td-warning-color-5, #e37318))
      )
      50%,
    var(
        --td-rate-unselected-color,
        var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
      )
      51%,
    var(
        --td-rate-unselected-color,
        var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
      )
      100%
  );
  background-clip: text;
  color: transparent;
}
.t-rate__icon--unselected {
  color: var(
    --td-rate-unselected-color,
    var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
  );
}
.t-rate__text {
  color: var(
    --td-rate-text-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
  font-size: var(--td-rate-text-font-size, var(--td-font-size-m, 32rpx));
  margin-left: 32rpx;
  vertical-align: middle;
}
.t-rate__text--active {
  color: var(
    --td-rate-text-active-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  font-weight: var(--td-rate-text-active-font-weight, 600);
}
.t-rate__text--sr-only {
  clip: rect(0, 0, 0, 0);
  border: 0;
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
.t-rate__tips {
  align-items: center;
  background-color: var(--td-bg-color-container, var(--td-font-white-1, #fff));
  border-radius: 12rpx;
  bottom: calc(100% + 16rpx);
  box-shadow: var(
    --td-shadow-1,
    0 1px 10px rgba(0, 0, 0, 0.05),
    0 4px 5px rgba(0, 0, 0, 0.08),
    0 2px 4px -1px rgba(0, 0, 0, 0.12)
  );
  display: -webkit-flex;
  display: flex;
  padding: 8rpx;
  position: absolute;
  transform: translateX(-50%);
}
.t-rate__tips--bottom {
  bottom: auto;
  top: calc(100% + 16rpx);
}
.t-rate__tips-item {
  align-items: center;
  border-radius: 6rpx;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 64rpx;
}
.t-rate__tips-item--active {
  background-color: var(
    --td-bg-color-component,
    var(--td-gray-color-3, #e7e7e7)
  );
}
.t-rate__tips-text {
  font-size: 24rpx;
  line-height: 40rpx;
  text-align: center;
}
