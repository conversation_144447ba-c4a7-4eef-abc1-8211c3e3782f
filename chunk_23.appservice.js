__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/drawer/drawer": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          g,
          i = (C, T) => {
            C || K || U.title ? T(Y(D.title)) : T();
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__title");
                },
                i
              );
            }
          },
          k = (C, l, m, n, o, p, T, E) => {
            var r,
              u = (C) => {},
              t = (C, T, E) => {
                E(
                  "t-icon",
                  {},
                  (N, C) => {
                    if (C || K || Z(n, "icon")) O(N, "name", X(l).icon);
                  },
                  u
                );
              },
              s = (C, T, E) => {
                if (r === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__sidebar-item-icon");
                      if (C || K || undefined) O(N, "aria-hidden", true);
                    },
                    t
                  );
                }
              },
              v = (C, T) => {
                C || K || Z(n, "title") ? T(Y(X(l).title)) : T();
              },
              q = (C, T, E, B) => {
                r = X(l).icon ? 1 : 0;
                B(r, s);
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!U.classPrefix || undefined)
                      L(N, Y(D.classPrefix) + "__sidebar-item-title");
                  },
                  v
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__sidebar-item");
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "hover-class", Y(D.classPrefix) + "--hover");
                if (C || K || undefined) O(N, "hover-start-time", 0);
                if (C || K || undefined) O(N, "hover-stay-time", 100);
                if (C) O(N, "bindtap", "itemClick");
                if (C || K || !!U.ariaRole || undefined)
                  O(N, "aria-role", D.ariaRole || "button");
                if (C || K || Z(n, "title")) O(N, "aria-label", X(l).title);
                if (C || K || n) R.d(N, "item", l);
                if (C || K || o) R.d(N, "index", m);
              },
              q
            );
          },
          j = (C, T, E, B, F) => {
            F(D.items, "index", U ? U.items : undefined, [0, "items"], k);
          },
          l = (C, T, E, B, F, S) => {
            S("");
            S("footer");
          },
          f = (C, T, E, B, F, S) => {
            S("title");
            g = D.title ? 1 : 0;
            B(g, h);
            E(
              "scroll-view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__sidebar");
                if (C) O(N, "scroll-y", true);
                if (C) O(N, "type", "list");
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__footer");
              },
              l
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || U.classPrefix) L(N, D.classPrefix);
              },
              f
            );
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "t-popup",
                {},
                (N, C) => {
                  if (C) L(N, "class");
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C || K || U.zIndex) O(N, "zIndex", D.zIndex);
                  if (C || K || U.usingCustomNavbar)
                    O(N, "usingCustomNavbar", D.usingCustomNavbar);
                  var $A = D.placement == "right";
                  if (C || K || !!U.placement || ($A ? undefined : undefined))
                    O(N, "placement", $A ? "right" : "left");
                  if (C || K || U.showOverlay)
                    O(N, "showOverlay", D.showOverlay);
                  if (C || K || U.closeOnOverlayClick)
                    O(N, "closeOnOverlayClick", D.closeOnOverlayClick);
                  if (C)
                    R.v(N, "visible-change", "visibleChange", !1, !1, !1, !1);
                },
                e
              );
            }
          },
          b = (C, T, E, B) => {
            c = !D.destroyOnClose || D.visible ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/drawer/drawer";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/drawer/drawer.js";
define(
  "miniprogram_npm/tdesign-miniprogram/drawer/drawer.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      a = require("../common/src/index"),
      l = o(require("../common/config")),
      n = o(require("./props")),
      u = o(require("../mixins/using-custom-navbar"));
    function o(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var c = l.default.prefix,
      p = "".concat(c, "-drawer"),
      d = (function (s) {
        t(l, s);
        var a = i(l);
        function l() {
          var e;
          return (
            r(this, l),
            ((e = a.apply(this, arguments)).behaviors = [u.default]),
            (e.externalClasses = []),
            (e.options = { multipleSlots: !0 }),
            (e.properties = n.default),
            (e.data = { classPrefix: p }),
            (e.methods = {
              visibleChange: function (e) {
                var r = e.detail.visible,
                  t = this.data.showOverlay;
                this.setData({ visible: r }),
                  r || this.triggerEvent("close", { trigger: "overlay" }),
                  t && this.triggerEvent("overlay-click", { visible: r });
              },
              itemClick: function (e) {
                var r = e.currentTarget.dataset,
                  t = r.index,
                  i = r.item;
                this.triggerEvent("item-click", { index: t, item: i });
              },
            }),
            e
          );
        }
        return e(l);
      })(a.SuperComponent),
      v = (d = (0, s.__decorate)([(0, a.wxComponent)()], d));
    exports.default = v;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/drawer/drawer.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/drawer/drawer.js");
