__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/articles-info/articles-info": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b = (C) => {},
          d,
          g = (C, T) => {
            C ? T("加载中...") : T();
          },
          f = (C, T, E) => {
            E("text", {}, (N, C) => {}, g);
          },
          i,
          l = (C, T) => {
            C || K || Z(U.articlesData, "content")
              ? T(Y(X(D.articlesData).content))
              : T();
          },
          k = (C, T, E) => {
            E("text", {}, (N, C) => {}, l);
          },
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "article-content");
                },
                k
              );
            }
          },
          m,
          p = (C, q, r, s, t, u, T, E) => {
            var w = (C) => {},
              v = (C, T, E) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "detail-image");
                    if (C || K || s) O(N, "src", q);
                    if (C) O(N, "mode", "widthFix");
                    if (C) O(N, "bindtap", "previewImage");
                    if (C || K || s) R.d(N, "src", q);
                  },
                  w
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_item");
              },
              v
            );
          },
          o = (C, T, E, B, F) => {
            F(
              X(D.articlesData).detail_images,
              "index",
              U ? Z(U.articlesData, "detail_images") : undefined,
              [0, "articlesData", "detail_images"],
              p
            );
          },
          n = (C, T, E) => {
            if (m === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "detail_images");
                },
                o
              );
            }
          },
          h = (C, T, E, B) => {
            i = X(D.articlesData).content ? 1 : 0;
            B(i, j);
            m =
              X(D.articlesData).detail_images &&
              X(X(D.articlesData).detail_images).length > 0
                ? 1
                : 0;
            B(m, n);
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                f
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "article-wrapper");
                },
                h
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.loading ? 1 : 0;
            B(d, e);
          },
          a = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || !!Z(U.articlesData, "title") || undefined)
                  O(N, "title", X(D.articlesData).title || "文章详情");
                if (C) O(N, "background", "#FFF");
              },
              b
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              c
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/articles-info/articles-info";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/articles-info/articles-info.js";
define(
  "pages/articles-info/articles-info.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      t = require("../../@babel/runtime/helpers/asyncToGenerator");
    Page({
      data: {
        articlesData: {
          article_id: null,
          title: "",
          content: "",
          detail_images: "",
        },
        loading: !0,
      },
      onLoad: function (e) {
        var t = this,
          a = e.id;
        a
          ? this.loadArticleDetail(a)
          : (wx.showToast({ title: "文章ID不存在", icon: "error" }),
            setTimeout(function () {
              t.safeNavigateBack();
            }, 1500));
      },
      safeNavigateBack: function () {
        getCurrentPages().length > 1
          ? wx.navigateBack()
          : wx.reLaunch({ url: "/pages/index/index" });
      },
      loadArticleDetail: function (a) {
        var n = this;
        return t(
          e().mark(function t() {
            var r, i;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return (
                        (r = getApp()),
                        n.setData({ loading: !0 }),
                        (e.prev = 2),
                        (e.next = 5),
                        r.call({
                          path: "/api/mp/articles/".concat(a),
                          method: "GET",
                        })
                      );
                    case 5:
                      if (0 !== (i = e.sent).code) {
                        e.next = 11;
                        break;
                      }
                      console.log("文章详情获取成功:", i.data),
                        n.setData({ articlesData: i.data, loading: !1 }),
                        (e.next = 12);
                      break;
                    case 11:
                      throw new Error(i.message || "获取文章详情失败");
                    case 12:
                      e.next = 19;
                      break;
                    case 14:
                      (e.prev = 14),
                        (e.t0 = e.catch(2)),
                        console.error("获取文章详情失败：", e.t0),
                        n.setData({ loading: !1 }),
                        wx.showToast({ title: "加载失败", icon: "error" });
                    case 19:
                    case "end":
                      return e.stop();
                  }
              },
              t,
              null,
              [[2, 14]]
            );
          })
        )();
      },
      refreshArticleDetail: function () {
        var a = this;
        return t(
          e().mark(function t() {
            var n;
            return e().wrap(function (e) {
              for (;;)
                switch ((e.prev = e.next)) {
                  case 0:
                    if (!(n = a.data.articlesData.article_id)) {
                      e.next = 4;
                      break;
                    }
                    return (e.next = 4), a.loadArticleDetail(n);
                  case 4:
                  case "end":
                    return e.stop();
                }
            }, t);
          })
        )();
      },
      onReady: function () {},
      onShow: function () {},
      onHide: function () {},
      onUnload: function () {},
      onPullDownRefresh: function () {
        this.refreshArticleDetail().then(function () {
          wx.stopPullDownRefresh();
        });
      },
      onReachBottom: function () {},
      onShareAppMessage: function () {
        var e = this.data.articlesData;
        return {
          title: e.title || "精彩文章分享",
          path: "/pages/articles-info/articles-info?id=".concat(e.article_id),
          imageUrl: e.detail_images || "",
        };
      },
    });
  },
  {
    isPage: true,
    isComponent: true,
    currentFile: "pages/articles-info/articles-info.js",
  }
);
require("pages/articles-info/articles-info.js");
