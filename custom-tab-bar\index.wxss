.tab-bar {
  background: #fff;
  bottom: 0;
  box-shadow: 0-2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: -webkit-flex;
  display: flex;
  height: 100rpx;
  left: 0;
  padding-bottom: env(safe-area-inset-bottom);
  position: fixed;
  right: 0;
}
.tab-bar-border {
  background-color: rgba(0, 0, 0, 0.1);
  height: 1rpx;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}
.tab-bar-item {
  flex: 1;
  position: relative;
  text-align: center;
}
.normal-tab,
.tab-bar-item {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.tab-icon {
  height: 48rpx;
  margin-bottom: 8rpx;
  width: 48rpx;
}
.tab-text {
  font-size: 24rpx;
  line-height: 1;
}
.publish-btn {
  position: relative;
  top: -20rpx;
}
.publish-circle {
  align-items: center;
  background: linear-gradient(135deg, #ff6b6b, #ff4d4f);
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
  display: -webkit-flex;
  display: flex;
  height: 100rpx;
  justify-content: center;
  width: 100rpx;
}
.publish-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
}
.publish-modal {
  align-items: flex-end;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  position: fixed;
  z-index: 9999;
}
.modal-mask,
.publish-modal {
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
}
.modal-mask {
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
}
.modal-content {
  background-color: #fff;
  border-radius: 40rpx 40rpx 0 0;
  margin-bottom: 100rpx;
  padding: 40rpx 40rpx 60rpx;
  position: relative;
  width: 100%;
}
.modal-header {
  margin-bottom: 60rpx;
  text-align: center;
}
.modal-title {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}
.modal-body {
  justify-content: space-around;
}
.modal-body,
.option-item {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.option-item {
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 20rpx;
  flex-direction: column;
  min-width: 200rpx;
  padding: 30rpx;
  transition: all 0.3s ease;
}
.option-item:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}
.option-icon {
  align-items: center;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: -webkit-flex;
  display: flex;
  height: 80rpx;
  justify-content: center;
  margin-bottom: 20rpx;
  width: 80rpx;
}
.icon-img {
  height: 48rpx;
  width: 48rpx;
}
.option-text {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}
