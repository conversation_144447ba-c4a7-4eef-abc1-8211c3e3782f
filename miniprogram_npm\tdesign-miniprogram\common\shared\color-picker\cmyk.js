Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.rgb2cmyk = exports.cmykInputToColor = exports.cmyk2rgb = void 0);
exports.rgb2cmyk = function (r, t, e) {
  var n = 0,
    c = 0,
    a = 0,
    o = parseInt("".concat(r).replace(/\s/g, ""), 10),
    s = parseInt("".concat(t).replace(/\s/g, ""), 10),
    p = parseInt("".concat(e).replace(/\s/g, ""), 10);
  if (0 === o && 0 === s && 0 === p) return 1, [0, 0, 0, 1];
  (n = 1 - o / 255), (c = 1 - s / 255), (a = 1 - p / 255);
  var u = Math.min(n, Math.min(c, a));
  return [
    (n = (n - u) / (1 - u)),
    (c = (c - u) / (1 - u)),
    (a = (a - u) / (1 - u)),
    u,
  ];
};
var r = function (r, t, e, n) {
  var c = r / 100,
    a = t / 100,
    o = e / 100,
    s = n / 100,
    p = 1 - (c = c * (1 - s) + s),
    u = 1 - (a = a * (1 - s) + s),
    i = 1 - (o = o * (1 - s) + s);
  return {
    r: (p = Math.round(255 * p)),
    g: (u = Math.round(255 * u)),
    b: (i = Math.round(255 * i)),
  };
};
exports.cmyk2rgb = r;
var t = /cmyk\((\d+%?),(\d+%?),(\d+%?),(\d+%?)\)/,
  e = function (r) {
    return Math.max(0, Math.min(255, parseInt(r, 10)));
  };
exports.cmykInputToColor = function (n) {
  if (/cmyk/i.test(n)) {
    var c = n.replace(/\s/g, "").match(t),
      a = e(c[1]),
      o = e(c[2]),
      s = e(c[3]),
      p = e(c[4]),
      u = r(a, o, s, p),
      i = u.r,
      m = u.g,
      g = u.b;
    return "rgb(".concat(i, ", ").concat(m, ", ").concat(g, ")");
  }
  return n;
};
