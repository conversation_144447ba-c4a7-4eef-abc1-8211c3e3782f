@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-upload {
  position: relative;
}
.t-upload__grid-content {
  padding: 0;
}
.t-upload__grid-file {
  position: relative;
}
.t-upload__add-icon {
  align-items: center;
  background-color: var(
    --td-upload-add-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  border-radius: var(--td-upload-radius, var(--td-radius-default, 12rpx));
  color: var(
    --td-upload-add-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  display: none;
  font-size: var(--td-upload-add-icon-font-size, 56rpx);
  height: 100%;
  justify-content: center;
  width: 100%;
}
.t-upload__add-icon--disabled {
  background-color: var(
    --td-upload-add-disabled-bg-color,
    var(--td-bg-color-component-disabled, var(--td-gray-color-2, #eee))
  );
  color: var(
    --td-upload-add-icon-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-upload__add-icon:only-child {
  display: -webkit-flex;
  display: flex;
}
.t-upload__thumbnail {
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  width: 100%;
}
.t-upload__wrapper {
  border-radius: var(--td-upload-radius, var(--td-radius-default, 12rpx));
  overflow: hidden;
  position: relative;
}
.t-upload__wrapper--disabled::before {
  background-color: var(--td-upload-disabled-mask, rgba(0, 0.6));
  content: "";
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1;
}
.t-upload__close-btn {
  background-color: var(--td-font-gray-3, rgba(0, 0, 0, 0.4));
  border-bottom-left-radius: var(
    --td-upload-radius,
    var(--td-radius-default, 12rpx)
  );
  border-top-right-radius: var(
    --td-upload-radius,
    var(--td-radius-default, 12rpx)
  );
  height: 40rpx;
  right: 0;
  width: 40rpx;
}
.t-upload__close-btn,
.t-upload__progress-mask {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  position: absolute;
  top: 0;
}
.t-upload__progress-mask {
  background-color: var(--td-font-gray-2, rgba(0, 0, 0, 0.6));
  border-radius: var(--td-upload-radius, var(--td-radius-default, 12rpx));
  box-sizing: border-box;
  color: var(--td-text-color-anti, var(--td-font-white-1, #fff));
  flex-direction: column;
  height: 100%;
  left: 0;
  padding: 32rpx 0;
  width: 100%;
}
.t-upload__progress-text {
  font-size: 24rpx;
  line-height: 40rpx;
  margin-top: 8rpx;
}
.t-upload__progress-loading {
  animation: spin 0.6s linear infinite;
}
.t-upload__drag {
  --td-grid-item-bg-color: transparent;
  position: relative;
  width: 100%;
}
.t-upload__drag-item {
  height: auto;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1;
}
.t-upload__drag--fixed {
  z-index: 0;
}
.t-upload__drag--tran {
  transition-duration: var(--td-upload-drag-transition-duration);
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  transition-timing-function: var(--td-upload-drag-transition-timing-function);
}
.t-upload__drag--cur {
  z-index: var(--td-upload-drag-z-index, 999);
}
@-webkit-keyframes spin {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(1turn);
  }
}
@keyframes spin {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(1turn);
  }
}
