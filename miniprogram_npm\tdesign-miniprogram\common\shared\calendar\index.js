Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../../../@babel/runtime/helpers/toConsumableArray"),
  t = require("../../../../../@babel/runtime/helpers/slicedToArray"),
  a = require("../../../../../@babel/runtime/helpers/classCallCheck"),
  r = require("../../../../../@babel/runtime/helpers/createClass"),
  i = require("../date"),
  n = (function () {
    function n() {
      var e =
        arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
      a(this, n),
        (this.type = "single"),
        Object.assign(this, e),
        this.minDate || (this.minDate = (0, i.getDate)()),
        this.maxDate || (this.maxDate = (0, i.getDate)(6));
    }
    return (
      r(n, [
        {
          key: "getTrimValue",
          value: function () {
            var e = this.value,
              t = this.type,
              a = function (e) {
                return e instanceof Date
                  ? e
                  : "number" == typeof e
                  ? new Date(e)
                  : new Date();
              };
            return "single" === t && (0, i.isValidDate)(e)
              ? a(e)
              : "multiple" === t || "range" === t
              ? Array.isArray(e) &&
                e.every(function (e) {
                  return (0, i.isValidDate)(e);
                })
                ? e.map(function (e) {
                    return a(e);
                  })
                : []
              : void 0;
          },
        },
        {
          key: "getDays",
          value: function (e) {
            for (var t = [], a = this.firstDayOfWeek % 7; t.length < 7; )
              t.push(e[a]), (a = (a + 1) % 7);
            return t;
          },
        },
        {
          key: "getMonths",
          value: function () {
            for (
              var e = [],
                a = this.getTrimValue(),
                r = this.minDate,
                n = this.maxDate,
                s = this.type,
                u = this.format,
                l = (0, i.getDateRect)(r),
                h = l.year,
                m = l.month,
                o = l.time,
                y = (0, i.getDateRect)(n),
                f = y.year,
                D = y.month,
                c = y.time,
                d = function (e, r, n) {
                  var u = new Date(e, r, n, 23, 59, 59);
                  if (
                    "single" === s &&
                    a &&
                    (0, i.isSameDate)({ year: e, month: r, date: n }, a)
                  )
                    return "selected";
                  if (
                    "multiple" === s &&
                    a &&
                    a.some(function (t) {
                      return (0,
                      i.isSameDate)({ year: e, month: r, date: n }, t);
                    })
                  )
                    return "selected";
                  if ("range" === s && a && Array.isArray(a)) {
                    var l = t(a, 2),
                      h = l[0],
                      m = l[1];
                    if (
                      h &&
                      (0, i.isSameDate)({ year: e, month: r, date: n }, h)
                    )
                      return "start";
                    if (
                      m &&
                      (0, i.isSameDate)({ year: e, month: r, date: n }, m)
                    )
                      return "end";
                    if (
                      h &&
                      m &&
                      u.getTime() > h.getTime() &&
                      u.getTime() < m.getTime()
                    )
                      return "centre";
                  }
                  var y = new Date(e, r, n, 0, 0, 0);
                  return u.getTime() < o || y.getTime() > c ? "disabled" : "";
                };
              h < f || (h === f && m <= D);

            ) {
              for (
                var v = (0, i.getMonthDateRect)(new Date(h, m, 1)),
                  g = [],
                  p = 1;
                p <= 31 && !(p > v.lastDate);
                p += 1
              ) {
                var b = { date: new Date(h, m, p), day: p, type: d(h, m, p) };
                g.push(u ? u(b) : b);
              }
              e.push({
                year: h,
                month: m,
                months: g,
                weekdayOfFirstDay: v.weekdayOfFirstDay,
              });
              var T = (0, i.getDateRect)(new Date(h, m + 1, 1));
              (h = T.year), (m = T.month);
            }
            return e;
          },
        },
        {
          key: "select",
          value: function (t) {
            var a = t.cellType,
              r = t.year,
              n = t.month,
              s = t.date,
              u = this.type,
              l = this.getTrimValue();
            if ("disabled" !== a) {
              var h = new Date(r, n, s);
              if (((this.value = h), "range" === u && Array.isArray(l)))
                1 === l.length && h > l[0]
                  ? (this.value = [l[0], h])
                  : (this.value = [h]);
              else if ("multiple" === u && Array.isArray(l)) {
                var m = e(l),
                  o = l.findIndex(function (e) {
                    return (0, i.isSameDate)(e, h);
                  });
                o > -1 ? m.splice(o, 1) : m.push(h), (this.value = m);
              }
              return this.value;
            }
          },
        },
      ]),
      n
    );
  })();
exports.default = n;
