__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/pull-down-refresh/pull-down-refresh":
      (() => {
        var H = {};
        var S;
        var I = (P) => {
          if (!S) S = Object.assign({}, H);
          return S[P];
        };
        var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
        H[""] = (R, C, D, U) => {
          R.setFnFilter(Q.A, Q.B);
          if (typeof R.setEventListenerWrapper === "function")
            R.setEventListenerWrapper(Q.C);
          var L = R.c,
            M = R.m,
            O = R.r,
            A = {
              lowerThreshold: new Array(1),
              showScrollbar: new Array(1),
              loosing: new Array(2),
              upperThreshold: new Array(1),
              tipsHeight: new Array(1),
              scrollIntoView: new Array(1),
              style: new Array(1),
              scrollTop: new Array(1),
              enablePassive: new Array(1),
              distanceTop: new Array(1),
              barHeight: new Array(2),
              customStyle: new Array(1),
              enableBackToTop: new Array(1),
            },
            K = U === true,
            f,
            h = (C) => {},
            i = (C, T) => {
              var $A = D.refreshStatus;
              C || K || !!U.refreshStatus || Z(U.loadingTexts, $A)
                ? T(Y(X(D.loadingTexts)[$A]))
                : T();
            },
            g = (C, T, E) => {
              if (f === 1) {
                E(
                  "t-loading",
                  {},
                  (N, C) => {
                    if (C || K || !!Z(U.loadingProps, "delay") || undefined)
                      O(N, "delay", X(D.loadingProps).delay || 0);
                    if (C || K || !!Z(U.loadingProps, "duration") || undefined)
                      O(N, "duration", X(D.loadingProps).duration || 800);
                    if (C || K || !!Z(U.loadingProps, "indicator") || undefined)
                      O(N, "indicator", X(D.loadingProps).indicator || true);
                    if (C || K || !!Z(U.loadingProps, "layout") || undefined)
                      O(N, "layout", X(D.loadingProps).layout || "horizontal");
                    if (C || K || !!Z(U.loadingProps, "loading") || undefined)
                      O(N, "loading", X(D.loadingProps).loading || true);
                    if (C || K || !!Z(U.loadingProps, "pause") || undefined)
                      O(N, "pause", X(D.loadingProps).pause || false);
                    if (C || K || !!Z(U.loadingProps, "progress") || undefined)
                      O(N, "progress", X(D.loadingProps).progress || 0);
                    if (C || K || !!Z(U.loadingProps, "reverse") || undefined)
                      O(N, "reverse", X(D.loadingProps).reverse || false);
                    if (C || K || !!Z(U.loadingProps, "size") || undefined)
                      O(N, "size", X(D.loadingProps).size || "50rpx");
                    var $A = D.refreshStatus;
                    if (
                      C ||
                      K ||
                      !!(
                        Z(U.loadingProps, "text") ||
                        U.refreshStatus ||
                        Z(U.loadingTexts, $A)
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "text",
                        X(D.loadingProps).text || X(D.loadingTexts)[$A]
                      );
                    if (C || K || !!Z(U.loadingProps, "theme") || undefined)
                      O(N, "theme", X(D.loadingProps).theme || "circular");
                    if (C || K || !!U.prefix || undefined)
                      O(
                        N,
                        "t-class-indicator",
                        Y(D.prefix) + "-class-indicator"
                      );
                  },
                  h
                );
              } else if (f === 2) {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__text " +
                          Y(D.prefix) +
                          "-class-text"
                      );
                  },
                  i
                );
              }
            },
            e = (C, T, E, B) => {
              f = D.refreshStatus === 2 ? 1 : D.refreshStatus >= 0 ? 2 : 0;
              B(f, g);
            },
            d = (C, T, E, B, F, S) => {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.loosing;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.loosing ||
                      ($A ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__tips " +
                        Y(D.classPrefix + "__tips--" + ($A ? "loosing" : ""))
                    );
                  A["loosing"][1] = (D, E, T) => {
                    var $B = D.loosing;
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__tips " +
                        Y(D.classPrefix + "__tips--" + ($B ? "loosing" : ""))
                    );
                  };
                  if (C || K || !!U.tipsHeight || undefined)
                    R.y(N, "height:" + Y(D.tipsHeight) + "px");
                  A["tipsHeight"][0] = (D, E, T) => {
                    R.y(N, "height:" + Y(D.tipsHeight) + "px");
                  };
                  if (C) O(N, "aria-live", "polite");
                },
                e
              );
              S("");
            },
            c = (C, T, E, B, F, S) => {
              S("header");
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.loosing;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.loosing ||
                      ($A ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__track " +
                        Y(D.classPrefix + "__track--" + ($A ? "loosing" : ""))
                    );
                  A["loosing"][0] = (D, E, T) => {
                    var $B = D.loosing;
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__track " +
                        Y(D.classPrefix + "__track--" + ($B ? "loosing" : ""))
                    );
                  };
                  var $B = D.barHeight > 0;
                  if (
                    C ||
                    K ||
                    !!U.barHeight ||
                    ($B ? !!U.barHeight || undefined : undefined)
                  )
                    R.y(
                      N,
                      $B
                        ? "transform: translate3d(0, " + D.barHeight + "px, 0);"
                        : ""
                    );
                  A["barHeight"][0] = A["barHeight"][1] = (D, E, T) => {
                    var $C = D.barHeight > 0;
                    R.y(
                      N,
                      $C
                        ? "transform: translate3d(0, " + D.barHeight + "px, 0);"
                        : ""
                    );
                  };
                },
                d
              );
            },
            b = (C, T, E) => {
              E(
                "scroll-view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        U.style,
                        U.customStyle,
                        !!U.distanceTop || undefined,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        D.style,
                        D.customStyle,
                        "max-height: calc(100vh - " + D.distanceTop + "px)",
                      ])
                    );
                  A["style"][0] =
                    A["customStyle"][0] =
                    A["distanceTop"][0] =
                      (D, E, T) => {
                        R.y(
                          N,
                          P(X(a)._style)([
                            D.style,
                            D.customStyle,
                            "max-height: calc(100vh - " + D.distanceTop + "px)",
                          ])
                        );
                      };
                  if (C) O(N, "type", "list");
                  if (C || K || U.scrollTop) O(N, "scroll-top", D.scrollTop);
                  A["scrollTop"][0] = (D, E, T) => {
                    O(N, "scroll-top", D.scrollTop);
                    E(N);
                  };
                  if (C) O(N, "scroll-y", true);
                  if (C || K || U.enableBackToTop)
                    O(N, "enable-back-to-top", D.enableBackToTop);
                  A["enableBackToTop"][0] = (D, E, T) => {
                    O(N, "enable-back-to-top", D.enableBackToTop);
                    E(N);
                  };
                  if (C || K || U.enablePassive)
                    O(N, "enable-passive", D.enablePassive);
                  A["enablePassive"][0] = (D, E, T) => {
                    O(N, "enable-passive", D.enablePassive);
                    E(N);
                  };
                  if (C || K || U.lowerThreshold)
                    O(N, "lower-threshold", D.lowerThreshold);
                  A["lowerThreshold"][0] = (D, E, T) => {
                    O(N, "lower-threshold", D.lowerThreshold);
                    E(N);
                  };
                  if (C || K || U.upperThreshold)
                    O(N, "upper-threshold", D.upperThreshold);
                  A["upperThreshold"][0] = (D, E, T) => {
                    O(N, "upper-threshold", D.upperThreshold);
                    E(N);
                  };
                  if (C || K || U.scrollIntoView)
                    O(N, "scroll-into-view", D.scrollIntoView);
                  A["scrollIntoView"][0] = (D, E, T) => {
                    O(N, "scroll-into-view", D.scrollIntoView);
                    E(N);
                  };
                  if (C || K || U.showScrollbar)
                    O(N, "show-scrollbar", D.showScrollbar);
                  A["showScrollbar"][0] = (D, E, T) => {
                    O(N, "show-scrollbar", D.showScrollbar);
                    E(N);
                  };
                  if (C) O(N, "enhanced", true);
                  if (C) O(N, "scroll-with-animation", true);
                  if (C || K || undefined) O(N, "bounces", false);
                  if (C) O(N, "binddragstart", "onDragStart");
                  if (C) O(N, "binddragging", "onDragging");
                  if (C) O(N, "binddragend", "onDragEnd");
                  if (C) O(N, "bindscrolltoupper", "onScrollToTop");
                  if (C) O(N, "bindscrolltolower", "onScrollToBottom");
                  if (C || K || undefined) O(N, "throttle", false);
                  if (C) R.v(N, "touchstart", "onTouchStart", !1, !1, !1, !1);
                  if (C) R.v(N, "touchmove", "onTouchMove", !1, !1, !1, !1);
                  if (C) R.v(N, "touchend", "onTouchEnd", !1, !1, !1, !1);
                  if (C) R.v(N, "scroll", "onScroll", !1, !1, !1, !1);
                },
                c
              );
            };
          return { C: b, B: A };
        };
        return Object.assign(
          function (R) {
            return H[R];
          },
          { _: H }
        );
      })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/pull-down-refresh/pull-down-refresh.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-pull-down-refresh{height:100%;overflow:hidden}\n.",
      [1],
      "t-pull-down-refresh__track{position:relative}\n.",
      [1],
      "t-pull-down-refresh__track--loosing{transition:-webkit-transform .24s ease;transition:transform .24s ease;transition:transform .24s ease,-webkit-transform .24s ease}\n.",
      [1],
      "t-pull-down-refresh__tips{-webkit-align-items:center;align-items:center;color:var(--td-pull-down-refresh-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;font-size:",
      [0, 24],
      ";-webkit-justify-content:center;justify-content:center;overflow:hidden;position:absolute;top:0;-webkit-transform:translateY(-100%);transform:translateY(-100%);width:100%}\n.",
      [1],
      "t-pull-down-refresh__tips--loosing{transition:height .24s ease}\n.",
      [1],
      "t-pull-down-refresh__text{margin:",
      [0, 16],
      " 0 0}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/pull-down-refresh/pull-down-refresh.wxss",
    }
  );
}
