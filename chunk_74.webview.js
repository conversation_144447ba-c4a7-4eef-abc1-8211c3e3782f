__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/service-order-list/service-order-list": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["pages/service-order-list/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { serviceInfo: new Array(1) },
          K = U === true,
          d = (C) => {},
          c = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || !!Z(U.serviceInfo, "service_name") || undefined)
                  O(N, "title", X(D.serviceInfo).service_name || "预约管理");
                A["serviceInfo"][0] = (D, E, T) => {
                  O(N, "title", X(D.serviceInfo).service_name || "预约管理");
                  E(N);
                };
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              d
            );
          },
          h = (C) => {},
          i = (C) => {},
          j = (C) => {},
          k = (C) => {},
          g = (C, T, E) => {
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已下单");
                if (C) O(N, "value", "0");
              },
              h
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已接单");
                if (C) O(N, "value", "1");
              },
              i
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已完成");
                if (C) O(N, "value", "2");
              },
              j
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已取消");
                if (C) O(N, "value", "3");
              },
              k
            );
          },
          f = (C, T, E) => {
            E(
              "t-tabs",
              {},
              (N, C) => {
                if (C || K || undefined) O(N, "defaultValue", 0);
                if (C) O(N, "t-class", "custom-tabs");
                if (C) O(N, "t-class-track", "custom-track");
                if (C) R.v(N, "change", "onTabsChange", !1, !1, !1, !1);
                if (C) R.v(N, "click", "onTabsClick", !1, !1, !1, !1);
              },
              g
            );
          },
          l,
          o = (C, T) => {
            C ? T("加载中...") : T();
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loading_text");
              },
              o
            );
          },
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_container");
                },
                n
              );
            }
          },
          p,
          s = (C, t, u, v, w, x, T, E) => {
            var B0 = (C) => {},
              D0 = (C, T) => {
                C || K || Z(Z(v, "customer"), "nickname")
                  ? T(Y(X(X(t).customer).nickname))
                  : T();
              },
              E0 = (C, T) => {
                var $A = X(X(t).customer).neighbor_type === "owner";
                var $B = X(X(t).customer).neighbor_type === "resident";
                C ||
                K ||
                !!Z(Z(v, "customer"), "neighbor_type") ||
                ($A
                  ? undefined
                  : !!Z(Z(v, "customer"), "neighbor_type") ||
                    ($B ? undefined : undefined))
                  ? T(Y($A ? "业主" : $B ? "住户" : "其他"))
                  : T();
              },
              C0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_name");
                  },
                  D0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_type");
                  },
                  E0
                );
              },
              A0 = (C, T, E) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_avatar");
                    if (C || K || !!Z(Z(v, "customer"), "avatar") || undefined)
                      O(
                        N,
                        "src",
                        X(X(t).customer).avatar || "/assets/tx/短发职业女.png"
                      );
                  },
                  B0
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_details");
                  },
                  C0
                );
              },
              G0 = (C, T) => {
                var $A = X(t).order_status;
                C ||
                K ||
                !!Z(v, "order_status") ||
                Z(Z(U.statusMap, $A), "text")
                  ? T(Y(X(X(D.statusMap)[$A]).text))
                  : T();
              },
              F0 = (C, T, E) => {
                E(
                  "t-tag",
                  {},
                  (N, C) => {
                    if (C) O(N, "variant", "light");
                    var $A = X(t).order_status;
                    if (
                      C ||
                      K ||
                      !!Z(v, "order_status") ||
                      Z(Z(U.statusMap, $A), "theme")
                    )
                      O(N, "theme", X(X(D.statusMap)[$A]).theme);
                  },
                  G0
                );
              },
              z = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_info");
                  },
                  A0
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_status");
                  },
                  F0
                );
              },
              J0 = (C, T) => {
                C ? T("下单时间：") : T();
              },
              K0 = (C, T) => {
                C ||
                K ||
                !!(Z(undefined, "formatTime") || Z(v, "order_time")) ||
                undefined
                  ? T(Y(P(X(a).formatTime)(X(t).order_time)))
                  : T();
              },
              I0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  J0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  K0
                );
              },
              L0,
              O0 = (C, T) => {
                C ? T("地址：") : T();
              },
              P0 = (C, T) => {
                C ||
                K ||
                !!(
                  Z(Z(Z(v, "customer"), "address"), "building") ||
                  Z(Z(Z(v, "customer"), "address"), "unit") ||
                  Z(Z(Z(v, "customer"), "address"), "room")
                ) ||
                undefined
                  ? T(
                      Y(
                        Y(X(X(X(t).customer).address).building) +
                          "-" +
                          Y(X(X(X(t).customer).address).unit) +
                          "-" +
                          Y(X(X(X(t).customer).address).room)
                      )
                    )
                  : T();
              },
              N0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  O0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  P0
                );
              },
              M0 = (C, T, E) => {
                if (L0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    N0
                  );
                }
              },
              Q0,
              T0 = (C, T) => {
                C ? T("接单时间：") : T();
              },
              U0 = (C, T) => {
                C || K || !!(U.formatTime || Z(v, "accept_time")) || undefined
                  ? T(Y(P(D.formatTime)(X(t).accept_time)))
                  : T();
              },
              S0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  T0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  U0
                );
              },
              R0 = (C, T, E) => {
                if (Q0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    S0
                  );
                }
              },
              V0,
              Y0 = (C, T) => {
                C ? T("完成时间：") : T();
              },
              Z0 = (C, T) => {
                C || K || !!(U.formatTime || Z(v, "complete_time")) || undefined
                  ? T(Y(P(D.formatTime)(X(t).complete_time)))
                  : T();
              },
              X0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  Y0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  Z0
                );
              },
              W0 = (C, T, E) => {
                if (V0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    X0
                  );
                }
              },
              H0 = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "detail_row");
                  },
                  I0
                );
                L0 = X(X(t).customer).address ? 1 : 0;
                B(L0, M0);
                Q0 = X(t).accept_time ? 1 : 0;
                B(Q0, R0);
                V0 = X(t).complete_time ? 1 : 0;
                B(V0, W0);
              },
              a0,
              e0 = (C, T) => {
                C || K || Z(v, "remark") ? T(Y(X(t).remark)) : T();
              },
              d0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "content_text");
                  },
                  e0
                );
              },
              c0 = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "content_box");
                  },
                  d0
                );
              },
              b0 = (C, T, E) => {
                if (a0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "order_content");
                    },
                    c0
                  );
                }
              },
              f0,
              j0 = (C, T) => {
                C ? T("联系客户") : T();
              },
              i0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, j0);
              },
              k0,
              n0 = (C, T) => {
                C ? T("取消订单") : T();
              },
              m0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, n0);
              },
              l0 = (C, T, E) => {
                if (k0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "quxiao-btn");
                      if (C || K || Z(v, "order_id"))
                        R.d(N, "orderId", X(t).order_id);
                      if (C) R.v(N, "tap", "onCancelOrder", !1, !1, !1, !1);
                    },
                    m0
                  );
                }
              },
              o0,
              r0 = (C, T) => {
                C ? T("接单") : T();
              },
              q0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, r0);
              },
              p0 = (C, T, E) => {
                if (o0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "accept-btn");
                      if (C || K || Z(v, "order_id"))
                        R.d(N, "orderId", X(t).order_id);
                      if (C) R.v(N, "tap", "onAcceptOrder", !1, !1, !1, !1);
                    },
                    q0
                  );
                }
              },
              s0,
              v0 = (C, T) => {
                C ? T("确认完成") : T();
              },
              u0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, v0);
              },
              t0 = (C, T, E) => {
                if (s0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "confirm-btn");
                      if (C || K || Z(v, "order_id"))
                        R.d(N, "orderId", X(t).order_id);
                      if (C) R.v(N, "tap", "onCompleteOrder", !1, !1, !1, !1);
                    },
                    u0
                  );
                }
              },
              h0 = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "contact-btn");
                    if (C || K || Z(v, "order_id"))
                      R.d(N, "orderId", X(t).order_id);
                    if (C || K || Z(Z(v, "customer"), "contact_info"))
                      R.d(N, "phone", X(X(t).customer).contact_info);
                    if (C) R.v(N, "tap", "onContactCustomer", !1, !1, !1, !1);
                  },
                  i0
                );
                k0 = X(t).order_status === "ordered" ? 1 : 0;
                B(k0, l0);
                o0 = X(t).order_status === "ordered" ? 1 : 0;
                B(o0, p0);
                s0 = X(t).order_status === "accepted" ? 1 : 0;
                B(s0, t0);
              },
              g0 = (C, T, E) => {
                if (f0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "fwdd_list_bottom");
                    },
                    h0
                  );
                }
              },
              y = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_header");
                  },
                  z
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_details");
                  },
                  H0
                );
                a0 = X(t).remark ? 1 : 0;
                B(a0, b0);
                f0 =
                  X(t).order_status === "ordered" ||
                  X(t).order_status === "accepted"
                    ? 1
                    : 0;
                B(f0, g0);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "order_card");
              },
              y
            );
          },
          t,
          w = (C, T) => {
            C ? T("暂无订单") : T();
          },
          v = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "empty_text");
              },
              w
            );
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty_container");
                },
                v
              );
            }
          },
          x,
          A0 = (C, T) => {
            C ? T("上拉加载更多") : T();
          },
          z = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loading_text");
              },
              A0
            );
          },
          y = (C, T, E) => {
            if (x === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_more");
                },
                z
              );
            }
          },
          B0,
          E0 = (C, T) => {
            C ? T("没有更多数据了") : T();
          },
          D0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loading_text");
              },
              E0
            );
          },
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_more");
                },
                D0
              );
            }
          },
          r = (C, T, E, B, F) => {
            F(
              D.filteredOrderList,
              "order_id",
              U ? U.filteredOrderList : undefined,
              [0, "filteredOrderList"],
              s
            );
            t = D.isEmpty ? 1 : 0;
            B(t, u);
            var $A = D.currentTab;
            x =
              X(D.filteredOrderList).length > 0 &&
              X(X(D.tabPagination)[$A]).has_more
                ? 1
                : 0;
            B(x, y);
            var $B = D.currentTab;
            B0 =
              X(D.filteredOrderList).length > 0 &&
              !X(X(D.tabPagination)[$B]).has_more
                ? 1
                : 0;
            B(B0, C0);
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                r
              );
            }
          },
          e = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "tags_box");
              },
              f
            );
            l = D.loading ? 1 : 0;
            B(l, m);
            p = !D.loading ? 1 : 0;
            B(p, q);
          },
          b = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, c);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              e
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/service-order-list/service-order-list.wxss"] =
    setCssToHead(
      [
        "body{background-color:#eff2f5}\n.",
        [1],
        "nr_box{margin:",
        [0, 10],
        " auto;width:95%}\n.",
        [1],
        "tags_box{margin:0 auto;width:100%}\n.",
        [1],
        "custom-tabs{background-color:#eff2f5!important;-webkit-justify-content:flex-start!important;justify-content:flex-start!important}\n.",
        [1],
        "custom-tabs .",
        [1],
        "t-tabs__item{color:#1e1e1e!important}\n.",
        [1],
        "custom-tabs .",
        [1],
        "t-tabs__item--active{color:#ff4d4f!important}\n.",
        [1],
        "custom-track{background-color:#ff4d4f!important}\n.",
        [1],
        "order_card{background-color:#fff;border:",
        [0, 2],
        " solid #f0f0f0;border-radius:",
        [0, 20],
        ";box-shadow:0 ",
        [0, 2],
        " ",
        [0, 10],
        " rgba(0,0,0,.05);margin-bottom:",
        [0, 20],
        ";padding:",
        [0, 30],
        "}\n.",
        [1],
        "order_header{border-bottom:",
        [0, 2],
        " solid #f5f5f5;-webkit-justify-content:space-between;justify-content:space-between;margin-bottom:",
        [0, 30],
        ";padding-bottom:",
        [0, 20],
        "}\n.",
        [1],
        "order_header,.",
        [1],
        "user_info{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
        [1],
        "user_avatar{border-radius:50%;height:",
        [0, 80],
        ";margin-right:",
        [0, 20],
        ";width:",
        [0, 80],
        "}\n.",
        [1],
        "user_name{color:#333;font-size:",
        [0, 32],
        ";font-weight:500}\n.",
        [1],
        "order_status{padding:",
        [0, 10],
        " ",
        [0, 20],
        "}\n.",
        [1],
        "status_text{color:#666;font-size:",
        [0, 24],
        "}\n.",
        [1],
        "order_details{margin-bottom:",
        [0, 30],
        "}\n.",
        [1],
        "detail_row{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;margin-bottom:",
        [0, 15],
        "}\n.",
        [1],
        "detail_row:last-child{margin-bottom:0}\n.",
        [1],
        "label{color:#666;-webkit-flex-shrink:0;flex-shrink:0;font-size:",
        [0, 28],
        ";width:",
        [0, 160],
        "}\n.",
        [1],
        "value{color:#333}\n.",
        [1],
        "price,.",
        [1],
        "value{-webkit-flex:1;flex:1;font-size:",
        [0, 28],
        "}\n.",
        [1],
        "price{color:#ff4d4f;font-weight:500}\n.",
        [1],
        "order_content{margin-bottom:",
        [0, 30],
        "}\n.",
        [1],
        "content_box{background-color:#f8f8f8;border:",
        [0, 2],
        " solid #f0f0f0;border-radius:",
        [0, 15],
        ";padding:",
        [0, 25],
        "}\n.",
        [1],
        "content_text{color:#333;font-size:",
        [0, 26],
        ";line-height:1.5}\n.",
        [1],
        "order_actions{display:-webkit-flex;display:flex;gap:",
        [0, 20],
        ";-webkit-justify-content:space-between;justify-content:space-between}\n.",
        [1],
        "action_btn{border:",
        [0, 5],
        " solid;border-radius:",
        [0, 20],
        ";font-size:",
        [0, 28],
        ";padding:",
        [0, 15],
        " 0;text-align:center;transition:all .3s ease}\n.",
        [1],
        "contact_btn{background-color:#fff;border:none;color:#195abf;-webkit-flex:1;flex:1}\n.",
        [1],
        "contact_btn:active{background-color:#f0f7ff}\n.",
        [1],
        "confirm_btn{background-color:#fff;border-color:#e03131;color:#e03131;-webkit-flex:0.5;flex:0.5}\n.",
        [1],
        "confirm_btn:active{background-color:#fff5f5}\n.",
        [1],
        "action_btn wx-text{font-weight:500}\n.",
        [1],
        "fwdd_list_bottom{-webkit-align-items:center;align-items:center;border-top:",
        [0, 2],
        " solid #f5f5f5;display:-webkit-flex;display:flex;gap:",
        [0, 10],
        ";-webkit-justify-content:space-between;justify-content:space-between;padding-top:",
        [0, 30],
        "}\n.",
        [1],
        "quxiao-btn{border:",
        [0, 5],
        " solid #ff4d4f;border-radius:",
        [0, 20],
        ";color:#ff4d4f;font-size:",
        [0, 24],
        ";margin-left:",
        [0, 10],
        ";padding:",
        [0, 8],
        " ",
        [0, 32],
        "}\n.",
        [1],
        "contact-btn{color:#2b5ee3;font-size:",
        [0, 28],
        ";margin-left:0}\n.",
        [1],
        "accept-btn{margin-left:",
        [0, 10],
        "}\n.",
        [1],
        "accept-btn,.",
        [1],
        "confirm-btn{background-color:#ff4d4f;border:",
        [0, 5],
        " solid #ff4d4f;border-radius:",
        [0, 20],
        ";color:#f0f7ff;font-size:",
        [0, 24],
        ";padding:",
        [0, 8],
        " ",
        [0, 32],
        "}\n.",
        [1],
        "loading_more{padding:",
        [0, 20],
        " 0;text-align:center}\n.",
        [1],
        "loading_more .",
        [1],
        "loading_text{color:#999;font-size:",
        [0, 24],
        "}\n.",
        [1],
        "empty_container{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center;padding:",
        [0, 100],
        " 0}\n.",
        [1],
        "empty_text{color:#999;font-size:",
        [0, 28],
        "}\n.",
        [1],
        "user_type{background-color:#f2f3ff;border-radius:",
        [0, 10],
        ";color:#1971c2;display:inline-block;font-size:",
        [0, 24],
        ";margin-left:",
        [0, 20],
        ";padding:",
        [0, 8],
        " ",
        [0, 16],
        "}\n",
      ],
      "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/service-order-list/service-order-list.wxss:1:2107)",
      { path: "./pages/service-order-list/service-order-list.wxss" }
    );
}
