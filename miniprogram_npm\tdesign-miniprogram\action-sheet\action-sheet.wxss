@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-action-sheet__content {
  background-color: var(--td-bg-color-container, var(--td-font-white-1, #fff));
  border-top-left-radius: var(
    --td-action-sheet-border-radius,
    var(--td-radius-extra-large, 24rpx)
  );
  border-top-right-radius: var(
    --td-action-sheet-border-radius,
    var(--td-radius-extra-large, 24rpx)
  );
  color: var(
    --td-action-sheet-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  overflow: hidden;
}
.t-action-sheet__content--grid {
  padding-top: 16rpx;
}
.t-action-sheet__content:focus {
  outline: 0;
}
.t-action-sheet__grid {
  padding-bottom: 16rpx;
}
.t-action-sheet__grid--swiper {
  padding-bottom: 48rpx;
}
.t-action-sheet__description {
  color: var(
    --td-action-sheet-description-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  font-size: 28rpx;
  line-height: 44rpx;
  padding: 24rpx 32rpx;
  position: relative;
  text-align: var(--td-action-sheet-text-align, center);
}
.t-action-sheet__description:focus {
  outline: 0;
}
.t-action-sheet__description::after {
  background-color: var(
    --td-action-sheet-border-color,
    var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: unset;
  transform: scaleY(0.5);
}
.t-action-sheet__description--left {
  text-align: left;
}
.t-action-sheet__description--left::after {
  left: 32rpx;
}
.t-action-sheet__list-item {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  height: var(--td-action-sheet-list-item-height, 112rpx);
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}
.t-action-sheet__list-item::after {
  background-color: var(
    --td-action-sheet-border-color,
    var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: unset;
  transform: scaleY(0.5);
}
.t-action-sheet__list-item:focus {
  outline: 0;
}
.t-action-sheet__list-item--left {
  justify-content: start;
}
.t-action-sheet__list-item--left::after {
  left: 32rpx;
}
.t-action-sheet__list-item--disabled {
  color: var(
    --td-action-sheet-list-item-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-action-sheet__list-item-text {
  word-wrap: normal;
  font-size: var(--td-font-size-m, 32rpx);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.t-action-sheet__list-item-icon {
  margin-right: 16rpx;
}
.t-action-sheet__list-item-icon--suffix {
  margin-left: auto;
}
.t-action-sheet__swiper-wrap {
  margin-top: 8rpx;
  position: relative;
}
.t-action-sheet__footer {
  background-color: var(--td-bg-color-container, var(--td-font-white-1, #fff));
}
.t-action-sheet__gap-list {
  background-color: var(
    --td-action-sheet-gap-color,
    var(--td-bg-color-page, var(--td-gray-color-1, #f3f3f3))
  );
  height: 16rpx;
}
.t-action-sheet__gap-grid {
  background-color: var(
    --td-action-sheet-border-color,
    var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7))
  );
  height: 1rpx;
}
.t-action-sheet__cancel {
  align-items: center;
  color: var(
    --td-action-sheet-cancel-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-flex;
  display: flex;
  height: var(--td-action-sheet-cancel-height, 96rpx);
  justify-content: center;
}
.t-action-sheet__dots {
  bottom: 32rpx;
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
}
.t-action-sheet__dots-item {
  background-color: #dcdcdc;
  border-radius: 50%;
  height: 16rpx;
  margin: 0 16rpx;
  transition: all 0.4s ease-in;
  width: 16rpx;
}
.t-action-sheet__dots-item.t-is-active {
  background-color: #0052d9;
}
