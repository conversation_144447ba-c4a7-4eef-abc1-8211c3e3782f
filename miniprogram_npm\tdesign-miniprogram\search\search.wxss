@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-search {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
}
.t-search__label {
  color: var(
    --td-search-label-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  padding: 8rpx;
}
.t-search__input-box {
  align-items: center;
  background: var(
    --td-search-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  border: 2rpx solid
    var(
      --td-search-bg-color,
      var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
    );
  box-sizing: border-box;
  display: -webkit-flex;
  display: flex;
  flex: 1;
  height: var(--td-search-height, 80rpx);
  padding: var(--td-search-padding, 16rpx 24rpx);
}
.t-search__input-box.t-is-focused {
  border-color: var(
    --td-search-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
}
.t-search__input-box--round {
  border-radius: calc(var(--td-search-height, 80rpx) / 2);
}
.t-search__input-box--square {
  border-radius: var(
    --td-search-square-radius,
    var(--td-radius-default, 12rpx)
  );
}
.t-search__input-box--center {
  text-align: center;
}
.t-search__input-box .t-input__keyword {
  color: var(
    --td-search-text-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: inline-block;
  flex: 1;
  font-size: var(--td-search-font-size, var(--td-font-size-m, 32rpx));
  line-height: 48rpx;
  min-height: 48rpx;
  padding-left: 10rpx;
}
.t-search__input-box .t-input--disabled {
  -webkit-text-fill-color: currentColor;
  color: var(
    --td-search-disabled-text-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
  cursor: not-allowed;
  opacity: 1;
}
.t-search__input-box .t-icon {
  color: var(
    --td-search-icon-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  font-size: var(--td-search-icon-font-size, 48rpx);
}
.t-search__clear {
  color: var(
    --td-search-clear-icon-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  font-size: var(--td-search-clear-icon-font-size, 48rpx);
  margin-left: 10px;
  position: relative;
}
.t-search__search-action {
  color: var(
    --td-search-action-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  font-size: var(--td-search-font-size, var(--td-font-size-m, 32rpx));
  margin-left: 30rpx;
}
.t-search__placeholder {
  color: var(
    --td-search-placeholder-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
}
.t-search__placeholder--center {
  text-align: center;
}
.t-search__result-item--highLight {
  color: var(
    --td-search-result-high-light-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-search__result-list .t-search__result-item {
  padding-left: 0;
}
.t-search__result-list .t-search__result-item::after {
  left: 0;
}
