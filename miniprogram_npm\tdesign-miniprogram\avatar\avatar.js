Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  r = require("../../../@babel/runtime/helpers/inherits"),
  a = require("../../../@babel/runtime/helpers/createSuper"),
  s = require("tslib"),
  i = require("../common/src/index"),
  o = l(require("../common/config")),
  n = l(require("./props")),
  c = require("../common/utils");
function l(e) {
  return e && e.__esModule ? e : { default: e };
}
var u = o.default.prefix,
  p = "".concat(u, "-avatar"),
  d = (function (s) {
    r(o, s);
    var i = a(o);
    function o() {
      var e;
      return (
        t(this, o),
        ((e = i.apply(this, arguments)).options = { multipleSlots: !0 }),
        (e.externalClasses = [
          "".concat(u, "-class"),
          "".concat(u, "-class-image"),
          "".concat(u, "-class-icon"),
          "".concat(u, "-class-alt"),
          "".concat(u, "-class-content"),
        ]),
        (e.properties = n.default),
        (e.data = {
          prefix: u,
          classPrefix: p,
          isShow: !0,
          zIndex: 0,
          systemInfo: c.systemInfo,
        }),
        (e.relations = {
          "../avatar-group/avatar-group": {
            type: "ancestor",
            linked: function (e) {
              (this.parent = e),
                this.setData({
                  shape: this.data.shape || e.data.shape || "circle",
                  size: this.data.size || e.data.size,
                  bordered: !0,
                });
            },
          },
        }),
        (e.observers = {
          icon: function (e) {
            var t = (0, c.setIcon)("icon", e, "");
            this.setData(Object.assign({}, t));
          },
        }),
        (e.methods = {
          hide: function () {
            this.setData({ isShow: !1 });
          },
          onLoadError: function (e) {
            this.properties.hideOnLoadFailed && this.setData({ isShow: !1 }),
              this.triggerEvent("error", e.detail);
          },
        }),
        e
      );
    }
    return e(o);
  })(i.SuperComponent),
  h = (d = (0, s.__decorate)([(0, i.wxComponent)()], d));
exports.default = h;
