__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/badge/badge": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/badge/badge"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            labelID: new Array(2),
            customStyle: new Array(1),
            ariaRole: new Array(1),
          },
          K = U === true,
          f,
          h = (C, T) => {
            C || K || U.content ? T(Y(D.content)) : T();
          },
          g = (C, T, E, B, F, S) => {
            if (f === 1) {
              S("", (N) => {
                if (C || K || !!U.classPrefix || undefined)
                  R.l(N, "class", Y(D.classPrefix) + "__content-slot");
              });
            } else {
              E(
                "text",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__content-text");
                },
                h
              );
            }
          },
          e = (C, T, E, B) => {
            f = !D.content ? 1 : 0;
            B(f, g);
          },
          i,
          k = (C, T) => {
            C ||
            K ||
            !!(
              Z(undefined, "getBadgeValue") ||
              Q.b({ dot: U.dot, count: U.count, maxCount: U.maxCount })
            ) ||
            undefined
              ? T(
                  Y(
                    P(X(a).getBadgeValue)({
                      dot: D.dot,
                      count: D.count,
                      maxCount: D.maxCount,
                    })
                  )
                )
              : T();
          },
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "getBadgeInnerClass") ||
                      Q.b({
                        dot: U.dot,
                        size: U.size,
                        shape: U.shape,
                        count: U.count,
                      }) ||
                      U.prefix ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).getBadgeInnerClass)({
                          dot: D.dot,
                          size: D.size,
                          shape: D.shape,
                          count: D.count,
                        })
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-has-count " +
                        Y(D.prefix) +
                        "-class-count"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!(
                          Z(undefined, "getBadgeStyles") ||
                          Q.b({ color: U.color, offset: U.offset })
                        ) || undefined,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(b)._style)([
                        P(X(a).getBadgeStyles)({
                          color: D.color,
                          offset: D.offset,
                        }),
                      ])
                    );
                  if (C) O(N, "aria-hidden", "true");
                  if (
                    C ||
                    K ||
                    !!(
                      U.ariaLabel ||
                      Z(undefined, "getBadgeAriaLabel") ||
                      Q.b({ dot: U.dot, count: U.count, maxCount: U.maxCount })
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "aria-label",
                      D.ariaLabel ||
                        P(X(b).getBadgeAriaLabel)({
                          dot: D.dot,
                          count: D.count,
                          maxCount: D.maxCount,
                        })
                    );
                  if (C || K || U.descriptionID) R.i(N, D.descriptionID);
                },
                k
              );
            }
          },
          d = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C) O(N, "aria-hidden", "true");
                if (C || K || U.labelID) R.i(N, D.labelID);
                A["labelID"][1] = (D, E, T) => {
                  R.i(N, D.labelID);
                };
              },
              e
            );
            i = P(X(a).isShowBadge)({
              dot: D.dot,
              count: D.count,
              showZero: D.showZero,
            })
              ? 1
              : 0;
            B(i, j);
            S("count");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getBadgeOuterClass") ||
                    Q.b({ shape: U.shape }) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).getBadgeOuterClass)({ shape: D.shape })) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
                if (C || K || U.labelID) O(N, "aria-labelledby", D.labelID);
                A["labelID"][0] = (D, E, T) => {
                  O(N, "aria-labelledby", D.labelID);
                  E(N);
                };
                if (C || K || U.descriptionID)
                  O(N, "aria-describedby", D.descriptionID);
                if (C || K || !!U.ariaRole || undefined)
                  O(N, "aria-role", D.ariaRole || "option");
                A["ariaRole"][0] = (D, E, T) => {
                  O(N, "aria-role", D.ariaRole || "option");
                  E(N);
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/badge/badge";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/badge/badge.js";
define(
  "miniprogram_npm/tdesign-miniprogram/badge/badge.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      s = require("../common/src/index"),
      n = c(require("../common/config")),
      l = c(require("./props")),
      o = require("../common/utils");
    function c(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = n.default.prefix,
      p = "".concat(u, "-badge"),
      d = (0, o.uniqueFactory)("badge"),
      b = (function (i) {
        t(n, i);
        var s = a(n);
        function n() {
          var e;
          return (
            r(this, n),
            ((e = s.apply(this, arguments)).options = { multipleSlots: !0 }),
            (e.externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-count"),
              "".concat(u, "-class-content"),
            ]),
            (e.properties = l.default),
            (e.data = {
              prefix: u,
              classPrefix: p,
              value: "",
              labelID: "",
              descriptionID: "",
            }),
            (e.lifetimes = {
              ready: function () {
                var e = d();
                this.setData({
                  labelID: "".concat(e, "_label"),
                  descriptionID: "".concat(e, "_description"),
                });
              },
            }),
            e
          );
        }
        return e(n);
      })(s.SuperComponent),
      f = (b = (0, i.__decorate)([(0, s.wxComponent)()], b));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/badge/badge.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/badge/badge.js");
