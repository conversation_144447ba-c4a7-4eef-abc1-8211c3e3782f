__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/avatar-group/avatar-group": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            className: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          d = (C, T, E, B, F, S) => {
            S("collapse-avatar");
          },
          e,
          h = (C, T) => {
            C || K || U.collapseAvatar ? T(Y(D.collapseAvatar)) : T();
          },
          g = (C, T, E) => {
            E(
              "t-avatar",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.prefix || U.prefix || U.size || U.prefix) ||
                  undefined
                )
                  O(
                    N,
                    "t-class-image",
                    Y(D.prefix) +
                      "-avatar--border " +
                      Y(D.prefix) +
                      "-avatar--border-" +
                      Y(D.size) +
                      " " +
                      Y(D.prefix) +
                      "-class-image"
                  );
                if (C || K || !!U.prefix || undefined)
                  O(N, "t-class-content", Y(D.prefix) + "-class-content");
                if (C || K || U.size) O(N, "size", D.size);
                if (C || K || U.shape) O(N, "shape", D.shape);
                var $A = D.collapseAvatar;
                if (
                  C ||
                  K ||
                  !!U.collapseAvatar ||
                  ($A ? undefined : undefined)
                )
                  O(N, "icon", $A ? "" : "user-add");
                if (C) O(N, "aria-role", "none");
              },
              h
            );
          },
          f = (C, T, E) => {
            if (e === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__collapse--default");
                  if (C) O(N, "bindtap", "onCollapsedItemClick");
                },
                g
              );
            }
          },
          c = (C, T, E, B, F, S) => {
            S("");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__collapse--slot");
              },
              d
            );
            e = D.max && D.max < D.length ? 1 : 0;
            B(e, f);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.className || undefined)
                  L(N, Y(D.className) + " class");
                A["className"][0] = (D, E, T) => {
                  L(N, Y(D.className) + " class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/avatar/avatar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/avatar/avatar"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            ariaRole: new Array(1),
            size: new Array(2),
            systemInfo: new Array(1),
            bordered: new Array(1),
            ariaLabel: new Array(1),
            isShow: new Array(1),
            customStyle: new Array(1),
            badgeProps: new Array(12),
            ariaHidden: new Array(1),
            shape: new Array(1),
          },
          K = U === true,
          g,
          i = (C) => {},
          j,
          k = (C, T, E, B, F, S, J) => {
            var $A = I(j);
            if (j && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix + "__icon " + D.prefix + "-class-icon",
                    name: D.iconName,
                  },
                  X(D.iconData),
                  {}
                ),
                K ||
                  (U
                    ? U.iconData === true ||
                      Object.assign(
                        {
                          tClass: !!(U.classPrefix || U.prefix) || undefined,
                          name: U.iconName,
                        },
                        X(U.iconData),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          l = (C, T, E, B, F, S) => {
            S("");
          },
          h = (C, T, E, B, F, S, J) => {
            if (g === 1) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "style")) ||
                    undefined
                  )
                    R.y(N, (D.imageProps && X(D.imageProps).style) || "");
                  if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                    O(
                      N,
                      "t-class",
                      Y(D.prefix) + "-image " + Y(D.classPrefix) + "__image"
                    );
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class-load", Y(D.prefix) + "-class-alt");
                  if (C || K || U.image) O(N, "src", D.image);
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "mode")) ||
                    undefined
                  )
                    O(
                      N,
                      "mode",
                      (D.imageProps && X(D.imageProps).mode) || "aspectFill"
                    );
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "lazy")) ||
                    undefined
                  )
                    O(
                      N,
                      "lazy",
                      (D.imageProps && X(D.imageProps).lazy) || false
                    );
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "loading")) ||
                    undefined
                  )
                    O(
                      N,
                      "loading",
                      (D.imageProps && X(D.imageProps).loading) || "default"
                    );
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "shape")) ||
                    undefined
                  )
                    O(
                      N,
                      "shape",
                      (D.imageProps && X(D.imageProps).shape) || "round"
                    );
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "webp")) ||
                    undefined
                  )
                    O(
                      N,
                      "webp",
                      (D.imageProps && X(D.imageProps).webp) || false
                    );
                  if (C || K || !!U.alt || undefined)
                    O(N, "error", D.alt || "default");
                  if (C) R.v(N, "error", "onLoadError", !1, !1, !1, !1);
                },
                i
              );
            } else if (g === 2) {
              j = "icon";
              B(j, k);
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__text " +
                        Y(D.prefix) +
                        "-class-content"
                    );
                },
                l
              );
            }
          },
          f = (C, T, E, B) => {
            g = D.image
              ? 1
              : D.iconName || P(X(a).isNoEmptyObj)(D.iconData)
              ? 2
              : 0;
            B(g, h);
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getClass") ||
                    U.classPrefix ||
                    U.size ||
                    U.shape ||
                    U.bordered ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).getClass)(
                        D.classPrefix,
                        D.size || "medium",
                        D.shape,
                        D.bordered
                      )
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-image"
                  );
                A["size"][0] =
                  A["shape"][0] =
                  A["bordered"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(
                          P(X(b).getClass)(
                            D.classPrefix,
                            D.size || "medium",
                            D.shape,
                            D.bordered
                          )
                        ) +
                          " " +
                          Y(D.prefix) +
                          "-class-image"
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getSize") || U.size || U.systemInfo) ||
                  undefined
                )
                  R.y(N, P(X(b).getSize)(D.size, D.systemInfo));
                A["size"][1] = A["systemInfo"][0] = (D, E, T) => {
                  R.y(N, P(X(b).getSize)(D.size, D.systemInfo));
                };
                if (C || K || !!(U.ariaLabel || U.alt) || undefined)
                  O(N, "aria-label", D.ariaLabel || D.alt || "头像");
                A["ariaLabel"][0] = (D, E, T) => {
                  O(N, "aria-label", D.ariaLabel || D.alt || "头像");
                  E(N);
                };
                if (C || K || !!U.ariaRole || undefined)
                  O(N, "aria-role", D.ariaRole || "img");
                A["ariaRole"][0] = (D, E, T) => {
                  O(N, "aria-role", D.ariaRole || "img");
                  E(N);
                };
                if (C || K || U.ariaHidden) O(N, "aria-hidden", D.ariaHidden);
                A["ariaHidden"][0] = (D, E, T) => {
                  O(N, "aria-hidden", D.ariaHidden);
                  E(N);
                };
              },
              f
            );
          },
          d = (C, T, E) => {
            E(
              "t-badge",
              {},
              (N, C) => {
                if (C || K || !!Z(U.badgeProps, "color") || undefined)
                  O(N, "color", X(D.badgeProps).color || "");
                A["badgeProps"][0] = (D, E, T) => {
                  O(N, "color", X(D.badgeProps).color || "");
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "content") || undefined)
                  O(N, "content", X(D.badgeProps).content || "");
                A["badgeProps"][1] = (D, E, T) => {
                  O(N, "content", X(D.badgeProps).content || "");
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "count") || undefined)
                  O(N, "count", X(D.badgeProps).count || 0);
                A["badgeProps"][2] = (D, E, T) => {
                  O(N, "count", X(D.badgeProps).count || 0);
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "dot") || undefined)
                  O(N, "dot", X(D.badgeProps).dot || false);
                A["badgeProps"][3] = (D, E, T) => {
                  O(N, "dot", X(D.badgeProps).dot || false);
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "maxCount") || undefined)
                  O(N, "max-count", X(D.badgeProps).maxCount || 99);
                A["badgeProps"][4] = (D, E, T) => {
                  O(N, "max-count", X(D.badgeProps).maxCount || 99);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(Z(U.badgeProps, "offset") || Q.a([])) ||
                  undefined
                )
                  O(N, "offset", X(D.badgeProps).offset || []);
                A["badgeProps"][5] = (D, E, T) => {
                  O(N, "offset", X(D.badgeProps).offset || []);
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "shape") || undefined)
                  O(N, "shape", X(D.badgeProps).shape || "circle");
                A["badgeProps"][6] = (D, E, T) => {
                  O(N, "shape", X(D.badgeProps).shape || "circle");
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "showZero") || undefined)
                  O(N, "show-zero", X(D.badgeProps).showZero || false);
                A["badgeProps"][7] = (D, E, T) => {
                  O(N, "show-zero", X(D.badgeProps).showZero || false);
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "size") || undefined)
                  O(N, "size", X(D.badgeProps).size || "medium");
                A["badgeProps"][8] = (D, E, T) => {
                  O(N, "size", X(D.badgeProps).size || "medium");
                  E(N);
                };
                if (C || K || Z(U.badgeProps, "tClass"))
                  O(N, "t-class", X(D.badgeProps).tClass);
                A["badgeProps"][9] = (D, E, T) => {
                  O(N, "t-class", X(D.badgeProps).tClass);
                  E(N);
                };
                if (C || K || Z(U.badgeProps, "tClassContent"))
                  O(N, "t-class-content", X(D.badgeProps).tClassContent);
                A["badgeProps"][10] = (D, E, T) => {
                  O(N, "t-class-content", X(D.badgeProps).tClassContent);
                  E(N);
                };
                if (C || K || Z(U.badgeProps, "tClassCount"))
                  O(N, "t-class-count", X(D.badgeProps).tClassCount);
                A["badgeProps"][11] = (D, E, T) => {
                  O(N, "t-class-count", X(D.badgeProps).tClassCount);
                  E(N);
                };
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__wrapper class " +
                      Y(D.prefix) +
                      "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([
                      !!(Z(undefined, "getStyles") || U.isShow) || undefined,
                      U.style,
                      U.customStyle,
                    ])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([
                      P(X(b).getStyles)(D.isShow),
                      D.style,
                      D.customStyle,
                    ])
                  );
                A["isShow"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([
                          P(X(b).getStyles)(D.isShow),
                          D.style,
                          D.customStyle,
                        ])
                      );
                    };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/avatar-group/avatar-group.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-avatar-group{-webkit-align-items:center;align-items:center;display:-webkit-inline-flex;display:inline-flex;-webkit-flex-wrap:wrap;flex-wrap:wrap}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper,.",
      [1],
      "t-avatar-group-offset-right .",
      [1],
      "t-avatar__wrapper{padding:var(--td-avatar-group-line-spacing,",
      [0, 4],
      ") 0}\n.",
      [1],
      "t-avatar-group-offset-left-small,.",
      [1],
      "t-avatar-group-offset-right-small{--td-avatar-margin-left:var(--td-avatar-group-margin-left-small,",
      [0, -16],
      ")}\n.",
      [1],
      "t-avatar-group-offset-left-medium,.",
      [1],
      "t-avatar-group-offset-right-medium{--td-avatar-margin-left:var(--td-avatar-group-margin-left-medium,",
      [0, -16],
      ")}\n.",
      [1],
      "t-avatar-group-offset-left-large,.",
      [1],
      "t-avatar-group-offset-right-large{--td-avatar-margin-left:var(--td-avatar-group-margin-left-large,",
      [0, -16],
      ")}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(1){z-index:calc(var(--td-avatar-group-init-z-index,50) - 1)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(2){z-index:calc(var(--td-avatar-group-init-z-index,50) - 2)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(3){z-index:calc(var(--td-avatar-group-init-z-index,50) - 3)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(4){z-index:calc(var(--td-avatar-group-init-z-index,50) - 4)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(5){z-index:calc(var(--td-avatar-group-init-z-index,50) - 5)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(6){z-index:calc(var(--td-avatar-group-init-z-index,50) - 6)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(7){z-index:calc(var(--td-avatar-group-init-z-index,50) - 7)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(8){z-index:calc(var(--td-avatar-group-init-z-index,50) - 8)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(9){z-index:calc(var(--td-avatar-group-init-z-index,50) - 9)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(10){z-index:calc(var(--td-avatar-group-init-z-index,50) - 10)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(11){z-index:calc(var(--td-avatar-group-init-z-index,50) - 11)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(12){z-index:calc(var(--td-avatar-group-init-z-index,50) - 12)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(13){z-index:calc(var(--td-avatar-group-init-z-index,50) - 13)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(14){z-index:calc(var(--td-avatar-group-init-z-index,50) - 14)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(15){z-index:calc(var(--td-avatar-group-init-z-index,50) - 15)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(16){z-index:calc(var(--td-avatar-group-init-z-index,50) - 16)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(17){z-index:calc(var(--td-avatar-group-init-z-index,50) - 17)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(18){z-index:calc(var(--td-avatar-group-init-z-index,50) - 18)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(19){z-index:calc(var(--td-avatar-group-init-z-index,50) - 19)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(20){z-index:calc(var(--td-avatar-group-init-z-index,50) - 20)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(21){z-index:calc(var(--td-avatar-group-init-z-index,50) - 21)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(22){z-index:calc(var(--td-avatar-group-init-z-index,50) - 22)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(23){z-index:calc(var(--td-avatar-group-init-z-index,50) - 23)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(24){z-index:calc(var(--td-avatar-group-init-z-index,50) - 24)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(25){z-index:calc(var(--td-avatar-group-init-z-index,50) - 25)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(26){z-index:calc(var(--td-avatar-group-init-z-index,50) - 26)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(27){z-index:calc(var(--td-avatar-group-init-z-index,50) - 27)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(28){z-index:calc(var(--td-avatar-group-init-z-index,50) - 28)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(29){z-index:calc(var(--td-avatar-group-init-z-index,50) - 29)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(30){z-index:calc(var(--td-avatar-group-init-z-index,50) - 30)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(31){z-index:calc(var(--td-avatar-group-init-z-index,50) - 31)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(32){z-index:calc(var(--td-avatar-group-init-z-index,50) - 32)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(33){z-index:calc(var(--td-avatar-group-init-z-index,50) - 33)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(34){z-index:calc(var(--td-avatar-group-init-z-index,50) - 34)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(35){z-index:calc(var(--td-avatar-group-init-z-index,50) - 35)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(36){z-index:calc(var(--td-avatar-group-init-z-index,50) - 36)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(37){z-index:calc(var(--td-avatar-group-init-z-index,50) - 37)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(38){z-index:calc(var(--td-avatar-group-init-z-index,50) - 38)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(39){z-index:calc(var(--td-avatar-group-init-z-index,50) - 39)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(40){z-index:calc(var(--td-avatar-group-init-z-index,50) - 40)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(41){z-index:calc(var(--td-avatar-group-init-z-index,50) - 41)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(42){z-index:calc(var(--td-avatar-group-init-z-index,50) - 42)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(43){z-index:calc(var(--td-avatar-group-init-z-index,50) - 43)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(44){z-index:calc(var(--td-avatar-group-init-z-index,50) - 44)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(45){z-index:calc(var(--td-avatar-group-init-z-index,50) - 45)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(46){z-index:calc(var(--td-avatar-group-init-z-index,50) - 46)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(47){z-index:calc(var(--td-avatar-group-init-z-index,50) - 47)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(48){z-index:calc(var(--td-avatar-group-init-z-index,50) - 48)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(49){z-index:calc(var(--td-avatar-group-init-z-index,50) - 49)}\n.",
      [1],
      "t-avatar-group-offset-left .",
      [1],
      "t-avatar__wrapper:nth-child(50){z-index:calc(var(--td-avatar-group-init-z-index,50) - 50)}\n.",
      [1],
      "t-avatar-group__collapse--default,.",
      [1],
      "t-avatar-group__collapse--slot{font-weight:600;z-index:0}\n.",
      [1],
      "t-avatar-group__collapse--slot{float:left}\n.",
      [1],
      "t-avatar-group__collapse--slot:not(:empty)+.",
      [1],
      "t-avatar-group__collapse--default{display:none;float:left}\n.",
      [1],
      "t-avatar-group__collapse--slot:empty+.",
      [1],
      "t-avatar-group__collapse--default{display:block;float:left}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/avatar-group/avatar-group.wxss",
    }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/avatar/avatar.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-avatar{-webkit-align-items:center;align-items:center;background-color:var(--td-avatar-bg-color,var(--td-brand-color-light-active,var(--td-primary-color-2,#d9e1ff)));box-sizing:border-box;color:var(--td-avatar-content-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
        [1],
        "t-avatar__wrapper{display:-webkit-inline-flex;display:inline-flex;margin-left:var(--td-avatar-margin-left,0);position:relative;vertical-align:top}\n.",
        [1],
        "t-avatar--large{font-size:var(--td-avatar-text-large-font-size,var(--td-font-size-xl,",
        [0, 40],
        "));height:var(--td-avatar-large-width,",
        [0, 128],
        ");width:var(--td-avatar-large-width,",
        [0, 128],
        ")}\n.",
        [1],
        "t-avatar--large .",
        [1],
        "t-avatar__icon{font-size:var(--td-avatar-icon-large-font-size,",
        [0, 64],
        ")}\n.",
        [1],
        "t-avatar--medium{font-size:var(--td-avatar-text-medium-font-size,var(--td-font-size-m,",
        [0, 32],
        "));height:var(--td-avatar-medium-width,",
        [0, 96],
        ");width:var(--td-avatar-medium-width,",
        [0, 96],
        ")}\n.",
        [1],
        "t-avatar--medium .",
        [1],
        "t-avatar__icon{font-size:var(--td-avatar-icon-medium-font-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-avatar--small{font-size:var(--td-avatar-text-small-font-size,var(--td-font-size-base,",
        [0, 28],
        "));height:var(--td-avatar-small-width,",
        [0, 80],
        ");width:var(--td-avatar-small-width,",
        [0, 80],
        ")}\n.",
        [1],
        "t-avatar--small .",
        [1],
        "t-avatar__icon{font-size:var(--td-avatar-icon-small-font-size,",
        [0, 40],
        ")}\n.",
        [1],
        "t-avatar .",
        [1],
        "t-image,.",
        [1],
        "t-avatar__image{height:100%;width:100%}\n.",
        [1],
        "t-avatar--circle{border-radius:var(--td-avatar-circle-border-radius,var(--td-radius-circle,50%));overflow:hidden}\n.",
        [1],
        "t-avatar--round{border-radius:var(--td-avatar-round-border-radius,var(--td-radius-default,",
        [0, 12],
        "));overflow:hidden}\n.",
        [1],
        "t-avatar__icon,.",
        [1],
        "t-avatar__text{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;height:100%;-webkit-justify-content:center;justify-content:center;width:100%}\n.",
        [1],
        "t-avatar__icon:empty,.",
        [1],
        "t-avatar__text:empty{height:0;width:0}\n.",
        [1],
        "t-avatar--border{border-color:var(--td-avatar-border-color,#fff);border-style:solid}\n.",
        [1],
        "t-avatar--border-small{border-width:var(--td-avatar-border-width-small,",
        [0, 2],
        ")}\n.",
        [1],
        "t-avatar--border-medium{border-width:var(--td-avatar-border-width-medium,",
        [0, 4],
        ")}\n.",
        [1],
        "t-avatar--border-large{border-width:var(--td-avatar-border-width-large,",
        [0, 6],
        ")}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/avatar/avatar.wxss" }
    );
}
