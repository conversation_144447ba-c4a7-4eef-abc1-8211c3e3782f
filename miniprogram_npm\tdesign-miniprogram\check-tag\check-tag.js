Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  a = require("../../../@babel/runtime/helpers/inherits"),
  c = require("../../../@babel/runtime/helpers/createSuper"),
  s = require("tslib"),
  r = require("../common/src/index"),
  i = l(require("../common/config")),
  n = l(require("./props")),
  o = require("../common/utils");
function l(e) {
  return e && e.__esModule ? e : { default: e };
}
var u = i.default.prefix,
  d = "".concat(u, "-tag"),
  h = (function (s) {
    a(i, s);
    var r = c(i);
    function i() {
      var e;
      return (
        t(this, i),
        ((e = r.apply(this, arguments)).data = {
          prefix: u,
          classPrefix: d,
          className: "",
        }),
        (e.properties = n.default),
        (e.externalClasses = ["".concat(u, "-class")]),
        (e.controlledProps = [{ key: "checked", event: "change" }]),
        (e.options = { multipleSlots: !0 }),
        (e.lifetimes = {
          attached: function () {
            this.setClass();
          },
        }),
        (e.observers = {
          "size, disabled, checked": function () {
            this.setClass();
          },
          icon: function (e) {
            this.setData({ _icon: (0, o.calcIcon)(e) });
          },
        }),
        (e.methods = {
          setClass: function () {
            var e = this.data.classPrefix,
              t = this.properties,
              a = t.size,
              c = t.variant,
              s = t.disabled,
              r = t.checked,
              i = t.shape,
              n = (0, o.classNames)([
                e,
                "".concat(e, "--checkable"),
                s ? "".concat(e, "--disabled") : "",
                r ? "".concat(e, "--checked") : "",
                "".concat(e, "--").concat(r ? "primary" : "default"),
                "".concat(e, "--").concat(a),
                "".concat(e, "--").concat(c),
                "".concat(e, "--").concat(i),
              ]);
            this.setData({ className: n });
          },
          onClick: function () {
            if (!this.data.disabled) {
              var e = this.data.checked;
              this._trigger("click"), this._trigger("change", { checked: !e });
            }
          },
          onClose: function (e) {
            this.data.disabled || this._trigger("close", e);
          },
        }),
        e
      );
    }
    return e(i);
  })(r.SuperComponent),
  p = (h = (0, s.__decorate)([(0, r.wxComponent)()], h));
exports.default = p;
