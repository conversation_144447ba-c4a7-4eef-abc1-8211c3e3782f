__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/add-help/add-help": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { noteLength: new Array(1), titleLength: new Array(1) },
          K = U === true,
          b = (C) => {},
          f = (C, T) => {
            C ? T("标题") : T();
          },
          h = (C) => {},
          i = (C, T) => {
            C || K || !!U.titleLength || undefined
              ? T(Y(Y(D.titleLength) + "/15"), (N) => {
                  A["titleLength"][0] = (D, E, T) => {
                    T(N, Y(Y(D.titleLength) + "/15"));
                  };
                })
              : T();
          },
          g = (C, T, E) => {
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请输入事情名称");
                if (C || K || Z(U.formData, "title"))
                  O(N, "value", X(D.formData).title);
                if (C) O(N, "bindinput", "onTitleInput");
                if (C) O(N, "maxlength", "15");
              },
              h
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "char-count-inner");
              },
              i
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "input-wrapper");
              },
              g
            );
          },
          k = (C, T) => {
            C ? T("缩略图") : T();
          },
          n,
          p = (C) => {},
          r = (C, T) => {
            C ? T("点击选择缩略图") : T();
          },
          q = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder-text");
              },
              r
            );
          },
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "image",
                {},
                (N, C) => {
                  if (C) L(N, "selected-thumbnail");
                  if (C || K || Z(U.formData, "thumbnail"))
                    O(N, "src", X(D.formData).thumbnail);
                  if (C) O(N, "mode", "aspectFill");
                },
                p
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "thumbnail-placeholder");
                },
                q
              );
            }
          },
          m = (C, T, E, B) => {
            n = X(D.formData).thumbnail ? 1 : 0;
            B(n, o);
          },
          l = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-preview");
                if (C) O(N, "bindtap", "showThumbnailSelector");
              },
              m
            );
          },
          j = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-section");
              },
              l
            );
          },
          t = (C, T) => {
            C ? T("报酬") : T();
          },
          u = (C) => {},
          s = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              t
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请写简单的内容");
                if (C || K || Z(U.formData, "reward"))
                  O(N, "value", X(D.formData).reward);
                if (C) O(N, "bindinput", "onRewardInput");
              },
              u
            );
          },
          w = (C, T) => {
            C ? T("截止时间") : T();
          },
          y = (C, T) => {
            C || K || !!Z(U.formData, "deadline") || undefined
              ? T(Y(X(D.formData).deadline || "2025-05-22 17:00"))
              : T();
          },
          x = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-input picker-input");
              },
              y
            );
          },
          v = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              w
            );
            E(
              "picker",
              {},
              (N, C) => {
                if (C) O(N, "mode", "date");
                if (C || K || Z(U.formData, "deadline"))
                  O(N, "value", X(D.formData).deadline);
                if (C) O(N, "bindchange", "onDateChange");
              },
              x
            );
          },
          A0 = (C, T) => {
            C ? T("说明") : T();
          },
          C0 = (C) => {},
          D0 = (C, T) => {
            C || K || !!U.noteLength || undefined
              ? T(Y(Y(D.noteLength) + "/200"), (N) => {
                  A["noteLength"][0] = (D, E, T) => {
                    T(N, Y(Y(D.noteLength) + "/200"));
                  };
                })
              : T();
          },
          B0 = (C, T, E) => {
            E(
              "textarea",
              {},
              (N, C) => {
                if (C) L(N, "form-textarea");
                if (C)
                  O(
                    N,
                    "placeholder",
                    "请输入具体的详细说明，需要什么商品或详细内容"
                  );
                if (C || K || Z(U.formData, "note"))
                  O(N, "value", X(D.formData).note);
                if (C) O(N, "bindinput", "onNoteInput");
                if (C) O(N, "maxlength", "200");
              },
              C0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "char-count-inner textarea-count");
              },
              D0
            );
          },
          z = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              A0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "textarea-wrapper");
              },
              B0
            );
          },
          F0 = (C, T) => {
            C ? T("接单人限制") : T();
          },
          J0 = (C) => {},
          K0 = (C, T) => {
            C ? T("不限制") : T();
          },
          I0 = (C, T, E) => {
            E(
              "radio",
              {},
              (N, C) => {
                if (C) O(N, "value", "unlimited");
                if (C || K || !!Z(U.formData, "contactType") || undefined)
                  O(N, "checked", X(D.formData).contactType === "unlimited");
              },
              J0
            );
            E("text", {}, (N, C) => {}, K0);
          },
          M0 = (C) => {},
          N0 = (C, T) => {
            C ? T("未认证用户") : T();
          },
          L0 = (C, T, E) => {
            E(
              "radio",
              {},
              (N, C) => {
                if (C) O(N, "value", "verified");
                if (C || K || !!Z(U.formData, "contactType") || undefined)
                  O(N, "checked", X(D.formData).contactType === "verified");
              },
              M0
            );
            E("text", {}, (N, C) => {}, N0);
          },
          P0 = (C) => {},
          Q0 = (C, T) => {
            C ? T("认证业主") : T();
          },
          O0 = (C, T, E) => {
            E(
              "radio",
              {},
              (N, C) => {
                if (C) O(N, "value", "owner");
                if (C || K || !!Z(U.formData, "contactType") || undefined)
                  O(N, "checked", X(D.formData).contactType === "owner");
              },
              P0
            );
            E("text", {}, (N, C) => {}, Q0);
          },
          S0 = (C) => {},
          T0 = (C, T) => {
            C ? T("认证租客") : T();
          },
          R0 = (C, T, E) => {
            E(
              "radio",
              {},
              (N, C) => {
                if (C) O(N, "value", "tenant");
                if (C || K || !!Z(U.formData, "contactType") || undefined)
                  O(N, "checked", X(D.formData).contactType === "tenant");
              },
              S0
            );
            E("text", {}, (N, C) => {}, T0);
          },
          H0 = (C, T, E) => {
            E(
              "label",
              {},
              (N, C) => {
                if (C) L(N, "radio-item");
              },
              I0
            );
            E(
              "label",
              {},
              (N, C) => {
                if (C) L(N, "radio-item");
              },
              L0
            );
            E(
              "label",
              {},
              (N, C) => {
                if (C) L(N, "radio-item");
              },
              O0
            );
            E(
              "label",
              {},
              (N, C) => {
                if (C) L(N, "radio-item");
              },
              R0
            );
          },
          G0 = (C, T, E) => {
            E(
              "radio-group",
              {},
              (N, C) => {
                if (C) O(N, "bindchange", "onContactChange");
              },
              H0
            );
          },
          E0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              F0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "contact-options");
              },
              G0
            );
          },
          V0 = (C, T) => {
            C ? T("取消") : T();
          },
          W0 = (C, T) => {
            C ? T("保存") : T();
          },
          X0 = (C, T) => {
            C ? T("发布") : T();
          },
          U0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "onCancel");
              },
              V0
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-save");
                if (C) O(N, "bindtap", "onSave");
              },
              W0
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-publish");
                if (C) O(N, "bindtap", "onPublish");
              },
              X0
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              z
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              E0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "button-group");
              },
              U0
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-container");
              },
              d
            );
          },
          Y0,
          b0 = (C) => {},
          e0 = (C, T) => {
            C ? T("选择缩略图") : T();
          },
          d0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "modal-title");
              },
              e0
            );
          },
          g0 = (C, h0, i0, j0, k0, l0, T, E) => {
            var n0 = (C) => {},
              o0 = (C, T) => {
                C || K || Z(j0, "name") ? T(Y(X(h0).name)) : T();
              },
              p0,
              r0 = (C, T) => {
                C ? T("✓") : T();
              },
              q0 = (C, T, E) => {
                if (p0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "selected-mark");
                    },
                    r0
                  );
                }
              },
              m0 = (C, T, E, B) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "thumbnail-img");
                    if (C || K || Z(j0, "src")) O(N, "src", X(h0).src);
                    if (C) O(N, "mode", "aspectFill");
                  },
                  n0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "thumbnail-name");
                  },
                  o0
                );
                p0 = X(D.formData).thumbnail === X(h0).src ? 1 : 0;
                B(p0, q0);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-item");
                if (C) O(N, "bindtap", "selectThumbnail");
                if (C || K || Z(j0, "src")) R.d(N, "src", X(h0).src);
              },
              m0
            );
          },
          f0 = (C, T, E, B, F) => {
            F(
              D.thumbnailList,
              "index",
              U ? U.thumbnailList : undefined,
              [0, "thumbnailList"],
              g0
            );
          },
          i0 = (C, T) => {
            C ? T("取消") : T();
          },
          h0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "hideThumbnailSelector");
              },
              i0
            );
          },
          c0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-header");
              },
              d0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-grid");
              },
              f0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-footer");
              },
              h0
            );
          },
          a0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-mask");
                if (C) O(N, "bindtap", "hideThumbnailSelector");
              },
              b0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-content");
              },
              c0
            );
          },
          Z0 = (C, T, E) => {
            if (Y0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "thumbnail-modal");
                },
                a0
              );
            }
          },
          a = (C, T, E, B) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "发布-有偿求助");
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              b
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              c
            );
            Y0 = D.showThumbnailModal ? 1 : 0;
            B(Y0, Z0);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/add-help/add-help.wxss"] = setCssToHead(
    [
      ".",
      [1],
      "container{background-color:#fff;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;min-height:100vh;padding:",
      [0, 120],
      " ",
      [0, 30],
      " ",
      [0, 30],
      "}\n.",
      [1],
      "form-container{background-color:#fff;border-radius:",
      [0, 20],
      ";margin:",
      [0, 20],
      " auto;padding:",
      [0, 40],
      " ",
      [0, 30],
      ";width:95%}\n.",
      [1],
      "form-title{color:#333;font-size:",
      [0, 32],
      ";font-weight:700;margin-bottom:",
      [0, 40],
      ";text-align:center}\n.",
      [1],
      "form-item{margin-bottom:",
      [0, 30],
      ";position:relative}\n.",
      [1],
      "form-label{-webkit-align-items:center;align-items:center;color:#333;display:-webkit-flex;display:flex;font-size:",
      [0, 28],
      ";margin-bottom:",
      [0, 15],
      "}\n.",
      [1],
      "form-label.",
      [1],
      "required::before{color:#ff4d4f;content:\x22*\x22;font-size:",
      [0, 28],
      ";margin-right:",
      [0, 8],
      "}\n.",
      [1],
      "input-wrapper{position:relative}\n.",
      [1],
      "form-input{background-color:#fff;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 10],
      ";box-sizing:border-box;color:#333;font-size:",
      [0, 26],
      ";height:",
      [0, 80],
      ";padding:0 ",
      [0, 20],
      ";width:100%}\n.",
      [1],
      "form-input:focus{border-color:#195abf}\n.",
      [1],
      "form-input::-webkit-input-placeholder{color:#999}\n.",
      [1],
      "form-input::placeholder{color:#999}\n.",
      [1],
      "picker-input{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;height:",
      [0, 80],
      ";line-height:",
      [0, 80],
      "}\n.",
      [1],
      "textarea-wrapper{position:relative}\n.",
      [1],
      "form-textarea{background-color:#fff;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 10],
      ";box-sizing:border-box;color:#333;font-size:",
      [0, 26],
      ";min-height:",
      [0, 160],
      ";padding:",
      [0, 20],
      ";resize:none;width:100%}\n.",
      [1],
      "form-textarea:focus{border-color:#195abf}\n.",
      [1],
      "form-textarea::-webkit-input-placeholder{color:#999}\n.",
      [1],
      "form-textarea::placeholder{color:#999}\n.",
      [1],
      "char-count-inner{background-color:#fff;color:#999;font-size:",
      [0, 22],
      ";padding:0 ",
      [0, 5],
      ";position:absolute;right:",
      [0, 20],
      ";top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.",
      [1],
      "textarea-count{bottom:",
      [0, 20],
      ";top:auto;-webkit-transform:none;transform:none}\n.",
      [1],
      "contact-options{margin-top:",
      [0, 10],
      "}\n.",
      [1],
      "radio-item{-webkit-align-items:center;align-items:center;color:#333;display:-webkit-flex;display:flex;font-size:",
      [0, 26],
      ";margin-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "radio-item wx-radio{margin-right:",
      [0, 15],
      ";-webkit-transform:scale(.8);transform:scale(.8)}\n.",
      [1],
      "button-group{gap:",
      [0, 20],
      ";-webkit-justify-content:space-between;justify-content:space-between;margin-top:",
      [0, 60],
      "}\n.",
      [1],
      "btn,.",
      [1],
      "button-group{display:-webkit-flex;display:flex}\n.",
      [1],
      "btn{-webkit-align-items:center;align-items:center;border:none;border-radius:",
      [0, 20],
      ";-webkit-flex:1;flex:1;font-size:",
      [0, 28],
      ";height:",
      [0, 80],
      ";-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "btn-cancel{background-color:#fff;border:",
      [0, 3],
      " solid #1e1e1e;color:#666}\n.",
      [1],
      "btn-save{background-color:#a5d8ff;border:",
      [0, 3],
      " solid #1971c2;color:#1971c2}\n.",
      [1],
      "btn-publish{background-color:#ffec99;border:",
      [0, 3],
      " solid #f08c00;color:#f08c00}\n.",
      [1],
      "btn:active{opacity:.8}\n.",
      [1],
      "thumbnail-section{margin-top:",
      [0, 15],
      "}\n.",
      [1],
      "thumbnail-preview{-webkit-align-items:center;align-items:center;background-color:#f8f9fa;border:",
      [0, 2],
      " dashed #e0e0e0;border-radius:",
      [0, 10],
      ";display:-webkit-flex;display:flex;height:",
      [0, 200],
      ";-webkit-justify-content:center;justify-content:center;overflow:hidden;position:relative;width:",
      [0, 200],
      "}\n.",
      [1],
      "selected-thumbnail{border-radius:",
      [0, 8],
      ";height:100%;width:100%}\n.",
      [1],
      "thumbnail-placeholder{-webkit-align-items:center;align-items:center;color:#999;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "placeholder-text{color:#999;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "thumbnail-modal{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;position:fixed;z-index:9999}\n.",
      [1],
      "modal-mask,.",
      [1],
      "thumbnail-modal{bottom:0;left:0;right:0;top:0}\n.",
      [1],
      "modal-mask{background-color:rgba(0,0,0,.5);position:absolute}\n.",
      [1],
      "modal-content{background-color:#fff;border-radius:",
      [0, 20],
      ";max-width:",
      [0, 600],
      ";padding:",
      [0, 40],
      ";position:relative;width:90%}\n.",
      [1],
      "modal-header{margin-bottom:",
      [0, 30],
      ";text-align:center}\n.",
      [1],
      "modal-title{color:#333;font-size:",
      [0, 32],
      ";font-weight:500}\n.",
      [1],
      "thumbnail-grid{-webkit-flex-wrap:wrap;flex-wrap:wrap;gap:",
      [0, 20],
      ";-webkit-justify-content:space-around;justify-content:space-around;margin-bottom:",
      [0, 30],
      "}\n.",
      [1],
      "thumbnail-grid,.",
      [1],
      "thumbnail-item{display:-webkit-flex;display:flex}\n.",
      [1],
      "thumbnail-item{-webkit-align-items:center;align-items:center;border-radius:",
      [0, 10],
      ";-webkit-flex-direction:column;flex-direction:column;padding:",
      [0, 10],
      ";position:relative;transition:all .3s ease;width:",
      [0, 150],
      "}\n.",
      [1],
      "thumbnail-item:active{background-color:#f0f0f0;-webkit-transform:scale(.95);transform:scale(.95)}\n.",
      [1],
      "thumbnail-img{border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 8],
      ";height:",
      [0, 120],
      ";width:",
      [0, 120],
      "}\n.",
      [1],
      "thumbnail-name{color:#666;font-size:",
      [0, 24],
      ";margin-top:",
      [0, 10],
      ";text-align:center}\n.",
      [1],
      "selected-mark{-webkit-align-items:center;align-items:center;background-color:#195abf;border-radius:50%;color:#fff;font-size:",
      [0, 24],
      ";font-weight:700;height:",
      [0, 40],
      ";position:absolute;right:",
      [0, 5],
      ";top:",
      [0, 5],
      ";width:",
      [0, 40],
      "}\n.",
      [1],
      "modal-footer,.",
      [1],
      "selected-mark{display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "modal-footer{margin:0 auto;width:50%}\n",
    ],
    "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/add-help/add-help.wxss:1:1892)",
    { path: "./pages/add-help/add-help.wxss" }
  );
}
