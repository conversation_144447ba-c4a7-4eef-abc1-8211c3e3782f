function _isNativeReflectConstruct() {
  if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
  if (Reflect.construct.sham) return !1;
  if ("function" == typeof Proxy) return !0;
  try {
    return (
      Boolean.prototype.valueOf.call(
        Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {})
      ),
      !0
    );
  } catch (t) {
    return !1;
  }
}
module.exports = _isNativeReflectConstruct;
