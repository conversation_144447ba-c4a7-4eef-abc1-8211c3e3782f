Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/defineProperty"),
  r = require("../../../@babel/runtime/helpers/createClass"),
  i = require("../../../@babel/runtime/helpers/classCallCheck"),
  t = require("../../../@babel/runtime/helpers/inherits"),
  n = require("../../../@babel/runtime/helpers/createSuper"),
  l = require("tslib"),
  a = require("../common/src/index"),
  s = o(require("../common/config")),
  u = o(require("./props"));
function o(e) {
  return e && e.__esModule ? e : { default: e };
}
var c = s.default.prefix,
  d = "".concat(c, "-side-bar"),
  p = (function (l) {
    t(s, l);
    var a = n(s);
    function s() {
      var r;
      return (
        i(this, s),
        ((r = a.apply(this, arguments)).externalClasses = [
          "".concat(c, "-class"),
        ]),
        (r.children = []),
        (r.relations = e({}, "../side-bar-item/side-bar-item", {
          type: "child",
          linked: function (e) {
            this.children.push(e);
          },
          unlinked: function (e) {
            var r = this.children.findIndex(function (r) {
              return r === e;
            });
            this.children.splice(r, 1);
          },
        })),
        (r.controlledProps = [{ key: "value", event: "change" }]),
        (r.properties = u.default),
        (r.observers = {
          value: function (e) {
            this.$children.forEach(function (r) {
              r.updateActive(e);
            });
          },
        }),
        (r.data = { classPrefix: d, prefix: c }),
        (r.methods = {
          doChange: function (e) {
            var r = e.value,
              i = e.label;
            this._trigger("change", { value: r, label: i });
          },
        }),
        r
      );
    }
    return r(s);
  })(a.SuperComponent),
  h = (p = (0, l.__decorate)([(0, a.wxComponent)()], p));
exports.default = h;
