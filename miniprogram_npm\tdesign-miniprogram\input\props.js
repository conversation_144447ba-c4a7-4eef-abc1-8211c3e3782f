Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = {
  adjustPosition: { type: Boolean, value: !0 },
  align: { type: String, value: "left" },
  allowInputOverMax: { type: <PERSON>olean, value: !1 },
  alwaysEmbed: { type: Boolean, value: !1 },
  autoFocus: { type: Boolean, value: !1 },
  borderless: { type: Boolean, value: !1 },
  clearTrigger: { type: String, value: "always" },
  clearable: { type: null, value: !1 },
  confirmHold: { type: Boolean, value: !1 },
  confirmType: { type: String, value: "done" },
  cursor: { type: Number, required: !0 },
  cursorColor: { type: String, value: "#0052d9" },
  cursorSpacing: { type: Number, value: 0 },
  disabled: { type: null, value: void 0 },
  focus: { type: Boolean, value: !1 },
  format: { type: null },
  holdKeyboard: { type: Boolean, value: !1 },
  label: { type: String },
  layout: { type: String, value: "horizontal" },
  maxcharacter: { type: Number },
  maxlength: { type: Number, value: -1 },
  placeholder: { type: String, value: void 0 },
  placeholderClass: { type: String, value: "input-placeholder" },
  placeholderStyle: { type: String, value: "", required: !0 },
  prefixIcon: { type: null },
  readonly: { type: null, value: void 0 },
  safePasswordCertPath: { type: String, value: "" },
  safePasswordCustomHash: { type: String, value: "" },
  safePasswordLength: { type: Number },
  safePasswordNonce: { type: String, value: "" },
  safePasswordSalt: { type: String, value: "" },
  safePasswordTimeStamp: { type: Number },
  selectionEnd: { type: Number, value: -1 },
  selectionStart: { type: Number, value: -1 },
  status: { type: String, value: "default" },
  suffix: { type: String },
  suffixIcon: { type: null },
  tips: { type: String },
  type: { type: String, value: "text" },
  value: { type: null },
};
exports.default = e;
