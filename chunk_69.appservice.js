__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/help-info/help-info": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b = (C) => {},
          e = (C, T) => {
            C ? T("服务说明") : T();
          },
          g = (C, T) => {
            C ? T("这是是服务的文字说明") : T();
          },
          h = (C, T) => {
            C ? T("这是是服务的文字说明") : T();
          },
          f = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "description_text");
              },
              g
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "description_text");
              },
              h
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "section_title");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "description_box");
              },
              f
            );
          },
          k = (C, T) => {
            C ? T("图片") : T();
          },
          j = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder_text");
              },
              k
            );
          },
          m = (C, T) => {
            C ? T("图片") : T();
          },
          l = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder_text");
              },
              m
            );
          },
          i = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_placeholder");
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_placeholder");
              },
              l
            );
          },
          p = (C, T) => {
            C ? T("联系服务者") : T();
          },
          o = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "contact_text");
              },
              p
            );
          },
          s = (C, T) => {
            C ? T("申请接单") : T();
          },
          r = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "order_button_text");
              },
              s
            );
          },
          q = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "custom_order_button");
                if (C) O(N, "bindtap", "onOrderClick");
              },
              r
            );
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "contact_section");
              },
              o
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "order_button_section");
              },
              q
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service_description_section");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_section");
              },
              i
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "bottom_actions");
              },
              n
            );
          },
          a = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "代买早餐");
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              b
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service_info_container");
              },
              c
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/help-info/help-info";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/help-info/help-info.js";
define(
  "pages/help-info/help-info.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Page({
      data: {
        serviceData: {
          title: "代买早餐",
          description: ["这是是服务的文字说明", "这是是服务的文字说明"],
          images: [],
        },
      },
      onLoad: function (o) {
        console.log("Service Info Page Loaded", o);
      },
      onReady: function () {
        console.log("Service Info Page Ready");
      },
      onShow: function () {
        console.log("Service Info Page Show");
      },
      onHide: function () {
        console.log("Service Info Page Hide");
      },
      onUnload: function () {
        console.log("Service Info Page Unload");
      },
      onPullDownRefresh: function () {
        setTimeout(function () {
          wx.stopPullDownRefresh();
        }, 1e3);
      },
      onReachBottom: function () {
        console.log("Reach Bottom");
      },
      onShareAppMessage: function () {
        return {
          title: "代买早餐服务",
          path: "/pages/service-info/service-info",
        };
      },
      onOrderClick: function () {
        console.log("Order button clicked"),
          wx.showToast({ title: "立即下单", icon: "success" });
      },
    });
  },
  {
    isPage: true,
    isComponent: true,
    currentFile: "pages/help-info/help-info.js",
  }
);
require("pages/help-info/help-info.js");
