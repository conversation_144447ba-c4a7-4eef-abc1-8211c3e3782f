@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-tag {
  align-items: center;
  border: 2rpx solid transparent;
  border-radius: var(--td-tag-square-border-radius, 8rpx);
  box-sizing: border-box;
  display: -webkit-inline-flex;
  display: inline-flex;
  font-size: var(--td-tag-medium-font-size, var(--td-font-size-s, 24rpx));
  user-select: none;
  vertical-align: middle;
}
.t-tag__text {
  word-wrap: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.t-tag__icon {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.t-tag__icon:not(:empty) + .t-tag__text:not(:empty) {
  margin-left: 8rpx;
}
.t-tag--small {
  font-size: var(--td-tag-small-font-size, var(--td-font-size, 20rpx));
  height: var(--td-tag-small-height, 40rpx);
  line-height: var(--td-tag-small-height, 40rpx);
  padding: 0 var(--td-tag-small-padding, 11rpx);
}
.t-tag--small .t-icon,
.t-tag--small .t-icon-close {
  font-size: var(--td-tag-small-icon-size, 24rpx);
}
.t-tag--medium {
  font-size: var(--td-tag-medium-font-size, var(--td-font-size-s, 24rpx));
  height: var(--td-tag-medium-height, 48rpx);
  line-height: var(--td-tag-medium-height, 48rpx);
  padding: 0 var(--td-tag-medium-padding, 15rpx);
}
.t-tag--medium .t-icon,
.t-tag--medium .t-icon-close {
  font-size: var(--td-tag-medium-icon-size, 28rpx);
}
.t-tag--large {
  font-size: var(--td-tag-large-font-size, var(--td-font-size-base, 28rpx));
  height: var(--td-tag-large-height, 56rpx);
  line-height: var(--td-tag-large-height, 56rpx);
  padding: 0 var(--td-tag-large-padding, 15rpx);
}
.t-tag--large .t-icon,
.t-tag--large .t-icon-close {
  font-size: var(--td-tag-large-icon-size, 32rpx);
}
.t-tag--extra-large {
  font-size: var(
    --td-tag-extra-large-font-size,
    var(--td-font-size-base, 28rpx)
  );
  height: var(--td-tag-extra-large-height, 80rpx);
  line-height: var(--td-tag-extra-large-height, 80rpx);
  padding: 0 var(--td-tag-extra-large-padding, 31rpx);
}
.t-tag--extra-large .t-icon,
.t-tag--extra-large .t-icon-close {
  font-size: var(--td-tag-extra-large-icon-size, 32rpx);
}
.t-tag.t-tag--square {
  border-radius: var(--td-tag-square-border-radius, 8rpx);
}
.t-tag.t-tag--round {
  border-radius: var(--td-tag-round-border-radius, 999px);
}
.t-tag.t-tag--mark {
  border-radius: 0
    var(--td-tag-mark-border-radius, var(--td-tag-round-border-radius, 999px))
    var(--td-tag-mark-border-radius, var(--td-tag-round-border-radius, 999px)) 0;
}
.t-tag--dark.t-tag--default {
  background-color: var(
    --td-tag-default-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
  border-color: var(
    --td-tag-default-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
}
.t-tag--dark.t-tag--default,
.t-tag--dark.t-tag--primary {
  color: var(--td-text-color-anti, var(--td-font-white-1, #fff));
}
.t-tag--dark.t-tag--primary {
  background-color: var(
    --td-tag-primary-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-color: var(
    --td-tag-primary-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-tag--dark.t-tag--success {
  background-color: var(
    --td-tag-success-color,
    var(--td-success-color, var(--td-success-color-5, #2ba471))
  );
  border-color: var(
    --td-tag-success-color,
    var(--td-success-color, var(--td-success-color-5, #2ba471))
  );
}
.t-tag--dark.t-tag--success,
.t-tag--dark.t-tag--warning {
  color: var(--td-text-color-anti, var(--td-font-white-1, #fff));
}
.t-tag--dark.t-tag--warning {
  background-color: var(
    --td-tag-warning-color,
    var(--td-warning-color, var(--td-warning-color-5, #e37318))
  );
  border-color: var(
    --td-tag-warning-color,
    var(--td-warning-color, var(--td-warning-color-5, #e37318))
  );
}
.t-tag--dark.t-tag--danger {
  background-color: var(
    --td-tag-danger-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
  border-color: var(
    --td-tag-danger-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
  color: var(--td-text-color-anti, var(--td-font-white-1, #fff));
}
.t-tag--dark.t-tag--default {
  color: var(
    --td-tag-default-font-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
}
.t-tag--outline.t-tag--default {
  background-color: var(
    --td-tag-default-light-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  border-color: var(
    --td-tag-default-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
  color: var(
    --td-tag-default-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
}
.t-tag--outline.t-tag--primary {
  background-color: var(
    --td-tag-primary-light-color,
    var(--td-brand-color-light, var(--td-primary-color-1, #f2f3ff))
  );
  border-color: var(
    --td-tag-primary-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  color: var(
    --td-tag-primary-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-tag--outline.t-tag--success {
  background-color: var(
    --td-tag-success-light-color,
    var(--td-success-color-1, #e3f9e9)
  );
  border-color: var(
    --td-tag-success-color,
    var(--td-success-color, var(--td-success-color-5, #2ba471))
  );
  color: var(
    --td-tag-success-color,
    var(--td-success-color, var(--td-success-color-5, #2ba471))
  );
}
.t-tag--outline.t-tag--warning {
  background-color: var(
    --td-tag-warning-light-color,
    var(--td-warning-color-1, #fff1e9)
  );
  border-color: var(
    --td-tag-warning-color,
    var(--td-warning-color, var(--td-warning-color-5, #e37318))
  );
  color: var(
    --td-tag-warning-color,
    var(--td-warning-color, var(--td-warning-color-5, #e37318))
  );
}
.t-tag--outline.t-tag--danger {
  background-color: var(
    --td-tag-danger-light-color,
    var(--td-error-color-1, #fff0ed)
  );
  border-color: var(
    --td-tag-danger-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
  color: var(
    --td-tag-danger-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
}
.t-tag--outline.t-tag--default {
  color: var(
    --td-tag-default-font-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
}
.t-tag--outline.t-tag--danger,
.t-tag--outline.t-tag--default,
.t-tag--outline.t-tag--primary,
.t-tag--outline.t-tag--success,
.t-tag--outline.t-tag--warning {
  background-color: var(
    --td-tag-outline-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
}
.t-tag--light.t-tag--default {
  background-color: var(
    --td-tag-default-light-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  border-color: var(
    --td-tag-default-light-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  color: var(
    --td-tag-default-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
}
.t-tag--light.t-tag--primary {
  background-color: var(
    --td-tag-primary-light-color,
    var(--td-brand-color-light, var(--td-primary-color-1, #f2f3ff))
  );
  border-color: var(
    --td-tag-primary-light-color,
    var(--td-brand-color-light, var(--td-primary-color-1, #f2f3ff))
  );
  color: var(
    --td-tag-primary-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-tag--light.t-tag--success {
  background-color: var(
    --td-tag-success-light-color,
    var(--td-success-color-1, #e3f9e9)
  );
  border-color: var(
    --td-tag-success-light-color,
    var(--td-success-color-1, #e3f9e9)
  );
  color: var(
    --td-tag-success-color,
    var(--td-success-color, var(--td-success-color-5, #2ba471))
  );
}
.t-tag--light.t-tag--warning {
  background-color: var(
    --td-tag-warning-light-color,
    var(--td-warning-color-1, #fff1e9)
  );
  border-color: var(
    --td-tag-warning-light-color,
    var(--td-warning-color-1, #fff1e9)
  );
  color: var(
    --td-tag-warning-color,
    var(--td-warning-color, var(--td-warning-color-5, #e37318))
  );
}
.t-tag--light.t-tag--danger {
  background-color: var(
    --td-tag-danger-light-color,
    var(--td-error-color-1, #fff0ed)
  );
  border-color: var(
    --td-tag-danger-light-color,
    var(--td-error-color-1, #fff0ed)
  );
  color: var(
    --td-tag-danger-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
}
.t-tag--light.t-tag--default {
  color: var(
    --td-tag-default-font-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
}
.t-tag--light-outline.t-tag--default {
  background-color: var(
    --td-tag-default-light-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  border-color: var(
    --td-tag-default-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
  color: var(
    --td-tag-default-color,
    var(--td-bg-color-component, var(--td-gray-color-3, #e7e7e7))
  );
}
.t-tag--light-outline.t-tag--primary {
  background-color: var(
    --td-tag-primary-light-color,
    var(--td-brand-color-light, var(--td-primary-color-1, #f2f3ff))
  );
  border-color: var(
    --td-tag-primary-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  color: var(
    --td-tag-primary-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-tag--light-outline.t-tag--success {
  background-color: var(
    --td-tag-success-light-color,
    var(--td-success-color-1, #e3f9e9)
  );
  border-color: var(
    --td-tag-success-color,
    var(--td-success-color, var(--td-success-color-5, #2ba471))
  );
  color: var(
    --td-tag-success-color,
    var(--td-success-color, var(--td-success-color-5, #2ba471))
  );
}
.t-tag--light-outline.t-tag--warning {
  background-color: var(
    --td-tag-warning-light-color,
    var(--td-warning-color-1, #fff1e9)
  );
  border-color: var(
    --td-tag-warning-color,
    var(--td-warning-color, var(--td-warning-color-5, #e37318))
  );
  color: var(
    --td-tag-warning-color,
    var(--td-warning-color, var(--td-warning-color-5, #e37318))
  );
}
.t-tag--light-outline.t-tag--danger {
  background-color: var(
    --td-tag-danger-light-color,
    var(--td-error-color-1, #fff0ed)
  );
  border-color: var(
    --td-tag-danger-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
  color: var(
    --td-tag-danger-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
}
.t-tag--light-outline.t-tag--default {
  border-color: var(--td-component-border, var(--td-gray-color-4, #dcdcdc));
  color: var(
    --td-tag-default-font-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
}
.t-tag.t-tag--closable.t-tag--disabled {
  background-color: var(
    --td-tag-disabled-background-color,
    var(--td-bg-color-component-disabled, var(--td-gray-color-2, #eee))
  );
  border-color: var(
    --td-tag-disabled-border-color,
    var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
  );
  color: var(
    --td-tag-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
  cursor: not-allowed;
}
