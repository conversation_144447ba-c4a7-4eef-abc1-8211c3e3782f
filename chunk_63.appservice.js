__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/add-service/add-service": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            serviceCategoryIndex: new Array(2),
            titleLength: new Array(1),
            noteLength: new Array(1),
            serviceCategoryList: new Array(2),
          },
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "发布-邻里服务");
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
                if (C || K || undefined) O(N, "show-back", true);
              },
              c
            );
          },
          g = (C, T) => {
            C ? T("服务名称") : T();
          },
          i = (C) => {},
          j = (C, T) => {
            C || K || !!U.titleLength || undefined
              ? T(Y(Y(D.titleLength) + "/15"), (N) => {
                  A["titleLength"][0] = (D, E, T) => {
                    T(N, Y(Y(D.titleLength) + "/15"));
                  };
                })
              : T();
          },
          h = (C, T, E) => {
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请输入服务名称");
                if (C || K || Z(U.formData, "title"))
                  O(N, "value", X(D.formData).title);
                if (C) O(N, "bindinput", "onTitleInput");
                if (C) O(N, "maxlength", "15");
              },
              i
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "char-count-inner");
              },
              j
            );
          },
          f = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              g
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "input-wrapper");
              },
              h
            );
          },
          l = (C, T) => {
            C ? T("价格") : T();
          },
          m = (C) => {},
          k = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              l
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C)
                  O(N, "placeholder", "请输入服务价格，不填则显示为价格面议");
                if (C || K || Z(U.formData, "price"))
                  O(N, "value", X(D.formData).price);
                if (C) O(N, "bindinput", "onPriceInput");
                if (C) O(N, "type", "digit");
              },
              m
            );
          },
          o = (C, T) => {
            C ? T("服务类目") : T();
          },
          q = (C, T) => {
            var $A = D.serviceCategoryIndex;
            C ||
            K ||
            !!(U.serviceCategoryIndex || Z(U.serviceCategoryList, $A)) ||
            undefined
              ? T(Y(X(D.serviceCategoryList)[$A] || "餐饮美食"), (N) => {
                  A["serviceCategoryList"][1] = A["serviceCategoryIndex"][1] = (
                    D,
                    E,
                    T
                  ) => {
                    var $B = D.serviceCategoryIndex;
                    T(N, Y(X(D.serviceCategoryList)[$B] || "餐饮美食"));
                  };
                })
              : T();
          },
          p = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-input picker-input");
              },
              q
            );
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              o
            );
            E(
              "picker",
              {},
              (N, C) => {
                if (C) O(N, "mode", "selector");
                if (C || K || U.serviceCategoryList)
                  O(N, "range", D.serviceCategoryList);
                A["serviceCategoryList"][0] = (D, E, T) => {
                  O(N, "range", D.serviceCategoryList);
                  E(N);
                };
                if (C || K || U.serviceCategoryIndex)
                  O(N, "value", D.serviceCategoryIndex);
                A["serviceCategoryIndex"][0] = (D, E, T) => {
                  O(N, "value", D.serviceCategoryIndex);
                  E(N);
                };
                if (C) O(N, "bindchange", "onServiceCategoryChange");
              },
              p
            );
          },
          s = (C, T) => {
            C ? T("缩略图") : T();
          },
          v,
          x = (C) => {},
          z = (C, T) => {
            C ? T("点击选择缩略图") : T();
          },
          y = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder-text");
              },
              z
            );
          },
          w = (C, T, E) => {
            if (v === 1) {
              E(
                "image",
                {},
                (N, C) => {
                  if (C) L(N, "selected-thumbnail");
                  if (C || K || Z(U.formData, "thumbnail"))
                    O(N, "src", X(D.formData).thumbnail);
                  if (C) O(N, "mode", "aspectFill");
                },
                x
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "thumbnail-placeholder");
                },
                y
              );
            }
          },
          u = (C, T, E, B) => {
            v = X(D.formData).thumbnail ? 1 : 0;
            B(v, w);
          },
          t = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-preview");
                if (C) O(N, "bindtap", "showThumbnailSelector");
              },
              u
            );
          },
          r = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-section");
              },
              t
            );
          },
          B0 = (C, T) => {
            C ? T("详情页图片") : T();
          },
          D0,
          G0 = (C, H0, I0, J0, K0, L0, T, E) => {
            var N0 = (C) => {},
              O0 = (C, T) => {
                C ? T("×") : T();
              },
              M0 = (C, T, E) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "uploaded-image");
                    if (C || K || Z(J0, "tempUrl")) O(N, "src", X(H0).tempUrl);
                    if (C) O(N, "mode", "aspectFill");
                  },
                  N0
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "delete-btn");
                    if (C) O(N, "bindtap", "deleteImage");
                    if (C || K || K0) R.d(N, "index", I0);
                  },
                  O0
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image-item");
              },
              M0
            );
          },
          F0 = (C, T, E, B, F) => {
            F(
              X(D.formData).serviceImages,
              "index",
              U ? Z(U.formData, "serviceImages") : undefined,
              [0, "formData", "serviceImages"],
              G0
            );
          },
          E0 = (C, T, E) => {
            if (D0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "uploaded-images");
                },
                F0
              );
            }
          },
          H0,
          K0 = (C, T) => {
            C ? T("+") : T();
          },
          L0 = (C, T) => {
            C ? T("添加图片") : T();
          },
          J0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "upload-text");
              },
              K0
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "upload-desc");
              },
              L0
            );
          },
          I0 = (C, T, E) => {
            if (H0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "upload-btn");
                  if (C) O(N, "bindtap", "chooseImage");
                },
                J0
              );
            }
          },
          N0 = (C, T) => {
            C ? T("最多可上传6张图片，建议尺寸750x750像素") : T();
          },
          M0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, N0);
          },
          C0 = (C, T, E, B) => {
            D0 = X(X(D.formData).serviceImages).length > 0 ? 1 : 0;
            B(D0, E0);
            H0 = X(X(D.formData).serviceImages).length < 6 ? 1 : 0;
            B(H0, I0);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "upload-tip");
              },
              M0
            );
          },
          A0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              B0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image-upload-section");
              },
              C0
            );
          },
          P0 = (C, T) => {
            C ? T("服务说明") : T();
          },
          R0 = (C) => {},
          S0 = (C, T) => {
            C || K || !!U.noteLength || undefined
              ? T(Y(Y(D.noteLength) + "/200"), (N) => {
                  A["noteLength"][0] = (D, E, T) => {
                    T(N, Y(Y(D.noteLength) + "/200"));
                  };
                })
              : T();
          },
          Q0 = (C, T, E) => {
            E(
              "textarea",
              {},
              (N, C) => {
                if (C) L(N, "form-textarea");
                if (C)
                  O(
                    N,
                    "placeholder",
                    "例如：接单时间，接单数量，服务范围（全小区还是本栋楼）"
                  );
                if (C || K || Z(U.formData, "note"))
                  O(N, "value", X(D.formData).note);
                if (C) O(N, "bindinput", "onNoteInput");
                if (C) O(N, "maxlength", "200");
              },
              R0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "char-count-inner textarea-count");
              },
              S0
            );
          },
          O0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              P0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "textarea-wrapper");
              },
              Q0
            );
          },
          U0 = (C, T) => {
            C ? T("取消") : T();
          },
          V0 = (C, T) => {
            C ? T("发布") : T();
          },
          T0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "onCancel");
              },
              U0
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-publish");
                if (C) O(N, "bindtap", "onPublish");
              },
              V0
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              n
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              r
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              A0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              O0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "button-group");
              },
              T0
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-container");
              },
              e
            );
          },
          W0,
          Z0 = (C) => {},
          c0 = (C, T) => {
            C ? T("选择缩略图") : T();
          },
          b0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "modal-title");
              },
              c0
            );
          },
          e0 = (C, f0, g0, h0, i0, j0, T, E) => {
            var l0 = (C) => {},
              m0,
              o0 = (C, T) => {
                C ? T("✓") : T();
              },
              n0 = (C, T, E) => {
                if (m0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "selected-mark");
                    },
                    o0
                  );
                }
              },
              k0 = (C, T, E, B) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "thumbnail-img");
                    if (C || K || Z(h0, "src")) O(N, "src", X(f0).src);
                    if (C) O(N, "mode", "aspectFill");
                  },
                  l0
                );
                m0 = X(D.formData).thumbnail === X(f0).src ? 1 : 0;
                B(m0, n0);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-item");
                if (C) O(N, "bindtap", "selectThumbnail");
                if (C || K || Z(h0, "src")) R.d(N, "src", X(f0).src);
                if (C || K || Z(h0, "file_id")) R.d(N, "fileId", X(f0).file_id);
              },
              k0
            );
          },
          d0 = (C, T, E, B, F) => {
            F(
              D.thumbnailList,
              "index",
              U ? U.thumbnailList : undefined,
              [0, "thumbnailList"],
              e0
            );
          },
          g0 = (C, T) => {
            C ? T("取消") : T();
          },
          f0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "hideThumbnailSelector");
              },
              g0
            );
          },
          a0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-header");
              },
              b0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-grid");
              },
              d0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-footer");
              },
              f0
            );
          },
          Y0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-mask");
                if (C) O(N, "bindtap", "hideThumbnailSelector");
              },
              Z0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-content");
              },
              a0
            );
          },
          X0 = (C, T, E) => {
            if (W0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "thumbnail-modal");
                },
                Y0
              );
            }
          },
          a = (C, T, E, B) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
            W0 = D.showThumbnailModal ? 1 : 0;
            B(W0, X0);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/add-service/add-service";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/add-service/add-service.js";
define(
  "pages/add-service/add-service.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var e = require("../../@babel/runtime/helpers/toConsumableArray"),
      t = require("../../@babel/runtime/helpers/typeof"),
      a = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      n = require("../../@babel/runtime/helpers/asyncToGenerator");
    Page({
      data: {
        formData: {
          title: "",
          price: "",
          serviceCategory: "",
          note: "",
          thumbnail: "",
          thumbnailName: "",
          serviceImages: [],
        },
        titleLength: 0,
        contactLength: 0,
        noteLength: 0,
        serviceCategoryIndex: 0,
        serviceCategoryList: [],
        serviceCategoryData: [],
        showThumbnailModal: !1,
        thumbnailList: [],
      },
      onLoad: function (e) {
        var t = this;
        return n(
          a().mark(function e() {
            return a().wrap(function (e) {
              for (;;)
                switch ((e.prev = e.next)) {
                  case 0:
                    return (e.next = 2), t.getServiceCategories();
                  case 2:
                    return (e.next = 4), t.getDefaultThumbnails();
                  case 4:
                  case "end":
                    return e.stop();
                }
            }, e);
          })
        )();
      },
      getServiceCategories: function () {
        var e = this;
        return n(
          a().mark(function t() {
            var n, r, u, i, o;
            return a().wrap(
              function (t) {
                for (;;)
                  switch ((t.prev = t.next)) {
                    case 0:
                      return (
                        (n = getApp()),
                        (t.prev = 1),
                        wx.showLoading({ title: "加载中..." }),
                        (t.next = 5),
                        n.call({
                          path: "/api/mp/services/categories?status=enabled",
                          method: "GET",
                        })
                      );
                    case 5:
                      0 === (r = t.sent).code && r.data
                        ? ((u = r.data.map(function (e) {
                            return e.name;
                          })),
                          e.setData({
                            serviceCategoryData: r.data,
                            serviceCategoryList: u,
                            "formData.serviceCategory":
                              u.length > 0 ? u[0] : "",
                          }))
                        : (console.error("获取服务类目失败:", r.message),
                          (i = [
                            "餐饮美食",
                            "生活服务",
                            "家政服务",
                            "维修服务",
                            "代购服务",
                            "其他服务",
                          ]),
                          e.setData({
                            serviceCategoryList: i,
                            "formData.serviceCategory": i[0],
                          }),
                          wx.showToast({
                            title: "获取类目失败，使用默认类目",
                            icon: "none",
                          })),
                        (t.next = 15);
                      break;
                    case 9:
                      (t.prev = 9),
                        (t.t0 = t.catch(1)),
                        console.error("获取服务类目接口调用失败:", t.t0),
                        (o = [
                          "餐饮美食",
                          "生活服务",
                          "家政服务",
                          "维修服务",
                          "代购服务",
                          "其他服务",
                        ]),
                        e.setData({
                          serviceCategoryList: o,
                          "formData.serviceCategory": o[0],
                        }),
                        wx.showToast({
                          title: "网络异常，使用默认类目",
                          icon: "none",
                        });
                    case 15:
                      return (t.prev = 15), wx.hideLoading(), t.finish(15);
                    case 18:
                    case "end":
                      return t.stop();
                  }
              },
              t,
              null,
              [[1, 9, 15, 18]]
            );
          })
        )();
      },
      getDefaultThumbnails: function () {
        var e = this;
        return n(
          a().mark(function t() {
            var n, r, u, i, o;
            return a().wrap(
              function (t) {
                for (;;)
                  switch ((t.prev = t.next)) {
                    case 0:
                      return (
                        (n = getApp()),
                        (t.prev = 1),
                        (t.next = 4),
                        n.call({
                          path: "/api/mp/images/default-thumbnails",
                          method: "GET",
                        })
                      );
                    case 4:
                      if (
                        !(
                          0 === (r = t.sent).code &&
                          r.data &&
                          r.data.length > 0
                        )
                      ) {
                        t.next = 19;
                        break;
                      }
                      return (
                        (u = r.data.map(function (e) {
                          return e.file_id;
                        })),
                        (t.next = 9),
                        n.getTempFile(u)
                      );
                    case 9:
                      if ("cloud.getTempFileURL:ok" !== (i = t.sent).errMsg) {
                        t.next = 16;
                        break;
                      }
                      (o = i.fileList.map(function (e, t) {
                        var a = r.data[t];
                        return {
                          id: a.id,
                          file_id: a.file_id,
                          src: e.tempFileURL,
                          name: "缩略图".concat(a.id),
                        };
                      })),
                        e.setData({ thumbnailList: o }),
                        console.log("获取默认缩略图成功:", o),
                        (t.next = 17);
                      break;
                    case 16:
                      throw new Error("获取临时文件地址失败");
                    case 17:
                      t.next = 21;
                      break;
                    case 19:
                      console.error("获取默认缩略图失败:", r.message),
                        e.setDefaultThumbnails();
                    case 21:
                      t.next = 27;
                      break;
                    case 23:
                      (t.prev = 23),
                        (t.t0 = t.catch(1)),
                        console.error("获取默认缩略图接口调用失败:", t.t0),
                        e.setDefaultThumbnails();
                    case 27:
                    case "end":
                      return t.stop();
                  }
              },
              t,
              null,
              [[1, 23]]
            );
          })
        )();
      },
      setDefaultThumbnails: function () {
        this.setData({
          thumbnailList: [
            { id: "local_1", src: "/assets/slt/汉堡包.png", name: "汉堡包" },
            { id: "local_2", src: "/assets/slt/美食.png", name: "美食" },
            {
              id: "local_3",
              src: "/assets/slt/鱼生套餐.png",
              name: "鱼生套餐",
            },
          ],
        }),
          wx.showToast({
            title: "使用本地默认缩略图",
            icon: "none",
            duration: 1500,
          });
      },
      filterEmoji: function (e) {
        return e.replace(
          /(?:\uD83D[\uDE00-\uDE4F])|(?:\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDDFF])|(?:\uD83D[\uDE80-\uDEFF])|(?:\uD83C[\uDDE0-\uDDFF])|[\u2600-\u26FF]|[\u2700-\u27BF]|(?:\uD83E[\uDD00-\uDDFF])|(?:\uD83C[\uDC18-\uDE70])|\u238C|[\u2194-\u2199]|[\u21A9\u21AA]|[\u231A\u231B]|[\u23E9-\u23EC]|\u23F0|\u23F3|[\u25FD\u25FE]|[\u2614\u2615]|[\u2648-\u2653]|\u267F|\u2693|\u26A1|[\u26AA\u26AB]|[\u26BD\u26BE]|[\u26C4\u26C5]|\u26CE|\u26D4|\u26EA|[\u26F2\u26F3]|\u26F5|\u26FA|\u26FD|\u2705|[\u270A\u270B]|\u2728|\u274C|\u274E|[\u2753-\u2755]|\u2757|[\u2795-\u2797]|\u27B0|\u27BF|[\u2B1B\u2B1C]|\u2B50|\u2B55/g,
          ""
        );
      },
      hasEmoji: function (e) {
        return /(?:\uD83D[\uDE00-\uDE4F])|(?:\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDDFF])|(?:\uD83D[\uDE80-\uDEFF])|(?:\uD83C[\uDDE0-\uDDFF])|[\u2600-\u26FF]|[\u2700-\u27BF]|(?:\uD83E[\uDD00-\uDDFF])|(?:\uD83C[\uDC18-\uDE70])|\u238C|[\u2194-\u2199]|[\u21A9\u21AA]|[\u231A\u231B]|[\u23E9-\u23EC]|\u23F0|\u23F3|[\u25FD\u25FE]|[\u2614\u2615]|[\u2648-\u2653]|\u267F|\u2693|\u26A1|[\u26AA\u26AB]|[\u26BD\u26BE]|[\u26C4\u26C5]|\u26CE|\u26D4|\u26EA|[\u26F2\u26F3]|\u26F5|\u26FA|\u26FD|\u2705|[\u270A\u270B]|\u2728|\u274C|\u274E|[\u2753-\u2755]|\u2757|[\u2795-\u2797]|\u27B0|\u27BF|[\u2B1B\u2B1C]|\u2B50|\u2B55/g.test(
          e
        );
      },
      onTitleInput: function (e) {
        var t = e.detail.value;
        if (this.hasEmoji(t)) {
          wx.showToast({
            title: "服务名称不支持输入emoji",
            icon: "none",
            duration: 2e3,
          });
          var a = this.filterEmoji(t);
          this.setData({ "formData.title": a, titleLength: a.length });
        } else this.setData({ "formData.title": t, titleLength: t.length });
      },
      onPriceInput: function (e) {
        this.setData({ "formData.price": e.detail.value });
      },
      onNoteInput: function (e) {
        var t = e.detail.value;
        if (this.hasEmoji(t)) {
          wx.showToast({
            title: "服务说明不支持输入emoji",
            icon: "none",
            duration: 2e3,
          });
          var a = this.filterEmoji(t);
          this.setData({ "formData.note": a, noteLength: a.length });
        } else this.setData({ "formData.note": t, noteLength: t.length });
      },
      onServiceCategoryChange: function (e) {
        var t = e.detail.value;
        this.setData({
          serviceCategoryIndex: t,
          "formData.serviceCategory": this.data.serviceCategoryList[t],
        });
      },
      showThumbnailSelector: function () {
        this.setData({ showThumbnailModal: !0 });
      },
      hideThumbnailSelector: function () {
        this.setData({ showThumbnailModal: !1 });
      },
      selectThumbnail: function (e) {
        var t = e.currentTarget.dataset.src,
          a = e.currentTarget.dataset.name,
          n = e.currentTarget.dataset.fileId;
        this.setData({
          "formData.thumbnail": t,
          "formData.thumbnailName": a,
          "formData.thumbnailFileId": n,
          showThumbnailModal: !1,
        });
      },
      validateForm: function () {
        var e = this.data.formData,
          t = e.title,
          a = e.serviceCategory;
        return t.trim()
          ? !!a || (wx.showToast({ title: "请选择服务类目", icon: "none" }), !1)
          : (wx.showToast({ title: "请输入服务名称", icon: "none" }), !1);
      },
      onPublish: function () {
        var e = this;
        return n(
          a().mark(function n() {
            var r, u, i, o, s, c, l;
            return a().wrap(
              function (a) {
                for (;;)
                  switch ((a.prev = a.next)) {
                    case 0:
                      if (e.validateForm()) {
                        a.next = 2;
                        break;
                      }
                      return a.abrupt("return");
                    case 2:
                      if (
                        (wx.showLoading({ title: "发布中..." }),
                        (r = getApp()),
                        (a.prev = 4),
                        (i = e.data.formData.serviceCategory),
                        (o = e.data.serviceCategoryData.find(function (e) {
                          return e.name === i;
                        })))
                      ) {
                        a.next = 9;
                        break;
                      }
                      throw new Error("未找到对应的服务类目");
                    case 9:
                      if (
                        (s =
                          r.globalData.openid ||
                          (null === (u = r.globalData.userInfo) || void 0 === u
                            ? void 0
                            : u.openid) ||
                          wx.getStorageSync("openid"))
                      ) {
                        a.next = 12;
                        break;
                      }
                      throw new Error("未获取到用户openid，请重新登录");
                    case 12:
                      return (
                        (c = {
                          openid: s,
                          name: e.data.formData.title,
                          price: e.data.formData.price || null,
                          category_id: o.id,
                          thumbnail: e.data.formData.thumbnailFileId,
                          detail_images: e.data.formData.serviceImages.map(
                            function (e) {
                              return "object" === t(e) ? e.fileId : e;
                            }
                          ),
                          description: e.data.formData.note || "",
                        }),
                        console.log("提交数据:", c),
                        (a.next = 16),
                        r.call({
                          path: "/api/mp/services",
                          method: "POST",
                          data: c,
                        })
                      );
                    case 16:
                      if (0 !== (l = a.sent).code) {
                        a.next = 23;
                        break;
                      }
                      wx.hideLoading(),
                        wx.showToast({
                          title: "发布成功",
                          icon: "success",
                          duration: 2e3,
                        }),
                        setTimeout(function () {
                          wx.navigateBack();
                        }, 2e3),
                        (a.next = 24);
                      break;
                    case 23:
                      throw new Error(l.message || "发布失败");
                    case 24:
                      a.next = 31;
                      break;
                    case 26:
                      (a.prev = 26),
                        (a.t0 = a.catch(4)),
                        console.error("发布服务失败:", a.t0),
                        wx.hideLoading(),
                        wx.showToast({
                          title: a.t0.message || "发布失败",
                          icon: "error",
                        });
                    case 31:
                    case "end":
                      return a.stop();
                  }
              },
              n,
              null,
              [[4, 26]]
            );
          })
        )();
      },
      uploadImageToCloud: function (e, t) {
        return n(
          a().mark(function n() {
            var r, u, i, o;
            return a().wrap(
              function (a) {
                for (;;)
                  switch ((a.prev = a.next)) {
                    case 0:
                      return (
                        (r = getApp()),
                        (a.prev = 1),
                        (a.next = 4),
                        r.cloud.uploadFile({
                          cloudPath: "service-images/"
                            .concat(Date.now(), "-")
                            .concat(t),
                          filePath: e,
                        })
                      );
                    case 4:
                      return (
                        (u = a.sent),
                        console.log("上传成功:", u),
                        (a.next = 8),
                        r.getTempFile(u.fileID)
                      );
                    case 8:
                      return (
                        (i = a.sent),
                        (o = i.fileList[0].tempFileURL),
                        a.abrupt("return", { fileId: u.fileID, tempUrl: o })
                      );
                    case 13:
                      throw (
                        ((a.prev = 13),
                        (a.t0 = a.catch(1)),
                        console.error("上传失败:", a.t0),
                        a.t0)
                      );
                    case 17:
                    case "end":
                      return a.stop();
                  }
              },
              n,
              null,
              [[1, 13]]
            );
          })
        )();
      },
      chooseImage: function () {
        var t = this;
        return n(
          a().mark(function r() {
            var u, i, o, s;
            return a().wrap(
              function (r) {
                for (;;)
                  switch ((r.prev = r.next)) {
                    case 0:
                      return (
                        (r.prev = 0),
                        (r.next = 3),
                        new Promise(function (e, a) {
                          wx.chooseMedia({
                            count: 9 - t.data.formData.serviceImages.length,
                            mediaType: ["image"],
                            sourceType: ["album", "camera"],
                            success: e,
                            fail: a,
                          });
                        })
                      );
                    case 3:
                      return (
                        (u = r.sent),
                        wx.showLoading({ title: "上传中..." }),
                        (i = u.tempFiles.map(
                          (function () {
                            var e = n(
                              a().mark(function e(n, r) {
                                var u;
                                return a().wrap(function (e) {
                                  for (;;)
                                    switch ((e.prev = e.next)) {
                                      case 0:
                                        return (
                                          (u = "image_"
                                            .concat(Date.now(), "_")
                                            .concat(r, ".jpg")),
                                          (e.next = 3),
                                          t.uploadImageToCloud(
                                            n.tempFilePath,
                                            u
                                          )
                                        );
                                      case 3:
                                        return e.abrupt("return", e.sent);
                                      case 4:
                                      case "end":
                                        return e.stop();
                                    }
                                }, e);
                              })
                            );
                            return function (t, a) {
                              return e.apply(this, arguments);
                            };
                          })()
                        )),
                        (r.next = 8),
                        Promise.all(i)
                      );
                    case 8:
                      (o = r.sent),
                        (s = [].concat(e(t.data.formData.serviceImages), e(o))),
                        t.setData({ "formData.serviceImages": s }),
                        wx.hideLoading(),
                        wx.showToast({ title: "上传成功", icon: "success" }),
                        (r.next = 20);
                      break;
                    case 15:
                      (r.prev = 15),
                        (r.t0 = r.catch(0)),
                        wx.hideLoading(),
                        console.error("选择或上传图片失败:", r.t0),
                        wx.showToast({ title: "上传失败", icon: "error" });
                    case 20:
                    case "end":
                      return r.stop();
                  }
              },
              r,
              null,
              [[0, 15]]
            );
          })
        )();
      },
      uploadCustomThumbnail: function () {
        var t = this;
        return n(
          a().mark(function n() {
            var r, u, i, o, s, c, l, m;
            return a().wrap(
              function (a) {
                for (;;)
                  switch ((a.prev = a.next)) {
                    case 0:
                      return (
                        (a.prev = 0),
                        (a.next = 3),
                        new Promise(function (e, t) {
                          wx.chooseMedia({
                            count: 1,
                            mediaType: ["image"],
                            sourceType: ["album", "camera"],
                            success: e,
                            fail: t,
                          });
                        })
                      );
                    case 3:
                      return (
                        (r = a.sent),
                        wx.showLoading({ title: "上传中..." }),
                        (u = r.tempFiles[0]),
                        (i = "thumbnail_".concat(Date.now(), ".jpg")),
                        (a.next = 9),
                        t.uploadImageToCloud(u.tempFilePath, i)
                      );
                    case 9:
                      return (
                        (o = a.sent),
                        (s = getApp()),
                        (a.next = 13),
                        s.getTempFile(o.fileId)
                      );
                    case 13:
                      (c = a.sent),
                        (l = {
                          url: c,
                          fileId: o.fileId,
                          name: "自定义缩略图",
                          isCustom: !0,
                        }),
                        (m = [l].concat(e(t.data.thumbnailList))),
                        t.setData({
                          thumbnailList: m,
                          "formData.thumbnail": c,
                          "formData.thumbnailName": "自定义缩略图",
                          "formData.thumbnailFileId": o.fileId,
                          selectedThumbnailIndex: 0,
                        }),
                        wx.hideLoading(),
                        wx.showToast({ title: "上传成功", icon: "success" }),
                        (a.next = 26);
                      break;
                    case 21:
                      (a.prev = 21),
                        (a.t0 = a.catch(0)),
                        wx.hideLoading(),
                        console.error("上传自定义缩略图失败:", a.t0),
                        wx.showToast({ title: "上传失败", icon: "error" });
                    case 26:
                    case "end":
                      return a.stop();
                  }
              },
              n,
              null,
              [[0, 21]]
            );
          })
        )();
      },
      deleteImage: function (t) {
        var a = t.currentTarget.dataset.index,
          n = e(this.data.formData.serviceImages);
        n.splice(a, 1),
          this.setData({ "formData.serviceImages": n }),
          wx.showToast({ title: "删除成功", icon: "success", duration: 1e3 });
      },
      onReady: function () {},
      onShow: function () {},
      onHide: function () {},
      onUnload: function () {},
    });
  },
  {
    isPage: true,
    isComponent: true,
    currentFile: "pages/add-service/add-service.js",
  }
);
require("pages/add-service/add-service.js");
