Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0),
  require("../../../@babel/runtime/helpers/Arrayincludes");
var e = require("../../../@babel/runtime/helpers/toConsumableArray"),
  t = require("../../../@babel/runtime/helpers/slicedToArray"),
  i = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  s = require("../../../@babel/runtime/helpers/createClass"),
  n = require("../../../@babel/runtime/helpers/classCallCheck"),
  l = require("../../../@babel/runtime/helpers/inherits"),
  a = require("../../../@babel/runtime/helpers/createSuper"),
  r = require("../../../@babel/runtime/helpers/defineProperty"),
  h = require("tslib"),
  u = require("../common/src/index"),
  o = v(require("../common/config")),
  d = v(require("./props")),
  c = require("../common/utils");
function v(e) {
  return e && e.__esModule ? e : { default: e };
}
var p = o.default.prefix,
  g = "".concat(p, "-cascader");
function f(e, t) {
  var i,
    s,
    n =
      null !== (i = null == t ? void 0 : t.label) && void 0 !== i ? i : "label",
    l =
      null !== (s = null == t ? void 0 : t.value) && void 0 !== s ? s : "value";
  return e.map(function (e) {
    var t;
    return r((t = {}), n, e[n]), r(t, l, e[l]), t;
  });
}
var b = {
    contentHeight: 0,
    stepHeight: 0,
    tabsHeight: 0,
    subTitlesHeight: 0,
    stepsInitHeight: 0,
  },
  m = (function (u) {
    l(v, u);
    var o = a(v);
    function v() {
      var s;
      return (
        n(this, v),
        ((s = o.apply(this, arguments)).externalClasses = [
          "".concat(p, "-class"),
        ]),
        (s.options = { multipleSlots: !0, pureDataPattern: /^options$/ }),
        (s.properties = d.default),
        (s.controlledProps = [{ key: "value", event: "change" }]),
        (s.state = Object.assign({}, b)),
        (s.data = {
          prefix: p,
          name: g,
          stepIndex: 0,
          selectedIndexes: [],
          selectedValue: [],
          scrollTopList: [],
          steps: [],
          _optionsHeight: 0,
        }),
        (s.observers = {
          visible: function (e) {
            var t = this;
            if (e) {
              var i = this.selectComponent("#tabs");
              null == i || i.setTrack(),
                null == i ||
                  i.getTabHeight().then(function (e) {
                    t.state.tabsHeight = e.height;
                  }),
                this.initOptionsHeight(this.data.steps.length),
                this.updateScrollTop(),
                this.initWithValue();
            } else this.state = Object.assign({}, b);
          },
          value: function () {
            this.initWithValue();
          },
          options: function () {
            var e = this.genItems(),
              t = e.selectedValue,
              i = e.steps,
              s = e.items;
            this.setData({
              steps: i,
              items: s,
              selectedValue: t,
              stepIndex: s.length - 1,
            });
          },
          selectedIndexes: function () {
            var e = this.properties,
              t = e.visible,
              i = e.theme,
              s = this.genItems(),
              n = s.selectedValue,
              l = s.steps,
              a = s.items,
              r = { steps: l, selectedValue: n, stepIndex: a.length - 1 };
            JSON.stringify(a) !== JSON.stringify(this.data.items) &&
              Object.assign(r, { items: a }),
              this.setData(r),
              t && "step" === i && this.updateOptionsHeight(l.length);
          },
          stepIndex: function () {
            return (0, h.__awaiter)(
              this,
              void 0,
              void 0,
              i().mark(function e() {
                return i().wrap(
                  function (e) {
                    for (;;)
                      switch ((e.prev = e.next)) {
                        case 0:
                          this.data.visible && this.updateScrollTop();
                        case 2:
                        case "end":
                          return e.stop();
                      }
                  },
                  e,
                  this
                );
              })
            );
          },
        }),
        (s.methods = {
          updateOptionsHeight: function (e) {
            var t = this.state,
              i = t.contentHeight,
              s = t.stepsInitHeight,
              n = t.stepHeight,
              l = t.subTitlesHeight;
            this.setData({ _optionsHeight: i - s - l - (e - 1) * n });
          },
          initOptionsHeight: function (e) {
            return (0, h.__awaiter)(
              this,
              void 0,
              void 0,
              i().mark(function s() {
                var n,
                  l,
                  a,
                  r,
                  h,
                  u,
                  o,
                  d,
                  v = this;
                return i().wrap(
                  function (i) {
                    for (;;)
                      switch ((i.prev = i.next)) {
                        case 0:
                          return (
                            (n = this.properties),
                            (l = n.theme),
                            (a = n.subTitles),
                            (i.next = 5),
                            (0, c.getRect)(this, ".".concat(g, "__content"))
                          );
                        case 5:
                          if (
                            ((r = i.sent),
                            (h = r.height),
                            (this.state.contentHeight = h),
                            (i.t0 = "step" === l),
                            !i.t0)
                          ) {
                            i.next = 12;
                            break;
                          }
                          return (
                            (i.next = 12),
                            Promise.all([
                              (0, c.getRect)(this, ".".concat(g, "__steps")),
                              (0, c.getRect)(this, ".".concat(g, "__step")),
                            ]).then(function (i) {
                              var s = t(i, 2),
                                n = s[0],
                                l = s[1];
                              (v.state.stepsInitHeight =
                                n.height - (e - 1) * l.height),
                                (v.state.stepHeight = l.height);
                            })
                          );
                        case 12:
                          if (!(a.length > 0)) {
                            i.next = 18;
                            break;
                          }
                          return (
                            (i.next = 15),
                            (0, c.getRect)(
                              this,
                              ".".concat(g, "__options-title")
                            )
                          );
                        case 15:
                          (u = i.sent),
                            (o = u.height),
                            (this.state.subTitlesHeight = o);
                        case 18:
                          (d =
                            this.state.contentHeight -
                            this.state.subTitlesHeight),
                            this.setData({
                              _optionsHeight:
                                "step" === l
                                  ? d -
                                    this.state.stepsInitHeight -
                                    (e - 1) * this.state.stepHeight
                                  : d - this.state.tabsHeight,
                            });
                        case 20:
                        case "end":
                          return i.stop();
                      }
                  },
                  s,
                  this
                );
              })
            );
          },
          initWithValue: function () {
            if (null != this.data.value && "" !== this.data.value) {
              var e = this.getIndexesByValue(
                this.data.options,
                this.data.value
              );
              e && this.setData({ selectedIndexes: e });
            } else this.setData({ selectedIndexes: [] });
          },
          getIndexesByValue: function (t, i) {
            for (
              var s, n, l, a = this.data.keys, r = 0, h = t.length;
              r < h;
              r += 1
            ) {
              var u = t[r];
              if (
                u[
                  null !== (s = null == a ? void 0 : a.value) && void 0 !== s
                    ? s
                    : "value"
                ] === i
              )
                return [r];
              if (
                u[
                  null !== (n = null == a ? void 0 : a.children) && void 0 !== n
                    ? n
                    : "children"
                ]
              ) {
                var o = this.getIndexesByValue(
                  u[
                    null !== (l = null == a ? void 0 : a.children) &&
                    void 0 !== l
                      ? l
                      : "children"
                  ],
                  i
                );
                if (o) return [r].concat(e(o));
              }
            }
          },
          updateScrollTop: function () {
            var e = this,
              t = this.data,
              i = t.visible,
              s = t.items,
              n = t.selectedIndexes,
              l = t.stepIndex;
            i &&
              (0, c.getRect)(this, ".cascader-radio-group-0").then(function (
                t
              ) {
                var i,
                  a =
                    t.height /
                    (null === (i = s[0]) || void 0 === i ? void 0 : i.length);
                e.setData(r({}, "scrollTopList[".concat(l, "]"), a * n[l]));
              });
          },
          hide: function (e) {
            this.setData({ visible: !1 }),
              this.triggerEvent("close", { trigger: e });
          },
          onVisibleChange: function () {
            this.hide("overlay");
          },
          onClose: function () {
            this.data.checkStrictly && this.triggerChange(),
              this.hide("close-btn");
          },
          onStepClick: function (e) {
            var t = e.currentTarget.dataset.index;
            this.setData({ stepIndex: t });
          },
          onTabChange: function (e) {
            var t = e.detail.value;
            this.setData({ stepIndex: t });
          },
          genItems: function () {
            var e,
              t,
              i,
              s,
              n,
              l = this.data,
              a = l.options,
              r = l.selectedIndexes,
              h = l.keys,
              u = l.placeholder,
              o = [],
              d = [],
              c = [f(a, h)];
            if (a.length > 0)
              for (var v = a, p = 0, g = r.length; p < g; p += 1) {
                var b = v[r[p]];
                (v =
                  b[
                    null !== (e = null == h ? void 0 : h.children) &&
                    void 0 !== e
                      ? e
                      : "children"
                  ]),
                  o.push(
                    b[
                      null !== (t = null == h ? void 0 : h.value) &&
                      void 0 !== t
                        ? t
                        : "value"
                    ]
                  ),
                  d.push(
                    b[
                      null !== (i = null == h ? void 0 : h.label) &&
                      void 0 !== i
                        ? i
                        : "label"
                    ]
                  ),
                  b[
                    null !== (s = null == h ? void 0 : h.children) &&
                    void 0 !== s
                      ? s
                      : "children"
                  ] &&
                    c.push(
                      f(
                        b[
                          null !== (n = null == h ? void 0 : h.children) &&
                          void 0 !== n
                            ? n
                            : "children"
                        ],
                        h
                      )
                    );
              }
            return (
              d.length < c.length && d.push(u),
              { selectedValue: o, steps: d, items: c }
            );
          },
          handleSelect: function (e) {
            var t,
              i,
              s,
              n,
              l,
              a = e.target.dataset.level,
              h = e.detail.value,
              u = this.properties.checkStrictly,
              o = this.data,
              d = o.selectedIndexes,
              c = o.items,
              v = o.keys,
              p = o.options,
              g = o.selectedValue,
              f = c[a].findIndex(function (e) {
                var t;
                return (
                  e[
                    null !== (t = null == v ? void 0 : v.value) && void 0 !== t
                      ? t
                      : "value"
                  ] === h
                );
              }),
              b = d.slice(0, a).reduce(function (e, t, i) {
                var s;
                return 0 === i
                  ? e[t]
                  : e[
                      null !== (s = null == v ? void 0 : v.children) &&
                      void 0 !== s
                        ? s
                        : "children"
                    ][t];
              }, p);
            if (
              !(b =
                0 === a
                  ? b[f]
                  : b[
                      null !== (t = null == v ? void 0 : v.children) &&
                      void 0 !== t
                        ? t
                        : "children"
                    ][f]).disabled
            ) {
              if (
                (this.triggerEvent("pick", {
                  value:
                    b[
                      null !== (i = null == v ? void 0 : v.value) &&
                      void 0 !== i
                        ? i
                        : "value"
                    ],
                  label:
                    b[
                      null !== (s = null == v ? void 0 : v.label) &&
                      void 0 !== s
                        ? s
                        : "label"
                    ],
                  index: f,
                  level: a,
                }),
                (d[a] = f),
                u && g.includes(String(h)))
              )
                return (
                  (d.length = a), void this.setData({ selectedIndexes: d })
                );
              d.length = a + 1;
              var m = this.genItems().items;
              (null ===
                (l =
                  null == b
                    ? void 0
                    : b[
                        null !== (n = null == v ? void 0 : v.children) &&
                        void 0 !== n
                          ? n
                          : "children"
                      ]) || void 0 === l
                ? void 0
                : l.length) >= 0
                ? this.setData(
                    r(
                      { selectedIndexes: d },
                      "items[".concat(a + 1, "]"),
                      m[a + 1]
                    )
                  )
                : (this.setData({ selectedIndexes: d }, this.triggerChange),
                  this.hide("finish"));
            }
          },
          triggerChange: function () {
            var e,
              t = this.data,
              i = t.items,
              s = t.selectedValue,
              n = t.selectedIndexes;
            this._trigger("change", {
              value: null !== (e = s[s.length - 1]) && void 0 !== e ? e : "",
              selectedOptions: i
                .map(function (e, t) {
                  return e[n[t]];
                })
                .filter(Boolean),
            });
          },
        }),
        s
      );
    }
    return s(v);
  })(u.SuperComponent),
  x = (m = (0, h.__decorate)([(0, u.wxComponent)()], m));
exports.default = x;
