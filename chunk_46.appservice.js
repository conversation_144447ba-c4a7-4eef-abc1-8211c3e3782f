__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/scroll-view/scroll-view": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { scrollIntoView: new Array(1), prefix: new Array(1) },
          K = U === true,
          b = (C, T, E, B, F, S) => {
            S("");
          },
          a = (C, T, E) => {
            E(
              "scroll-view",
              {},
              (N, C) => {
                if (C || K || !!U.prefix || undefined)
                  L(N, "class " + Y(D.prefix) + "-class");
                A["prefix"][0] = (D, E, T) => {
                  L(N, "class " + Y(D.prefix) + "-class");
                };
                if (C) O(N, "type", "list");
                if (C) O(N, "scroll-y", true);
                if (C) O(N, "enhanced", true);
                if (C || K || undefined) O(N, "show-scrollbar", false);
                if (C || K || U.scrollIntoView)
                  O(N, "scroll-into-view", D.scrollIntoView);
                A["scrollIntoView"][0] = (D, E, T) => {
                  O(N, "scroll-into-view", D.scrollIntoView);
                  E(N);
                };
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/side-bar-item/side-bar-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/badge"] ||
              {}
            )._,
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            prefix: new Array(1),
            tId: new Array(1),
            customStyle: new Array(1),
            disabled: new Array(2),
          },
          K = U === true,
          d,
          f = (C) => {},
          g = (C) => {},
          h = (C) => {},
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__line");
                },
                f
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__prefix");
                },
                g
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__suffix");
                },
                h
              );
            }
          },
          i,
          k,
          l = (C, T, E, B, F, S, J) => {
            var $A = I(k);
            if (k && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.classPrefix + "__icon" },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.classPrefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          j = (C, T, E, B, F, S, J) => {
            if (i === 1) {
              k = "icon";
              B(k, l);
            }
          },
          m,
          o,
          p = (C, T, E, B, F, S, J) => {
            var $A = I(o);
            if (o && $A)
              $A(
                R,
                C,
                Object.assign({}, X(D.badgeProps), { content: D.label }),
                K ||
                  (U
                    ? U.badgeProps === true ||
                      Object.assign({}, X(U.badgeProps), { content: U.label })
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          n = (C, T, E, B, F, S, J) => {
            if (m === 1) {
              o = "badge";
              B(o, p);
            } else {
              C || K || U.label ? T(Y(D.label)) : T();
            }
          },
          c = (C, T, E, B) => {
            d = D.active ? 1 : 0;
            B(d, e);
            i = D._icon ? 1 : 0;
            B(i, j);
            m = D.badgeProps ? 1 : 0;
            B(m, n);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.active]), Q.a([U.disabled])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["active", D.active],
                        ["disabled", D.disabled],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["disabled"][1] = A["prefix"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["active", D.active],
                        ["disabled", D.disabled],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-role", "button");
                var $A = D.active;
                if (
                  C ||
                  K ||
                  !!U.active ||
                  ($A ? !!U.label || undefined : U.label)
                )
                  O(N, "aria-label", $A ? "已选中，" + D.label : D.label);
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                A["disabled"][0] = (D, E, T) => {
                  O(N, "aria-disabled", D.disabled);
                  E(N);
                };
                if (C) R.v(N, "tap", "handleClick", !1, !1, !1, !1);
                if (C || K || U.tId) R.i(N, D.tId);
                A["tId"][0] = (D, E, T) => {
                  R.i(N, D.tId);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/side-bar/side-bar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            style: new Array(1),
            classPrefix: new Array(2),
            prefix: new Array(1),
          },
          K = U === true,
          d = (C) => {},
          c = (C, T, E, B, F, S) => {
            S("");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__padding");
                A["classPrefix"][1] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + "__padding");
                };
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["classPrefix"][0] = A["prefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/tree-select/tree-select": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/tree-select/index"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            style: new Array(1),
            height: new Array(1),
          },
          K = U === true,
          e = (C, f, g, h, i, j, T, E) => {
            var l,
              o = (C, p, q, r, s, t, T, E) => {
                var u = (C) => {};
                E(
                  "t-side-bar-item",
                  {},
                  (N, C) => {
                    if (C) L(N, "scroll-into-view");
                    if (C || K || Z(r, "label")) O(N, "label", X(p).label);
                    if (C || K || Z(r, "value")) O(N, "value", X(p).value);
                    if (C || K || !!Z(r, "value") || undefined)
                      O(N, "tId", "scroll-to-" + Y(X(p).value));
                    if (C || K || !!U.prefix || undefined)
                      O(N, "t-class", Y(D.prefix) + "-class-left-item");
                  },
                  u
                );
              },
              n = (C, T, E, B, F) => {
                var $A = g;
                F(
                  X(D.treeOptions)[$A],
                  "index",
                  U ? !!i || Z(U.treeOptions, $A) : undefined,
                  [0, "treeOptions", $A],
                  o
                );
              },
              p = (C, q, r, s, t, u, T, E) => {
                var w = (C, T) => {
                    C || K || Z(s, "label") ? T(Y(X(q).label)) : T();
                  },
                  v = (C, T, E) => {
                    E(
                      "view",
                      {},
                      (N, C) => {
                        if (C || K || !!Z(s, "value") || undefined)
                          R.i(N, "scroll-to-" + Y(X(q).value));
                      },
                      w
                    );
                  };
                E(
                  "view",
                  {},
                  (N, C) => {
                    var $A = g;
                    if (
                      C ||
                      K ||
                      !!(
                        Z(undefined, "cls") ||
                        U.classPrefix ||
                        Q.a([
                          Q.a([
                            !!(Z(s, "value") || i || Z(U.innerValue, $A)) ||
                              undefined,
                          ]),
                        ]) ||
                        U.prefix
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        Y(
                          P(X(a).cls)(D.classPrefix + "__item", [
                            ["active", X(q).value === X(D.innerValue)[$A]],
                          ])
                        ) +
                          " " +
                          Y(D.prefix) +
                          "-class-middle-item scroll-into-view"
                      );
                    if (C || K || i) R.d(N, "level", g);
                    if (C || K || Z(s, "value")) R.d(N, "value", X(q).value);
                    if (C) R.v(N, "tap", "handleTreeClick", !1, !1, !1, !1);
                  },
                  v
                );
              },
              r = (C, s, t, u, v, w, T, E) => {
                var x = (C, T) => {
                  C || K || Z(u, "label") ? T(Y(X(s).label)) : T();
                };
                E(
                  "t-radio",
                  {},
                  (N, C) => {
                    if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                      L(
                        N,
                        "scroll-into-view " +
                          Y(D.classPrefix) +
                          "__radio-item " +
                          Y(D.prefix) +
                          "-class-right-item"
                      );
                    if (C || K || !!Z(u, "value") || undefined)
                      O(N, "tId", "scroll-to-" + Y(X(s).value));
                    if (C || K || !!U.prefix || undefined)
                      O(
                        N,
                        "t-class-label",
                        Y(D.prefix) + "-class-right-item-label"
                      );
                    if (C) O(N, "icon", "line");
                    if (C || K || Z(u, "value")) O(N, "value", X(s).value);
                    if (C || K || undefined) O(N, "maxLabelRow", 1);
                    if (C) O(N, "borderless", true);
                    if (C) O(N, "placement", "right");
                  },
                  x
                );
              },
              q = (C, T, E, B, F) => {
                var $A = g;
                F(
                  X(D.treeOptions)[$A],
                  "value",
                  U ? !!i || Z(U.treeOptions, $A) : undefined,
                  [0, "treeOptions", $A],
                  r
                );
              },
              t = (C, u, v, w, x, y, T, E) => {
                var z = (C, T) => {
                  C || K || Z(w, "label") ? T(Y(X(u).label)) : T();
                };
                E(
                  "t-checkbox",
                  {},
                  (N, C) => {
                    if (C || K || !!U.prefix || undefined)
                      L(
                        N,
                        "scroll-into-view " + Y(D.prefix) + "-class-right-item"
                      );
                    if (C) O(N, "placement", "right");
                    if (C) O(N, "icon", "line");
                    if (C || K || undefined) O(N, "maxLabelRow", 1);
                    if (C || K || !!Z(w, "value") || undefined)
                      O(N, "tId", "scroll-to-" + Y(X(u).value));
                    if (C || K || !!U.prefix || undefined)
                      O(
                        N,
                        "t-class-label",
                        Y(D.prefix) + "-class-right-item-label"
                      );
                    if (C) O(N, "borderless", true);
                    if (C || K || Z(w, "value")) O(N, "value", X(u).value);
                  },
                  z
                );
              },
              s = (C, T, E, B, F) => {
                var $A = g;
                F(
                  X(D.treeOptions)[$A],
                  "value",
                  U ? !!i || Z(U.treeOptions, $A) : undefined,
                  [0, "treeOptions", $A],
                  t
                );
              },
              m = (C, T, E, B, F) => {
                if (l === 1) {
                  E(
                    "t-side-bar",
                    {},
                    (N, C) => {
                      var $A = g;
                      if (C || K || !!i || Z(U.innerValue, $A))
                        O(N, "value", X(D.innerValue)[$A]);
                      if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                        O(
                          N,
                          "t-class",
                          Y(D.classPrefix) +
                            "-column " +
                            Y(D.prefix) +
                            "-class-left-column"
                        );
                      if (C) R.v(N, "change", "onRootChange", !1, !1, !1, !1);
                    },
                    n
                  );
                } else if (l === 2) {
                  var $A = g;
                  F(
                    X(D.treeOptions)[$A],
                    "value",
                    U ? !!i || Z(U.treeOptions, $A) : undefined,
                    [0, "treeOptions", $A],
                    p
                  );
                } else if (l === 3) {
                  E(
                    "t-radio-group",
                    {},
                    (N, C) => {
                      if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                        L(
                          N,
                          Y(D.classPrefix) +
                            "__radio " +
                            Y(D.prefix) +
                            "-class-right-column"
                        );
                      var $A = g;
                      if (C || K || !!i || Z(U.innerValue, $A))
                        O(N, "value", X(D.innerValue)[$A]);
                      if (C || K || i) R.d(N, "level", g);
                      if (C) R.d(N, "type", "single");
                      if (C) R.v(N, "change", "handleChange", !1, !1, !1, !1);
                    },
                    q
                  );
                } else {
                  E(
                    "t-checkbox-group",
                    {},
                    (N, C) => {
                      if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                        L(
                          N,
                          Y(D.classPrefix) +
                            "__checkbox " +
                            Y(D.prefix) +
                            "-class-right-column"
                        );
                      var $A = g;
                      if (
                        C ||
                        K ||
                        !!(i || Z(U.innerValue, $A) || Q.a([])) ||
                        undefined
                      )
                        O(N, "value", X(D.innerValue)[$A] || []);
                      if (C || K || i) R.d(N, "level", g);
                      if (C) R.d(N, "type", "multiple");
                      if (C) R.v(N, "change", "handleChange", !1, !1, !1, !1);
                    },
                    s
                  );
                }
              },
              k = (C, T, E, B) => {
                l = g == 0 ? 1 : g != D.leafLevel ? 2 : !D.multiple ? 3 : 0;
                B(l, m);
              };
            E(
              "t-scroll-view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      !!(
                        Z(undefined, "getTreeClass") ||
                        U.leafLevel ||
                        i ||
                        Z(U.treeOptions, "length")
                      ) || undefined,
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__column", [
                        P(X(b).getTreeClass)(
                          D.leafLevel - g,
                          X(D.treeOptions).length
                        ),
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class"
                  );
                var $B = g;
                var $A = D.scrollIntoView && X(D.scrollIntoView)[$B];
                var $C = g;
                if (
                  C ||
                  K ||
                  !!(U.scrollIntoView || i || Z(U.scrollIntoView, $B)) ||
                  ($A
                    ? !!(i || Z(U.scrollIntoView, $C)) || undefined
                    : undefined)
                )
                  O(
                    N,
                    "scrollIntoView",
                    $A
                      ? ".scroll-into-view >>> #scroll-to-" +
                          X(D.scrollIntoView)[$C]
                      : ""
                  );
              },
              k
            );
          },
          d = (C, T, E, B, F, S) => {
            F(
              D.treeOptions,
              "level",
              U ? U.treeOptions : undefined,
              [0, "treeOptions"],
              e
            );
            S("content");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + " class");
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([
                      !!(Z(undefined, "addUnit") || U.height) || undefined,
                      U.style,
                      U.customStyle,
                    ])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([
                      "height:" + P(X(a).addUnit)(D.height),
                      D.style,
                      D.customStyle,
                    ])
                  );
                A["height"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([
                          "height:" + P(X(a).addUnit)(D.height),
                          D.style,
                          D.customStyle,
                        ])
                      );
                    };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/scroll-view/scroll-view";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/scroll-view/scroll-view.js";
define(
  "miniprogram_npm/tdesign-miniprogram/scroll-view/scroll-view.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e,
      r = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      o = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      l = require("../common/src/index"),
      n =
        (e = require("../common/config")) && e.__esModule ? e : { default: e },
      a = require("../common/version");
    var u = n.default.prefix,
      c = (function (e) {
        i(l, e);
        var s = o(l);
        function l() {
          var e;
          return (
            t(this, l),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
            ]),
            (e.behaviors = (0, a.canUseProxyScrollView)()
              ? ["wx://proxy-scroll-view"]
              : []),
            (e.properties = { scrollIntoView: { type: String } }),
            e
          );
        }
        return r(l);
      })(l.SuperComponent),
      p = (c = (0, s.__decorate)([(0, l.wxComponent)()], c));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/scroll-view/scroll-view.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/scroll-view/scroll-view.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/side-bar-item/side-bar-item";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/side-bar-item/side-bar-item.js";
define(
  "miniprogram_npm/tdesign-miniprogram/side-bar-item/side-bar-item.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      s = require("../common/src/index"),
      n = u(require("../common/config")),
      l = u(require("./props"));
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = n.default.prefix,
      c = "".concat(o, "-side-bar-item"),
      d = (function (i) {
        r(n, i);
        var s = a(n);
        function n() {
          var e;
          return (
            t(this, n),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(o, "-class"),
            ]),
            (e.properties = Object.assign(Object.assign({}, l.default), {
              tId: { type: String },
            })),
            (e.relations = {
              "../side-bar/side-bar": {
                type: "parent",
                linked: function (e) {
                  (this.parent = e), this.updateActive(e.data.value);
                },
              },
            }),
            (e.observers = {
              icon: function (e) {
                this.setData({ _icon: "string" == typeof e ? { name: e } : e });
              },
            }),
            (e.data = {
              classPrefix: c,
              prefix: o,
              active: !1,
              isPre: !1,
              isNext: !1,
            }),
            (e.methods = {
              updateActive: function (e) {
                var t = e === this.data.value;
                this.setData({ active: t });
              },
              handleClick: function () {
                var e;
                if (!this.data.disabled) {
                  var t = this.data,
                    r = t.value,
                    a = t.label;
                  null === (e = this.parent) ||
                    void 0 === e ||
                    e.doChange({ value: r, label: a });
                }
              },
            }),
            e
          );
        }
        return e(n);
      })(s.SuperComponent),
      p = (d = (0, i.__decorate)([(0, s.wxComponent)()], d));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/side-bar-item/side-bar-item.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/side-bar-item/side-bar-item.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/side-bar/side-bar";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/side-bar/side-bar.js";
define(
  "miniprogram_npm/tdesign-miniprogram/side-bar/side-bar.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/defineProperty"),
      r = require("../../../@babel/runtime/helpers/createClass"),
      i = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      l = require("tslib"),
      a = require("../common/src/index"),
      s = o(require("../common/config")),
      u = o(require("./props"));
    function o(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var c = s.default.prefix,
      d = "".concat(c, "-side-bar"),
      p = (function (l) {
        t(s, l);
        var a = n(s);
        function s() {
          var r;
          return (
            i(this, s),
            ((r = a.apply(this, arguments)).externalClasses = [
              "".concat(c, "-class"),
            ]),
            (r.children = []),
            (r.relations = e({}, "../side-bar-item/side-bar-item", {
              type: "child",
              linked: function (e) {
                this.children.push(e);
              },
              unlinked: function (e) {
                var r = this.children.findIndex(function (r) {
                  return r === e;
                });
                this.children.splice(r, 1);
              },
            })),
            (r.controlledProps = [{ key: "value", event: "change" }]),
            (r.properties = u.default),
            (r.observers = {
              value: function (e) {
                this.$children.forEach(function (r) {
                  r.updateActive(e);
                });
              },
            }),
            (r.data = { classPrefix: d, prefix: c }),
            (r.methods = {
              doChange: function (e) {
                var r = e.value,
                  i = e.label;
                this._trigger("change", { value: r, label: i });
              },
            }),
            r
          );
        }
        return r(s);
      })(a.SuperComponent),
      h = (p = (0, l.__decorate)([(0, a.wxComponent)()], p));
    exports.default = h;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/side-bar/side-bar.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/side-bar/side-bar.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/tree-select/tree-select";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/tree-select/tree-select.js";
define(
  "miniprogram_npm/tdesign-miniprogram/tree-select/tree-select.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/slicedToArray"),
      l = require("../../../@babel/runtime/helpers/toConsumableArray"),
      t = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      u = require("../common/src/index"),
      o = require("../common/validator"),
      s = v(require("../common/config")),
      c = v(require("./props"));
    function v(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var h = s.default.prefix,
      d = "".concat(h, "-tree-select"),
      p = (function (n) {
        a(s, n);
        var u = i(s);
        function s() {
          var t;
          return (
            r(this, s),
            ((t = u.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
              "".concat(h, "-class-left-column"),
              "".concat(h, "-class-left-item"),
              "".concat(h, "-class-middle-item"),
              "".concat(h, "-class-right-column"),
              "".concat(h, "-class-right-item"),
              "".concat(h, "-class-right-item-label"),
            ]),
            (t.options = { multipleSlots: !0 }),
            (t.data = { prefix: h, classPrefix: d, scrollIntoView: null }),
            (t.properties = Object.assign(Object.assign({}, c.default), {
              customValue: { type: null, value: null },
            })),
            (t.controlledProps = [{ key: "value", event: "change" }]),
            (t.observers = {
              "value, customValue, options, keys, multiple": function () {
                this.buildTreeOptions();
              },
            }),
            (t.lifetimes = {
              ready: function () {
                this.getScrollIntoView("init");
              },
            }),
            (t.methods = {
              buildTreeOptions: function () {
                var t = this.data,
                  r = t.options,
                  a = t.value,
                  i = t.defaultValue,
                  n = t.customValue,
                  u = t.multiple,
                  o = t.keys,
                  s = [],
                  c = -1,
                  v = { children: r };
                if (0 !== r.length) {
                  for (
                    var h = function () {
                      c += 1;
                      var t = v.children.map(function (e) {
                          return {
                            label: e[(null == o ? void 0 : o.label) || "label"],
                            value: e[(null == o ? void 0 : o.value) || "value"],
                            children: e.children,
                          };
                        }),
                        r =
                          (null == n ? void 0 : n[c]) ||
                          (null == a ? void 0 : a[c]);
                      if ((s.push(l(t)), null == r)) {
                        var i = e(t, 1)[0];
                        v = i;
                      } else {
                        var u = t.find(function (e) {
                          return e.value === r;
                        });
                        v = null != u ? u : t[0];
                      }
                    };
                    v && v.children;

                  )
                    h();
                  var d = Math.max(0, c);
                  if (u) {
                    var p = n || a || i;
                    if (null != p[d] && !Array.isArray(p[d]))
                      throw TypeError("应传入数组类型的 value");
                  }
                  this.setData({
                    innerValue:
                      n ||
                      (null == s
                        ? void 0
                        : s.map(function (e, l) {
                            var t =
                              l === s.length - 1 && u
                                ? [e[0].value]
                                : e[0].value;
                            return (null == a ? void 0 : a[l]) || t;
                          })),
                    leafLevel: d,
                    treeOptions: s,
                  });
                }
              },
              getScrollIntoView: function (e) {
                var l = this.data,
                  t = l.value,
                  r = l.customValue,
                  a = l.scrollIntoView;
                if ("init" === e) {
                  var i = r || t,
                    n = Array.isArray(i)
                      ? i.map(function (e) {
                          return Array.isArray(e) ? e[0] : e;
                        })
                      : [i];
                  this.setData({ scrollIntoView: n });
                } else {
                  if (null === a) return;
                  this.setData({ scrollIntoView: null });
                }
              },
              onRootChange: function (e) {
                var l = this.data.innerValue,
                  t = e.detail.value;
                this.getScrollIntoView("none"),
                  (l[0] = t),
                  this._trigger("change", { value: l, level: 0 });
              },
              handleTreeClick: function (e) {
                var l = e.currentTarget.dataset,
                  t = l.level,
                  r = l.value,
                  a = this.data.innerValue;
                (a[t] = r),
                  this.getScrollIntoView("none"),
                  this._trigger("change", { value: a, level: 1 });
              },
              handleChange: function (e) {
                var l = this.data.innerValue,
                  t = e.target.dataset,
                  r = t.level,
                  a = t.type,
                  i = ("multiple" === a ? e.detail.context : e.detail).value;
                if ("multiple" === a) {
                  (0, o.isDef)(l[r]) || (l[r] = []);
                  var n = l[r].indexOf(i);
                  -1 === n ? l[r].push(i) : l[r].splice(n, 1);
                } else l[r] = i;
                this.getScrollIntoView("none"),
                  this._trigger("change", { value: l, level: r });
              },
            }),
            t
          );
        }
        return t(s);
      })(u.SuperComponent),
      f = (p = (0, n.__decorate)([(0, u.wxComponent)()], p));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/tree-select/tree-select.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/tree-select/tree-select.js");
