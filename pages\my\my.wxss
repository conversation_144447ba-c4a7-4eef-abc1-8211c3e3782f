page {
  background-color: #f5f5f5;
  box-sizing: border-box;
  min-height: 100vh;
  padding-bottom: 180rpx;
}
.dt_box {
  width: 100%;
}
.top_box {
  align-items: center;
  background-color: #fff;
  display: -webkit-flex;
  display: flex;
  padding: 30rpx;
}
.top_box_left {
  margin-right: 30rpx;
}
.top_box_left image {
  border-radius: 50%;
  height: 120rpx;
  width: 120rpx;
}
.edit-button {
  align-self: flex-end;
  background-color: #e9ecef;
  border: 3px solid #e9ecef;
  border-radius: 10rpx;
  color: #1e1e1e;
  font-size: 26rpx;
  margin-top: 30rpx;
  text-align: right;
  text-align: center;
}
.top_box_midle {
  display: -webkit-flex;
  display: flex;
  flex: 1;
  flex-direction: column;
}
.top_box_midle_username {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 30rpx;
}
.top_box_midle_shuoming {
  color: #888;
  font-size: 28rpx;
}
.button_box {
  background-color: #fff;
  display: -webkit-flex;
  display: flex;
  justify-content: flex-start;
  padding: 20rpx 30rpx;
}
.button_box_item {
  background-color: #f0f0f0;
  border-radius: 20rpx;
  color: #666;
  font-size: 28rpx;
  height: 70rpx;
  line-height: 70rpx;
  margin-left: 20rpx;
  text-align: center;
  transition: all 0.3s ease;
  width: 30%;
}
.button_box_item.active {
  background-color: #fdd;
  color: #f66;
}
.nr_box {
  margin: 0 auto;
  width: 95%;
}
.scroll-container {
  height: 100vh;
  width: 100%;
}
.container {
  min-height: 100%;
  padding-bottom: 20rpx;
}
.tags_box {
  margin: 20rpx auto;
  width: 95%;
}
.custom-tabs {
  background-color: initial !important;
  justify-content: flex-start !important;
}
.custom-tabs .t-tabs__item {
  color: #1e1e1e !important;
}
.custom-tabs .t-tabs__item--active {
  color: #ff4d4f !important;
}
.custom-track {
  background-color: #ff4d4f !important;
}
.fwdd_list {
  background: #fff;
  border-radius: 24rpx;
  margin: 20rpx;
  padding: 24rpx;
}
.fwdd_list_top {
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.fwdd_list_top,
.fwdd_list_top_left {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.fwdd_list_top_left {
  gap: 12rpx;
}
.avatar {
  border-radius: 50%;
  height: 36rpx;
  width: 36rpx;
}
.name {
  color: #333;
  font-size: 28rpx;
}
.id {
  color: #999;
  font-size: 24rpx;
}
.fwdd_list_top_right {
  display: -webkit-flex;
  display: flex;
  gap: 12rpx;
}
.btn-delivery,
.btn-service {
  border-radius: 8rpx;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
}
.btn-service {
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
}
.btn-delivery {
  border: 2rpx solid #ddd;
  color: #666;
}
.fwdd_list_info {
  border-bottom: 2rpx solid #f5f5f5;
  border-top: 2rpx solid #f5f5f5;
  display: -webkit-flex;
  display: flex;
  padding: 20rpx 0;
}
.service-icon {
  border-radius: 16rpx;
  height: 120rpx;
  width: 120rpx;
}
.fwdd_list_info_midle {
  display: -webkit-flex;
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20rpx;
}
.service-title {
  color: #333;
  font-size: 30rpx;
  font-weight: 500;
}
.attachment-box {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  margin: 8rpx 0;
}
.attachment-label {
  color: #2b5ee3;
  font-size: 26rpx;
}
.attachment-count {
  color: #666;
  font-size: 26rpx;
}
.download-hint {
  color: #999;
  font-size: 24rpx;
}
.fwdd_list_info_right {
  align-items: flex-end;
  flex-direction: column;
  justify-content: space-between;
  min-width: 120rpx;
  text-align: right;
}
.fwdd_list_info_right,
.price-box {
  display: -webkit-flex;
  display: flex;
}
.price-box {
  align-items: baseline;
}
.price-symbol {
  color: #333;
  font-size: 24rpx;
}
.price-value {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}
.quantity {
  color: #999;
  font-size: 24rpx;
  margin: 8rpx 0;
}
.total-price {
  color: #666;
  font-size: 24rpx;
}
.amount {
  color: #ff4d4f;
}
.fwdd_list_bottom {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}
.contact-btn {
  color: #2b5ee3;
  font-size: 28rpx;
}
.confirm-btn {
  border: 2rpx solid #ff4d4f;
  border-radius: 32rpx;
  color: #ff4d4f;
  font-size: 28rpx;
  padding: 8rpx 32rpx;
}
.empty-state {
  color: #999;
  padding: 60rpx 30rpx;
  text-align: center;
}
.empty-state text {
  color: #999;
  font-size: 28rpx;
}
