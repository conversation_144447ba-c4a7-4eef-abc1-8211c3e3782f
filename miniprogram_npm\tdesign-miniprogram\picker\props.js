Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = {
  autoClose: { type: Boolean, value: !0 },
  cancelBtn: { type: null, value: !0 },
  confirmBtn: { type: null, value: !0 },
  header: { type: Boolean, value: !0 },
  itemHeight: { type: Number, value: 80 },
  keys: { type: Object },
  popupProps: { type: Object, value: {} },
  title: { type: String, value: "" },
  usePopup: { type: Boolean, value: !0 },
  usingCustomNavbar: { type: Boolean, value: !1 },
  value: { type: Array, value: null },
  defaultValue: { type: Array },
  visible: { type: Boolean, value: !1 },
};
exports.default = e;
