__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/tab-bar/tab-bar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            classPrefix: new Array(1),
            style: new Array(1),
            bordered: new Array(1),
            shape: new Array(1),
            prefix: new Array(1),
            safeAreaInsetBottom: new Array(1),
            fixed: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([U.bordered]),
                      Q.a([U.fixed]),
                      Q.a([U.safeAreaInsetBottom]),
                      U.shape,
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["border", D.bordered],
                        ["fixed", D.fixed],
                        ["safe", D.safeAreaInsetBottom],
                        D.shape,
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["classPrefix"][0] =
                  A["bordered"][0] =
                  A["fixed"][0] =
                  A["safeAreaInsetBottom"][0] =
                  A["shape"][0] =
                  A["prefix"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(
                          P(X(a).cls)(D.classPrefix, [
                            ["border", D.bordered],
                            ["fixed", D.fixed],
                            ["safe", D.safeAreaInsetBottom],
                            D.shape,
                          ])
                        ) +
                          " class " +
                          Y(D.prefix) +
                          "-class"
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-role", "tablist");
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/tab-bar/tab-bar.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-tab-bar{-webkit-align-items:center;align-items:center;background-color:var(--td-tab-bar-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));box-sizing:border-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:nowrap;flex-wrap:nowrap;font-size:16px;position:relative}\n.",
        [1],
        "t-tab-bar--normal.",
        [1],
        "t-tab-bar--border::before{border-top:1px solid var(--td-tab-bar-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));box-sizing:border-box;content:\x22 \x22;left:0;pointer-events:none;position:absolute;right:0;top:0;-webkit-transform:scaleY(.5);transform:scaleY(.5);-webkit-transform-origin:0 0;transform-origin:0 0;z-index:1}\n.",
        [1],
        "t-tab-bar--fixed{bottom:0;left:0;position:fixed;right:0}\n.",
        [1],
        "t-tab-bar--normal.",
        [1],
        "t-tab-bar--safe{padding-bottom:env(safe-area-inset-bottom)}\n.",
        [1],
        "t-tab-bar--round{border-radius:999px;box-shadow:var(--td-tab-bar-round-shadow,var(--td-shadow-3,0 6px 30px 5px rgba(0,0,0,.05),0 16px 24px 2px rgba(0,0,0,.04),0 8px 10px -5px rgba(0,0,0,.08)));margin-left:",
        [0, 32],
        ";margin-right:",
        [0, 32],
        "}\n.",
        [1],
        "t-tab-bar--fixed.",
        [1],
        "t-tab-bar--round.",
        [1],
        "t-tab-bar--safe{bottom:constant(safe-area-inset-bottom);bottom:env(safe-area-inset-bottom)}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/tab-bar/tab-bar.wxss" }
    );
}
