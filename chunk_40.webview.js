__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/progress/progress": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/progress/progress"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { customStyle: new Array(1), style: new Array(1) },
          K = U === true,
          e,
          i = (C) => {},
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__inner " + Y(D.prefix) + "-class-bar"
                  );
                if (C || K || !!(U.colorBar || U.computedProgress) || undefined)
                  R.y(
                    N,
                    "background:" +
                      Y(D.colorBar) +
                      ";width:" +
                      Y(D.computedProgress + "%")
                  );
              },
              i
            );
          },
          j,
          m,
          o,
          p = (C, T, E, B, F, S, J) => {
            var $A = I(o);
            var $B = D.status;
            if (o && $A)
              $A(
                R,
                C,
                {
                  tClass: D.classPrefix + "__icon",
                  size: "44rpx",
                  name: X(X(b).LINE_STATUS_ICON)[$B],
                },
                K ||
                  (U
                    ? {
                        tClass: !!U.classPrefix || undefined,
                        name:
                          !!U.status || Z(Z(undefined, "LINE_STATUS_ICON"), $B),
                      }
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          q = (C, T) => {
            var $A = P(X(a).isString)(D.label);
            C ||
            K ||
            !!(Z(undefined, "isString") || U.label) ||
            ($A ? U.label : !!U.computedProgress || undefined)
              ? T(Y($A ? D.label : D.computedProgress + "%"))
              : T();
          },
          n = (C, T, E, B, F, S, J) => {
            if (m === 1) {
              o = "icon";
              B(o, p);
            } else {
              E("text", {}, (N, C) => {}, q);
            }
          },
          l = (C, T, E, B) => {
            m = P(X(a).includes)(X(b).STATUS, D.status) ? 1 : 0;
            B(m, n);
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__info " +
                        Y(D.prefix) +
                        "-class-label"
                    );
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                l
              );
            }
          },
          g = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__bar");
                if (
                  C ||
                  K ||
                  !!(U.heightBar || U.heightBar || U.bgColorBar) ||
                  undefined
                )
                  R.y(
                    N,
                    "height:" +
                      Y(D.heightBar) +
                      "px;border-radius:" +
                      Y(D.heightBar) +
                      "px;background-color:" +
                      Y(D.bgColorBar)
                  );
                if (C) O(N, "aria-role", "progressbar");
                if (C) O(N, "aria-valuemin", "0");
                if (C) O(N, "aria-valuemax", "100");
                if (C || K || U.computedProgress)
                  O(N, "aria-valuenow", D.computedProgress);
                var $A = D.isIOS;
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    U.isIOS ||
                    ($A
                      ? !!(Z(undefined, "getIOSAriaLabel") || U.status) ||
                        undefined
                      : !!(Z(undefined, "getAndroidAriaLabel") || U.status) ||
                        undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($A
                        ? P(X(b).getIOSAriaLabel)(D.status)
                        : P(X(b).getAndroidAriaLabel)(D.status))
                  );
                if (C) O(N, "aria-live", "polite");
              },
              h
            );
            j = D.label ? 1 : 0;
            B(j, k);
            S("label");
          },
          f = (C, T, E) => {
            if (e === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.status ||
                      U.computedStatus ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "--thin " +
                        Y(D.classPrefix) +
                        "--status--" +
                        Y(D.status || D.computedStatus) +
                        " " +
                        Y(D.prefix) +
                        "-class"
                    );
                },
                g
              );
            }
          },
          r,
          v,
          y = (C, T) => {
            var $A = P(X(a).isString)(D.label);
            C ||
            K ||
            !!(Z(undefined, "isString") || U.label) ||
            ($A ? U.label : !!U.computedProgress || undefined)
              ? T(Y($A ? D.label : D.computedProgress + "%"))
              : T();
          },
          x = (C, T, E) => {
            E("text", {}, (N, C) => {}, y);
          },
          w = (C, T, E) => {
            if (v === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__info " +
                        Y(D.prefix) +
                        "-class-label"
                    );
                },
                x
              );
            }
          },
          z,
          A0 = (C, T, E, B, F, S) => {
            if (z === 1) {
              S("label");
            }
          },
          u = (C, T, E, B) => {
            v = D.label && D.computedProgress > 10 ? 1 : 0;
            B(v, w);
            z = D.computedProgress > 10 ? 1 : 0;
            B(z, A0);
          },
          B0,
          E0 = (C, T) => {
            var $A = P(X(a).isString)(D.label);
            C ||
            K ||
            !!(Z(undefined, "isString") || U.label) ||
            ($A ? U.label : !!U.computedProgress || undefined)
              ? T(Y($A ? D.label : D.computedProgress + "%"))
              : T();
          },
          D0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, E0);
          },
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__info " +
                        Y(D.prefix) +
                        "-class-label"
                    );
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                D0
              );
            }
          },
          F0,
          G0 = (C, T, E, B, F, S) => {
            if (F0 === 1) {
              S("label");
            }
          },
          t = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__inner " + Y(D.prefix) + "-class-bar"
                  );
                if (C || K || !!(U.colorBar || U.computedProgress) || undefined)
                  R.y(
                    N,
                    "background:" +
                      Y(D.colorBar) +
                      ";width:" +
                      Y(D.computedProgress) +
                      "%"
                  );
              },
              u
            );
            B0 = D.label && D.computedProgress <= 10 ? 1 : 0;
            B(B0, C0);
            F0 = D.computedProgress <= 10 ? 1 : 0;
            B(F0, G0);
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.computedProgress > 10;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.computedProgress ||
                      ($A
                        ? !!U.classPrefix || undefined
                        : !!U.classPrefix || undefined) ||
                      U.classPrefix ||
                      U.status ||
                      U.computedStatus ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__bar " +
                        Y(D.classPrefix) +
                        "--plump " +
                        Y(
                          $A
                            ? D.classPrefix + "--over-ten"
                            : D.classPrefix + "--under-ten"
                        ) +
                        " " +
                        Y(D.classPrefix) +
                        "--status--" +
                        Y(D.status || D.computedStatus) +
                        " " +
                        Y(D.prefix) +
                        "-class"
                    );
                  if (
                    C ||
                    K ||
                    !!(U.heightBar || U.heightBar || U.bgColorBar) ||
                    undefined
                  )
                    R.y(
                      N,
                      "height:" +
                        Y(D.heightBar) +
                        "px;border-radius:" +
                        Y(D.heightBar) +
                        "px;background-color:" +
                        Y(D.bgColorBar)
                    );
                  if (C) O(N, "aria-role", "progressbar");
                  if (C) O(N, "aria-valuemin", "0");
                  if (C) O(N, "aria-valuemax", "100");
                  if (C || K || U.computedProgress)
                    O(N, "aria-valuenow", D.computedProgress);
                  var $B = D.isIOS;
                  if (
                    C ||
                    K ||
                    !!(
                      U.ariaLabel ||
                      U.isIOS ||
                      ($B
                        ? !!(Z(undefined, "getIOSAriaLabel") || U.status) ||
                          undefined
                        : !!(Z(undefined, "getAndroidAriaLabel") || U.status) ||
                          undefined)
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "aria-label",
                      D.ariaLabel ||
                        ($B
                          ? P(X(b).getIOSAriaLabel)(D.status)
                          : P(X(b).getAndroidAriaLabel)(D.status))
                    );
                  if (C) O(N, "aria-live", "polite");
                },
                t
              );
            }
          },
          H0,
          M0,
          P0,
          R0,
          S0 = (C, T, E, B, F, S, J) => {
            var $A = I(R0);
            var $B = D.status;
            if (R0 && $A)
              $A(
                R,
                C,
                {
                  tClass: D.classPrefix + "__icon",
                  size: "96rpx",
                  name: X(X(b).CIRCLE_STATUS_ICON)[$B],
                },
                K ||
                  (U
                    ? {
                        tClass: !!U.classPrefix || undefined,
                        name:
                          !!U.status ||
                          Z(Z(undefined, "CIRCLE_STATUS_ICON"), $B),
                      }
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          T0 = (C, T) => {
            var $A = P(X(a).isString)(D.label);
            C ||
            K ||
            !!(Z(undefined, "isString") || U.label) ||
            ($A ? U.label : !!U.computedProgress || undefined)
              ? T(Y($A ? D.label : D.computedProgress + "%"))
              : T();
          },
          Q0 = (C, T, E, B, F, S, J) => {
            if (P0 === 1) {
              R0 = "icon";
              B(R0, S0);
            } else {
              E("text", {}, (N, C) => {}, T0);
            }
          },
          O0 = (C, T, E, B) => {
            P0 = P(X(a).includes)(X(b).STATUS, D.status) ? 1 : 0;
            B(P0, Q0);
          },
          N0 = (C, T, E) => {
            if (M0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__info " +
                        Y(D.prefix) +
                        "-class-label"
                    );
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                O0
              );
            }
          },
          L0 = (C, T, E, B, F, S) => {
            M0 = D.label ? 1 : 0;
            B(M0, N0);
            S("label");
          },
          K0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__canvas--inner " +
                      Y(D.prefix) +
                      "-class-bar"
                  );
              },
              L0
            );
          },
          J0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.size])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__canvas--circle", [
                      [D.size, true],
                    ])
                  );
                var $A = D.status;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getCircleStyle") ||
                    U.size ||
                    U.heightBar ||
                    U.colorCircle ||
                    U.status ||
                    Z(Z(undefined, "STATUS_COLOR"), $A) ||
                    U.computedProgress ||
                    U.bgColorBar
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    Y(P(X(b).getCircleStyle)(D.size, D.heightBar)) +
                      ";background-image:conic-gradient(from var(--td-progress-circle-from), " +
                      Y(
                        D.colorCircle ||
                          X(X(b).STATUS_COLOR)[$A] ||
                          "var(--td-progress-inner-bg-color)"
                      ) +
                      " " +
                      Y(D.computedProgress) +
                      "%, " +
                      Y(D.bgColorBar || "var(--td-progress-track-bg-color)") +
                      " 0%);"
                  );
                if (C) O(N, "aria-role", "progressbar");
                if (C) O(N, "aria-valuemin", "0");
                if (C) O(N, "aria-valuemax", "100");
                if (C || K || U.computedProgress)
                  O(N, "aria-valuenow", D.computedProgress);
                var $B = D.isIOS;
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    U.isIOS ||
                    ($B
                      ? !!(Z(undefined, "getIOSAriaLabel") || U.status) ||
                        undefined
                      : !!(Z(undefined, "getAndroidAriaLabel") || U.status) ||
                        undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($B
                        ? P(X(b).getIOSAriaLabel)(D.status)
                        : P(X(b).getAndroidAriaLabel)(D.status))
                  );
                if (C) O(N, "aria-live", "polite");
              },
              K0
            );
          },
          I0 = (C, T, E) => {
            if (H0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.status ||
                      U.computedStatus ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "--status--" +
                        Y(D.status || D.computedStatus) +
                        " " +
                        Y(D.prefix) +
                        "-class"
                    );
                },
                J0
              );
            }
          },
          d = (C, T, E, B) => {
            e = D.theme === X(X(b).PRO_THEME).LINE ? 1 : 0;
            B(e, f);
            r = D.theme === X(X(b).PRO_THEME).PLUMP ? 1 : 0;
            B(r, s);
            H0 = D.theme === X(X(b).PRO_THEME).CIRCLE ? 1 : 0;
            B(H0, I0);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + " class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/progress/progress.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-progress{--td-progress-inner-bg-color:var(--td-brand-color,var(--td-primary-color-7,#0052d9));--td-progress-track-bg-color:var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7));--td-progress-circle-from:0deg}\n.",
        [1],
        "t-progress__inner{background:var(--td-progress-inner-bg-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));height:100%;position:relative;transition:all var(--td-anim-duration-base,.2s) var(--td-anim-time-fn-easing,cubic-bezier(.38,0,.24,1))}\n.",
        [1],
        "t-progress__bar,.",
        [1],
        "t-progress__inner{border-radius:var(--td-radius-round,999px)}\n.",
        [1],
        "t-progress__bar{background:var(--td-progress-track-bg-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));height:var(--td-progress-line-stroke-width,",
        [0, 12],
        ");overflow:hidden;width:100%}\n.",
        [1],
        "t-progress__info{box-sizing:border-box;color:var(--td-progress-info-dark-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-inline-flex;display:inline-flex;padding-left:var(--td-spacer,",
        [0, 16],
        ");white-space:nowrap}\n.",
        [1],
        "t-progress--thin{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between}\n.",
        [1],
        "t-progress--thin .",
        [1],
        "t-progress__icon{font-size:calc(var(--td-font-size-base,",
        [0, 28],
        ") + 2px)}\n.",
        [1],
        "t-progress--plump{-webkit-align-items:center;align-items:center;border-radius:calc(var(--td-progress-stroke-plump-width,",
        [0, 40],
        ")/ 2);display:-webkit-flex;display:flex;height:var(--td-progress-stroke-plump-width,",
        [0, 40],
        ")}\n.",
        [1],
        "t-progress--plump .",
        [1],
        "t-progress__info{font-size:var(--td-font-size-s,",
        [0, 24],
        ")}\n.",
        [1],
        "t-progress--over-ten .",
        [1],
        "t-progress__info{color:var(--td-progress-info-light-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)));position:absolute;right:var(--td-spacer,",
        [0, 16],
        ");top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.",
        [1],
        "t-progress--under-ten .",
        [1],
        "t-progress__info,.",
        [1],
        "t-progress--under-ten .",
        [1],
        "t-progress__inner{display:inline-block}\n.",
        [1],
        "t-progress--under-ten .",
        [1],
        "t-progress__info{vertical-align:top}\n.",
        [1],
        "t-progress__canvas--circle{border-radius:var(--td-radius-circle,50%);height:var(--td-progress-circle-width,",
        [0, 224],
        ");position:relative;width:var(--td-progress-circle-width,",
        [0, 224],
        ")}\n.",
        [1],
        "t-progress__canvas--circle .",
        [1],
        "t-progress__canvas--inner{-webkit-align-items:center;align-items:center;background-color:var(--td-progress-circle-inner-bg-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)));border-radius:var(--td-radius-circle,50%);display:-webkit-flex;display:flex;-webkit-flex-direction:row;flex-direction:row;height:calc(100% - var(--td-progress-stroke-circle-width,",
        [0, 12],
        ")*2);-webkit-justify-content:center;justify-content:center;left:50%;position:absolute;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:calc(100% - var(--td-progress-stroke-circle-width,",
        [0, 12],
        ")*2)}\n.",
        [1],
        "t-progress__canvas--circle .",
        [1],
        "t-progress__info{-webkit-box-orient:vertical;-webkit-line-clamp:2;display:-webkit-box;font-size:var(--td-progress-circle-label-font-size,",
        [0, 40],
        ");font-weight:var(--td-progress-circle-label-font-weight,700);line-height:var(--td-progress-circle-label-line-height,",
        [0, 56],
        ");margin:0;overflow:hidden;text-overflow:ellipsis}\n.",
        [1],
        "t-progress__canvas--circle--micro{--td-progress-circle-width:",
        [0, 48],
        ";--td-progress-stroke-circle-width:",
        [0, 4],
        ";--td-progress-circle-icon-font-size:",
        [0, 40],
        "}\n.",
        [1],
        "t-progress--status--active .",
        [1],
        "t-progress__inner::before{-webkit-animation:progress-active-animation 2s cubic-bezier(.23,.99,.86,.2) infinite;animation:progress-active-animation 2s cubic-bezier(.23,.99,.86,.2) infinite;background:var(--td-progress-inner-bg-color-active,var(--td-bg-color-container,var(--td-font-white-1,#fff)));bottom:0;content:\x22\x22;left:0;opacity:.2;position:absolute;right:0;top:0;z-index:1}\n.",
        [1],
        "t-progress--status--success .",
        [1],
        "t-progress__inner{background:var(--td-progress-inner-bg-color-success,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
        [1],
        "t-progress--status--success .",
        [1],
        "t-progress__icon{color:var(--td-success-color,var(--td-success-color-5,#2ba471))}\n.",
        [1],
        "t-progress--status--warning .",
        [1],
        "t-progress__inner{background:var(--td-progress-inner-bg-color-warning,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
        [1],
        "t-progress--status--warning .",
        [1],
        "t-progress__icon{color:var(--td-warning-color,var(--td-warning-color-5,#e37318))}\n.",
        [1],
        "t-progress--status--error .",
        [1],
        "t-progress__inner{background:var(--td-progress-inner-bg-color-error,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-progress--status--error .",
        [1],
        "t-progress__icon{color:var(--td-error-color,var(--td-error-color-6,#d54941))}\n@-webkit-keyframes progress-active-animation{0%{opacity:.1;width:0}\n35%{opacity:.4;width:50%}\n100%{opacity:0;width:100%}\n}@keyframes progress-active-animation{0%{opacity:.1;width:0}\n35%{opacity:.4;width:50%}\n100%{opacity:0;width:100%}\n}",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/progress/progress.wxss" }
    );
}
