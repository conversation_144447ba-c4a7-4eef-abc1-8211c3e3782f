__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/slider/slider": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/slider/slider"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { customStyle: new Array(1), style: new Array(1) },
          K = U === true,
          e,
          g,
          i = (C, T) => {
            var $A = D.label;
            C ||
            K ||
            !!U.label ||
            ($A
              ? !!(Z(undefined, "getValue") || U.label || U.min) || undefined
              : U.min)
              ? T(Y($A ? P(X(a).getValue)(D.label, D.min) : D.min))
              : T();
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__value " +
                        Y(D.classPrefix) +
                        "__value--min"
                    );
                },
                i
              );
            }
          },
          k,
          m = (C, n, o, p, q, r, T, E) => {
            var t,
              v = (C, T) => {
                var $A = o;
                C || K || !!q || Z(U.scaleTextArray, $A)
                  ? T(Y(X(D.scaleTextArray)[$A]))
                  : T();
              },
              u = (C, T, E) => {
                if (t === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (
                        C ||
                        K ||
                        !!(
                          Z(undefined, "cls") ||
                          U.classPrefix ||
                          Q.a([U.theme])
                        ) ||
                        undefined
                      )
                        L(
                          N,
                          P(X(b).cls)(D.classPrefix + "__scale-desc", [D.theme])
                        );
                    },
                    v
                  );
                }
              },
              s = (C, T, E, B) => {
                t = X(D.scaleTextArray).length ? 1 : 0;
                B(t, u);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([!!(U._value || Z(p, "val")) || undefined]),
                      Q.a([U.disabled]),
                      U.theme,
                      Q.a([
                        !!(
                          q ||
                          q ||
                          Z(U.scaleArray, "length") ||
                          U.theme ||
                          U.value ||
                          Z(p, "val")
                        ) || undefined,
                      ]),
                    ])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__scale-item", [
                      ["active", D._value >= X(n).val],
                      ["disabled", D.disabled],
                      D.theme,
                      [
                        "hidden",
                        ((o == 0 || o == X(D.scaleArray).length - 1) &&
                          D.theme == "capsule") ||
                          D.value == X(n).val,
                      ],
                    ])
                  );
                var $A = D.vertical;
                var $B = D.vertical;
                if (
                  C ||
                  K ||
                  !!(
                    U.vertical ||
                    ($A ? undefined : undefined) ||
                    Z(p, "left") ||
                    U.vertical ||
                    ($B ? undefined : undefined)
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    Y($A ? "top" : "left") +
                      ":" +
                      Y(X(n).left) +
                      "px; " +
                      Y(
                        $B
                          ? "transform: translate(-50%, -50%);"
                          : "transform: translateX(-50%);"
                      )
                  );
                if (C || K || undefined) O(N, "aria-hidden", true);
              },
              s
            );
          },
          l = (C, T, E, B, F) => {
            if (k === 1) {
              F(
                D.scaleArray,
                "index",
                U ? U.scaleArray : undefined,
                [0, "scaleArray"],
                m
              );
            }
          },
          p,
          r = (C, T) => {
            C ||
            K ||
            !!(Z(undefined, "getValue") || U.label || U._value || U._value) ||
            undefined
              ? T(Y(P(X(a).getValue)(D.label, D._value) || D._value))
              : T();
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([!!U.label || undefined])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(b).cls)(D.classPrefix + "__dot-value", [
                        ["sr-only", !D.label],
                      ])
                    );
                  if (C) O(N, "aria-role", "alert");
                  if (C) O(N, "aria-live", "assertive");
                  if (C || K || !!U.isVisibleToScreenReader || undefined)
                    O(N, "aria-hidden", !D.isVisibleToScreenReader);
                },
                r
              );
            }
          },
          s = (C) => {},
          o = (C, T, E, B) => {
            p = D.label || D.isVisibleToScreenReader ? 1 : 0;
            B(p, q);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__dot-slider");
                if (C) O(N, "aria-role", "slider");
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                if (C || K || U.max) O(N, "aria-valuemax", D.max);
                if (C || K || U.min) O(N, "aria-valuemin", D.min);
                if (C || K || U._value) O(N, "aria-valuenow", D._value);
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getValue") ||
                    U.label ||
                    U._value ||
                    U._value
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-valuetext",
                    P(X(a).getValue)(D.label, D._value) || D._value
                  );
              },
              s
            );
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__dot " + Y(D.prefix) + "-class-cursor"
                  );
                if (C) R.v(N, "touchstart", "onTouchStart", !0, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onSingleLineTap", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.i(N, "singleDot");
              },
              o
            );
          },
          j = (C, T, E, B) => {
            k = D.isScale ? 1 : 0;
            B(k, l);
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.disabled]), U.theme]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__line", [
                        ["disabled", D.disabled],
                        D.theme,
                        "single",
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-bar-active"
                  );
                var $A = D.vertical;
                if (
                  C ||
                  K ||
                  !!(
                    U.vertical ||
                    ($A ? undefined : undefined) ||
                    U.lineBarWidth
                  ) ||
                  undefined
                )
                  R.y(N, Y($A ? "height" : "width") + ": " + Y(D.lineBarWidth));
              },
              n
            );
          },
          t,
          v = (C, T) => {
            var $A = D.label;
            C ||
            K ||
            !!U.label ||
            ($A
              ? !!(Z(undefined, "getValue") || U.label || U.max) || undefined
              : U.max)
              ? T(Y($A ? P(X(a).getValue)(D.label, D.max) : D.max))
              : T();
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__value " +
                        Y(D.classPrefix) +
                        "__value--max"
                    );
                },
                v
              );
            }
          },
          f = (C, T, E, B) => {
            if (e === 1) {
              g = D.showExtremeValue ? 1 : 0;
              B(g, h);
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([U.disabled]),
                        U.theme,
                        Q.a([!!(U.isScale || U.theme) || undefined]),
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(b).cls)(D.classPrefix + "__bar", [
                          ["disabled", D.disabled],
                          D.theme,
                          ["marks", D.isScale && D.theme == "capsule"],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-bar"
                    );
                  if (C) R.v(N, "tap", "onSingleLineTap", !1, !1, !1, !1);
                  if (C) R.i(N, "sliderLine");
                },
                j
              );
              t = D.showExtremeValue ? 1 : 0;
              B(t, u);
            }
          },
          w,
          y,
          A0 = (C, T) => {
            C || K || U.min ? T(Y(D.min)) : T();
          },
          z = (C, T, E) => {
            if (y === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__range-extreme " +
                        Y(D.classPrefix) +
                        "__range-extreme--min"
                    );
                },
                A0
              );
            }
          },
          C0,
          E0 = (C, F0, G0, H0, I0, J0, T, E) => {
            var L0,
              N0 = (C, T) => {
                var $A = G0;
                C || K || !!I0 || Z(U.scaleTextArray, $A)
                  ? T(Y(X(D.scaleTextArray)[$A]))
                  : T();
              },
              M0 = (C, T, E) => {
                if (L0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (
                        C ||
                        K ||
                        !!(
                          Z(undefined, "cls") ||
                          U.classPrefix ||
                          Q.a([U.theme])
                        ) ||
                        undefined
                      )
                        L(
                          N,
                          P(X(b).cls)(D.classPrefix + "__scale-desc", [D.theme])
                        );
                    },
                    N0
                  );
                }
              },
              K0 = (C, T, E, B) => {
                L0 = X(D.scaleTextArray).length ? 1 : 0;
                B(L0, M0);
              };
            E(
              "view",
              {},
              (N, C) => {
                var $A = 1;
                var $B = 0;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([
                        !!(
                          Z(U.dotTopValue, $A) ||
                          Z(H0, "val") ||
                          Z(H0, "val") ||
                          Z(U.dotTopValue, $B)
                        ) || undefined,
                      ]),
                      Q.a([U.disabled]),
                      U.theme,
                      Q.a([
                        !!(
                          I0 ||
                          I0 ||
                          Z(U.scaleArray, "length") ||
                          U.theme ||
                          U.value ||
                          Z(H0, "val")
                        ) || undefined,
                      ]),
                    ])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__scale-item", [
                      [
                        "active",
                        X(D.dotTopValue)[$A] >= X(F0).val &&
                          X(F0).val >= X(D.dotTopValue)[$B],
                      ],
                      ["disabled", D.disabled],
                      D.theme,
                      [
                        "hidden",
                        ((G0 == 0 || G0 == X(D.scaleArray).length - 1) &&
                          D.theme == "capsule") ||
                          D.value == X(F0).val,
                      ],
                    ])
                  );
                var $C = D.vertical;
                var $D = D.vertical;
                if (
                  C ||
                  K ||
                  !!(
                    U.vertical ||
                    ($C ? undefined : undefined) ||
                    Z(H0, "left") ||
                    U.vertical ||
                    ($D ? undefined : undefined)
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    Y($C ? "top" : "left") +
                      ":" +
                      Y(X(F0).left) +
                      "px; " +
                      Y(
                        $D
                          ? "transform: translate(-50%, -50%);"
                          : "transform: translateX(-50%);"
                      )
                  );
                if (C || K || undefined) O(N, "aria-hidden", true);
              },
              K0
            );
          },
          D0 = (C, T, E, B, F) => {
            if (C0 === 1) {
              F(
                D.scaleArray,
                "index",
                U ? U.scaleArray : undefined,
                [0, "scaleArray"],
                E0
              );
            }
          },
          H0,
          J0 = (C, T) => {
            var $A = 0;
            var $B = 0;
            C ||
            K ||
            !!(
              Z(undefined, "getValue") ||
              U.label ||
              Z(U.dotTopValue, $A) ||
              Z(U.dotTopValue, $B)
            ) ||
            undefined
              ? T(
                  Y(
                    P(X(a).getValue)(D.label, X(D.dotTopValue)[$A]) ||
                      X(D.dotTopValue)[$B]
                  )
                )
              : T();
          },
          I0 = (C, T, E) => {
            if (H0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([!!U.label || undefined])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(b).cls)(D.classPrefix + "__dot-value", [
                        ["sr-only", !D.label],
                      ])
                    );
                  if (C) O(N, "aria-role", "alert");
                  if (C) O(N, "aria-live", "assertive");
                  if (C || K || !!U.isVisibleToScreenReader || undefined)
                    O(N, "aria-hidden", !D.isVisibleToScreenReader);
                },
                J0
              );
            }
          },
          K0 = (C) => {},
          G0 = (C, T, E, B) => {
            H0 = D.label || D.isVisibleToScreenReader ? 1 : 0;
            B(H0, I0);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__dot-slider");
                if (C) O(N, "aria-role", "slider");
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                if (C || K || U.max) O(N, "aria-valuemax", D.max);
                if (C || K || U.min) O(N, "aria-valuemin", D.min);
                var $A = 0;
                if (C || K || Z(U.dotTopValue, $A))
                  O(N, "aria-valuenow", X(D.dotTopValue)[$A]);
                var $B = 0;
                var $C = 0;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getValue") ||
                    U.label ||
                    Z(U.dotTopValue, $B) ||
                    Z(U.dotTopValue, $C)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-valuetext",
                    P(X(a).getValue)(D.label, X(D.dotTopValue)[$B]) ||
                      X(D.dotTopValue)[$C]
                  );
              },
              K0
            );
          },
          M0,
          O0 = (C, T) => {
            var $A = 1;
            var $B = 1;
            C ||
            K ||
            !!(
              Z(undefined, "getValue") ||
              U.label ||
              Z(U.dotTopValue, $A) ||
              Z(U.dotTopValue, $B)
            ) ||
            undefined
              ? T(
                  Y(
                    P(X(a).getValue)(D.label, X(D.dotTopValue)[$A]) ||
                      X(D.dotTopValue)[$B]
                  )
                )
              : T();
          },
          N0 = (C, T, E) => {
            if (M0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([!!U.label || undefined])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(b).cls)(D.classPrefix + "__dot-value", [
                        ["sr-only", !D.label],
                      ])
                    );
                  if (C) O(N, "aria-role", "alert");
                  if (C) O(N, "aria-live", "assertive");
                  if (C || K || !!U.isVisibleToScreenReader || undefined)
                    O(N, "aria-hidden", !D.isVisibleToScreenReader);
                },
                O0
              );
            }
          },
          P0 = (C) => {},
          L0 = (C, T, E, B) => {
            M0 = D.label || D.isVisibleToScreenReader ? 1 : 0;
            B(M0, N0);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__dot-slider");
                if (C) O(N, "aria-role", "slider");
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                if (C || K || U.max) O(N, "aria-valuemax", D.max);
                if (C || K || U.min) O(N, "aria-valuemin", D.min);
                var $A = 1;
                if (C || K || Z(U.dotTopValue, $A))
                  O(N, "aria-valuenow", X(D.dotTopValue)[$A]);
                var $B = 1;
                var $C = 1;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getValue") ||
                    U.label ||
                    Z(U.dotTopValue, $B) ||
                    Z(U.dotTopValue, $C)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-valuetext",
                    P(X(a).getValue)(D.label, X(D.dotTopValue)[$B]) ||
                      X(D.dotTopValue)[$C]
                  );
              },
              P0
            );
          },
          F0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.prefix) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__dot " +
                      Y(D.classPrefix) +
                      "__dot--left " +
                      Y(D.prefix) +
                      "-class-cursor"
                  );
                if (C) R.v(N, "touchstart", "onTouchStart", !0, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onTouchMoveLeft", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.i(N, "leftDot");
              },
              G0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.prefix) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__dot " +
                      Y(D.classPrefix) +
                      "__dot--right " +
                      Y(D.prefix) +
                      "-class-cursor"
                  );
                if (C) R.v(N, "touchstart", "onTouchStart", !0, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onTouchMoveRight", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.i(N, "rightDot");
              },
              L0
            );
          },
          B0 = (C, T, E, B) => {
            C0 = D.isScale ? 1 : 0;
            B(C0, D0);
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.disabled]), U.theme]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__line", [
                        ["disabled", D.disabled],
                        D.theme,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-bar-active"
                  );
                var $A = D.vertical;
                var $B = D.vertical;
                if (
                  C ||
                  K ||
                  !!(
                    U.vertical ||
                    ($A ? undefined : undefined) ||
                    U.lineLeft ||
                    U.vertical ||
                    ($B ? undefined : undefined) ||
                    U.lineRight
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    Y($A ? "top" : "left") +
                      ": " +
                      Y(D.lineLeft) +
                      "px; " +
                      Y($B ? "bottom" : "right") +
                      ": " +
                      Y(D.lineRight) +
                      "px"
                  );
              },
              F0
            );
          },
          Q0,
          S0 = (C, T) => {
            C || K || U.max ? T(Y(D.max)) : T();
          },
          R0 = (C, T, E) => {
            if (Q0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__range-extreme " +
                        Y(D.classPrefix) +
                        "__range-extreme--max"
                    );
                },
                S0
              );
            }
          },
          x = (C, T, E, B) => {
            if (w === 1) {
              y = D.showExtremeValue ? 1 : 0;
              B(y, z);
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([U.disabled]),
                        U.theme,
                        Q.a([!!(U.isScale || U.theme) || undefined]),
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(b).cls)(D.classPrefix + "__bar", [
                          ["disabled", D.disabled],
                          D.theme,
                          ["marks", D.isScale && D.theme == "capsule"],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-bar"
                    );
                  if (C) R.v(N, "tap", "onLineTap", !1, !1, !1, !1);
                  if (C) R.i(N, "sliderLine");
                },
                B0
              );
              Q0 = D.showExtremeValue ? 1 : 0;
              B(Q0, R0);
            }
          },
          d = (C, T, E, B) => {
            e = !D.range ? 1 : 0;
            B(e, f);
            w = D.range ? 1 : 0;
            B(w, x);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.vertical;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([
                        !!(U.label || Z(U.scaleTextArray, "length")) ||
                          undefined,
                      ]),
                      Q.a([U.disabled]),
                      Q.a([U.range]),
                    ]) ||
                    U.prefix ||
                    U.vertical ||
                    ($A ? !!U.classPrefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix, [
                        ["top", D.label || X(D.scaleTextArray).length],
                        ["disabled", D.disabled],
                        ["range", D.range],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class " +
                      Y($A ? D.classPrefix + "--vertical" : "")
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/slider/slider.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-slider{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;font-size:14px;width:100%}\n.",
        [1],
        "t-slider--disabled .",
        [1],
        "t-slider__dot-value,.",
        [1],
        "t-slider--disabled .",
        [1],
        "t-slider__range-extreme,.",
        [1],
        "t-slider--disabled .",
        [1],
        "t-slider__scale-desc,.",
        [1],
        "t-slider--disabled .",
        [1],
        "t-slider__value{color:var(--td-slider-disabled-text-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-slider--disabled .",
        [1],
        "t-slider__dot{background-color:var(--td-slider-dot-disabled-bg-color,#fff);border-color:var(--td-slider-dot-disabled-border-color,#f3f3f3)}\n.",
        [1],
        "t-slider--top{padding-top:",
        [0, 40],
        "}\n.",
        [1],
        "t-slider__line{background-color:var(--td-slider-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:calc(var(--td-slider-bar-height,",
        [0, 8],
        ")/ 2);height:var(--td-slider-bar-height,",
        [0, 8],
        ");position:absolute;top:0}\n.",
        [1],
        "t-slider__line--disabled{background-color:var(--td-slider-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-slider__line--capsule{height:var(--td-slider-capsule-line-heihgt,",
        [0, 36],
        ")}\n.",
        [1],
        "t-slider__line--capsule.",
        [1],
        "t-slider__line--single{border-bottom-left-radius:calc(var(--td-slider-capsule-line-heihgt,",
        [0, 36],
        ")/ 2);border-top-left-radius:calc(var(--td-slider-capsule-line-heihgt,",
        [0, 36],
        ")/ 2)}\n.",
        [1],
        "t-slider__dot{background-color:var(--td-slider-dot-bg-color,#fff);border:",
        [0, 2],
        " solid var(--td-slider-dot-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));border-radius:50%;box-shadow:var(--td-shadow-1,0 1px 10px rgba(0,0,0,.05),0 4px 5px rgba(0,0,0,.08),0 2px 4px -1px rgba(0,0,0,.12));box-sizing:border-box;height:var(--td-slider-dot-size,",
        [0, 40],
        ");position:absolute;right:0;top:50%;-webkit-transform:translate3d(50%,-50%,0);transform:translate3d(50%,-50%,0);width:var(--td-slider-dot-size,",
        [0, 40],
        ");z-index:2}\n.",
        [1],
        "t-slider__dot--left{left:0;-webkit-transform:translate3d(-50%,-50%,0);transform:translate3d(-50%,-50%,0)}\n.",
        [1],
        "t-slider__dot-value{height:",
        [0, 44],
        ";left:50%;line-height:",
        [0, 44],
        ";position:relative;text-align:center;top:",
        [0, -52],
        ";-webkit-transform:translateX(-50%);transform:translateX(-50%);width:",
        [0, 96],
        "}\n.",
        [1],
        "t-slider__dot-value,.",
        [1],
        "t-slider__range-extreme,.",
        [1],
        "t-slider__value{color:var(--td-slider-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-slider__dot-value--sr-only,.",
        [1],
        "t-slider__range-extreme--sr-only,.",
        [1],
        "t-slider__value--sr-only{clip:rect(0,0,0,0);border:0;-webkit-clip-path:inset(50%);clip-path:inset(50%);height:1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}\n.",
        [1],
        "t-slider__dot-slider{height:100%;left:0;position:absolute;top:0;width:100%}\n.",
        [1],
        "t-slider__value--min{margin-left:",
        [0, 32],
        "}\n.",
        [1],
        "t-slider__value--max{margin-right:",
        [0, 32],
        "}\n.",
        [1],
        "t-slider__value--right{-webkit-flex-basis:",
        [0, 80],
        ";flex-basis:",
        [0, 80],
        "}\n.",
        [1],
        "t-slider__value--right__value--text{display:block;margin-right:",
        [0, 32],
        ";text-align:right}\n.",
        [1],
        "t-slider__bar{background-clip:content-box;background-color:var(--td-slider-default-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));border-radius:calc(var(--td-slider-bar-height,",
        [0, 8],
        ")/ 2);-webkit-flex:10;flex:10;height:var(--td-slider-bar-height,",
        [0, 8],
        ");margin:",
        [0, 16],
        " ",
        [0, 32],
        ";position:relative}\n.",
        [1],
        "t-slider__bar--capsule{background-color:var(--td-slider-capsule-bar-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));border:",
        [0, 6],
        " solid var(--td-slider-capsule-bar-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));border-radius:calc(var(--td-slider-capsule-bar-heihgt,",
        [0, 48],
        ")/ 2);box-sizing:border-box;height:var(--td-slider-capsule-bar-heihgt,",
        [0, 48],
        ")}\n.",
        [1],
        "t-slider__bar--marks{background-color:var(--td-slider-default-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)))}\n.",
        [1],
        "t-slider__bar--disabled{background-color:var(--td-slider-default-color,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)))}\n.",
        [1],
        "t-slider__range-extreme--min{margin-left:",
        [0, 32],
        ";text-align:left}\n.",
        [1],
        "t-slider__range-extreme--max{margin-right:",
        [0, 32],
        ";text-align:right}\n.",
        [1],
        "t-slider__scale-item{background-color:var(--td-slider-default-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));border-radius:50%;height:",
        [0, 16],
        ";margin-top:",
        [0, -8],
        ";position:absolute;top:50%;width:var(--td-slider-bar-height,",
        [0, 8],
        ");width:",
        [0, 16],
        ";z-index:1}\n.",
        [1],
        "t-slider__scale-item--active{background-color:var(--td-slider-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-slider__scale-item--disabled{background-color:var(--td-slider-default-color,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)))}\n.",
        [1],
        "t-slider__scale-item--active.",
        [1],
        "t-slider__scale-item--disabled{background-color:var(--td-slider-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-slider__scale-item--capsule{background-color:var(--td-slider-capsule-bar-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));border-radius:0;height:var(--td-slider-capsule-line-heihgt,",
        [0, 36],
        ");margin-top:calc(-.5 * var(--td-slider-capsule-line-heihgt,",
        [0, 36],
        "));width:",
        [0, 4],
        "}\n.",
        [1],
        "t-slider__scale-item--hidden{background-color:initial}\n.",
        [1],
        "t-slider__scale-desc{bottom:",
        [0, 32],
        ";color:var(--td-slider-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));left:50%;position:absolute;-webkit-transform:translateX(-50%);transform:translateX(-50%)}\n.",
        [1],
        "t-slider__scale-desc--capsule{bottom:",
        [0, 46],
        "}\n.",
        [1],
        "t-slider--vertical{--td-slider-bar-height:",
        [0, 400],
        ";height:var(--td-slider-bar-height,",
        [0, 8],
        ");-webkit-justify-content:center;justify-content:center;position:relative}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__bar{-webkit-flex:none;flex:none;height:100%;width:var(--td-slider-bar-width,",
        [0, 8],
        ")}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__bar--capsule{border-radius:calc(var(--td-slider-capsule-bar-width,",
        [0, 48],
        ")/ 2);width:var(--td-slider-capsule-bar-width,",
        [0, 48],
        ")}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__line{border-radius:calc(var(--td-slider-bar-width,",
        [0, 8],
        ")/ 2);height:unset;left:0;width:100%}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__line--capsule.",
        [1],
        "t-slider__line--single{border-top-left-radius:calc(var(--td-slider-capsule-line-heihgt,",
        [0, 36],
        ")/ 2);border-top-right-radius:calc(var(--td-slider-capsule-line-heihgt,",
        [0, 36],
        ")/ 2)}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__dot{top:100%}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__dot,.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__dot--left{left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__dot--left{top:0}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__dot--right{left:50%;top:100%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__dot-value{left:",
        [0, 54],
        ";top:50%;-webkit-transform:translate(0,-50%);transform:translate(0,-50%);width:auto}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__range-extreme{left:50%;margin:0;position:absolute;-webkit-transform:translateX(-50%);transform:translateX(-50%)}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__range-extreme--min{top:0}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__range-extreme--max{bottom:0}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__scale-item{left:50%;margin-top:0}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__scale-item--capsule{height:",
        [0, 4],
        ";width:var(--td-slider-capsule-line-heihgt,",
        [0, 36],
        ")}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__scale-desc{bottom:unset;left:",
        [0, 38],
        ";top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.",
        [1],
        "t-slider--vertical .",
        [1],
        "t-slider__scale-desc--capsule{left:",
        [0, 52],
        "}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/slider/slider.wxss" }
    );
}
