__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/dropdown-item/dropdown-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/dropdown-item/index"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          d,
          g = (C) => {},
          j,
          n = (C, o, p, q, r, s, T, E) => {
            var u = (C) => {},
              t = (C, T, E) => {
                E(
                  "t-radio",
                  {},
                  (N, C) => {
                    if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__radio-item " +
                          Y(D.prefix) +
                          "-class-column-item"
                      );
                    if (C || K || U.placement) O(N, "placement", D.placement);
                    if (C) O(N, "tabindex", "0");
                    if (C) O(N, "icon", "line");
                    if (C) O(N, "t-class", "radio");
                    if (C || K || !!U.prefix || undefined)
                      O(
                        N,
                        "t-class-label",
                        Y(D.prefix) + "-class-column-item-label"
                      );
                    var $A = D.valueAlias;
                    if (C || K || !!U.valueAlias || Z(q, $A))
                      O(N, "value", X(o)[$A]);
                    var $B = D.labelAlias;
                    if (C || K || !!U.labelAlias || Z(q, $B))
                      O(N, "label", X(o)[$B]);
                    if (C || K || Z(q, "disabled"))
                      O(N, "disabled", X(o).disabled);
                  },
                  u
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.valueAlias;
                if (C || K || !!(U.valueAlias || Z(q, $A)) || undefined)
                  R.i(N, "id_" + Y(X(o)[$A]));
              },
              t
            );
          },
          m = (C, T, E, B, F) => {
            F(D.options, "index", U ? U.options : undefined, [0, "options"], n);
          },
          l = (C, T, E) => {
            E(
              "t-radio-group",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__radio " +
                      Y(D.prefix) +
                      "-class-column"
                  );
                if (C || K || !!U.optionsColumns || undefined)
                  R.y(
                    N,
                    "grid-template-columns:repeat(" +
                      Y(D.optionsColumns) +
                      ", 1fr)"
                  );
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class", Y(D.classPrefix) + "__radio-group");
                if (C || K || U.value) O(N, "value", D.value);
                if (C) R.v(N, "change", "handleRadioChange", !1, !1, !1, !1);
              },
              m
            );
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "scroll-view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__scroll");
                  if (C) O(N, "scroll-y", true);
                  if (C || K || !!U.value || undefined)
                    O(N, "scroll-into-view", "id_" + Y(D.value));
                },
                l
              );
            }
          },
          o,
          s = (C, t, u, v, w, x, T, E) => {
            var z = (C) => {},
              y = (C, T, E) => {
                E(
                  "t-checkbox",
                  {},
                  (N, C) => {
                    if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__checkbox-item " +
                          Y(D.prefix) +
                          "-class-column-item"
                      );
                    if (C) O(N, "tabindex", "0");
                    if (C) O(N, "theme", "tag");
                    var $A = D.valueAlias;
                    if (C || K || !!U.valueAlias || Z(v, $A))
                      O(N, "value", X(t)[$A]);
                    var $B = D.labelAlias;
                    if (C || K || !!U.labelAlias || Z(v, $B))
                      O(N, "label", X(t)[$B]);
                    if (C || K || Z(v, "disabled"))
                      O(N, "disabled", X(t).disabled);
                  },
                  z
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.valueAlias;
                if (C || K || !!(U.valueAlias || Z(v, $A)) || undefined)
                  R.i(N, "id_" + Y(X(t)[$A]));
              },
              y
            );
          },
          r = (C, T, E, B, F) => {
            F(D.options, "index", U ? U.options : undefined, [0, "options"], s);
          },
          q = (C, T, E) => {
            E(
              "t-checkbox-group",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__checkbox " +
                      Y(D.prefix) +
                      "-class-column"
                  );
                if (C || K || !!U.optionsColumns || undefined)
                  R.y(
                    N,
                    "grid-template-columns:repeat(" +
                      Y(D.optionsColumns) +
                      ", 1fr)"
                  );
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class", Y(D.classPrefix) + "__checkbox-group");
                var $A = D.value;
                if (C || K || !!U.value || ($A ? U.value : Q.a([])))
                  O(N, "value", $A ? D.value : []);
                if (C) R.v(N, "change", "handleRadioChange", !1, !1, !1, !1);
              },
              r
            );
          },
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "scroll-view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__scroll");
                  if (C) O(N, "scroll-y", true);
                  if (C || K || !!U.firstCheckedValue || undefined)
                    O(N, "scroll-into-view", "id_" + Y(D.firstCheckedValue));
                },
                q
              );
            }
          },
          i = (C, T, E, B, F, S) => {
            j = !D.multiple && D.options && X(D.options).length > 0 ? 1 : 0;
            B(j, k);
            o = D.multiple && D.options && X(D.options).length > 0 ? 1 : 0;
            B(o, p);
            S("");
          },
          u,
          w = (C) => {},
          x = (C) => {},
          v = (C, T, E) => {
            if (u === 1) {
              E(
                "t-button",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__footer-btn " +
                        Y(D.classPrefix) +
                        "__reset-btn"
                    );
                  if (C) O(N, "block", true);
                  if (C) O(N, "theme", "light");
                  if (C) O(N, "content", "重置");
                  if (C || K || !!Z(U.value, "length") || undefined)
                    O(N, "disabled", X(D.value).length == 0);
                  if (C) O(N, "bindtap", "handleReset");
                },
                w
              );
              E(
                "t-button",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__footer-btn " +
                        Y(D.classPrefix) +
                        "__confirm-btn"
                    );
                  if (C) O(N, "block", true);
                  if (C) O(N, "theme", "primary");
                  if (C) O(N, "content", "确定");
                  if (C || K || !!Z(U.value, "length") || undefined)
                    O(N, "disabled", X(D.value).length == 0);
                  if (C) O(N, "bindtap", "handleConfirm");
                },
                x
              );
            }
          },
          t = (C, T, E, B, F, S) => {
            S("footer");
            u = D.multiple ? 1 : 0;
            B(u, v);
          },
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__body");
              },
              i
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__footer " +
                      Y(D.prefix) +
                      "-class-footer"
                  );
              },
              t
            );
          },
          f = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__mask");
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([!!U.maskHeight || undefined, U.style, U.customStyle])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(b)._style)([
                      "height:" + D.maskHeight + "px",
                      D.style,
                      D.customStyle,
                    ])
                  );
                if (C || K || !!U.show || undefined) O(N, "hidden", !D.show);
                if (C) O(N, "catchtouchmove", "closeDropdown");
                if (C) R.v(N, "tap", "handleMaskClick", !1, !1, !1, !1);
              },
              g
            );
            E(
              "t-popup",
              {},
              (N, C) => {
                if (C || K || U.show) O(N, "visible", D.show);
                if (C || K || !!U.zIndex || undefined)
                  O(N, "z-index", D.zIndex + 1);
                if (C || K || U.duration) O(N, "duration", D.duration);
                if (C || K || U.showOverlay)
                  O(N, "show-overlay", D.showOverlay);
                if (C) O(N, "custom-style", "position: absolute");
                if (C || K || Q.b({}))
                  O(N, "overlay-props", { style: "position: absolute" });
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class", Y(D.classPrefix) + "__popup-host");
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  O(
                    N,
                    "t-class-content",
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C) R.v(N, "leaved", "onLeaved", !1, !1, !1, !1);
                if (C)
                  R.v(N, "visible-change", "handleMaskClick", !1, !1, !1, !1);
              },
              h
            );
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!(Z(undefined, "getStyles") || U.top || U.zIndex) ||
                          undefined,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(b)._style)([
                        P(X(a).getStyles)(D.top, D.zIndex),
                        D.style,
                        D.customStyle,
                      ])
                    );
                },
                f
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.wrapperVisible ? 1 : 0;
            B(d, e);
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/dropdown-item/dropdown-item.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-dropdown-item{bottom:0;left:0;overflow:hidden;position:fixed;right:0;top:0}\n.",
      [1],
      "t-dropdown-item__content{display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;overflow:hidden;z-index:11600}\n.",
      [1],
      "t-dropdown-item__popup-host{display:block;left:0;overflow:hidden;position:absolute;top:0;width:100%}\n.",
      [1],
      "t-dropdown-item__body{background:var(--td-dropdown-menu-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));-webkit-flex:1;flex:1;max-height:var(--td-dropdown-body-max-height,",
      [0, 560],
      ");overflow:auto}\n.",
      [1],
      "t-dropdown-item__body--tree{display:-webkit-flex;display:flex;overflow:hidden}\n.",
      [1],
      "t-dropdown-item__body--multi{overflow-y:auto;padding-bottom:var(--td-spacer,",
      [0, 16],
      ");padding-top:var(--td-spacer,",
      [0, 16],
      ")}\n.",
      [1],
      "t-dropdown-item__scroll{max-height:var(--td-dropdown-body-max-height,",
      [0, 560],
      ")}\n.",
      [1],
      "t-dropdown-item__footer{background:var(--td-dropdown-menu-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));display:-webkit-flex;display:flex;padding:",
      [0, 32],
      ";position:relative}\n.",
      [1],
      "t-dropdown-item__footer::after{background-color:var(--td-component-border,var(--td-gray-color-4,#dcdcdc));bottom:unset;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
      [1],
      "t-dropdown-item__footer-btn{-webkit-flex:1;flex:1}\n.",
      [1],
      "t-dropdown-item__footer-btn+.",
      [1],
      "t-dropdown-item__footer-btn{margin-left:",
      [0, 32],
      "}\n.",
      [1],
      "t-dropdown-item__body:empty,.",
      [1],
      "t-dropdown-item__footer:empty{display:none}\n.",
      [1],
      "t-dropdown-item__checkbox,.",
      [1],
      "t-dropdown-item__radio{box-sizing:border-box;overflow:scroll;width:100%}\n.",
      [1],
      "t-dropdown-item__checkbox-group,.",
      [1],
      "t-dropdown-item__radio-group{grid-gap:",
      [0, 24],
      ";display:grid}\n.",
      [1],
      "t-dropdown-item__radio-group{grid-gap:",
      [0, 0],
      ";display:grid}\n.",
      [1],
      "t-dropdown-item__checkbox-group{padding:",
      [0, 32],
      "}\n.",
      [1],
      "t-dropdown-item__tree-item{font-size:var(--td-tree-item-font-size,",
      [0, 32],
      ");height:var(--td-tree-item-height,",
      [0, 96],
      ");line-height:var(--td-tree-item-height,",
      [0, 96],
      ");padding-left:",
      [0, 32],
      "}\n.",
      [1],
      "t-dropdown-item__tree-item--active{color:var(--td-tree-item-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-dropdown-item__mask{left:0;position:fixed;top:0;width:100vh}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/dropdown-item/dropdown-item.wxss",
    }
  );
}
