var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/toConsumableArray"),
  a = require("../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../env/index.js");
Page({
  data: {
    appName: r.appName,
    current: 1,
    autoplay: !0,
    duration: 500,
    interval: 5e3,
    height: 210,
    categoryList: [],
    categoryLoading: !1,
    swiperList: [],
    showIncompleteNotice: !1,
    activeTab: "service",
    img1: "/assets/icon/025-map point.svg",
    img2: "/assets/icon/026-wallet.svg",
    serviceList: [],
    serviceLoading: !1,
    servicePage: 1,
    serviceLimit: 10,
    serviceHasMore: !0,
    orderData: {
      userInfo: {
        avatar: "/assets/tx/时尚职业男.png",
        name: "小美",
        id: "18-2-906",
      },
      serviceInfo: {
        icon: "/assets/img/鱼生套餐.png",
        title: "试卷打印",
        attachments: [1, 2],
        price: 0.5,
        quantity: 1,
        totalAmount: 1,
      },
    },
    helpItem: {
      imgSrc: "/assets/img/男人拿喇叭.png",
      title: "帮忙开窗通风每天按时开窗通风每天按时开窗通风",
      description: "每天按时开窗通风每天按时开窗通风每天按时开窗通风",
      overTime: "5月21日 10:00",
      userLimit: "认证用户",
      providerAvatar: "/assets/tx/商务职业男.png",
      providerName: "张三",
      price: "0.00",
    },
    userCardInfo: {
      avatar: "/assets/tx/眼镜职业女.png",
      name: "小美",
      serviceCount: 123,
      rating: 9.5,
      isVerified: !1,
    },
    userServices: [
      {
        image: "/assets/img/鱼生套餐.png",
        title: "现煮咖啡现煮咖啡现煮咖啡现煮咖啡现煮咖啡现煮咖啡",
        price: "9.9",
      },
      { image: "/assets/img/鱼生套餐.png", title: "打扫卫生", price: "30" },
      { image: "/assets/img/鱼生套餐.png", title: "试卷打印", price: "0.5" },
    ],
  },
  fetchServiceList: function () {
    var r = arguments,
      s = this;
    return a(
      e().mark(function a() {
        var i, n, c, o, u, l;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  if (
                    ((i = r.length > 0 && void 0 !== r[0] && r[0]),
                    !s.data.serviceLoading)
                  ) {
                    e.next = 3;
                    break;
                  }
                  return e.abrupt("return");
                case 3:
                  return (
                    (n = getApp()),
                    s.setData({ serviceLoading: !0 }),
                    (e.prev = 5),
                    (c = i ? 1 : s.data.servicePage),
                    (e.next = 9),
                    n.call({
                      path: "/api/mp/services?is_recommended=true",
                      method: "GET",
                      data: {
                        is_recommended: "true",
                        page: c,
                        limit: s.data.serviceLimit,
                      },
                    })
                  );
                case 9:
                  if (0 !== (o = e.sent).code) {
                    e.next = 17;
                    break;
                  }
                  (u = o.data.services || []),
                    (l = i ? u : [].concat(t(s.data.serviceList), t(u))),
                    s.setData({
                      serviceList: l,
                      servicePage: c + 1,
                      serviceHasMore: o.data.pagination.has_next,
                      serviceLoading: !1,
                    }),
                    console.log("获取服务列表成功:", l),
                    (e.next = 18);
                  break;
                case 17:
                  throw new Error(o.message || "获取服务列表失败");
                case 18:
                  e.next = 25;
                  break;
                case 20:
                  (e.prev = 20),
                    (e.t0 = e.catch(5)),
                    console.error("获取服务列表失败:", e.t0),
                    s.setData({ serviceLoading: !1 }),
                    wx.showToast({ title: "加载失败", icon: "error" });
                case 25:
                case "end":
                  return e.stop();
              }
          },
          a,
          null,
          [[5, 20]]
        );
      })
    )();
  },
  refreshServiceList: function () {
    var t = this;
    return a(
      e().mark(function a() {
        return e().wrap(function (e) {
          for (;;)
            switch ((e.prev = e.next)) {
              case 0:
                return (
                  console.log("开始刷新服务列表"),
                  t.setData({
                    servicePage: 1,
                    serviceHasMore: !0,
                    serviceList: [],
                  }),
                  (e.next = 4),
                  t.fetchServiceList(!0)
                );
              case 4:
              case "end":
                return e.stop();
            }
        }, a);
      })
    )();
  },
  loadMoreServices: function () {
    var t = this;
    return a(
      e().mark(function a() {
        return e().wrap(function (e) {
          for (;;)
            switch ((e.prev = e.next)) {
              case 0:
                if (!t.data.serviceHasMore || t.data.serviceLoading) {
                  e.next = 3;
                  break;
                }
                return (e.next = 3), t.fetchServiceList(!1);
              case 3:
              case "end":
                return e.stop();
            }
        }, a);
      })
    )();
  },
  switchTab: function (e) {
    var t = e.currentTarget.dataset.tab;
    this.setData({ activeTab: t }),
      "service" === t &&
        0 === this.data.serviceList.length &&
        this.fetchServiceList(!0);
  },
  goToEditProfile: function () {
    this.data.showIncompleteNotice &&
      wx.navigateTo({ url: "/pages/auth/auth?from=index" });
  },
  checkUserAuthStatus: function () {
    var e = getApp().globalData.userInfo;
    console.log("检查用户认证状态:", e),
      e && "incomplete" === e.data.auth_status.value
        ? this.setData({ showIncompleteNotice: !0 })
        : this.setData({ showIncompleteNotice: !1 });
  },
  onLoad: function () {
    var e = this;
    console.log("index页面onLoad被调用"),
      this.fetchArticleBanners(),
      this.fetchCategoryList(),
      setTimeout(function () {
        e.checkUserAuthStatus();
      }, 500),
      "service" === this.data.activeTab && this.fetchServiceList(!0);
  },
  onShow: function () {
    "function" == typeof this.getTabBar &&
      this.getTabBar() &&
      this.getTabBar().setData({ selected: 0 }),
      this.checkUserAuthStatus();
  },
  onUserLoginSuccess: function () {
    console.log("收到用户登录完成通知，重新检查用户状态"),
      this.checkUserAuthStatus();
  },
  onPullDownRefresh: function () {
    var t = this;
    return a(
      e().mark(function a() {
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  return (
                    console.log("用户触发下拉刷新"),
                    (e.prev = 1),
                    (e.next = 4),
                    t.fetchArticleBanners()
                  );
                case 4:
                  return (e.next = 6), t.fetchCategoryList();
                case 6:
                  if ("service" !== t.data.activeTab) {
                    e.next = 11;
                    break;
                  }
                  return (e.next = 9), t.refreshServiceList();
                case 9:
                  e.next = 12;
                  break;
                case 11:
                  "help" === t.data.activeTab &&
                    console.log("求助列表刷新逻辑待实现");
                case 12:
                  t.checkUserAuthStatus(),
                    wx.showToast({
                      title: "刷新成功",
                      icon: "success",
                      duration: 1500,
                    }),
                    (e.next = 20);
                  break;
                case 16:
                  (e.prev = 16),
                    (e.t0 = e.catch(1)),
                    console.error("下拉刷新失败:", e.t0),
                    wx.showToast({
                      title: "刷新失败",
                      icon: "error",
                      duration: 1500,
                    });
                case 20:
                  return (e.prev = 20), wx.stopPullDownRefresh(), e.finish(20);
                case 23:
                case "end":
                  return e.stop();
              }
          },
          a,
          null,
          [[1, 16, 20, 23]]
        );
      })
    )();
  },
  fetchArticleBanners: function () {
    var t = this;
    return a(
      e().mark(function a() {
        var r, s, i;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  return (
                    (r = getApp()),
                    (e.prev = 1),
                    (e.next = 4),
                    r.call({ path: "/api/mp/articles/banners", method: "GET" })
                  );
                case 4:
                  if (0 !== (s = e.sent).code) {
                    e.next = 11;
                    break;
                  }
                  (i = s.data.map(function (e) {
                    return {
                      value: e.banner_image,
                      ariaLabel: e.title,
                      articleId: e.article_id,
                      title: e.title,
                    };
                  })),
                    t.setData({ swiperList: i }),
                    console.log("获取文章轮播图成功:", i),
                    (e.next = 12);
                  break;
                case 11:
                  throw new Error(s.message || "获取文章轮播图失败");
                case 12:
                  e.next = 18;
                  break;
                case 14:
                  (e.prev = 14),
                    (e.t0 = e.catch(1)),
                    console.error("获取文章轮播图失败:", e.t0),
                    t.setData({
                      swiperList: [
                        {
                          value: "/assets/img/ms.png",
                          ariaLabel: "默认轮播图1",
                        },
                        {
                          value: "/assets/img/sfc.png",
                          ariaLabel: "默认轮播图2",
                        },
                      ],
                    });
                case 18:
                case "end":
                  return e.stop();
              }
          },
          a,
          null,
          [[1, 14]]
        );
      })
    )();
  },
  onSwiperClick: function (e) {
    var t = e.detail.index,
      a = this.data.swiperList[t];
    a &&
      a.articleId &&
      wx.navigateTo({
        url: "/pages/articles-info/articles-info?id=".concat(a.articleId),
      });
  },
  fetchCategoryList: function () {
    var t = this;
    return a(
      e().mark(function a() {
        var r, s, i;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  if (!t.data.categoryLoading) {
                    e.next = 2;
                    break;
                  }
                  return e.abrupt("return");
                case 2:
                  return (
                    (r = getApp()),
                    t.setData({ categoryLoading: !0 }),
                    (e.prev = 4),
                    (e.next = 7),
                    r.call({
                      path: "/api/mp/services/categories",
                      method: "GET",
                      data: { status: "enabled" },
                    })
                  );
                case 7:
                  if (0 !== (s = e.sent).code) {
                    e.next = 14;
                    break;
                  }
                  (i = (s.data || []).sort(function (e, t) {
                    return e.sort_order - t.sort_order;
                  })),
                    t.setData({ categoryList: i, categoryLoading: !1 }),
                    console.log("获取服务分类成功:", i),
                    (e.next = 15);
                  break;
                case 14:
                  throw new Error(s.message || "获取服务分类失败");
                case 15:
                  e.next = 22;
                  break;
                case 17:
                  (e.prev = 17),
                    (e.t0 = e.catch(4)),
                    console.error("获取服务分类失败:", e.t0),
                    t.setData({ categoryLoading: !1 }),
                    wx.showToast({ title: "加载分类失败", icon: "error" });
                case 22:
                case "end":
                  return e.stop();
              }
          },
          a,
          null,
          [[4, 17]]
        );
      })
    )();
  },
  onCategoryTap: function (e) {
    var t = e.currentTarget.dataset.category;
    console.log("点击分类:", t),
      wx.navigateTo({
        url: "/pages/service-list/service-list?categoryId="
          .concat(t.id, "&categoryName=")
          .concat(t.name),
      });
  },
  onShareAppMessage: function () {
    return {
      title: r.appName,
      path: "/pages/index/index",
      imageUrl: "/assets/icon/login.png",
    };
  },
});
