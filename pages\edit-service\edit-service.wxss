.container {
  background-color: #fff;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 180rpx 30rpx 30rpx;
}
.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx auto;
  padding: 40rpx 30rpx;
  width: 95%;
}
.form-item {
  margin-bottom: 40rpx;
}
.form-label {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.form-label.required::before {
  color: #ff4d4f;
  content: "*";
  margin-right: 8rpx;
}
.input-wrapper {
  position: relative;
}
.form-input {
  background-color: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  box-sizing: border-box;
  color: #333;
  font-size: 28rpx;
  height: 80rpx;
  padding: 0 20rpx;
  width: 100%;
}
.form-input:focus {
  border-color: #195abf;
}
.form-input.picker-input {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  line-height: 80rpx;
  position: relative;
}
.form-input.picker-input::after {
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #999;
  content: "";
  height: 0;
  width: 0;
}
.char-count-inner,
.form-input.picker-input::after {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}
.char-count-inner {
  color: #999;
  font-size: 24rpx;
}
.textarea-wrapper {
  position: relative;
}
.form-textarea {
  background-color: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  box-sizing: border-box;
  color: #333;
  font-size: 28rpx;
  min-height: 120rpx;
  padding: 20rpx;
  width: 100%;
}
.form-textarea.contact-textarea {
  min-height: 80rpx;
}
.form-textarea:focus {
  border-color: #195abf;
}
.textarea-count {
  bottom: 20rpx;
  position: absolute;
  right: 20rpx;
  top: auto;
  transform: none;
}
.thumbnail-section {
  margin-top: 15rpx;
}
.thumbnail-preview {
  align-items: center;
  background-color: #f8f9fa;
  border: 2rpx dashed #e0e0e0;
  border-radius: 10rpx;
  display: -webkit-flex;
  display: flex;
  height: 200rpx;
  justify-content: center;
  overflow: hidden;
  position: relative;
  width: 200rpx;
}
.selected-thumbnail {
  border-radius: 8rpx;
  height: 100%;
  width: 100%;
}
.thumbnail-placeholder {
  align-items: center;
  color: #999;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.placeholder-text {
  color: #999;
  font-size: 24rpx;
}
.modal-content {
  max-height: 80vh;
  overflow-y: auto;
}
.thumbnail-grid {
  flex-wrap: wrap;
  gap: 15rpx;
  justify-content: flex-start;
  margin-bottom: 30rpx;
}
.thumbnail-grid,
.thumbnail-item {
  display: -webkit-flex;
  display: flex;
}
.thumbnail-item {
  align-items: center;
  border-radius: 10rpx;
  box-sizing: border-box;
  flex-direction: column;
  padding: 8rpx;
  position: relative;
  transition: all 0.3s ease;
  width: calc(25% - 15rpx);
}
.thumbnail-img {
  border-radius: 8rpx;
  height: 80rpx;
  width: 80rpx;
}
.thumbnail-name {
  font-size: 22rpx;
  line-height: 1.2;
  margin-top: 8rpx;
  text-align: center;
}
.selected-mark {
  align-items: center;
  background-color: #195abf;
  border-radius: 50%;
  color: #fff;
  display: -webkit-flex;
  display: flex;
  font-size: 20rpx;
  font-weight: 700;
  height: 30rpx;
  justify-content: center;
  position: absolute;
  right: 3rpx;
  top: 3rpx;
  width: 30rpx;
}
.thumbnail-name {
  color: #666;
  font-size: 26rpx;
}
.radio-options {
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
  gap: 40rpx;
  margin-bottom: 20rpx;
}
.radio-item {
  color: #333;
  font-size: 28rpx;
  gap: 12rpx;
  margin-right: 25rpx;
}
.radio-item radio {
  transform: scale(0.8);
}
.button-group {
  gap: 20rpx;
  justify-content: space-between;
  margin-top: 60rpx;
}
.btn,
.button-group {
  display: -webkit-flex;
  display: flex;
}
.btn {
  align-items: center;
  border: none;
  border-radius: 20rpx;
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  justify-content: center;
}
.btn-cancel {
  background-color: #fff;
  border: 3rpx solid #1e1e1e;
  color: #666;
}
.btn-publish {
  background-color: #a5d8ff;
  border: 3rpx solid #1971c2;
  color: #1971c2;
}
.btn:active {
  opacity: 0.8;
}
.thumbnail-modal {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  position: fixed;
  z-index: 9999;
}
.modal-mask,
.thumbnail-modal {
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
}
.modal-mask {
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
}
.modal-content {
  background-color: #fff;
  border-radius: 20rpx;
  max-width: 600rpx;
  padding: 40rpx;
  position: relative;
  width: 90%;
}
.modal-header {
  margin-bottom: 30rpx;
  text-align: center;
}
.modal-title {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}
.modal-footer {
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  margin: 0 auto;
  width: 50%;
}
.image-upload-section {
  margin-top: 15rpx;
}
.uploaded-images {
  display: -webkit-flex;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.image-item {
  height: 200rpx;
  position: relative;
  width: 200rpx;
}
.uploaded-image {
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  height: 100%;
  width: 100%;
}
.delete-btn {
  background-color: #ff4d4f;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  color: #fff;
  font-size: 24rpx;
  font-weight: 700;
  height: 40rpx;
  position: absolute;
  right: -10rpx;
  top: -10rpx;
  width: 40rpx;
}
.delete-btn,
.upload-btn {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.upload-btn {
  background-color: #f8f9fa;
  border: 2rpx dashed #e0e0e0;
  border-radius: 10rpx;
  color: #999;
  flex-direction: column;
  height: 200rpx;
  width: 200rpx;
}
.upload-btn:active {
  background-color: #f0f0f0;
}
.upload-text {
  font-size: 48rpx;
  font-weight: 300;
  margin-bottom: 10rpx;
}
.upload-desc,
.upload-tip {
  font-size: 24rpx;
}
.upload-tip {
  color: #999;
  margin-top: 15rpx;
  text-align: left;
}
.selected-buildings-display {
  align-items: center;
  background-color: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  display: -webkit-flex;
  display: flex;
  margin-bottom: 20rpx;
  min-height: 80rpx;
  padding: 20rpx;
  position: relative;
}
.selected-buildings-display:active {
  background-color: #f5f5f5;
}
.selected-buildings-label {
  color: #666;
  font-size: 26rpx;
  margin-right: 15rpx;
  white-space: nowrap;
}
.selected-buildings-text {
  color: #333;
  flex: 1;
  font-size: 28rpx;
  line-height: 1.4;
}
.selected-buildings-text .placeholder-text {
  color: #999;
}
.arrow-icon {
  color: #999;
  font-size: 28rpx;
  margin-left: 15rpx;
}
.building-picker-modal {
  align-items: center;
  bottom: 0;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 9999;
}
.building-picker-modal .modal-mask {
  background-color: rgba(0, 0, 0, 0.5);
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}
.building-picker-modal .modal-content {
  background-color: #fff;
  border-radius: 20rpx;
  max-height: 70vh;
  max-width: 600rpx;
  overflow-y: auto;
  padding: 40rpx;
  position: relative;
  width: 90%;
}
.building-picker-modal .modal-header {
  align-items: center;
  border-bottom: 2rpx solid #f0f0f0;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
}
.building-picker-modal .modal-title {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}
.building-picker-modal .modal-footer {
  border-top: 2rpx solid #f0f0f0;
  margin-top: 30rpx;
  padding-top: 20rpx;
}
.building-picker-modal .btn-confirm {
  background-color: #195abf;
  border: none;
  border-radius: 12rpx;
  color: #fff;
  font-size: 28rpx;
  height: 80rpx;
  width: 100%;
}
.building-item {
  align-items: center;
  background-color: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  color: #333;
  display: -webkit-flex;
  display: flex;
  font-size: 26rpx;
  justify-content: center;
  min-width: 120rpx;
  padding: 15rpx 25rpx;
  position: relative;
  transition: all 0.3s ease;
}
.building-item.unselected {
  background-color: #fff;
  border-color: #e0e0e0;
  color: #333;
}
.building-item.unselected:hover {
  background-color: #f8fbff;
  border-color: #195abf;
}
.building-item.selected {
  background-color: #e6f3ff;
  border-color: #195abf;
  box-shadow: 0 4rpx 8rpx rgba(25, 90, 191, 0.15);
  color: #195abf;
  font-weight: 500;
}
.building-item:active {
  transform: scale(0.95);
}
.check-mark {
  align-items: center;
  background-color: #195abf;
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(25, 90, 191, 0.3);
  color: #fff;
  display: -webkit-flex;
  display: flex;
  font-size: 20rpx;
  font-weight: 700;
  height: 30rpx;
  justify-content: center;
  position: absolute;
  right: -5rpx;
  top: -5rpx;
  width: 30rpx;
}
