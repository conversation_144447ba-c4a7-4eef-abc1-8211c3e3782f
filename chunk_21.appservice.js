__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/dialog/dialog": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/button"] ||
              {}
            )._,
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/dialog/dialog"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            closeOnOverlayClick: new Array(1),
            visible: new Array(1),
            buttonVariant: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            showOverlay: new Array(1),
            zIndex: new Array(1),
            preventScrollThrough: new Array(1),
            overlayProps: new Array(1),
            usingCustomNavbar: new Array(1),
          },
          K = U === true,
          f,
          i,
          k,
          l = (C, T, E, B, F, S, J) => {
            var $A = I(k);
            if (k && $A)
              $A(
                R,
                C,
                Object.assign({ name: "close", size: 22 }, X(D.closeBtn), {}),
                K ||
                  (U
                    ? U.closeBtn === true ||
                      Object.assign({}, X(U.closeBtn), {})
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          m = (C) => {},
          j = (C, T, E, B, F, S, J) => {
            if (i === 1) {
              k = "icon";
              B(k, l);
            } else {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "name", "close");
                  if (C) O(N, "size", "44rpx");
                },
                m
              );
            }
          },
          h = (C, T, E, B) => {
            i = P(X(a).isObject)(D.closeBtn) ? 1 : 0;
            B(i, j);
          },
          g = (C, T, E) => {
            if (f === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__close-btn");
                  if (C) R.v(N, "tap", "onClose", !1, !1, !1, !1);
                },
                h
              );
            }
          },
          o,
          q = (C, T) => {
            C || K || U.title ? T(Y(D.title)) : T();
          },
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__header");
                },
                q
              );
            }
          },
          r,
          u = (C, T) => {
            C || K || U.content ? T(Y(D.content)) : T();
          },
          t = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__body-text");
              },
              u
            );
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__body");
                },
                t
              );
            }
          },
          n = (C, T, E, B, F, S) => {
            o = D.title ? 1 : 0;
            B(o, p);
            S("title");
            r = D.content ? 1 : 0;
            B(r, s);
            S("content");
          },
          w,
          y = (C, z, A0, B0, C0, D0, T, E, B, F, S, J) => {
            var E0,
              F0 = (C, T, E, B, F, S, J) => {
                var $A = I(E0);
                if (E0 && $A)
                  $A(
                    R,
                    C,
                    Object.assign(
                      {
                        block: true,
                        type: "action",
                        extra: A0,
                        tClass: D.prefix + "-class-action",
                        rootClass: P(X(b).getActionClass)(
                          D.classPrefix,
                          D.buttonLayout
                        ),
                      },
                      X(z),
                      {}
                    ),
                    K ||
                      (U
                        ? B0 === true ||
                          Object.assign(
                            {
                              extra: C0,
                              tClass: !!U.prefix || undefined,
                              rootClass:
                                !!(
                                  Z(undefined, "getActionClass") ||
                                  U.classPrefix ||
                                  U.buttonLayout
                                ) || undefined,
                            },
                            X(B0),
                            {}
                          )
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              };
            E0 = "button";
            B(E0, F0);
          },
          x = (C, T, E, B, F) => {
            if (w === 1) {
              F(
                D.actions,
                "index",
                U ? U.actions : undefined,
                [0, "actions"],
                y
              );
            }
          },
          z,
          B0,
          C0 = (C, T, E, B, F, S, J) => {
            var $A = I(B0);
            if (B0 && $A)
              $A(
                R,
                C,
                Object.assign({ type: "cancel" }, X(D._cancel), {}),
                K ||
                  (U
                    ? U._cancel === true || Object.assign({}, X(U._cancel), {})
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          A0 = (C, T, E, B, F, S, J) => {
            if (z === 1) {
              B0 = "button";
              B(B0, C0);
            }
          },
          D0,
          F0,
          G0 = (C, T, E, B, F, S, J) => {
            var $A = I(F0);
            if (F0 && $A)
              $A(
                R,
                C,
                Object.assign(
                  { type: "confirm", theme: "primary" },
                  X(D._confirm),
                  {}
                ),
                K ||
                  (U
                    ? U._confirm === true ||
                      Object.assign({}, X(U._confirm), {})
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          E0 = (C, T, E, B, F, S, J) => {
            if (D0 === 1) {
              F0 = "button";
              B(F0, G0);
            }
          },
          v = (C, T, E, B, F, S) => {
            w = D.actions ? 1 : 0;
            B(w, x);
            S("actions");
            z = D._cancel ? 1 : 0;
            B(z, A0);
            S("cancel-btn");
            D0 = D._confirm ? 1 : 0;
            B(D0, E0);
            S("confirm-btn");
          },
          e = (C, T, E, B, F, S) => {
            S("top");
            f = D.closeBtn ? 1 : 0;
            B(f, g);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
              },
              n
            );
            S("middle");
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([!!U.buttonLayout || undefined]),
                      Q.a([
                        !!(U.buttonVariant || Z(U.actions, "length")) ||
                          undefined,
                      ]),
                    ])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__footer", [
                      ["column", D.buttonLayout === "vertical"],
                      [
                        "full",
                        D.buttonVariant == "text" && X(D.actions).length == 0,
                      ],
                    ])
                  );
                A["buttonVariant"][0] = (D, E, T) => {
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__footer", [
                      ["column", D.buttonLayout === "vertical"],
                      [
                        "full",
                        D.buttonVariant == "text" && X(D.actions).length == 0,
                      ],
                    ])
                  );
                };
              },
              v
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " " + Y(D.prefix) + "-class");
              },
              e,
              "content"
            );
          },
          c = (C, T, E) => {
            E(
              "t-popup",
              {},
              (N, C) => {
                if (C) L(N, "class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "name", "dialog");
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class", Y(D.classPrefix) + "__wrapper");
                if (C || K || U.visible) O(N, "visible", D.visible);
                A["visible"][0] = (D, E, T) => {
                  O(N, "visible", D.visible);
                  E(N);
                };
                if (C || K || U.showOverlay) O(N, "showOverlay", D.showOverlay);
                A["showOverlay"][0] = (D, E, T) => {
                  O(N, "showOverlay", D.showOverlay);
                  E(N);
                };
                if (C || K || U.closeOnOverlayClick)
                  O(N, "closeOnOverlayClick", D.closeOnOverlayClick);
                A["closeOnOverlayClick"][0] = (D, E, T) => {
                  O(N, "closeOnOverlayClick", D.closeOnOverlayClick);
                  E(N);
                };
                if (C || K || U.preventScrollThrough)
                  O(N, "preventScrollThrough", D.preventScrollThrough);
                A["preventScrollThrough"][0] = (D, E, T) => {
                  O(N, "preventScrollThrough", D.preventScrollThrough);
                  E(N);
                };
                if (C || K || U.overlayProps)
                  O(N, "overlayProps", D.overlayProps);
                A["overlayProps"][0] = (D, E, T) => {
                  O(N, "overlayProps", D.overlayProps);
                  E(N);
                };
                if (C || K || U.zIndex) O(N, "zIndex", D.zIndex);
                A["zIndex"][0] = (D, E, T) => {
                  O(N, "zIndex", D.zIndex);
                  E(N);
                };
                if (C) O(N, "placement", "center");
                if (C || K || U.usingCustomNavbar)
                  O(N, "usingCustomNavbar", D.usingCustomNavbar);
                A["usingCustomNavbar"][0] = (D, E, T) => {
                  O(N, "usingCustomNavbar", D.usingCustomNavbar);
                  E(N);
                };
                if (C) R.v(N, "visible-change", "overlayClick", !1, !1, !1, !1);
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/dialog/dialog";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/dialog/dialog.js";
define(
  "miniprogram_npm/tdesign-miniprogram/dialog/dialog.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0),
      require("../../../@babel/runtime/helpers/Arrayincludes");
    var t = require("../../../@babel/runtime/helpers/typeof"),
      e = require("../../../@babel/runtime/helpers/createClass"),
      n = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      o = require("../../../@babel/runtime/helpers/createSuper"),
      r = require("tslib"),
      a = require("../common/src/index"),
      c = h(require("../common/config")),
      s = h(require("./props")),
      l = require("../common/utils"),
      u = require("../common/validator"),
      p = h(require("../mixins/using-custom-navbar"));
    function h(t) {
      return t && t.__esModule ? t : { default: t };
    }
    var v = c.default.prefix,
      f = "".concat(v, "-dialog"),
      b = (function (r) {
        i(c, r);
        var a = o(c);
        function c() {
          var e;
          return (
            n(this, c),
            ((e = a.apply(this, arguments)).behaviors = [p.default]),
            (e.options = { multipleSlots: !0 }),
            (e.externalClasses = [
              "".concat(v, "-class"),
              "".concat(v, "-class-content"),
              "".concat(v, "-class-confirm"),
              "".concat(v, "-class-cancel"),
              "".concat(v, "-class-action"),
            ]),
            (e.properties = s.default),
            (e.data = { prefix: v, classPrefix: f, buttonVariant: "text" }),
            (e.observers = {
              "confirmBtn, cancelBtn": function (e, n) {
                var i = this.data,
                  o = i.prefix,
                  r = i.classPrefix,
                  a = i.buttonLayout,
                  c = { buttonVariant: "text" },
                  s = [e, n].some(function (t) {
                    return (
                      (0, u.isObject)(t) && t.variant && "text" !== t.variant
                    );
                  }),
                  l = { confirm: e, cancel: n },
                  p = ["".concat(r, "__button")],
                  h = [];
                s
                  ? ((c.buttonVariant = "base"),
                    p.push("".concat(r, "__button--").concat(a)))
                  : (p.push("".concat(r, "__button--text")),
                    h.push("".concat(r, "-button"))),
                  Object.keys(l).forEach(function (e) {
                    var n = l[e],
                      i = {
                        block: !0,
                        rootClass: [].concat(p, [
                          "".concat(r, "__button--").concat(e),
                        ]),
                        tClass: [].concat(h, [
                          "".concat(o, "-class-").concat(e),
                        ]),
                        variant: c.buttonVariant,
                        openType: "",
                      };
                    "cancel" === e &&
                      "base" === c.buttonVariant &&
                      (i.theme = "light"),
                      (c["_".concat(e)] =
                        "string" == typeof n
                          ? Object.assign(Object.assign({}, i), { content: n })
                          : n && "object" == t(n)
                          ? Object.assign(Object.assign({}, i), n)
                          : null);
                  }),
                  this.setData(Object.assign({}, c));
              },
            }),
            (e.methods = {
              onTplButtonTap: function (t) {
                var e,
                  n,
                  i,
                  o = t.type,
                  r = t.target.dataset,
                  a = r.type,
                  c = r.extra,
                  s = this.data["_".concat(a)],
                  u = "bind".concat(o);
                if ("action" !== a) {
                  if (
                    ("function" == typeof s[u] && s[u](t) && this.close(),
                    !s.openType &&
                      ["confirm", "cancel"].includes(a) &&
                      (null === (e = this[(0, l.toCamel)("on-".concat(a))]) ||
                        void 0 === e ||
                        e.call(this, a)),
                    "tap" !== o)
                  ) {
                    var p =
                      (null ===
                        (i =
                          null === (n = t.detail) || void 0 === n
                            ? void 0
                            : n.errMsg) || void 0 === i
                        ? void 0
                        : i.indexOf("ok")) > -1;
                    this.triggerEvent(
                      p ? "open-type-event" : "open-type-error-event",
                      t.detail
                    );
                  }
                } else this.onActionTap(c);
              },
              onConfirm: function () {
                this.triggerEvent("confirm"),
                  this._onConfirm &&
                    (this._onConfirm({ trigger: "confirm" }), this.close());
              },
              onCancel: function () {
                var t = { trigger: "cancel" };
                this.triggerEvent("cancel"),
                  this.triggerEvent("close", t),
                  this._onCancel && (this._onCancel(t), this.close());
              },
              onClose: function () {
                var t,
                  e = { trigger: "close-btn" };
                this.triggerEvent("close", e),
                  null === (t = this._onCancel) ||
                    void 0 === t ||
                    t.call(this, e),
                  this.close();
              },
              close: function () {
                this.setData({ visible: !1 });
              },
              overlayClick: function () {
                var t;
                if (
                  (this.triggerEvent("overlay-click"),
                  this.properties.closeOnOverlayClick)
                ) {
                  var e = { trigger: "overlay" };
                  this.triggerEvent("close", e),
                    null === (t = this._onCancel) ||
                      void 0 === t ||
                      t.call(this, e),
                    this.close();
                }
              },
              onActionTap: function (t) {
                this.triggerEvent("action", { index: t }),
                  this._onAction &&
                    (this._onAction({ index: t }), this.close());
              },
              openValueCBHandle: function (t) {
                this.triggerEvent("open-type-event", t.detail);
              },
              openValueErrCBHandle: function (t) {
                this.triggerEvent("open-type-error-event", t.detail);
              },
            }),
            e
          );
        }
        return e(c);
      })(a.SuperComponent),
      g = (b = (0, r.__decorate)([(0, a.wxComponent)()], b));
    exports.default = g;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/dialog/dialog.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/dialog/dialog.js");
