__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/cascader/cascader": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            visible: new Array(1),
            style: new Array(1),
            title: new Array(1),
          },
          K = U === true,
          e = (C, T, E, B, F, S) => {
            S("title");
            C || K || U.title
              ? T(Y(D.title), (N) => {
                  A["title"][0] = (D, E, T) => {
                    T(N, Y(D.title));
                  };
                })
              : T();
          },
          g,
          i = (C) => {},
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "size", "48rpx");
                  if (C) O(N, "name", "close");
                },
                i
              );
            }
          },
          f = (C, T, E, B, F, S) => {
            S("close-btn");
            g = D.closeBtn ? 1 : 0;
            B(g, h);
          },
          k,
          m,
          p = (C, q, r, s, t, u, T, E) => {
            var w = (C) => {},
              x = (C, T) => {
                C || K || s ? T(Y(q)) : T();
              },
              y = (C) => {},
              v = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    var $A = q !== D.placeholder;
                    var $B = r === X(D.steps).length - 1;
                    if (
                      C ||
                      K ||
                      !!(
                        U.name ||
                        U.name ||
                        s ||
                        U.placeholder ||
                        ($A ? undefined : undefined) ||
                        U.name ||
                        t ||
                        Z(U.steps, "length") ||
                        ($B ? undefined : undefined)
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        Y(D.name) +
                          "__step-dot " +
                          Y(D.name) +
                          "__step-dot--" +
                          Y($A ? "active" : "") +
                          " " +
                          Y(D.name) +
                          "__step-dot--" +
                          Y($B ? "last" : "")
                      );
                  },
                  w
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    var $A = r === D.stepIndex;
                    if (
                      C ||
                      K ||
                      !!(
                        U.name ||
                        U.name ||
                        t ||
                        U.stepIndex ||
                        ($A ? undefined : undefined)
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        Y(D.name) +
                          "__step-label " +
                          Y(D.name) +
                          "__step-label--" +
                          Y($A ? "active" : "")
                      );
                  },
                  x
                );
                E(
                  "t-icon",
                  {},
                  (N, C) => {
                    if (C) O(N, "name", "chevron-right");
                    if (C) O(N, "size", "44rpx");
                    if (C || K || !!U.name || undefined)
                      O(N, "t-class", Y(D.name) + "__step-arrow");
                  },
                  y
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined) L(N, Y(D.name) + "__step");
                if (C || K || t) R.d(N, "index", r);
                if (C) R.v(N, "tap", "onStepClick", !1, !1, !1, !1);
              },
              v
            );
          },
          o = (C, T, E, B, F) => {
            F(D.steps, "index", U ? U.steps : undefined, [0, "steps"], p);
          },
          n = (C, T, E) => {
            if (m === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.name || undefined)
                    L(N, Y(D.name) + "__steps");
                },
                o
              );
            }
          },
          q,
          t = (C, u, v, w, x, y, T, E) => {
            var z = (C) => {};
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C || K || x) O(N, "value", v);
                if (C || K || w) O(N, "label", u);
              },
              z
            );
          },
          s = (C, T, E, B, F) => {
            F(D.steps, "index", U ? U.steps : undefined, [0, "steps"], t);
          },
          r = (C, T, E) => {
            if (q === 1) {
              E(
                "t-tabs",
                {},
                (N, C) => {
                  if (C || K || U.stepIndex) O(N, "value", D.stepIndex);
                  if (C || K || undefined) O(N, "space-evenly", false);
                  if (C) R.v(N, "change", "onTabChange", !1, !1, !1, !1);
                  if (C) R.i(N, "tabs");
                },
                s
              );
            }
          },
          l = (C, T, E, B) => {
            if (k === 1) {
              m = D.theme == "step" ? 1 : 0;
              B(m, n);
              q = D.theme == "tab" ? 1 : 0;
              B(q, r);
            }
          },
          u,
          w = (C, T) => {
            var $A = D.stepIndex;
            C || K || !!U.stepIndex || Z(U.subTitles, $A)
              ? T(Y(X(D.subTitles)[$A]))
              : T();
          },
          v = (C, T, E) => {
            if (u === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.name || undefined)
                    L(N, Y(D.name) + "__options-title");
                },
                w
              );
            }
          },
          y = (C, z, A0, B0, C0, D0, T, E) => {
            var G0 = (C) => {},
              F0 = (C, T, E) => {
                E(
                  "t-radio-group",
                  {},
                  (N, C) => {
                    var $A = A0;
                    if (C || K || !!C0 || Z(U.selectedValue, $A))
                      O(N, "value", X(D.selectedValue)[$A]);
                    if (C || K || U.keys) O(N, "keys", D.keys);
                    if (C || K || B0) O(N, "options", z);
                    if (C) O(N, "placement", "right");
                    if (C) O(N, "icon", "line");
                    if (C) O(N, "borderless", true);
                    if (C || K || C0) R.d(N, "level", A0);
                    if (C) R.v(N, "change", "handleSelect", !1, !1, !1, !1);
                  },
                  G0
                );
              },
              E0 = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!C0 || undefined)
                      L(N, "cascader-radio-group-" + Y(A0));
                  },
                  F0
                );
              };
            E(
              "scroll-view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined)
                  L(N, Y(D.name) + "__options");
                if (C || K || !!U._optionsHeight || undefined)
                  R.y(N, "height:" + Y(D._optionsHeight) + "px");
                if (C) O(N, "scroll-y", true);
                var $A = A0;
                if (C || K || !!C0 || Z(U.scrollTopList, $A))
                  O(N, "scroll-top", X(D.scrollTopList)[$A]);
                if (C) O(N, "type", "list");
              },
              E0
            );
          },
          x = (C, T, E, B, F) => {
            F(D.items, "index", U ? U.items : undefined, [0, "items"], y);
          },
          j = (C, T, E, B) => {
            k = D.steps && X(D.steps).length ? 1 : 0;
            B(k, l);
            var $A = D.stepIndex;
            u = D.subTitles && X(D.subTitles)[$A] ? 1 : 0;
            B(u, v);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined)
                  L(N, Y(D.name) + "__options-container");
                if (
                  C ||
                  K ||
                  !!(Z(U.items, "length") || U.stepIndex) ||
                  undefined
                )
                  R.y(
                    N,
                    "width:" +
                      Y(X(D.items).length + 1) +
                      "00vw;transform:translateX(-" +
                      Y(D.stepIndex) +
                      "00vw)"
                  );
              },
              x
            );
          },
          d = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined)
                  L(N, Y(D.name) + "__title");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined)
                  L(N, Y(D.name) + "__close-btn");
                if (C) R.v(N, "tap", "onClose", !1, !1, !1, !1);
              },
              f
            );
            S("header");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined)
                  L(N, Y(D.name) + "__content");
              },
              j
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || U.name) L(N, D.name);
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "t-popup",
              {},
              (N, C) => {
                if (C) L(N, "class");
                if (C || K || U.visible) O(N, "visible", D.visible);
                A["visible"][0] = (D, E, T) => {
                  O(N, "visible", D.visible);
                  E(N);
                };
                if (C) O(N, "placement", "bottom");
                if (C)
                  R.v(N, "visible-change", "onVisibleChange", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/cascader/cascader";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/cascader/cascader.js";
define(
  "miniprogram_npm/tdesign-miniprogram/cascader/cascader.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0),
      require("../../../@babel/runtime/helpers/Arrayincludes");
    var e = require("../../../@babel/runtime/helpers/toConsumableArray"),
      t = require("../../../@babel/runtime/helpers/slicedToArray"),
      i = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
      s = require("../../../@babel/runtime/helpers/createClass"),
      n = require("../../../@babel/runtime/helpers/classCallCheck"),
      l = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      r = require("../../../@babel/runtime/helpers/defineProperty"),
      h = require("tslib"),
      u = require("../common/src/index"),
      o = v(require("../common/config")),
      d = v(require("./props")),
      c = require("../common/utils");
    function v(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var p = o.default.prefix,
      g = "".concat(p, "-cascader");
    function f(e, t) {
      var i,
        s,
        n =
          null !== (i = null == t ? void 0 : t.label) && void 0 !== i
            ? i
            : "label",
        l =
          null !== (s = null == t ? void 0 : t.value) && void 0 !== s
            ? s
            : "value";
      return e.map(function (e) {
        var t;
        return r((t = {}), n, e[n]), r(t, l, e[l]), t;
      });
    }
    var b = {
        contentHeight: 0,
        stepHeight: 0,
        tabsHeight: 0,
        subTitlesHeight: 0,
        stepsInitHeight: 0,
      },
      m = (function (u) {
        l(v, u);
        var o = a(v);
        function v() {
          var s;
          return (
            n(this, v),
            ((s = o.apply(this, arguments)).externalClasses = [
              "".concat(p, "-class"),
            ]),
            (s.options = { multipleSlots: !0, pureDataPattern: /^options$/ }),
            (s.properties = d.default),
            (s.controlledProps = [{ key: "value", event: "change" }]),
            (s.state = Object.assign({}, b)),
            (s.data = {
              prefix: p,
              name: g,
              stepIndex: 0,
              selectedIndexes: [],
              selectedValue: [],
              scrollTopList: [],
              steps: [],
              _optionsHeight: 0,
            }),
            (s.observers = {
              visible: function (e) {
                var t = this;
                if (e) {
                  var i = this.selectComponent("#tabs");
                  null == i || i.setTrack(),
                    null == i ||
                      i.getTabHeight().then(function (e) {
                        t.state.tabsHeight = e.height;
                      }),
                    this.initOptionsHeight(this.data.steps.length),
                    this.updateScrollTop(),
                    this.initWithValue();
                } else this.state = Object.assign({}, b);
              },
              value: function () {
                this.initWithValue();
              },
              options: function () {
                var e = this.genItems(),
                  t = e.selectedValue,
                  i = e.steps,
                  s = e.items;
                this.setData({
                  steps: i,
                  items: s,
                  selectedValue: t,
                  stepIndex: s.length - 1,
                });
              },
              selectedIndexes: function () {
                var e = this.properties,
                  t = e.visible,
                  i = e.theme,
                  s = this.genItems(),
                  n = s.selectedValue,
                  l = s.steps,
                  a = s.items,
                  r = { steps: l, selectedValue: n, stepIndex: a.length - 1 };
                JSON.stringify(a) !== JSON.stringify(this.data.items) &&
                  Object.assign(r, { items: a }),
                  this.setData(r),
                  t && "step" === i && this.updateOptionsHeight(l.length);
              },
              stepIndex: function () {
                return (0, h.__awaiter)(
                  this,
                  void 0,
                  void 0,
                  i().mark(function e() {
                    return i().wrap(
                      function (e) {
                        for (;;)
                          switch ((e.prev = e.next)) {
                            case 0:
                              this.data.visible && this.updateScrollTop();
                            case 2:
                            case "end":
                              return e.stop();
                          }
                      },
                      e,
                      this
                    );
                  })
                );
              },
            }),
            (s.methods = {
              updateOptionsHeight: function (e) {
                var t = this.state,
                  i = t.contentHeight,
                  s = t.stepsInitHeight,
                  n = t.stepHeight,
                  l = t.subTitlesHeight;
                this.setData({ _optionsHeight: i - s - l - (e - 1) * n });
              },
              initOptionsHeight: function (e) {
                return (0, h.__awaiter)(
                  this,
                  void 0,
                  void 0,
                  i().mark(function s() {
                    var n,
                      l,
                      a,
                      r,
                      h,
                      u,
                      o,
                      d,
                      v = this;
                    return i().wrap(
                      function (i) {
                        for (;;)
                          switch ((i.prev = i.next)) {
                            case 0:
                              return (
                                (n = this.properties),
                                (l = n.theme),
                                (a = n.subTitles),
                                (i.next = 5),
                                (0, c.getRect)(this, ".".concat(g, "__content"))
                              );
                            case 5:
                              if (
                                ((r = i.sent),
                                (h = r.height),
                                (this.state.contentHeight = h),
                                (i.t0 = "step" === l),
                                !i.t0)
                              ) {
                                i.next = 12;
                                break;
                              }
                              return (
                                (i.next = 12),
                                Promise.all([
                                  (0, c.getRect)(
                                    this,
                                    ".".concat(g, "__steps")
                                  ),
                                  (0, c.getRect)(this, ".".concat(g, "__step")),
                                ]).then(function (i) {
                                  var s = t(i, 2),
                                    n = s[0],
                                    l = s[1];
                                  (v.state.stepsInitHeight =
                                    n.height - (e - 1) * l.height),
                                    (v.state.stepHeight = l.height);
                                })
                              );
                            case 12:
                              if (!(a.length > 0)) {
                                i.next = 18;
                                break;
                              }
                              return (
                                (i.next = 15),
                                (0, c.getRect)(
                                  this,
                                  ".".concat(g, "__options-title")
                                )
                              );
                            case 15:
                              (u = i.sent),
                                (o = u.height),
                                (this.state.subTitlesHeight = o);
                            case 18:
                              (d =
                                this.state.contentHeight -
                                this.state.subTitlesHeight),
                                this.setData({
                                  _optionsHeight:
                                    "step" === l
                                      ? d -
                                        this.state.stepsInitHeight -
                                        (e - 1) * this.state.stepHeight
                                      : d - this.state.tabsHeight,
                                });
                            case 20:
                            case "end":
                              return i.stop();
                          }
                      },
                      s,
                      this
                    );
                  })
                );
              },
              initWithValue: function () {
                if (null != this.data.value && "" !== this.data.value) {
                  var e = this.getIndexesByValue(
                    this.data.options,
                    this.data.value
                  );
                  e && this.setData({ selectedIndexes: e });
                } else this.setData({ selectedIndexes: [] });
              },
              getIndexesByValue: function (t, i) {
                for (
                  var s, n, l, a = this.data.keys, r = 0, h = t.length;
                  r < h;
                  r += 1
                ) {
                  var u = t[r];
                  if (
                    u[
                      null !== (s = null == a ? void 0 : a.value) &&
                      void 0 !== s
                        ? s
                        : "value"
                    ] === i
                  )
                    return [r];
                  if (
                    u[
                      null !== (n = null == a ? void 0 : a.children) &&
                      void 0 !== n
                        ? n
                        : "children"
                    ]
                  ) {
                    var o = this.getIndexesByValue(
                      u[
                        null !== (l = null == a ? void 0 : a.children) &&
                        void 0 !== l
                          ? l
                          : "children"
                      ],
                      i
                    );
                    if (o) return [r].concat(e(o));
                  }
                }
              },
              updateScrollTop: function () {
                var e = this,
                  t = this.data,
                  i = t.visible,
                  s = t.items,
                  n = t.selectedIndexes,
                  l = t.stepIndex;
                i &&
                  (0, c.getRect)(this, ".cascader-radio-group-0").then(
                    function (t) {
                      var i,
                        a =
                          t.height /
                          (null === (i = s[0]) || void 0 === i
                            ? void 0
                            : i.length);
                      e.setData(
                        r({}, "scrollTopList[".concat(l, "]"), a * n[l])
                      );
                    }
                  );
              },
              hide: function (e) {
                this.setData({ visible: !1 }),
                  this.triggerEvent("close", { trigger: e });
              },
              onVisibleChange: function () {
                this.hide("overlay");
              },
              onClose: function () {
                this.data.checkStrictly && this.triggerChange(),
                  this.hide("close-btn");
              },
              onStepClick: function (e) {
                var t = e.currentTarget.dataset.index;
                this.setData({ stepIndex: t });
              },
              onTabChange: function (e) {
                var t = e.detail.value;
                this.setData({ stepIndex: t });
              },
              genItems: function () {
                var e,
                  t,
                  i,
                  s,
                  n,
                  l = this.data,
                  a = l.options,
                  r = l.selectedIndexes,
                  h = l.keys,
                  u = l.placeholder,
                  o = [],
                  d = [],
                  c = [f(a, h)];
                if (a.length > 0)
                  for (var v = a, p = 0, g = r.length; p < g; p += 1) {
                    var b = v[r[p]];
                    (v =
                      b[
                        null !== (e = null == h ? void 0 : h.children) &&
                        void 0 !== e
                          ? e
                          : "children"
                      ]),
                      o.push(
                        b[
                          null !== (t = null == h ? void 0 : h.value) &&
                          void 0 !== t
                            ? t
                            : "value"
                        ]
                      ),
                      d.push(
                        b[
                          null !== (i = null == h ? void 0 : h.label) &&
                          void 0 !== i
                            ? i
                            : "label"
                        ]
                      ),
                      b[
                        null !== (s = null == h ? void 0 : h.children) &&
                        void 0 !== s
                          ? s
                          : "children"
                      ] &&
                        c.push(
                          f(
                            b[
                              null !== (n = null == h ? void 0 : h.children) &&
                              void 0 !== n
                                ? n
                                : "children"
                            ],
                            h
                          )
                        );
                  }
                return (
                  d.length < c.length && d.push(u),
                  { selectedValue: o, steps: d, items: c }
                );
              },
              handleSelect: function (e) {
                var t,
                  i,
                  s,
                  n,
                  l,
                  a = e.target.dataset.level,
                  h = e.detail.value,
                  u = this.properties.checkStrictly,
                  o = this.data,
                  d = o.selectedIndexes,
                  c = o.items,
                  v = o.keys,
                  p = o.options,
                  g = o.selectedValue,
                  f = c[a].findIndex(function (e) {
                    var t;
                    return (
                      e[
                        null !== (t = null == v ? void 0 : v.value) &&
                        void 0 !== t
                          ? t
                          : "value"
                      ] === h
                    );
                  }),
                  b = d.slice(0, a).reduce(function (e, t, i) {
                    var s;
                    return 0 === i
                      ? e[t]
                      : e[
                          null !== (s = null == v ? void 0 : v.children) &&
                          void 0 !== s
                            ? s
                            : "children"
                        ][t];
                  }, p);
                if (
                  !(b =
                    0 === a
                      ? b[f]
                      : b[
                          null !== (t = null == v ? void 0 : v.children) &&
                          void 0 !== t
                            ? t
                            : "children"
                        ][f]).disabled
                ) {
                  if (
                    (this.triggerEvent("pick", {
                      value:
                        b[
                          null !== (i = null == v ? void 0 : v.value) &&
                          void 0 !== i
                            ? i
                            : "value"
                        ],
                      label:
                        b[
                          null !== (s = null == v ? void 0 : v.label) &&
                          void 0 !== s
                            ? s
                            : "label"
                        ],
                      index: f,
                      level: a,
                    }),
                    (d[a] = f),
                    u && g.includes(String(h)))
                  )
                    return (
                      (d.length = a), void this.setData({ selectedIndexes: d })
                    );
                  d.length = a + 1;
                  var m = this.genItems().items;
                  (null ===
                    (l =
                      null == b
                        ? void 0
                        : b[
                            null !== (n = null == v ? void 0 : v.children) &&
                            void 0 !== n
                              ? n
                              : "children"
                          ]) || void 0 === l
                    ? void 0
                    : l.length) >= 0
                    ? this.setData(
                        r(
                          { selectedIndexes: d },
                          "items[".concat(a + 1, "]"),
                          m[a + 1]
                        )
                      )
                    : (this.setData({ selectedIndexes: d }, this.triggerChange),
                      this.hide("finish"));
                }
              },
              triggerChange: function () {
                var e,
                  t = this.data,
                  i = t.items,
                  s = t.selectedValue,
                  n = t.selectedIndexes;
                this._trigger("change", {
                  value:
                    null !== (e = s[s.length - 1]) && void 0 !== e ? e : "",
                  selectedOptions: i
                    .map(function (e, t) {
                      return e[n[t]];
                    })
                    .filter(Boolean),
                });
              },
            }),
            s
          );
        }
        return s(v);
      })(u.SuperComponent),
      x = (m = (0, h.__decorate)([(0, u.wxComponent)()], m));
    exports.default = x;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/cascader/cascader.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/cascader/cascader.js");
