var e = require("../../../../../@babel/runtime/helpers/typeof");
!(function (_, d) {
  "object" == ("undefined" == typeof exports ? "undefined" : e(exports)) &&
  "undefined" != typeof module
    ? (module.exports = d(require("dayjs")))
    : "function" == typeof define && define.amd
    ? define(["dayjs"], d)
    : ((_ =
        "undefined" != typeof globalThis
          ? globalThis
          : _ || self).dayjs_locale_zh_cn = d(_.dayjs));
})(void 0, function (_) {
  var d = (function (_) {
      return _ && "object" == e(_) && "default" in _ ? _ : { default: _ };
    })(_),
    t = {
      name: "zh-cn",
      weekdays: "星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),
      weekdaysShort: "周日_周一_周二_周三_周四_周五_周六".split("_"),
      weekdaysMin: "日_一_二_三_四_五_六".split("_"),
      months:
        "一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split(
          "_"
        ),
      monthsShort: "1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split(
        "_"
      ),
      ordinal: function (e, _) {
        return "W" === _ ? e + "周" : e + "日";
      },
      weekStart: 1,
      yearStart: 4,
      formats: {
        LT: "HH:mm",
        LTS: "HH:mm:ss",
        L: "YYYY/MM/DD",
        LL: "YYYY年M月D日",
        LLL: "YYYY年M月D日Ah点mm分",
        LLLL: "YYYY年M月D日ddddAh点mm分",
        l: "YYYY/M/D",
        ll: "YYYY年M月D日",
        lll: "YYYY年M月D日 HH:mm",
        llll: "YYYY年M月D日dddd HH:mm",
      },
      relativeTime: {
        future: "%s内",
        past: "%s前",
        s: "几秒",
        m: "1 分钟",
        mm: "%d 分钟",
        h: "1 小时",
        hh: "%d 小时",
        d: "1 天",
        dd: "%d 天",
        M: "1 个月",
        MM: "%d 个月",
        y: "1 年",
        yy: "%d 年",
      },
      meridiem: function (e, _) {
        var d = 100 * e + _;
        return d < 600
          ? "凌晨"
          : d < 900
          ? "早上"
          : d < 1100
          ? "上午"
          : d < 1300
          ? "中午"
          : d < 1800
          ? "下午"
          : "晚上";
      },
    };
  return d.default.locale(t, null, !0), t;
});
