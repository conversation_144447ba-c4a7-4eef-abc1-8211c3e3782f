Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var t = require("../../../@babel/runtime/helpers/slicedToArray"),
  e = require("../../../@babel/runtime/helpers/createClass"),
  a = require("../../../@babel/runtime/helpers/classCallCheck"),
  i = require("../../../@babel/runtime/helpers/inherits"),
  r = require("../../../@babel/runtime/helpers/createSuper");
require("../../../@babel/runtime/helpers/Objectvalues");
var o = require("../../../@babel/runtime/helpers/toConsumableArray"),
  l = require("tslib"),
  n = require("../common/src/index"),
  u = f(require("../common/config")),
  s = f(require("./props")),
  h = require("./constants"),
  c = require("../common/utils"),
  d = require("./utils");
function f(t) {
  return t && t.__esModule ? t : { default: t };
}
var p = u.default.prefix,
  v = "".concat(p, "-color-picker"),
  g = function (t, e, a) {
    var i,
      r = t.changedTouches[0] || {},
      o = r.pageX,
      l = r.pageY,
      n = r.clientY,
      u = a
        ? e.top
        : null === (i = t.currentTarget) || void 0 === i
        ? void 0
        : i.offsetTop;
    return {
      x: Math.min(Math.max(0, o - e.left), e.width),
      y: Math.min(Math.max(0, (a ? n : l) - u), e.height),
    };
  },
  m = function (t, e) {
    var a = {
        HSV: Object.values(e.getHsva()),
        HSVA: Object.values(e.getHsva()),
        HSL: Object.values(e.getHsla()),
        HSLA: Object.values(e.getHsla()),
        HSB: Object.values(e.getHsla()),
        RGB: Object.values(e.getRgba()),
        RGBA: Object.values(e.getRgba()),
        CMYK: [].concat(o(Object.values(e.getCmyk())), [0]),
        CSS: [e.css, 0],
        HEX: [e.hex, 0],
      },
      i = a[t];
    return i
      ? [].concat(o(i.slice(0, i.length - 1)), [
          "".concat(Math.round(100 * e.alpha), "%"),
        ])
      : a.RGB;
  },
  S = function (t) {
    return void 0 === t
      ? h.DEFAULT_SYSTEM_SWATCH_COLORS
      : t && t.length
      ? t
      : [];
  },
  b = (function (o) {
    i(n, o);
    var l = r(n);
    function n() {
      var e;
      return (
        a(this, n),
        ((e = l.apply(this, arguments)).options = { multipleSlots: !0 }),
        (e.properties = s.default),
        (e.observers = {
          format: function () {
            this.setCoreStyle();
          },
          swatchColors: function (t) {
            this.setData({ innerSwatchList: S(t) });
          },
          type: function (t) {
            this.setData({ isMultiple: "multiple" === t });
          },
          "usePopup, visible": function (t, e) {
            var a = this;
            this.timer && clearTimeout(this.timer),
              t &&
                e &&
                (this.timer = setTimeout(function () {
                  a.getEleReact();
                }, 350));
          },
          value: function (t) {
            t && this.init();
          },
        }),
        (e.color = new d.Color(
          s.default.defaultValue.value ||
            s.default.value.value ||
            h.DEFAULT_COLOR
        )),
        (e.data = {
          prefix: p,
          classPrefix: v,
          panelRect: {
            width: h.SATURATION_PANEL_DEFAULT_WIDTH,
            height: h.SATURATION_PANEL_DEFAULT_HEIGHT,
          },
          sliderRect: { width: h.SLIDER_DEFAULT_WIDTH, left: 0 },
          saturationInfo: { saturation: 0, value: 0 },
          saturationThumbStyle: { left: 0, top: 0 },
          sliderInfo: { value: 0 },
          hueSliderStyle: { left: 0 },
          alphaSliderStyle: { left: 0 },
          innerValue: s.default.defaultValue.value || s.default.value.value,
          showPrimaryColorPreview: !1,
          previewColor: s.default.defaultValue.value || s.default.value.value,
          formatList: m(s.default.format.value, e.color),
          innerSwatchList: S(s.default.swatchColors.value),
          isMultiple: "multiple" === s.default.type.value,
          defaultOverlayProps: {},
        }),
        (e.lifetimes = {
          ready: function () {
            this.init();
          },
          attached: function () {
            var t = this;
            this.debouncedUpdateEleRect = (0, c.debounce)(function (e) {
              return t.updateEleRect(e);
            }, 150);
          },
          detached: function () {
            clearTimeout(this.timer);
          },
        }),
        (e.methods = {
          init: function () {
            var t = this.properties,
              e = t.value,
              a = t.defaultValue,
              i = e || a;
            i && this.setData({ innerValue: i }),
              (this.color = new d.Color(i || h.DEFAULT_COLOR)),
              this.updateColor(),
              this.getEleReact();
          },
          updateEleRect: function (t) {
            if (t) {
              var e = t.detail.scrollTop,
                a = this.data.panelRect,
                i = a.width,
                r = a.height,
                o = a.left,
                l = a.initTop;
              this.setData({
                panelRect: {
                  width: i,
                  height: r,
                  left: o,
                  top: l - e,
                  initTop: l,
                },
              });
            }
          },
          getEleReact: function () {
            var e = this;
            Promise.all([
              (0, c.getRect)(this, ".".concat(v, "__saturation")),
              (0, c.getRect)(this, ".".concat(v, "__slider")),
            ]).then(function (a) {
              var i = t(a, 2),
                r = i[0],
                o = i[1];
              e.setData(
                {
                  panelRect: {
                    width: r.width || h.SATURATION_PANEL_DEFAULT_WIDTH,
                    height: r.height || h.SATURATION_PANEL_DEFAULT_HEIGHT,
                    left: r.left || 0,
                    top: r.top || 0,
                    initTop: r.top || 0,
                  },
                  sliderRect: {
                    left: o.left || 0,
                    width: o.width || h.SLIDER_DEFAULT_WIDTH,
                  },
                },
                function () {
                  e.setCoreStyle();
                }
              );
            });
          },
          clickSwatch: function (t) {
            var e = t.currentTarget.dataset.value;
            this.color.update(e),
              this.emitColorChange("preset"),
              this.setCoreStyle();
          },
          setCoreStyle: function () {
            this.setData({
              sliderInfo: { value: this.color.hue },
              hueSliderStyle: this.getSliderThumbStyle({
                value: this.color.hue,
                maxValue: h.HUE_MAX,
              }),
              alphaSliderStyle: this.getSliderThumbStyle({
                value: 100 * this.color.alpha,
                maxValue: h.ALPHA_MAX,
              }),
              saturationInfo: {
                saturation: this.color.saturation,
                value: this.color.value,
              },
              saturationThumbStyle: this.getSaturationThumbStyle({
                saturation: this.color.saturation,
                value: this.color.value,
              }),
              previewColor: this.color.rgba,
              formatList: m(this.properties.format, this.color),
            });
          },
          emitColorChange: function (t) {
            this.setData({ innerValue: this.formatValue() }),
              this.triggerEvent("change", {
                value: this.formatValue(),
                context: {
                  trigger: t,
                  color: (0, d.getColorObject)(this.color),
                },
              });
          },
          defaultEmptyColor: function () {
            return h.DEFAULT_COLOR;
          },
          updateColor: function () {
            var t = this.data.innerValue || this.defaultEmptyColor();
            this.color.update(t);
          },
          getSaturationAndValueByCoordinate: function (t) {
            var e = this.data.panelRect,
              a = e.width,
              i = e.height,
              r = t.x / a,
              o = 1 - t.y / i;
            return {
              saturation: (r = Math.min(1, Math.max(0, r))),
              value: (o = Math.min(1, Math.max(0, o))),
            };
          },
          getSaturationThumbStyle: function (t) {
            var e = t.saturation,
              a = t.value,
              i = this.data.panelRect,
              r = i.width,
              o = i.height,
              l = Math.round((1 - a) * o),
              n = Math.round(e * r);
            return {
              color: this.color.rgb,
              left: "".concat(n, "px"),
              top: "".concat(l, "px"),
            };
          },
          getSliderThumbStyle: function (t) {
            var e = t.value,
              a = t.maxValue;
            if (this.data.sliderRect.width)
              return {
                left: "".concat(Math.round((e / a) * 100), "%"),
                color: this.color.rgb,
              };
          },
          onChangeSaturation: function (t) {
            var e = t.saturation,
              a = t.value,
              i = this.color,
              r = i.saturation,
              o = i.value,
              l = "palette-saturation-brightness";
            if (a !== o && e !== r)
              (this.color.saturation = e),
                (this.color.value = a),
                (l = "palette-saturation-brightness");
            else if (e !== r)
              (this.color.saturation = e), (l = "palette-saturation");
            else {
              if (a === o) return;
              (this.color.value = a), (l = "palette-brightness");
            }
            this.triggerEvent("palette-bar-change", {
              color: (0, d.getColorObject)(this.color),
            }),
              this.emitColorChange(l),
              this.setCoreStyle();
          },
          formatValue: function () {
            return (
              this.color.getFormatsColorMap()[this.properties.format] ||
              this.color.css
            );
          },
          onChangeSlider: function (t) {
            var e = t.value,
              a = t.isAlpha;
            a ? (this.color.alpha = e / 100) : (this.color.hue = e),
              this.emitColorChange(a ? "palette-alpha-bar" : "palette-hue-bar"),
              this.setCoreStyle();
          },
          handleSaturationDrag: function (t) {
            var e = this.properties,
              a = e.usePopup,
              i = e.fixed,
              r = g(t, this.data.panelRect, a || i),
              o = this.getSaturationAndValueByCoordinate(r),
              l = o.saturation,
              n = o.value;
            this.onChangeSaturation({ saturation: l, value: n });
          },
          handleSliderDrag: function (t) {
            var e =
                arguments.length > 1 && void 0 !== arguments[1] && arguments[1],
              a = this.data.sliderRect.width,
              i = g(t, this.data.sliderRect),
              r = i.x,
              o = e ? h.ALPHA_MAX : h.HUE_MAX,
              l = Math.round((r / a) * o * 100) / 100;
            l < 0 && (l = 0),
              l > o && (l = o),
              this.onChangeSlider({ value: l, isAlpha: e });
          },
          handleDiffDrag: function (t) {
            switch (t.target.dataset.type || t.currentTarget.dataset.type) {
              case "saturation":
                this.handleSaturationDrag(t);
                break;
              case "hue-slider":
                this.handleSliderDrag(t);
                break;
              case "alpha-slider":
                this.handleSliderDrag(t, !0);
            }
          },
          onTouchStart: function (t) {
            this.handleDiffDrag(t);
          },
          onTouchMove: function (t) {
            this.handleDiffDrag(t);
          },
          onTouchEnd: function (t) {
            var e = this;
            wx.nextTick(function () {
              e.handleDiffDrag(t);
            });
          },
          close: function (t) {
            this.properties.autoClose && this.setData({ visible: !1 }),
              this.triggerEvent("close", { trigger: t });
          },
          onVisibleChange: function () {
            this.close("overlay");
          },
        }),
        e
      );
    }
    return e(n);
  })(n.SuperComponent),
  C = (b = (0, l.__decorate)([(0, n.wxComponent)()], b));
exports.default = C;
