var e,
  n,
  t = require("../../../../../@babel/runtime/helpers/typeof");
(e = void 0),
  (n = function () {
    return function (e, n, t) {
      var r = n.prototype,
        o = function (e) {
          return e && (e.indexOf ? e : e.s);
        },
        u = function (e, n, t, r, u) {
          var i = e.name ? e : e.$locale(),
            a = o(i[n]),
            d = o(i[t]),
            f =
              a ||
              d.map(function (e) {
                return e.slice(0, r);
              });
          if (!u) return f;
          var s = i.weekStart;
          return f.map(function (e, n) {
            return f[(n + (s || 0)) % 7];
          });
        },
        i = function () {
          return t.Ls[t.locale()];
        },
        a = function (e, n) {
          return (
            e.formats[n] ||
            (function (e) {
              return e.replace(
                /(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,
                function (e, n, t) {
                  return n || t.slice(1);
                }
              );
            })(e.formats[n.toUpperCase()])
          );
        },
        d = function () {
          var e = this;
          return {
            months: function (n) {
              return n ? n.format("MMMM") : u(e, "months");
            },
            monthsShort: function (n) {
              return n ? n.format("MMM") : u(e, "monthsShort", "months", 3);
            },
            firstDayOfWeek: function () {
              return e.$locale().weekStart || 0;
            },
            weekdays: function (n) {
              return n ? n.format("dddd") : u(e, "weekdays");
            },
            weekdaysMin: function (n) {
              return n ? n.format("dd") : u(e, "weekdaysMin", "weekdays", 2);
            },
            weekdaysShort: function (n) {
              return n ? n.format("ddd") : u(e, "weekdaysShort", "weekdays", 3);
            },
            longDateFormat: function (n) {
              return a(e.$locale(), n);
            },
            meridiem: this.$locale().meridiem,
            ordinal: this.$locale().ordinal,
          };
        };
      (r.localeData = function () {
        return d.bind(this)();
      }),
        (t.localeData = function () {
          var e = i();
          return {
            firstDayOfWeek: function () {
              return e.weekStart || 0;
            },
            weekdays: function () {
              return t.weekdays();
            },
            weekdaysShort: function () {
              return t.weekdaysShort();
            },
            weekdaysMin: function () {
              return t.weekdaysMin();
            },
            months: function () {
              return t.months();
            },
            monthsShort: function () {
              return t.monthsShort();
            },
            longDateFormat: function (n) {
              return a(e, n);
            },
            meridiem: e.meridiem,
            ordinal: e.ordinal,
          };
        }),
        (t.months = function () {
          return u(i(), "months");
        }),
        (t.monthsShort = function () {
          return u(i(), "monthsShort", "months", 3);
        }),
        (t.weekdays = function (e) {
          return u(i(), "weekdays", null, null, e);
        }),
        (t.weekdaysShort = function (e) {
          return u(i(), "weekdaysShort", "weekdays", 3, e);
        }),
        (t.weekdaysMin = function (e) {
          return u(i(), "weekdaysMin", "weekdays", 2, e);
        });
    };
  }),
  "object" == ("undefined" == typeof exports ? "undefined" : t(exports)) &&
  "undefined" != typeof module
    ? (module.exports = n())
    : "function" == typeof define && define.amd
    ? define(n)
    : ((e =
        "undefined" != typeof globalThis
          ? globalThis
          : e || self).dayjs_plugin_localeData = n());
