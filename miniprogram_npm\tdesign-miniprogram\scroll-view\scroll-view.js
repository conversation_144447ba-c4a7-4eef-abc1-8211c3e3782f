Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e,
  r = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  i = require("../../../@babel/runtime/helpers/inherits"),
  o = require("../../../@babel/runtime/helpers/createSuper"),
  s = require("tslib"),
  l = require("../common/src/index"),
  n = (e = require("../common/config")) && e.__esModule ? e : { default: e },
  a = require("../common/version");
var u = n.default.prefix,
  c = (function (e) {
    i(l, e);
    var s = o(l);
    function l() {
      var e;
      return (
        t(this, l),
        ((e = s.apply(this, arguments)).externalClasses = [
          "".concat(u, "-class"),
        ]),
        (e.behaviors = (0, a.canUseProxyScrollView)()
          ? ["wx://proxy-scroll-view"]
          : []),
        (e.properties = { scrollIntoView: { type: String } }),
        e
      );
    }
    return r(l);
  })(l.SuperComponent),
  p = (c = (0, s.__decorate)([(0, l.wxComponent)()], c));
exports.default = p;
