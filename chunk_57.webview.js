__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/tag/tag": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            className: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            tagStyle: new Array(1),
          },
          K = U === true,
          e,
          g,
          h = (C, T, E, B, F, S, J) => {
            var $A = I(g);
            if (g && $A)
              $A(
                R,
                C,
                Object.assign({ tClass: D.prefix + "-icon" }, X(D._icon), {}),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              g = "icon";
              B(g, h);
            }
          },
          d = (C, T, E, B, F, S) => {
            e = D._icon ? 1 : 0;
            B(e, f);
            S("icon");
          },
          i = (C, T, E, B, F, S) => {
            S("");
          },
          j,
          l,
          m = (C, T, E, B, F, S, J) => {
            var $A = I(l);
            if (l && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix + "__icon-close " + D.prefix + "-icon",
                    bindclick: "handleClose",
                    ariaRole: "button",
                    ariaLabel: "关闭",
                  },
                  X(D._closable),
                  {}
                ),
                K ||
                  (U
                    ? U._closable === true ||
                      Object.assign(
                        { tClass: !!(U.classPrefix || U.prefix) || undefined },
                        X(U._closable),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          k = (C, T, E, B, F, S, J) => {
            if (j === 1) {
              l = "icon";
              B(l, m);
            } else {
              S("closable");
            }
          },
          c = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon");
                if (C || K || undefined) O(N, "aria-hidden", true);
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__text");
              },
              i
            );
            j = D._closable ? 1 : 0;
            B(j, k);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.className || U.prefix) || undefined)
                  L(N, Y(D.className) + " class " + Y(D.prefix) + "-class");
                A["className"][0] = (D, E, T) => {
                  L(N, Y(D.className) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([U.tagStyle, U.style, U.customStyle])
                  ) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.tagStyle, D.style, D.customStyle]));
                A["tagStyle"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([D.tagStyle, D.style, D.customStyle])
                      );
                    };
                if (C) R.v(N, "tap", "handleClick", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/tag/tag.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-tag{-webkit-align-items:center;align-items:center;border:",
        [0, 2],
        " solid transparent;border-radius:var(--td-tag-square-border-radius,",
        [0, 8],
        ");box-sizing:border-box;display:-webkit-inline-flex;display:inline-flex;font-size:var(--td-tag-medium-font-size,var(--td-font-size-s,",
        [0, 24],
        "));-webkit-user-select:none;user-select:none;vertical-align:middle}\n.",
        [1],
        "t-tag__text{word-wrap:normal;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\n.",
        [1],
        "t-tag__icon,.",
        [1],
        "t-tag__icon-close{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
        [1],
        "t-tag__icon-close{color:var(--td-tag-close-icon-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))))}\n.",
        [1],
        "t-tag__icon:not(:empty)+.",
        [1],
        "t-tag__text:not(:empty),.",
        [1],
        "t-tag__text:not(:empty)+.",
        [1],
        "t-tag__icon-close:not(:empty){margin-left:",
        [0, 8],
        "}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--small{font-size:var(--td-tag-small-font-size,var(--td-font-size,",
        [0, 20],
        "));height:var(--td-tag-small-height,",
        [0, 40],
        ");line-height:var(--td-tag-small-height,",
        [0, 40],
        ");padding:0 var(--td-tag-small-padding,",
        [0, 11],
        ")}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--small .",
        [1],
        "t-icon,.",
        [1],
        "t-tag.",
        [1],
        "t-tag--small .",
        [1],
        "t-icon-close{font-size:var(--td-tag-small-icon-size,",
        [0, 24],
        ")}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--small .",
        [1],
        "t-tag__icon:not(:empty)+.",
        [1],
        "t-tag__text:not(:empty),.",
        [1],
        "t-tag.",
        [1],
        "t-tag--small .",
        [1],
        "t-tag__text:not(:empty)+.",
        [1],
        "t-tag__icon-close:not(:empty){margin-left:",
        [0, 4],
        "}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--medium{font-size:var(--td-tag-medium-font-size,var(--td-font-size-s,",
        [0, 24],
        "));height:var(--td-tag-medium-height,",
        [0, 48],
        ");line-height:var(--td-tag-medium-height,",
        [0, 48],
        ");padding:0 var(--td-tag-medium-padding,",
        [0, 15],
        ")}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--medium .",
        [1],
        "t-icon,.",
        [1],
        "t-tag.",
        [1],
        "t-tag--medium .",
        [1],
        "t-icon-close{font-size:var(--td-tag-medium-icon-size,",
        [0, 28],
        ")}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--large{font-size:var(--td-tag-large-font-size,var(--td-font-size-base,",
        [0, 28],
        "));height:var(--td-tag-large-height,",
        [0, 56],
        ");line-height:var(--td-tag-large-height,",
        [0, 56],
        ");padding:0 var(--td-tag-large-padding,",
        [0, 15],
        ")}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--large .",
        [1],
        "t-icon,.",
        [1],
        "t-tag.",
        [1],
        "t-tag--large .",
        [1],
        "t-icon-close{font-size:var(--td-tag-large-icon-size,",
        [0, 32],
        ")}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--extra-large{font-size:var(--td-tag-extra-large-font-size,var(--td-font-size-base,",
        [0, 28],
        "));height:var(--td-tag-extra-large-height,",
        [0, 80],
        ");line-height:var(--td-tag-extra-large-height,",
        [0, 80],
        ");padding:0 var(--td-tag-extra-large-padding,",
        [0, 31],
        ")}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--extra-large .",
        [1],
        "t-icon,.",
        [1],
        "t-tag.",
        [1],
        "t-tag--extra-large .",
        [1],
        "t-icon-close{font-size:var(--td-tag-extra-large-icon-size,",
        [0, 32],
        ")}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--square{border-radius:var(--td-tag-square-border-radius,",
        [0, 8],
        ")}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--round{border-radius:var(--td-tag-round-border-radius,999px)}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--mark{border-radius:0;border-bottom-right-radius:var(--td-tag-mark-border-radius,var(--td-tag-round-border-radius,999px));border-top-right-radius:var(--td-tag-mark-border-radius,var(--td-tag-round-border-radius,999px))}\n.",
        [1],
        "t-tag--dark.",
        [1],
        "t-tag--default{background-color:var(--td-tag-default-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));border-color:var(--td-tag-default-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)))}\n.",
        [1],
        "t-tag--dark.",
        [1],
        "t-tag--default,.",
        [1],
        "t-tag--dark.",
        [1],
        "t-tag--primary{color:var(--td-text-color-anti,var(--td-font-white-1,#fff))}\n.",
        [1],
        "t-tag--dark.",
        [1],
        "t-tag--primary{background-color:var(--td-tag-primary-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-color:var(--td-tag-primary-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-tag--dark.",
        [1],
        "t-tag--success{background-color:var(--td-tag-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)));border-color:var(--td-tag-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
        [1],
        "t-tag--dark.",
        [1],
        "t-tag--success,.",
        [1],
        "t-tag--dark.",
        [1],
        "t-tag--warning{color:var(--td-text-color-anti,var(--td-font-white-1,#fff))}\n.",
        [1],
        "t-tag--dark.",
        [1],
        "t-tag--warning{background-color:var(--td-tag-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)));border-color:var(--td-tag-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
        [1],
        "t-tag--dark.",
        [1],
        "t-tag--danger{background-color:var(--td-tag-danger-color,var(--td-error-color,var(--td-error-color-6,#d54941)));border-color:var(--td-tag-danger-color,var(--td-error-color,var(--td-error-color-6,#d54941)));color:var(--td-text-color-anti,var(--td-font-white-1,#fff))}\n.",
        [1],
        "t-tag--dark.",
        [1],
        "t-tag--default{color:var(--td-tag-default-font-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--default{background-color:var(--td-tag-default-light-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));border-color:var(--td-tag-default-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));color:var(--td-tag-default-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)))}\n.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--primary{background-color:var(--td-tag-primary-light-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));border-color:var(--td-tag-primary-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));color:var(--td-tag-primary-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--success{background-color:var(--td-tag-success-light-color,var(--td-success-color-1,#e3f9e9));border-color:var(--td-tag-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)));color:var(--td-tag-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--warning{background-color:var(--td-tag-warning-light-color,var(--td-warning-color-1,#fff1e9));border-color:var(--td-tag-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)));color:var(--td-tag-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--danger{background-color:var(--td-tag-danger-light-color,var(--td-error-color-1,#fff0ed));border-color:var(--td-tag-danger-color,var(--td-error-color,var(--td-error-color-6,#d54941)));color:var(--td-tag-danger-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--default{color:var(--td-tag-default-font-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--danger,.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--default,.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--primary,.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--success,.",
        [1],
        "t-tag--outline.",
        [1],
        "t-tag--warning{background-color:var(--td-tag-outline-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-tag--light.",
        [1],
        "t-tag--default{background-color:var(--td-tag-default-light-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));border-color:var(--td-tag-default-light-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));color:var(--td-tag-default-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)))}\n.",
        [1],
        "t-tag--light.",
        [1],
        "t-tag--primary{background-color:var(--td-tag-primary-light-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));border-color:var(--td-tag-primary-light-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));color:var(--td-tag-primary-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-tag--light.",
        [1],
        "t-tag--success{background-color:var(--td-tag-success-light-color,var(--td-success-color-1,#e3f9e9));border-color:var(--td-tag-success-light-color,var(--td-success-color-1,#e3f9e9));color:var(--td-tag-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
        [1],
        "t-tag--light.",
        [1],
        "t-tag--warning{background-color:var(--td-tag-warning-light-color,var(--td-warning-color-1,#fff1e9));border-color:var(--td-tag-warning-light-color,var(--td-warning-color-1,#fff1e9));color:var(--td-tag-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
        [1],
        "t-tag--light.",
        [1],
        "t-tag--danger{background-color:var(--td-tag-danger-light-color,var(--td-error-color-1,#fff0ed));border-color:var(--td-tag-danger-light-color,var(--td-error-color-1,#fff0ed));color:var(--td-tag-danger-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-tag--light.",
        [1],
        "t-tag--default{color:var(--td-tag-default-font-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-tag--light-outline.",
        [1],
        "t-tag--default{background-color:var(--td-tag-default-light-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));border-color:var(--td-tag-default-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));color:var(--td-tag-default-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)))}\n.",
        [1],
        "t-tag--light-outline.",
        [1],
        "t-tag--primary{background-color:var(--td-tag-primary-light-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));border-color:var(--td-tag-primary-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));color:var(--td-tag-primary-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-tag--light-outline.",
        [1],
        "t-tag--success{background-color:var(--td-tag-success-light-color,var(--td-success-color-1,#e3f9e9));border-color:var(--td-tag-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)));color:var(--td-tag-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
        [1],
        "t-tag--light-outline.",
        [1],
        "t-tag--warning{background-color:var(--td-tag-warning-light-color,var(--td-warning-color-1,#fff1e9));border-color:var(--td-tag-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)));color:var(--td-tag-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
        [1],
        "t-tag--light-outline.",
        [1],
        "t-tag--danger{background-color:var(--td-tag-danger-light-color,var(--td-error-color-1,#fff0ed));border-color:var(--td-tag-danger-color,var(--td-error-color,var(--td-error-color-6,#d54941)));color:var(--td-tag-danger-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-tag--light-outline.",
        [1],
        "t-tag--default{border-color:var(--td-component-border,var(--td-gray-color-4,#dcdcdc));color:var(--td-tag-default-font-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-tag.",
        [1],
        "t-tag--closable.",
        [1],
        "t-tag--disabled{background-color:var(--td-tag-disabled-background-color,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)));border-color:var(--td-tag-disabled-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));color:var(--td-tag-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))));cursor:not-allowed}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/tag/tag.wxss" }
    );
}
