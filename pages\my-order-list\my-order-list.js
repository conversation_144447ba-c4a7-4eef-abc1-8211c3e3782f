var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/toConsumableArray"),
  r = require("../../@babel/runtime/helpers/defineProperty"),
  a = require("../../@babel/runtime/helpers/asyncToGenerator");
Page({
  data: {
    serviceId: "",
    currentTab: "ordered",
    orderList: [],
    filteredOrderList: [],
    loading: !0,
    isEmpty: !1,
    loadingMore: !1,
    hasMore: !0,
    pagination: {
      all: { page: 1, hasMore: !0, loading: !1 },
      ordered: { page: 1, hasMore: !0, loading: !1 },
      accepted: { page: 1, hasMore: !0, loading: !1 },
      completed: { page: 1, hasMore: !0, loading: !1 },
      cancelled: { page: 1, hasMore: !0, loading: !1 },
    },
    statusOrderLists: {
      all: [],
      ordered: [],
      accepted: [],
      completed: [],
      cancelled: [],
    },
    statusMap: {
      ordered: {
        text: "已下单",
        theme: "primary",
        showActions: ["contact", "accept"],
      },
      accepted: {
        text: "已接单",
        theme: "warning",
        showActions: ["contact", "complete"],
      },
      completed: { text: "已完成", theme: "success", showActions: ["contact"] },
      cancelled: { text: "已取消", theme: "default", showActions: [] },
    },
  },
  onLoad: function (e) {
    console.log("Service Order List Page Loaded", e),
      e.serviceId
        ? (this.setData({ serviceId: e.serviceId, currentTab: "ordered" }),
          this.loadOrderList())
        : (wx.showToast({ title: "服务ID不存在", icon: "error" }),
          setTimeout(function () {
            wx.navigateBack();
          }, 1500));
  },
  loadOrderList: function () {
    var n = arguments,
      s = this;
    return a(
      e().mark(function a() {
        var o, c, i, d, l, u, p, h, g, f, w, x, m, b, v, k, L;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  (o = n.length > 0 && void 0 !== n[0] && n[0]),
                    (c = getApp()),
                    (i = s.data),
                    (d = i.currentTab),
                    (l = i.pagination),
                    (u = d),
                    (p = null),
                    (e.t0 = d),
                    (e.next =
                      "ordered" === e.t0
                        ? 8
                        : "accepted" === e.t0
                        ? 10
                        : "completed" === e.t0
                        ? 12
                        : "cancelled" === e.t0
                        ? 14
                        : 16);
                  break;
                case 8:
                  return (p = "ordered"), e.abrupt("break", 16);
                case 10:
                  return (p = "accepted"), e.abrupt("break", 16);
                case 12:
                  return (p = "completed"), e.abrupt("break", 16);
                case 14:
                  return (p = "cancelled"), e.abrupt("break", 16);
                case 16:
                  if (!o) {
                    e.next = 22;
                    break;
                  }
                  if (l[u].hasMore) {
                    e.next = 19;
                    break;
                  }
                  return e.abrupt("return");
                case 19:
                  s.setData(
                    r(
                      { loadingMore: !0 },
                      "pagination.".concat(u, ".loading"),
                      !0
                    )
                  ),
                    (e.next = 23);
                  break;
                case 22:
                  s.setData(
                    (r(
                      (h = { loading: !0 }),
                      "pagination.".concat(u, ".page"),
                      1
                    ),
                    r(h, "pagination.".concat(u, ".hasMore"), !0),
                    r(h, "statusOrderLists.".concat(u), []),
                    h)
                  );
                case 23:
                  if (((e.prev = 23), (g = c.globalData.userInfo.data.id))) {
                    e.next = 27;
                    break;
                  }
                  throw new Error("用户未登录");
                case 27:
                  return (
                    (f = { user_id: g, page: l[u].page, pageSize: 10 }),
                    p && (f.order_status = p),
                    (e.next = 31),
                    c.call({
                      path: "/api/mp/serviceOrder/user/"
                        .concat(g, "/service/")
                        .concat(s.data.serviceId),
                      method: "GET",
                      data: f,
                    })
                  );
                case 31:
                  if (!(w = e.sent) || 0 !== w.code) {
                    e.next = 41;
                    break;
                  }
                  (m = w.data.orders || []),
                    (b = w.data.pagination || {}),
                    (v = b.has_next_page || !1),
                    (k = o
                      ? [].concat(t(s.data.statusOrderLists[u]), t(m))
                      : m),
                    s.setData(
                      (r((x = {}), "statusOrderLists.".concat(u), k),
                      r(x, "pagination.".concat(u, ".hasMore"), v),
                      r(
                        x,
                        "pagination.".concat(u, ".page"),
                        b.current_page + 1
                      ),
                      r(x, "filteredOrderList", k),
                      r(x, "loading", !1),
                      r(x, "loadingMore", !1),
                      r(x, "pagination.".concat(u, ".loading"), !1),
                      r(x, "isEmpty", 0 === k.length),
                      x)
                    ),
                    console.log("".concat(u, " 订单列表获取成功:"), k),
                    (e.next = 42);
                  break;
                case 41:
                  throw new Error(w.message || "获取订单列表失败");
                case 42:
                  e.next = 49;
                  break;
                case 44:
                  (e.prev = 44),
                    (e.t1 = e.catch(23)),
                    console.error("获取订单列表失败：", e.t1),
                    s.setData(
                      (r(
                        (L = { loading: !1, loadingMore: !1 }),
                        "pagination.".concat(u, ".loading"),
                        !1
                      ),
                      r(L, "isEmpty", 0 === s.data.statusOrderLists[u].length),
                      L)
                    ),
                    wx.showToast({
                      title: e.t1.message || "加载失败",
                      icon: "error",
                    });
                case 49:
                case "end":
                  return e.stop();
              }
          },
          a,
          null,
          [[23, 44]]
        );
      })
    )();
  },
  onTabsChange: function (e) {
    var t = "all";
    switch (e.detail.value) {
      case "0":
        t = "ordered";
        break;
      case "1":
        t = "accepted";
        break;
      case "2":
        t = "completed";
        break;
      case "3":
        t = "cancelled";
    }
    this.setData({ currentTab: t }),
      0 === this.data.statusOrderLists[t].length
        ? this.loadOrderList()
        : this.setData({
            filteredOrderList: this.data.statusOrderLists[t],
            isEmpty: 0 === this.data.statusOrderLists[t].length,
          });
  },
  onTabsClick: function (e) {},
  filterOrdersByTab: function (e) {
    var t = this.data.orderList,
      r = [];
    switch (e) {
      case "all":
        r = t;
        break;
      case "ongoing":
        r = t.filter(function (e) {
          return "ordered" === e.order_status || "accepted" === e.order_status;
        });
        break;
      case "completed":
        r = t.filter(function (e) {
          return (
            "completed" === e.order_status || "cancelled" === e.order_status
          );
        });
    }
    this.setData({ filteredOrderList: r, isEmpty: 0 === r.length });
  },
  getOrderStatusInfo: function (e) {
    return (
      this.data.statusMap[e] || {
        text: "未知状态",
        theme: "default",
        showActions: [],
      }
    );
  },
  onContactCustomer: function (e) {
    var t = e.currentTarget.dataset.phone;
    t
      ? wx.makePhoneCall({
          phoneNumber: t,
          fail: function (e) {
            console.error("拨打电话失败：", e),
              wx.showToast({ title: "拨打失败", icon: "error" });
          },
        })
      : wx.showToast({ title: "客户电话不存在", icon: "none" });
  },
  onCompleteOrder: function (t) {
    var r = this;
    return a(
      e().mark(function n() {
        var s, o;
        return e().wrap(function (n) {
          for (;;)
            switch ((n.prev = n.next)) {
              case 0:
                (s = t.currentTarget.dataset.orderId),
                  (o = getApp()),
                  wx.showModal({
                    title: "确认完成",
                    content: "请输入完成备注（可选）",
                    editable: !0,
                    placeholderText: "服务已按时完成，质量满意",
                    success: (function () {
                      var t = a(
                        e().mark(function t(a) {
                          var n, c;
                          return e().wrap(
                            function (e) {
                              for (;;)
                                switch ((e.prev = e.next)) {
                                  case 0:
                                    if (!a.confirm) {
                                      e.next = 25;
                                      break;
                                    }
                                    if (
                                      ((e.prev = 1),
                                      wx.showLoading({ title: "处理中..." }),
                                      (n = o.getUserData()) && n.data.id)
                                    ) {
                                      e.next = 6;
                                      break;
                                    }
                                    throw new Error("无法获取用户信息");
                                  case 6:
                                    return (
                                      (e.next = 8),
                                      o.call({
                                        path: "/api/mp/serviceOrder/".concat(
                                          s,
                                          "/complete"
                                        ),
                                        method: "PUT",
                                        data: {
                                          user_id: n.data.id,
                                          completion_note: a.content || "",
                                        },
                                      })
                                    );
                                  case 8:
                                    if (!(c = e.sent) || 0 !== c.code) {
                                      e.next = 15;
                                      break;
                                    }
                                    return (
                                      wx.showToast({
                                        title: "订单已完成",
                                        icon: "success",
                                      }),
                                      (e.next = 13),
                                      r.loadOrderList()
                                    );
                                  case 13:
                                    e.next = 16;
                                    break;
                                  case 15:
                                    throw new Error(c.message || "操作失败");
                                  case 16:
                                    e.next = 22;
                                    break;
                                  case 18:
                                    (e.prev = 18),
                                      (e.t0 = e.catch(1)),
                                      console.error("完成订单失败：", e.t0),
                                      wx.showToast({
                                        title: e.t0.message || "操作失败",
                                        icon: "error",
                                      });
                                  case 22:
                                    return (
                                      (e.prev = 22),
                                      wx.hideLoading(),
                                      e.finish(22)
                                    );
                                  case 25:
                                  case "end":
                                    return e.stop();
                                }
                            },
                            t,
                            null,
                            [[1, 18, 22, 25]]
                          );
                        })
                      );
                      return function (e) {
                        return t.apply(this, arguments);
                      };
                    })(),
                  });
              case 3:
              case "end":
                return n.stop();
            }
        }, n);
      })
    )();
  },
  onCancelOrder: function (t) {
    var r = this;
    return a(
      e().mark(function n() {
        var s, o;
        return e().wrap(function (n) {
          for (;;)
            switch ((n.prev = n.next)) {
              case 0:
                (s = t.currentTarget.dataset.orderId),
                  (o = getApp()),
                  wx.showModal({
                    title: "确认取消",
                    content: "",
                    editable: !0,
                    placeholderText: "请输入取消原因（选填）",
                    success: (function () {
                      var t = a(
                        e().mark(function t(a) {
                          var n, c;
                          return e().wrap(
                            function (e) {
                              for (;;)
                                switch ((e.prev = e.next)) {
                                  case 0:
                                    if (!a.confirm) {
                                      e.next = 25;
                                      break;
                                    }
                                    if (
                                      ((e.prev = 1),
                                      wx.showLoading({ title: "处理中..." }),
                                      (n = o.getUserData()) && n.data.id)
                                    ) {
                                      e.next = 6;
                                      break;
                                    }
                                    throw new Error("无法获取用户信息");
                                  case 6:
                                    return (
                                      (e.next = 8),
                                      o.call({
                                        path: "/api/mp/serviceOrder/".concat(
                                          s,
                                          "/cancel"
                                        ),
                                        method: "PUT",
                                        data: {
                                          user_id: n.data.id,
                                          cancel_reason: a.content || "",
                                        },
                                      })
                                    );
                                  case 8:
                                    if (!(c = e.sent) || 0 !== c.code) {
                                      e.next = 15;
                                      break;
                                    }
                                    return (
                                      wx.showToast({
                                        title: "订单已取消",
                                        icon: "success",
                                      }),
                                      (e.next = 13),
                                      r.loadOrderList()
                                    );
                                  case 13:
                                    e.next = 16;
                                    break;
                                  case 15:
                                    throw new Error(c.message || "取消失败");
                                  case 16:
                                    e.next = 22;
                                    break;
                                  case 18:
                                    (e.prev = 18),
                                      (e.t0 = e.catch(1)),
                                      console.error("取消订单失败：", e.t0),
                                      wx.showToast({
                                        title: e.t0.message || "取消失败",
                                        icon: "error",
                                      });
                                  case 22:
                                    return (
                                      (e.prev = 22),
                                      wx.hideLoading(),
                                      e.finish(22)
                                    );
                                  case 25:
                                  case "end":
                                    return e.stop();
                                }
                            },
                            t,
                            null,
                            [[1, 18, 22, 25]]
                          );
                        })
                      );
                      return function (e) {
                        return t.apply(this, arguments);
                      };
                    })(),
                  });
              case 3:
              case "end":
                return n.stop();
            }
        }, n);
      })
    )();
  },
  updateOrderStatus: function (t, r, n) {
    var s = this;
    return a(
      e().mark(function a() {
        var o, c;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  return (
                    (o = getApp()),
                    (e.prev = 1),
                    wx.showLoading({ title: "处理中..." }),
                    (e.next = 5),
                    o.call({
                      path: "/api/mp/serviceOrder/".concat(t, "/status"),
                      method: "PUT",
                      data: { order_status: r },
                    })
                  );
                case 5:
                  if (!(c = e.sent) || 0 !== c.code) {
                    e.next = 12;
                    break;
                  }
                  return (
                    wx.showToast({ title: n, icon: "success" }),
                    (e.next = 10),
                    s.loadOrderList()
                  );
                case 10:
                  e.next = 13;
                  break;
                case 12:
                  throw new Error(c.message || "操作失败");
                case 13:
                  e.next = 19;
                  break;
                case 15:
                  (e.prev = 15),
                    (e.t0 = e.catch(1)),
                    console.error("更新订单状态失败：", e.t0),
                    wx.showToast({
                      title: e.t0.message || "操作失败",
                      icon: "error",
                    });
                case 19:
                  return (e.prev = 19), wx.hideLoading(), e.finish(19);
                case 22:
                case "end":
                  return e.stop();
              }
          },
          a,
          null,
          [[1, 15, 19, 22]]
        );
      })
    )();
  },
  formatTime: function (e) {
    if (!e) return "";
    var t = new Date(e),
      r = t.getFullYear(),
      a = String(t.getMonth() + 1).padStart(2, "0"),
      n = String(t.getDate()).padStart(2, "0"),
      s = String(t.getHours()).padStart(2, "0"),
      o = String(t.getMinutes()).padStart(2, "0");
    return ""
      .concat(r, "-")
      .concat(a, "-")
      .concat(n, " ")
      .concat(s, ":")
      .concat(o);
  },
  onPullDownRefresh: function () {
    this.loadOrderList().finally(function () {
      wx.stopPullDownRefresh();
    });
  },
  onReachBottom: function () {
    var e = this.data,
      t = e.currentTab,
      r = e.pagination;
    r[t].hasMore && !r[t].loading && this.loadOrderList(!0);
  },
  onShow: function () {
    this.data.serviceId && this.loadOrderList();
  },
});
