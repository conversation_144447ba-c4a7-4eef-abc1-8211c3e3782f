__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/upload/upload": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/upload/upload"]();
      var c = R["miniprogram_npm/tdesign-miniprogram/upload/drag"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            draggable: new Array(1),
            gutter: new Array(1),
            customStyle: new Array(1),
            prefix: new Array(1),
          },
          K = U === true,
          g,
          i = (C, j, k, l, m, n, T, E) => {
            var q,
              s = (C) => {},
              r = (C, T, E) => {
                if (q === 1) {
                  E(
                    "t-image",
                    {},
                    (N, C) => {
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "style")) ||
                        undefined
                      )
                        R.y(N, (D.imageProps && X(D.imageProps).style) || "");
                      if (C || K || !!U.classPrefix || undefined)
                        O(N, "t-class", Y(D.classPrefix) + "__thumbnail");
                      if (
                        C ||
                        K ||
                        !!(Z(l, "thumb") || Z(l, "url")) ||
                        undefined
                      )
                        O(N, "src", X(j).thumb || X(j).url);
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "mode")) ||
                        undefined
                      )
                        O(
                          N,
                          "mode",
                          (D.imageProps && X(D.imageProps).mode) || "aspectFill"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "error")) ||
                        undefined
                      )
                        O(
                          N,
                          "error",
                          (D.imageProps && X(D.imageProps).error) || "default"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "lazy")) ||
                        undefined
                      )
                        O(
                          N,
                          "lazy",
                          (D.imageProps && X(D.imageProps).lazy) || false
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "loading")) ||
                        undefined
                      )
                        O(
                          N,
                          "loading",
                          (D.imageProps && X(D.imageProps).loading) || "default"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "shape")) ||
                        undefined
                      )
                        O(
                          N,
                          "shape",
                          (D.imageProps && X(D.imageProps).shape) || "round"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "webp")) ||
                        undefined
                      )
                        O(
                          N,
                          "webp",
                          (D.imageProps && X(D.imageProps).webp) || false
                        );
                      if (
                        C ||
                        K ||
                        !!(
                          U.imageProps || Z(U.imageProps, "showMenuByLongpress")
                        ) ||
                        undefined
                      )
                        O(
                          N,
                          "showMenuByLongpress",
                          (D.imageProps &&
                            X(D.imageProps).showMenuByLongpress) ||
                            false
                        );
                      if (C || K || l) R.d(N, "file", j);
                      if (C || K || m) R.d(N, "index", k);
                      if (C) R.v(N, "tap", "onProofTap", !1, !1, !1, !1);
                    },
                    s
                  );
                }
              },
              t,
              v = (C) => {},
              u = (C, T, E) => {
                if (t === 1) {
                  E(
                    "video",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__thumbnail");
                      if (C || K || Z(l, "url")) O(N, "src", X(j).url);
                      if (C || K || Z(l, "thumb")) O(N, "poster", X(j).thumb);
                      if (C) O(N, "controls", true);
                      if (C || K || undefined) O(N, "autoplay", false);
                      if (C) O(N, "objectFit", "contain");
                      if (C || K || l) R.d(N, "file", j);
                      if (C) R.v(N, "tap", "onFileClick", !1, !1, !1, !1);
                    },
                    v
                  );
                }
              },
              w,
              z,
              B0 = (C) => {},
              C0 = (C, T) => {
                var $A = X(j).percent;
                C ||
                K ||
                !!Z(l, "percent") ||
                ($A ? !!Z(l, "percent") || undefined : undefined)
                  ? T(Y($A ? X(j).percent + "%" : "上传中..."))
                  : T();
              },
              D0 = (C) => {},
              A0 = (C, T, E) => {
                if (z === 1) {
                  E(
                    "t-icon",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        O(
                          N,
                          "t-class",
                          Y(D.classPrefix) + "__progress-loading"
                        );
                      if (C) O(N, "name", "loading");
                      if (C) O(N, "size", "48rpx");
                      if (C) O(N, "aria-hidden", true);
                    },
                    B0
                  );
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-text");
                    },
                    C0
                  );
                } else {
                  E(
                    "t-icon",
                    {},
                    (N, C) => {
                      var $A = X(j).status == "reload";
                      if (
                        C ||
                        K ||
                        !!Z(l, "status") ||
                        ($A ? undefined : undefined)
                      )
                        O(N, "name", $A ? "refresh" : "close-circle");
                      if (C) O(N, "size", "48rpx");
                      if (C) O(N, "aria-hidden", true);
                    },
                    D0
                  );
                }
              },
              E0,
              G0 = (C, T) => {
                var $A = X(j).status == "reload";
                C || K || !!Z(l, "status") || ($A ? undefined : undefined)
                  ? T(Y($A ? "重新上传" : "上传失败"))
                  : T();
              },
              F0 = (C, T, E) => {
                if (E0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-text");
                    },
                    G0
                  );
                }
              },
              y = (C, T, E, B) => {
                z = X(j).status == "loading" ? 1 : 0;
                B(z, A0);
                E0 = X(j).status == "reload" || X(j).status == "failed" ? 1 : 0;
                B(E0, F0);
              },
              x = (C, T, E) => {
                if (w === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-mask");
                      if (C || K || m) R.d(N, "index", k);
                      if (C || K || l) R.d(N, "file", j);
                      if (C) R.v(N, "tap", "onFileClick", !1, !1, !1, !1);
                    },
                    y
                  );
                }
              },
              H0,
              K0 = (C) => {},
              J0 = (C, T, E) => {
                E(
                  "t-icon",
                  {},
                  (N, C) => {
                    if (C) O(N, "name", "close");
                    if (C) O(N, "size", "32rpx");
                    if (C) O(N, "color", "#fff");
                  },
                  K0
                );
              },
              I0 = (C, T, E) => {
                if (H0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__close-btn hotspot-expanded");
                      if (C) O(N, "aria-role", "button");
                      if (C) O(N, "aria-label", "删除");
                      if (C) O(N, "bindtap", "onDelete");
                      if (C || K || m) R.d(N, "index", k);
                    },
                    J0
                  );
                }
              },
              p = (C, T, E, B) => {
                q = X(j).type !== "video" ? 1 : 0;
                B(q, r);
                t = X(j).type === "video" ? 1 : 0;
                B(t, u);
                w = X(j).status && X(j).status != "done" ? 1 : 0;
                B(w, x);
                var $A = P(X(a).isBoolean)(X(j).removeBtn);
                H0 = ($A ? X(j).removeBtn : D.removeBtn) ? 1 : 0;
                B(H0, I0);
              },
              o = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    var $A = D.disabled;
                    if (
                      C ||
                      K ||
                      !!(
                        U.classPrefix ||
                        U.disabled ||
                        ($A ? !!U.classPrefix || undefined : undefined)
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__wrapper " +
                          Y($A ? D.classPrefix + "__wrapper--disabled" : "")
                      );
                    if (C || K || U.gridItemStyle) R.y(N, D.gridItemStyle);
                    if (
                      C ||
                      K ||
                      !!(
                        U.ariaRole ||
                        Z(undefined, "getWrapperAriaRole") ||
                        l
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "aria-role",
                        D.ariaRole || P(X(b).getWrapperAriaRole)(j)
                      );
                    if (
                      C ||
                      K ||
                      !!(
                        U.ariaLabel ||
                        Z(undefined, "getWrapperAriaLabel") ||
                        l
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "aria-label",
                        D.ariaLabel || P(X(b).getWrapperAriaLabel)(j)
                      );
                  },
                  p
                );
              };
            E(
              "t-grid-item",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                  O(
                    N,
                    "t-class",
                    Y(D.classPrefix) +
                      "__grid " +
                      Y(D.classPrefix) +
                      "__grid-file"
                  );
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class-content", Y(D.classPrefix) + "__grid-content");
                if (C) O(N, "aria-role", "presentation");
              },
              o
            );
          },
          j,
          n,
          q = (C) => {},
          p = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C) O(N, "name", "add");
              },
              q
            );
          },
          o = (C, T, E) => {
            if (n === 1) {
              C || K || U.addContent ? T(Y(D.addContent)) : T();
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.disabled;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.disabled ||
                      ($A ? !!U.classPrefix || undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__add-icon " +
                        Y($A ? D.classPrefix + "__add-icon--disabled" : "")
                    );
                },
                p
              );
            }
          },
          m = (C, T, E, B, F, S) => {
            S("add-content");
            n = D.addContent ? 1 : 0;
            B(n, o);
          },
          l = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrapper");
                if (C || K || U.gridItemStyle) R.y(N, D.gridItemStyle);
              },
              m
            );
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "t-grid-item",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    O(N, "t-class", Y(D.classPrefix) + "__grid");
                  if (C || K || !!U.classPrefix || undefined)
                    O(
                      N,
                      "t-class-content",
                      Y(D.classPrefix) + "__grid-content"
                    );
                  if (C) O(N, "aria-label", "上传");
                  if (C) O(N, "bindclick", "onAddTap");
                },
                l
              );
            }
          },
          s = (C, t, u, v, w, x, T, E) => {
            var B0,
              D0 = (C) => {},
              C0 = (C, T, E) => {
                if (B0 === 1) {
                  E(
                    "t-image",
                    {},
                    (N, C) => {
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "style")) ||
                        undefined
                      )
                        R.y(N, (D.imageProps && X(D.imageProps).style) || "");
                      if (C || K || !!U.classPrefix || undefined)
                        O(N, "t-class", Y(D.classPrefix) + "__thumbnail");
                      if (
                        C ||
                        K ||
                        !!(Z(v, "thumb") || Z(v, "url")) ||
                        undefined
                      )
                        O(N, "src", X(t).thumb || X(t).url);
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "mode")) ||
                        undefined
                      )
                        O(
                          N,
                          "mode",
                          (D.imageProps && X(D.imageProps).mode) || "aspectFill"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "error")) ||
                        undefined
                      )
                        O(
                          N,
                          "error",
                          (D.imageProps && X(D.imageProps).error) || "default"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "lazy")) ||
                        undefined
                      )
                        O(
                          N,
                          "lazy",
                          (D.imageProps && X(D.imageProps).lazy) || false
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "loading")) ||
                        undefined
                      )
                        O(
                          N,
                          "loading",
                          (D.imageProps && X(D.imageProps).loading) || "default"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "shape")) ||
                        undefined
                      )
                        O(
                          N,
                          "shape",
                          (D.imageProps && X(D.imageProps).shape) || "round"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "webp")) ||
                        undefined
                      )
                        O(
                          N,
                          "webp",
                          (D.imageProps && X(D.imageProps).webp) || false
                        );
                      if (
                        C ||
                        K ||
                        !!(
                          U.imageProps || Z(U.imageProps, "showMenuByLongpress")
                        ) ||
                        undefined
                      )
                        O(
                          N,
                          "showMenuByLongpress",
                          (D.imageProps &&
                            X(D.imageProps).showMenuByLongpress) ||
                            false
                        );
                      if (C || K || v) R.d(N, "file", t);
                      if (C || K || w) R.d(N, "index", u);
                      if (C) R.v(N, "tap", "onProofTap", !1, !1, !1, !1);
                    },
                    D0
                  );
                }
              },
              E0,
              G0 = (C) => {},
              F0 = (C, T, E) => {
                if (E0 === 1) {
                  E(
                    "video",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__thumbnail");
                      if (C || K || Z(v, "url")) O(N, "src", X(t).url);
                      if (C || K || Z(v, "thumb")) O(N, "poster", X(t).thumb);
                      if (C) O(N, "controls", true);
                      if (C || K || undefined) O(N, "autoplay", false);
                      if (C) O(N, "objectFit", "contain");
                      if (C || K || v) R.d(N, "file", t);
                      if (C) R.v(N, "tap", "onFileClick", !1, !1, !1, !1);
                    },
                    G0
                  );
                }
              },
              H0,
              K0,
              M0 = (C) => {},
              N0 = (C, T) => {
                var $A = X(t).percent;
                C ||
                K ||
                !!Z(v, "percent") ||
                ($A ? !!Z(v, "percent") || undefined : undefined)
                  ? T(Y($A ? X(t).percent + "%" : "上传中..."))
                  : T();
              },
              O0 = (C) => {},
              L0 = (C, T, E) => {
                if (K0 === 1) {
                  E(
                    "t-icon",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        O(
                          N,
                          "t-class",
                          Y(D.classPrefix) + "__progress-loading"
                        );
                      if (C) O(N, "name", "loading");
                      if (C) O(N, "size", "48rpx");
                      if (C) O(N, "aria-hidden", true);
                    },
                    M0
                  );
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-text");
                    },
                    N0
                  );
                } else {
                  E(
                    "t-icon",
                    {},
                    (N, C) => {
                      var $A = X(t).status == "reload";
                      if (
                        C ||
                        K ||
                        !!Z(v, "status") ||
                        ($A ? undefined : undefined)
                      )
                        O(N, "name", $A ? "refresh" : "close-circle");
                      if (C) O(N, "size", "48rpx");
                      if (C) O(N, "aria-hidden", true);
                    },
                    O0
                  );
                }
              },
              P0,
              R0 = (C, T) => {
                var $A = X(t).status == "reload";
                C || K || !!Z(v, "status") || ($A ? undefined : undefined)
                  ? T(Y($A ? "重新上传" : "上传失败"))
                  : T();
              },
              Q0 = (C, T, E) => {
                if (P0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-text");
                    },
                    R0
                  );
                }
              },
              J0 = (C, T, E, B) => {
                K0 = X(t).status == "loading" ? 1 : 0;
                B(K0, L0);
                P0 = X(t).status == "reload" || X(t).status == "failed" ? 1 : 0;
                B(P0, Q0);
              },
              I0 = (C, T, E) => {
                if (H0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-mask");
                      if (C || K || w) R.d(N, "index", u);
                      if (C || K || v) R.d(N, "file", t);
                      if (C) R.v(N, "tap", "onFileClick", !1, !1, !1, !1);
                    },
                    J0
                  );
                }
              },
              S0,
              V0 = (C) => {},
              U0 = (C, T, E) => {
                E(
                  "t-icon",
                  {},
                  (N, C) => {
                    if (C) O(N, "name", "close");
                    if (C) O(N, "size", "32rpx");
                    if (C) O(N, "color", "#fff");
                  },
                  V0
                );
              },
              T0 = (C, T, E) => {
                if (S0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__close-btn hotspot-expanded");
                      if (C) O(N, "aria-role", "button");
                      if (C) O(N, "aria-label", "删除");
                      if (C) O(N, "bindtap", "onDelete");
                      if (C || K || w) R.d(N, "index", u);
                      if (C || K || Z(v, "url")) R.d(N, "url", X(t).url);
                    },
                    U0
                  );
                }
              },
              A0 = (C, T, E, B) => {
                B0 = X(t).type !== "video" ? 1 : 0;
                B(B0, C0);
                E0 = X(t).type === "video" ? 1 : 0;
                B(E0, F0);
                H0 = X(t).status && X(t).status != "done" ? 1 : 0;
                B(H0, I0);
                var $A = P(X(a).isBoolean)(X(t).removeBtn);
                S0 = ($A ? X(t).removeBtn : D.removeBtn) ? 1 : 0;
                B(S0, T0);
              },
              z = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    var $A = D.disabled;
                    if (
                      C ||
                      K ||
                      !!(
                        U.classPrefix ||
                        U.disabled ||
                        ($A ? !!U.classPrefix || undefined : undefined)
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__wrapper " +
                          Y($A ? D.classPrefix + "__wrapper--disabled" : "")
                      );
                    if (C || K || !!U.gridItemStyle || undefined)
                      R.y(N, Y(D.gridItemStyle) + ";");
                    if (
                      C ||
                      K ||
                      !!(
                        U.ariaRole ||
                        Z(undefined, "getWrapperAriaRole") ||
                        v
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "aria-role",
                        D.ariaRole || P(X(b).getWrapperAriaRole)(t)
                      );
                    if (
                      C ||
                      K ||
                      !!(
                        U.ariaLabel ||
                        Z(undefined, "getWrapperAriaLabel") ||
                        v
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "aria-label",
                        D.ariaLabel || P(X(b).getWrapperAriaLabel)(t)
                      );
                  },
                  A0
                );
              },
              y = (C, T, E) => {
                E(
                  "t-grid-item",
                  {},
                  (N, C) => {
                    if (C) R.y(N, "width:100%");
                    if (
                      C ||
                      K ||
                      !!(U.classPrefix || U.classPrefix) ||
                      undefined
                    )
                      O(
                        N,
                        "t-class",
                        Y(D.classPrefix) +
                          "__grid " +
                          Y(D.classPrefix) +
                          "__grid-file"
                      );
                    if (C || K || !!U.classPrefix || undefined)
                      O(
                        N,
                        "t-class-content",
                        Y(D.classPrefix) + "__grid-content"
                      );
                    if (C) O(N, "aria-role", "presentation");
                  },
                  z
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__drag-item");
                if (
                  C ||
                  K ||
                  !!(
                    U.column ||
                    Z(U.transition, "duration") ||
                    Z(U.transition, "timingFunction")
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    "width:" +
                      Y(100 / D.column) +
                      "%;--td-upload-drag-transition-duration:" +
                      Y(X(D.transition).duration) +
                      "ms;--td-upload-drag-transition-timing-function:" +
                      Y(X(D.transition).timingFunction)
                  );
                if (C || K || w) R.d(N, "index", u);
                if (C || K || Z(undefined, "longPress"))
                  R.v(N, "longpress", X(c).longPress, !1, !1, !1, !0, [
                    1,
                    "miniprogram_npm/tdesign-miniprogram/upload/drag",
                    "longPress",
                  ]);
                var $A = D.dragging;
                if (
                  C ||
                  K ||
                  !!U.dragging ||
                  ($A ? Z(undefined, "touchMove") : undefined)
                )
                  R.v(
                    N,
                    "touchmove",
                    $A ? X(c).touchMove : "",
                    !0,
                    !1,
                    !1,
                    !0,
                    $A
                      ? [
                          1,
                          "miniprogram_npm/tdesign-miniprogram/upload/drag",
                          "touchMove",
                        ]
                      : null
                  );
                var $B = D.dragging;
                if (
                  C ||
                  K ||
                  !!U.dragging ||
                  ($B ? Z(undefined, "touchEnd") : undefined)
                )
                  R.v(
                    N,
                    "touchend",
                    $B ? X(c).touchEnd : "",
                    !0,
                    !1,
                    !1,
                    !0,
                    $B
                      ? [
                          1,
                          "miniprogram_npm/tdesign-miniprogram/upload/drag",
                          "touchEnd",
                        ]
                      : null
                  );
              },
              y
            );
          },
          t,
          y,
          B0 = (C) => {},
          A0 = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C) O(N, "name", "add");
              },
              B0
            );
          },
          z = (C, T, E) => {
            if (y === 1) {
              C || K || U.addContent ? T(Y(D.addContent)) : T();
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.disabled;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.disabled ||
                      ($A ? !!U.classPrefix || undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__add-icon " +
                        Y($A ? D.classPrefix + "__add-icon--disabled" : "")
                    );
                },
                A0
              );
            }
          },
          x = (C, T, E, B, F, S) => {
            S("add-content");
            y = D.addContent ? 1 : 0;
            B(y, z);
          },
          w = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrapper");
                if (C || K || U.gridItemStyle) R.y(N, D.gridItemStyle);
              },
              x
            );
          },
          v = (C, T, E) => {
            E(
              "t-grid-item",
              {},
              (N, C) => {
                if (C) R.y(N, "width:100%");
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class", Y(D.classPrefix) + "__grid");
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class-content", Y(D.classPrefix) + "__grid-content");
                if (C) O(N, "aria-label", "上传");
                if (C) O(N, "bindclick", "onAddTap");
              },
              w
            );
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__drag-item");
                  if (C || K || !!U.column || undefined)
                    R.y(N, "width:" + Y(100 / D.column) + "%");
                },
                v
              );
            }
          },
          r = (C, T, E, B, F) => {
            F(
              D.customFiles,
              "url",
              U ? U.customFiles : undefined,
              [0, "customFiles"],
              s
            );
            t = D.addBtn && D.customLimit > 0 ? 1 : 0;
            B(t, u);
          },
          h = (C, T, E, B, F) => {
            if (g === 1) {
              F(
                D.customFiles,
                "url",
                U ? U.customFiles : undefined,
                [0, "customFiles"],
                i
              );
              j = D.addBtn && D.customLimit > 0 ? 1 : 0;
              B(j, k);
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__drag");
                  if (C || K || !!U.dragWrapStyle || undefined)
                    R.y(N, Y(D.dragWrapStyle) + ";");
                  if (C || K || Z(undefined, "listObserver"))
                    R.p(N, "list", X(c).listObserver, [
                      1,
                      "miniprogram_npm/tdesign-miniprogram/upload/drag",
                      "listObserver",
                    ]);
                  if (C || K || Z(undefined, "baseDataObserver"))
                    R.p(N, "dragBaseData", X(c).baseDataObserver, [
                      1,
                      "miniprogram_npm/tdesign-miniprogram/upload/drag",
                      "baseDataObserver",
                    ]);
                  if (C || K || U.dragList) O(N, "list", D.dragList);
                  if (C || K || U.dragBaseData)
                    O(N, "dragBaseData", D.dragBaseData);
                },
                r
              );
            }
          },
          f = (C, T, E, B) => {
            g = !D.dragLayout ? 1 : 0;
            B(g, h);
          },
          e = (C, T, E) => {
            E(
              "t-grid",
              {},
              (N, C) => {
                var $A = D.draggable;
                if (C || K || !!U.draggable || ($A ? undefined : undefined))
                  R.y(N, $A ? "overflow: visible" : "");
                A["draggable"][0] = (D, E, T) => {
                  var $B = D.draggable;
                  R.y(N, $B ? "overflow: visible" : "");
                };
                if (C || K || U.gutter) O(N, "gutter", D.gutter);
                A["gutter"][0] = (D, E, T) => {
                  O(N, "gutter", D.gutter);
                  E(N);
                };
                if (C || K || undefined) O(N, "border", false);
                if (C) O(N, "align", "center");
                if (C || K || U.column) O(N, "column", D.column);
              },
              f
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["prefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              e
            );
          };
        return { C: d, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/upload/upload.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-upload{position:relative}\n.",
        [1],
        "t-upload__grid-content{padding:0}\n.",
        [1],
        "t-upload__grid-file{position:relative}\n.",
        [1],
        "t-upload__add-icon{-webkit-align-items:center;align-items:center;background-color:var(--td-upload-add-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));border-radius:var(--td-upload-radius,var(--td-radius-default,",
        [0, 12],
        "));color:var(--td-upload-add-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));display:none;font-size:var(--td-upload-add-icon-font-size,",
        [0, 56],
        ");height:100%;-webkit-justify-content:center;justify-content:center;width:100%}\n.",
        [1],
        "t-upload__add-icon--disabled{background-color:var(--td-upload-add-disabled-bg-color,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)));color:var(--td-upload-add-icon-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-upload__add-icon:only-child{display:-webkit-flex;display:flex}\n.",
        [1],
        "t-upload__thumbnail{height:100%;max-height:100%;overflow:hidden;width:100%}\n.",
        [1],
        "t-upload__wrapper{border-radius:var(--td-upload-radius,var(--td-radius-default,",
        [0, 12],
        "));overflow:hidden;position:relative}\n.",
        [1],
        "t-upload__wrapper--disabled::before{background-color:var(--td-upload-disabled-mask,rgba(0,.6));content:\x22\x22;height:100%;left:0;position:absolute;top:0;width:100%;z-index:1}\n.",
        [1],
        "t-upload__close-btn{background-color:var(--td-font-gray-3,rgba(0,0,0,.4));border-bottom-left-radius:var(--td-upload-radius,var(--td-radius-default,",
        [0, 12],
        "));border-top-right-radius:var(--td-upload-radius,var(--td-radius-default,",
        [0, 12],
        "));height:",
        [0, 40],
        ";right:0;width:",
        [0, 40],
        "}\n.",
        [1],
        "t-upload__close-btn,.",
        [1],
        "t-upload__progress-mask{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;position:absolute;top:0}\n.",
        [1],
        "t-upload__progress-mask{background-color:var(--td-font-gray-2,rgba(0,0,0,.6));border-radius:var(--td-upload-radius,var(--td-radius-default,",
        [0, 12],
        "));box-sizing:border-box;color:var(--td-text-color-anti,var(--td-font-white-1,#fff));-webkit-flex-direction:column;flex-direction:column;height:100%;left:0;padding:",
        [0, 32],
        " 0;width:100%}\n.",
        [1],
        "t-upload__progress-text{font-size:",
        [0, 24],
        ";line-height:",
        [0, 40],
        ";margin-top:",
        [0, 8],
        "}\n.",
        [1],
        "t-upload__progress-loading{-webkit-animation:spin .6s linear infinite;animation:spin .6s linear infinite}\n.",
        [1],
        "t-upload__drag{--td-grid-item-bg-color:transparent;position:relative;width:100%}\n.",
        [1],
        "t-upload__drag-item{height:auto;left:0;position:absolute;top:0;width:100%;z-index:1}\n.",
        [1],
        "t-upload__drag--fixed{z-index:0}\n.",
        [1],
        "t-upload__drag--tran{transition-duration:var(--td-upload-drag-transition-duration);transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform;transition-timing-function:var(--td-upload-drag-transition-timing-function)}\n.",
        [1],
        "t-upload__drag--cur{z-index:var(--td-upload-drag-z-index,999)}\n@-webkit-keyframes spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}\n100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}\n}@keyframes spin{0%{-webkit-transform:rotate(0);transform:rotate(0)}\n100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}\n}",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/upload/upload.wxss" }
    );
}
