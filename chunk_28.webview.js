__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/grid-item/grid-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/image"] ||
              {}
            )._,
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = D(
        "miniprogram_npm/tdesign-miniprogram/grid-item/grid-item#util",
        (require, exports, module) => {
          function n(nv_require, nv_exports, nv_module) {
            nv_module.nv_exports.nv_getImageSize = function (nv_column) {
              if (nv_column >= 5) return "small";
              if (nv_column == 4) return "middle";
              return "large";
            };
          }
          const t = { nv_exports: {} };
          n(
            function (n) {
              n.startsWith("p_") && (n = "/" + n.slice(2)),
                n.endsWith(".wxs") && (n = n.slice(0, n.length - 4));
              const t = gdc(require(n), "nv_", 2);
              return function () {
                return t;
              };
            },
            t.nv_exports,
            t
          ),
            Object.assign(module, gdc(t, void 0, 2));
        }
      )();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            gridItemContentStyle: new Array(1),
            customStyle: new Array(1),
            gridItemWrapperStyle: new Array(1),
            describedbyID: new Array(2),
            ariaRole: new Array(1),
            align: new Array(1),
            gridItemStyle: new Array(1),
            hover: new Array(1),
            style: new Array(1),
            ariaLabel: new Array(2),
          },
          K = U === true,
          g,
          k,
          m,
          n = (C, T, E, B, F, S, J) => {
            var $A = I(m);
            if (m && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    src: D.image,
                    shape: "round",
                    mode: "widthFix",
                    tClass:
                      P(X(b).cls)(D.classPrefix + "__image", [
                        P(X(a).getImageSize)(D.column),
                      ]) +
                      " " +
                      D.prefix +
                      "-class-image",
                  },
                  X(D.imageProps),
                  {}
                ),
                K ||
                  (U
                    ? U.imageProps === true ||
                      Object.assign(
                        {
                          src: U.image,
                          tClass:
                            !!(
                              Z(undefined, "cls") ||
                              U.classPrefix ||
                              Q.a([
                                !!(Z(undefined, "getImageSize") || U.column) ||
                                  undefined,
                              ]) ||
                              U.prefix
                            ) || undefined,
                        },
                        X(U.imageProps),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          l = (C, T, E, B, F, S, J) => {
            if (k === 1) {
              m = "image";
              B(m, n);
            }
          },
          o,
          q,
          r = (C, T, E, B, F, S, J) => {
            var $A = I(q);
            if (q && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.classPrefix + "__icon", name: D.iconName },
                  X(D.iconData),
                  {}
                ),
                K ||
                  (U
                    ? U.iconData === true ||
                      Object.assign(
                        {
                          tClass: !!U.classPrefix || undefined,
                          name: U.iconName,
                        },
                        X(U.iconData),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          p = (C, T, E, B, F, S, J) => {
            if (o === 1) {
              q = "icon";
              B(q, r);
            }
          },
          j = (C, T, E, B, F, S) => {
            k = D.image && D.image != "slot" ? 1 : 0;
            B(k, l);
            S("image");
            o = D.iconName || P(X(b).isNoEmptyObj)(D.iconData) ? 1 : 0;
            B(o, p);
          },
          i = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      !!(Z(undefined, "getImageSize") || U.column) || undefined,
                      Q.a([U.icon]),
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__image", [
                        P(X(a).getImageSize)(D.column),
                        ["icon", D.icon],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-image"
                  );
              },
              j
            );
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "t-badge",
                {},
                (N, C) => {
                  if (C || K || !!Z(U.badgeProps, "color") || undefined)
                    O(N, "color", X(D.badgeProps).color || "");
                  if (C || K || !!Z(U.badgeProps, "content") || undefined)
                    O(N, "content", X(D.badgeProps).content || "");
                  if (C || K || !!Z(U.badgeProps, "count") || undefined)
                    O(N, "count", X(D.badgeProps).count || 0);
                  if (C || K || !!Z(U.badgeProps, "dot") || undefined)
                    O(N, "dot", X(D.badgeProps).dot || false);
                  if (C || K || !!Z(U.badgeProps, "maxCount") || undefined)
                    O(N, "max-count", X(D.badgeProps).maxCount || 99);
                  if (
                    C ||
                    K ||
                    !!(Z(U.badgeProps, "offset") || Q.a([])) ||
                    undefined
                  )
                    O(N, "offset", X(D.badgeProps).offset || []);
                  if (C || K || !!Z(U.badgeProps, "shape") || undefined)
                    O(N, "shape", X(D.badgeProps).shape || "circle");
                  if (C || K || !!Z(U.badgeProps, "showZero") || undefined)
                    O(N, "show-zero", X(D.badgeProps).showZero || false);
                  if (C || K || !!Z(U.badgeProps, "size") || undefined)
                    O(N, "size", X(D.badgeProps).size || "medium");
                  if (C || K || Z(U.badgeProps, "tClass"))
                    O(N, "t-class", X(D.badgeProps).tClass);
                  if (C || K || Z(U.badgeProps, "tClassContent"))
                    O(N, "t-class-content", X(D.badgeProps).tClassContent);
                  if (C || K || Z(U.badgeProps, "tClassCount"))
                    O(N, "t-class-count", X(D.badgeProps).tClassCount);
                },
                i
              );
            }
          },
          t,
          v = (C, T) => {
            C || K || U.text ? T(Y(D.text)) : T();
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        !!(Z(undefined, "getImageSize") || U.column) ||
                          undefined,
                        U.layout,
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(b).cls)(D.classPrefix + "__text", [
                          P(X(a).getImageSize)(D.column),
                          D.layout,
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-text"
                    );
                },
                v
              );
            }
          },
          w,
          y = (C, T) => {
            C || K || U.description ? T(Y(D.description)) : T();
          },
          x = (C, T, E) => {
            if (w === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        !!(Z(undefined, "getImageSize") || U.column) ||
                          undefined,
                        U.layout,
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(b).cls)(D.classPrefix + "__description", [
                          P(X(a).getImageSize)(D.column),
                          D.layout,
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-description"
                    );
                },
                y
              );
            }
          },
          s = (C, T, E, B, F, S) => {
            t = D.text ? 1 : 0;
            B(t, u);
            S("text");
            w = D.description ? 1 : 0;
            B(w, x);
            S("description");
          },
          f = (C, T, E, B, F, S) => {
            S("");
            g = D.image || D.icon ? 1 : 0;
            B(g, h);
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "cls") || U.classPrefix || Q.a([U.layout])) ||
                  undefined
                )
                  L(N, P(X(b).cls)(D.classPrefix + "__words", [D.layout]));
                var $A = X(D.badgeProps).dot || X(D.badgeProps).count;
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    Z(U.badgeProps, "dot") ||
                    Z(U.badgeProps, "count") ||
                    ($A
                      ? !!(
                          U.text ||
                          U.description ||
                          Z(undefined, "getBadgeAriaLabel") ||
                          U.badgeProps === true ||
                          Q.b(Object.assign({}, X(U.badgeProps), {}))
                        ) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($A
                        ? D.text +
                          "," +
                          D.description +
                          "," +
                          P(X(b).getBadgeAriaLabel)(
                            Object.assign({}, X(D.badgeProps), {})
                          )
                        : "")
                  );
                A["ariaLabel"][1] = (D, E, T) => {
                  var $B = X(D.badgeProps).dot || X(D.badgeProps).count;
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($B
                        ? D.text +
                          "," +
                          D.description +
                          "," +
                          P(X(b).getBadgeAriaLabel)(
                            Object.assign({}, X(D.badgeProps), {})
                          )
                        : "")
                  );
                  E(N);
                };
                if (C || K || U.describedbyID) R.i(N, D.describedbyID);
                A["describedbyID"][1] = (D, E, T) => {
                  R.i(N, D.describedbyID);
                };
              },
              s
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.align, U.layout]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__content", [
                        D.align,
                        D.layout,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                A["align"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__content", [
                        D.align,
                        D.layout,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                };
                if (C || K || U.gridItemContentStyle)
                  R.y(N, D.gridItemContentStyle);
                A["gridItemContentStyle"][0] = (D, E, T) => {
                  R.y(N, D.gridItemContentStyle);
                };
              },
              f
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "cls") || U.classPrefix || Q.a([U.layout])) ||
                  undefined
                )
                  L(N, P(X(b).cls)(D.classPrefix + "__wrapper", [D.layout]));
                if (C || K || U.gridItemWrapperStyle)
                  R.y(N, D.gridItemWrapperStyle);
                A["gridItemWrapperStyle"][0] = (D, E, T) => {
                  R.y(N, D.gridItemWrapperStyle);
                };
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!U.column || undefined])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix, [["auto-size", D.column == 0]])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([U.gridItemStyle, U.style, U.customStyle])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(b)._style)([D.gridItemStyle, D.style, D.customStyle])
                  );
                A["gridItemStyle"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(b)._style)([
                          D.gridItemStyle,
                          D.style,
                          D.customStyle,
                        ])
                      );
                    };
                var $A = D.hover;
                if (
                  C ||
                  K ||
                  !!U.hover ||
                  ($A ? !!U.classPrefix || undefined : undefined)
                )
                  O(N, "hover-class", $A ? D.classPrefix + "--hover" : "");
                A["hover"][0] = (D, E, T) => {
                  var $B = D.hover;
                  O(N, "hover-class", $B ? D.classPrefix + "--hover" : "");
                  E(N);
                };
                if (C || K || undefined) O(N, "hover-stay-time", 200);
                if (C) O(N, "bindtap", "onClick");
                if (C || K || !!U.ariaRole || undefined)
                  O(N, "aria-role", D.ariaRole || "button");
                A["ariaRole"][0] = (D, E, T) => {
                  O(N, "aria-role", D.ariaRole || "button");
                  E(N);
                };
                if (C || K || U.ariaLabel) O(N, "aria-label", D.ariaLabel);
                A["ariaLabel"][0] = (D, E, T) => {
                  O(N, "aria-label", D.ariaLabel);
                  E(N);
                };
                if (C || K || U.describedbyID)
                  O(N, "aria-describedby", D.describedbyID);
                A["describedbyID"][0] = (D, E, T) => {
                  O(N, "aria-describedby", D.describedbyID);
                  E(N);
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/grid/grid": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            customStyle: new Array(1),
            prefix: new Array(1),
            theme: new Array(1),
          },
          K = U === true,
          d,
          f = (C, T, E, B, F, S) => {
            S("");
          },
          g = (C, T, E, B, F, S) => {
            S("");
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__content");
                  if (C || K || U.contentStyle) R.y(N, D.contentStyle);
                },
                f
              );
            } else {
              E(
                "scroll-view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__content");
                  if (C || K || !!U.contentStyle || undefined)
                    R.y(N, "white-space: nowrap;" + D.contentStyle);
                  if (C) O(N, "scroll-x", true);
                  if (C) O(N, "scroll-with-animation", true);
                },
                g
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.column > 0 ? 1 : 0;
            B(d, e);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.theme]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix, [D.theme])) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["theme"][0] = A["prefix"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix, [D.theme])) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/grid-item/grid-item.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-grid-item{background-color:var(--td-grid-item-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));box-sizing:border-box;display:inline-block;height:100%;vertical-align:top}\n.",
      [1],
      "t-grid-item--hover{background-color:var(--td-grid-item-hover-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)))}\n.",
      [1],
      "t-grid-item--auto-size{width:",
      [0, 168],
      "}\n.",
      [1],
      "t-grid-item__content{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;overflow:hidden;padding:var(--td-grid-item-padding,",
      [0, 32],
      ") 0 ",
      [0, 24],
      ";position:relative}\n.",
      [1],
      "t-grid-item__content--horizontal{-webkit-flex-direction:row;flex-direction:row;padding-left:var(--td-grid-item-padding,",
      [0, 32],
      ")}\n.",
      [1],
      "t-grid-item__content--left{-webkit-align-items:flex-start;align-items:flex-start;justify-self:flex-start}\n.",
      [1],
      "t-grid-item__content--left .",
      [1],
      "t-grid-item__words{text-align:left}\n.",
      [1],
      "t-grid-item__words{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center;position:relative;text-align:center;width:100%}\n.",
      [1],
      "t-grid-item__words--horizontal{margin-left:",
      [0, 24],
      "}\n.",
      [1],
      "t-grid-item__words:empty{display:none}\n.",
      [1],
      "t-grid-item__image:not(:empty){height:var(--td-grid-item-image-width,",
      [0, 96],
      ");width:var(--td-grid-item-image-width,",
      [0, 96],
      ")}\n.",
      [1],
      "t-grid-item__image:not(:empty).",
      [1],
      "t-grid-item__image--small{height:var(--td-grid-item-image-small-width,",
      [0, 64],
      ");width:var(--td-grid-item-image-small-width,",
      [0, 64],
      ")}\n.",
      [1],
      "t-grid-item__image:not(:empty).",
      [1],
      "t-grid-item__image--middle{height:var(--td-grid-item-image-middle-width,",
      [0, 80],
      ");width:var(--td-grid-item-image-middle-width,",
      [0, 80],
      ")}\n.",
      [1],
      "t-grid-item__image:not(:empty) .",
      [1],
      "t-grid__image{height:100%;width:100%}\n.",
      [1],
      "t-grid-item__image--icon{-webkit-align-items:center;align-items:center;background:var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3));border-radius:var(--td-radius-default,",
      [0, 12],
      ");display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "t-grid-item__text{color:var(--td-grid-item-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:var(--td-grid-item-text-font-size,",
      [0, 28],
      ");line-height:var(--td-grid-item-text-line-height,",
      [0, 44],
      ");padding-top:var(--td-grid-item-text-padding-top,",
      [0, 16],
      ");width:inherit}\n.",
      [1],
      "t-grid-item__text--small{font-size:var(--td-grid-item-text-small-font-size,",
      [0, 24],
      ")}\n.",
      [1],
      "t-grid-item__text--middle{font-size:var(--td-grid-item-text-middle-font-size,",
      [0, 24],
      ")}\n.",
      [1],
      "t-grid-item__text--horizontal{padding-top:0;text-align:left}\n.",
      [1],
      "t-grid-item__description{color:var(--td-grid-item-description-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-grid-item-description-font-size,",
      [0, 24],
      ");line-height:var(--td-grid-item-description-line-height,",
      [0, 40],
      ");padding-top:var(--td-grid-item-description-padding-top,0);width:inherit}\n.",
      [1],
      "t-grid-item__description--horizontal{margin-top:var(--td-grid-item-horizontal-text-description-top,0);padding-left:var(--td-grid-item-horizontal-text-padding-left,0);text-align-last:left}\n.",
      [1],
      "t-grid-item__icon{font-size:",
      [0, 48],
      "}\n",
    ],
    undefined,
    { path: "./miniprogram_npm/tdesign-miniprogram/grid-item/grid-item.wxss" }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/grid/grid.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-grid{background-color:var(--td-grid-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));overflow:hidden;position:relative}\n.",
        [1],
        "t-grid__content{width:auto}\n.",
        [1],
        "t-grid--card{border-radius:var(--td-grid-card-radius,var(--td-radius-large,",
        [0, 18],
        "));margin:0 ",
        [0, 32],
        ";overflow:hidden}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/grid/grid.wxss" }
    );
}
