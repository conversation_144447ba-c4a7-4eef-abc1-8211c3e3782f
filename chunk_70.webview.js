__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/login/login": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b = (C, T) => {
            C || K || U.appName ? T(Y(D.appName)) : T();
          },
          d = (C) => {},
          c = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) O(N, "src", "/assets/icon/login.png");
              },
              d
            );
          },
          e = (C, T) => {
            C ? T("邻居自己的信息发布平台") : T();
          },
          f,
          j = (C, T) => {
            C ? T("隐私保护指引") : T();
          },
          k = (C, T) => {
            C || K || !!U.appName || undefined
              ? T(
                  Y(
                    "感谢您使用" +
                      Y(D.appName) +
                      "小程序。我们非常重视您的隐私保护和个人信息保护。"
                  )
                )
              : T();
          },
          m = (C, T) => {
            C || K || U.privacyContractName ? T(Y(D.privacyContractName)) : T();
          },
          l = (C, T, E) => {
            C ? T("请您阅读并同意我们的") : T();
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "privacy-link");
                if (C) O(N, "bindtap", "openPrivacyContract");
              },
              m
            );
            C ? T("后继续使用。") : T();
          },
          n = (C, T) => {
            C
              ? T("我们将严格按照相关法律法规和本指引的规定使用您的个人信息。")
              : T();
          },
          p = (C, T) => {
            C ? T("不同意") : T();
          },
          q = (C, T) => {
            C ? T("同意并继续") : T();
          },
          o = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "disagree-btn");
                if (C) O(N, "bindtap", "exitMiniProgram");
              },
              p
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "agree-btn");
                if (C) O(N, "open-type", "agreePrivacyAuthorization");
                if (C)
                  O(N, "bindagreeprivacyauthorization", "handleAgreePrivacy");
              },
              q
            );
          },
          i = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "privacy-title");
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "privacy-desc");
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "privacy-desc");
              },
              l
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "privacy-desc");
              },
              n
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "button-group");
              },
              o
            );
          },
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "privacy-content");
              },
              i
            );
          },
          g = (C, T, E) => {
            if (f === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "privacy-modal");
                },
                h
              );
            }
          },
          r,
          u = (C, T) => {
            C ? T("正在加载...") : T();
          },
          t = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loading-text");
              },
              u
            );
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                t
              );
            }
          },
          a = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "title");
              },
              b
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loing_img");
              },
              c
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "miaoshu");
              },
              e
            );
            f = D.showPrivacy ? 1 : 0;
            B(f, g);
            r = D.isLoading && !D.showPrivacy ? 1 : 0;
            B(r, s);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/login/login.wxss"] = setCssToHead(
    [
      "body{-webkit-align-items:center;align-items:center;background-color:#fff;box-sizing:border-box;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;height:100vh;-webkit-justify-content:center;justify-content:center;padding:0 ",
      [0, 60],
      "}\n.",
      [1],
      "title{color:#000;font-size:",
      [0, 48],
      ";font-weight:700;margin-bottom:",
      [0, 80],
      ";text-align:center}\n.",
      [1],
      "loing_img{margin-bottom:",
      [0, 60],
      ";text-align:center}\n.",
      [1],
      "loing_img wx-image{border-radius:",
      [0, 20],
      ";height:",
      [0, 300],
      ";width:",
      [0, 300],
      "}\n.",
      [1],
      "miaoshu{color:#b0abab;font-size:",
      [0, 28],
      ";line-height:1.5;text-align:center}\n.",
      [1],
      "privacy-modal{-webkit-align-items:center;align-items:center;background:rgba(0,0,0,.5);display:-webkit-flex;display:flex;height:100%;-webkit-justify-content:center;justify-content:center;left:0;position:fixed;top:0;width:100%;z-index:9999}\n.",
      [1],
      "privacy-content{background:#fff;border-radius:",
      [0, 20],
      ";box-shadow:0 ",
      [0, 10],
      " ",
      [0, 30],
      " rgba(0,0,0,.2);max-width:",
      [0, 600],
      ";padding:",
      [0, 40],
      ";width:85%}\n.",
      [1],
      "privacy-title{color:#333;font-size:",
      [0, 36],
      ";font-weight:700;margin-bottom:",
      [0, 30],
      ";text-align:center}\n.",
      [1],
      "privacy-desc{color:#666;font-size:",
      [0, 28],
      ";line-height:1.6;margin-bottom:",
      [0, 20],
      ";text-align:justify}\n.",
      [1],
      "privacy-link{color:#195abf;font-weight:700;text-decoration:underline}\n.",
      [1],
      "button-group{display:-webkit-flex;display:flex;gap:",
      [0, 20],
      ";margin-top:",
      [0, 40],
      "}\n.",
      [1],
      "disagree-btn{background:#f5f5f5;color:#666}\n.",
      [1],
      "agree-btn,.",
      [1],
      "disagree-btn{border:none;border-radius:",
      [0, 10],
      ";-webkit-flex:1;flex:1;font-size:",
      [0, 28],
      ";height:",
      [0, 80],
      ";line-height:",
      [0, 80],
      "}\n.",
      [1],
      "agree-btn{background:#195abf;color:#fff}\n.",
      [1],
      "agree-btn::after,.",
      [1],
      "disagree-btn::after{border:none}\n.",
      [1],
      "loading-container{left:50%;position:fixed;text-align:center;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}\n.",
      [1],
      "loading-text{color:#999;font-size:",
      [0, 28],
      "}\n",
    ],
    "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/login/login.wxss:1:413)",
    { path: "./pages/login/login.wxss" }
  );
}
