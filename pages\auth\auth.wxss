page {
  overflow: hidden;
}
.container,
page {
  background-color: #fff;
}
.container {
  box-sizing: border-box;
}
.shuoming_box {
  background-color: #f0f0f0;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin: 20rpx auto;
  padding: 20rpx;
  width: 90%;
}
.shuoming_box text {
  color: #666;
  display: block;
  line-height: 1.5;
  margin-bottom: 10rpx;
}
.shuoming_box text:nth-child(1),
.shuoming_box text:nth-child(2) {
  color: #f08c00;
  font-size: 28rpx;
  font-weight: 700;
  text-align: center;
}
.shuoming_box text:nth-child(3),
.shuoming_box text:nth-child(5),
.shuoming_box text:nth-child(7) {
  color: #333;
  font-weight: 700;
  margin-top: 15rpx;
}
.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx auto;
  padding: 40rpx 30rpx;
  width: 95%;
}
.form-item {
  margin-bottom: 30rpx;
  position: relative;
}
.form-label {
  align-items: center;
  color: #333;
  display: -webkit-flex;
  display: flex;
  font-size: 28rpx;
  margin-bottom: 15rpx;
}
.form-label.required::before {
  color: #ff4d4f;
  content: "*";
  font-size: 28rpx;
  margin-right: 8rpx;
}
.form-input {
  background-color: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-sizing: border-box;
  color: #333;
  font-size: 26rpx;
  height: 80rpx;
  padding: 0 20rpx;
  width: 100%;
}
.form-input:focus {
  border-color: #195abf;
}
.form-input::-webkit-input-placeholder {
  color: #999;
}
.form-input::placeholder {
  color: #999;
}
.picker-input {
  align-items: center;
  height: 80rpx;
  line-height: 80rpx;
}
.button-group,
.picker-input {
  display: -webkit-flex;
  display: flex;
}
.button-group {
  gap: 20rpx;
  justify-content: space-between;
  margin-top: 60rpx;
}
.btn {
  align-items: center;
  border: none;
  border-radius: 20rpx;
  display: -webkit-flex;
  display: flex;
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  justify-content: center;
}
.btn-cancel {
  background-color: #fff;
  border: 3rpx solid #1e1e1e;
  color: #666;
}
.btn-submit {
  background-color: #a5d8ff;
  border: 3rpx solid #1971c2;
  color: #1971c2;
}
.btn:active {
  opacity: 0.8;
}
