Component({
  options: { multipleSlots: !0 },
  properties: {
    extClass: { type: String, value: "" },
    title: { type: String, value: "" },
    background: { type: String, value: "" },
    color: { type: String, value: "" },
    back: { type: Boolean, value: !0 },
    loading: { type: Boolean, value: !1 },
    homeButton: { type: Boolean, value: !1 },
    animated: { type: Boolean, value: !0 },
    show: { type: Boolean, value: !0, observer: "_showChange" },
    delta: { type: Number, value: 1 },
  },
  data: { displayStyle: "" },
  lifetimes: {
    attached: function () {
      var t = wx.getMenuButtonBoundingClientRect(),
        e = (wx.getDeviceInfo() || wx.getSystemInfoSync()).platform,
        a = "android" === e,
        n = "devtools" === e,
        o = wx.getWindowInfo() || wx.getSystemInfoSync(),
        i = o.windowWidth,
        l = o.safeArea,
        d = void 0 === l ? {} : l,
        s = d.top,
        c = void 0 === s ? 0 : s;
      d.bottom;
      this.setData({
        ios: !a,
        innerPaddingRight: "padding-right: ".concat(i - t.left, "px"),
        leftWidth: "width: ".concat(i - t.left, "px"),
        safeAreaTop:
          n || a
            ? "height: calc(var(--height) + "
                .concat(c, "px); padding-top: ")
                .concat(c, "px")
            : "",
      });
    },
  },
  methods: {
    _showChange: function (t) {
      var e = "";
      (e = this.data.animated
        ? "opacity: ".concat(t ? "1" : "0", ";transition:opacity 0.5s;")
        : "display: ".concat(t ? "" : "none")),
        this.setData({ displayStyle: e });
    },
    back: function () {
      var t = this.data;
      getCurrentPages().length > 1
        ? t.delta && wx.navigateBack({ delta: t.delta })
        : wx.switchTab({ url: "/pages/index/index" }),
        this.triggerEvent("back", { delta: t.delta }, {});
    },
  },
});
