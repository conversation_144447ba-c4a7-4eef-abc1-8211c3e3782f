__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/cell-group/cell-group": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            theme: new Array(1),
            bordered: new Array(1),
            customStyle: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          c,
          e = (C, T) => {
            C || K || U.title ? T(Y(D.title)) : T();
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      "class " +
                        Y(D.classPrefix) +
                        "__title " +
                        Y(D.prefix) +
                        "-class-title"
                    );
                },
                e
              );
            }
          },
          f = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E, B) => {
            c = D.title ? 1 : 0;
            B(c, d);
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.bordered]), U.theme]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["bordered", D.bordered],
                        D.theme,
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["bordered"][0] = A["theme"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["bordered", D.bordered],
                        D.theme,
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              f
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/indexes/indexes": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/indexes/indexes"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { style: new Array(1), customStyle: new Array(1) },
          K = U === true,
          f = (C, g, h, i, j, k, T, E) => {
            var m = (C, T) => {
                C ||
                K ||
                !!(Z(undefined, "getFirstCharacter") || i) ||
                undefined
                  ? T(Y(P(X(b).getFirstCharacter)(g)))
                  : T();
              },
              n,
              p = (C, T) => {
                C || K || U.activeAnchor ? T(Y(D.activeAnchor)) : T();
              },
              o = (C, T, E) => {
                if (n === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__sidebar-tips");
                    },
                    p
                  );
                }
              },
              l = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) O(N, "aria-role", "button");
                    var $A = D.activeAnchor === g;
                    if (
                      C ||
                      K ||
                      !!(U.activeAnchor || i) ||
                      ($A ? !!i || undefined : undefined)
                    )
                      O(N, "aria-label", $A ? "已选中" + g : "");
                  },
                  m
                );
                n = D.showTips && D.activeAnchor === g ? 1 : 0;
                B(n, o);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!(U.activeAnchor || i) || undefined])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__sidebar-item", [
                        ["active", D.activeAnchor === g],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-sidebar-item"
                  );
                if (C || K || j) R.d(N, "index", h);
                if (C) R.v(N, "tap", "onClick", !1, !1, !1, !1);
              },
              l
            );
          },
          e = (C, T, E, B, F) => {
            F(
              D._indexList,
              "*this",
              U ? U._indexList : undefined,
              [0, "_indexList"],
              f
            );
          },
          d = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__sidebar " +
                      Y(D.prefix) +
                      "-class-sidebar"
                  );
                if (C) R.v(N, "touchmove", "onTouchMove", !0, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchCancel", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !0, !1, !1, !1);
                if (C || K || !!U.classPrefix || undefined)
                  R.i(N, "id-" + Y(D.classPrefix) + "__bar");
              },
              e
            );
            S("");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/cell-group/cell-group";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/cell-group/cell-group.js";
define(
  "miniprogram_npm/tdesign-miniprogram/cell-group/cell-group.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      n = require("../common/src/index"),
      s = u(require("../common/config")),
      l = u(require("./props"));
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var c = s.default.prefix,
      o = "".concat(c, "-cell-group"),
      p = (function (a) {
        t(s, a);
        var n = i(s);
        function s() {
          var e;
          return (
            r(this, s),
            ((e = n.apply(this, arguments)).externalClasses = [
              "".concat(c, "-class"),
              "".concat(c, "-class-title"),
            ]),
            (e.relations = {
              "../cell/cell": {
                type: "child",
                linked: function () {
                  this.updateLastChid();
                },
                unlinked: function () {
                  this.updateLastChid();
                },
              },
            }),
            (e.properties = l.default),
            (e.data = { prefix: c, classPrefix: o }),
            (e.methods = {
              updateLastChid: function () {
                var e = this.$children;
                e.forEach(function (r, t) {
                  return r.setData({ isLastChild: t === e.length - 1 });
                });
              },
            }),
            e
          );
        }
        return e(s);
      })(n.SuperComponent),
      d = (p = (0, a.__decorate)([(0, n.wxComponent)()], p));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/cell-group/cell-group.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/cell-group/cell-group.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/indexes/indexes";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/indexes/indexes.js";
define(
  "miniprogram_npm/tdesign-miniprogram/indexes/indexes.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var t = require("../../../@babel/runtime/helpers/createClass"),
      e = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      r = require("tslib"),
      o = require("../common/src/index"),
      s = l(require("../common/config")),
      h = l(require("./props")),
      a = require("../common/utils"),
      c = l(require("../mixins/page-scroll"));
    function l(t) {
      return t && t.__esModule ? t : { default: t };
    }
    var u = s.default.prefix,
      d = "".concat(u, "-indexes"),
      p = (function (r) {
        i(s, r);
        var o = n(s);
        function s() {
          var t;
          return (
            e(this, s),
            ((t = o.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-sidebar"),
              "".concat(u, "-class-sidebar-item"),
            ]),
            (t.properties = h.default),
            (t.data = {
              prefix: u,
              classPrefix: d,
              _height: 0,
              _indexList: [],
              scrollTop: 0,
              activeAnchor: null,
              showTips: !1,
            }),
            (t.relations = {
              "../indexes-anchor/indexes-anchor": { type: "child" },
            }),
            (t.behaviors = [(0, c.default)()]),
            (t.timer = null),
            (t.groupTop = []),
            (t.sidebar = null),
            (t.currentTouchAnchor = null),
            (t.observers = {
              indexList: function (t) {
                this.setIndexList(t), this.setHeight(this.data._height);
              },
              height: function (t) {
                this.setHeight(t);
              },
            }),
            (t.lifetimes = {
              ready: function () {
                (this.timer = null),
                  (this.groupTop = []),
                  (this.sidebar = null),
                  0 === this.data._height && this.setHeight(),
                  null === this.data.indexList && this.setIndexList();
              },
            }),
            (t.methods = {
              setHeight: function (t) {
                var e = this;
                t || (t = a.systemInfo.windowHeight);
                this.setData({ _height: t }, function () {
                  e.getAllRect();
                });
              },
              setIndexList: function (t) {
                if (t) this.setData({ _indexList: t });
                else {
                  for (
                    var e = "A".charCodeAt(0), i = [], n = e, r = e + 26;
                    n < r;
                    n += 1
                  )
                    i.push(String.fromCharCode(n));
                  this.setData({ _indexList: i });
                }
              },
              getAllRect: function () {
                var t = this;
                this.getAnchorsRect().then(function () {
                  t.groupTop.forEach(function (e, i) {
                    var n = t.groupTop[i + 1];
                    e.totalHeight =
                      ((null == n ? void 0 : n.top) || 1 / 0) - e.top;
                  }),
                    t.setAnchorOnScroll(0);
                }),
                  this.getSidebarRect();
              },
              getAnchorsRect: function () {
                var t = this;
                return Promise.all(
                  this.$children.map(function (e) {
                    return (0, a.getRect)(e, ".".concat(d, "-anchor")).then(
                      function (i) {
                        t.groupTop.push({
                          height: i.height,
                          top: i.top,
                          anchor: e.data.index,
                        });
                      }
                    );
                  })
                );
              },
              getSidebarRect: function () {
                var t = this;
                (0, a.getRect)(this, "#id-".concat(d, "__bar")).then(function (
                  e
                ) {
                  var i = e.top,
                    n = e.height,
                    r = t.data._indexList.length;
                  t.sidebar = {
                    top: i,
                    height: n,
                    itemHeight: (n - 2 * (r - 1)) / r,
                  };
                });
              },
              toggleTips: function (t) {
                var e = this;
                t
                  ? this.setData({ showTips: !0 })
                  : (clearInterval(this.timer),
                    (this.timer = setTimeout(function () {
                      e.setData({ showTips: !1 });
                    }, 300)));
              },
              setAnchorByIndex: function (t) {
                var e = this.data,
                  i = e._indexList,
                  n = e.stickyOffset,
                  r = i[t];
                if (
                  null === this.data.activeAnchor ||
                  this.data.activeAnchor !== r
                ) {
                  var o = this.groupTop.find(function (t) {
                    return t.anchor === r;
                  });
                  if (o) {
                    this.currentTouchAnchor = r;
                    var s = o.top - n;
                    wx.pageScrollTo({ scrollTop: s, duration: 0 }),
                      this.toggleTips(!0),
                      this.triggerEvent("select", { index: r }),
                      this.setData({ activeAnchor: r });
                  }
                }
              },
              onClick: function (t) {
                var e = t.currentTarget.dataset.index;
                this.setAnchorByIndex(e);
              },
              onTouchMove: function (t) {
                this.onAnchorTouch(t);
              },
              onTouchCancel: function () {
                this.toggleTips(!1);
              },
              onTouchEnd: function (t) {
                this.toggleTips(!1), this.onAnchorTouch(t);
              },
              onAnchorTouch: (0, a.throttle)(function (t) {
                var e = this,
                  i = (function (t) {
                    var i = t - e.sidebar.top;
                    return i <= 0
                      ? 0
                      : i > e.sidebar.height
                      ? e.data._indexList.length - 1
                      : Math.floor(i / e.sidebar.itemHeight);
                  })(t.changedTouches[0].clientY);
                this.setAnchorByIndex(i);
              }, 1e3 / 30),
              setAnchorOnScroll: function (t) {
                if (this.groupTop) {
                  var e = this.data,
                    i = e.sticky,
                    n = e.stickyOffset,
                    r = e.activeAnchor;
                  t += n;
                  var o = this.groupTop.findIndex(function (e) {
                    return (
                      t >= e.top - e.height &&
                      t <= e.top + e.totalHeight - e.height
                    );
                  });
                  if (-1 !== o) {
                    var s = this.groupTop[o];
                    if (
                      (null !== this.currentTouchAnchor
                        ? (this.triggerEvent("change", { index: s.anchor }),
                          (this.currentTouchAnchor = null))
                        : r !== s.anchor &&
                          (this.triggerEvent("change", { index: s.anchor }),
                          this.setData({ activeAnchor: s.anchor })),
                      i)
                    ) {
                      var h = s.top - t,
                        a = h < s.height && h > 0 && t > n;
                      this.$children.forEach(function (e, i) {
                        if (i === o) {
                          var r = t > n,
                            c = "transform: translate3d(0, "
                              .concat(a ? h : 0, "px, 0); top: ")
                              .concat(n, "px");
                          (c === e.data.anchorStyle && r === e.data.sticky) ||
                            e.setData({
                              sticky: r,
                              active: !0,
                              style: "height: ".concat(s.height, "px"),
                              anchorStyle: c,
                            });
                        } else if (i + 1 === o) {
                          var l = "transform: translate3d(0, "
                            .concat(a ? h - s.height : 0, "px, 0); top: ")
                            .concat(n, "px");
                          l !== e.data.anchorStyle &&
                            e.setData({
                              sticky: !0,
                              active: !0,
                              style: "height: ".concat(s.height, "px"),
                              anchorStyle: l,
                            });
                        } else
                          e.setData({
                            active: !1,
                            sticky: !1,
                            anchorStyle: "",
                          });
                      });
                    }
                  }
                }
              },
              onScroll: function (t) {
                var e = t.scrollTop;
                this.setAnchorOnScroll(e);
              },
            }),
            t
          );
        }
        return t(s);
      })(o.SuperComponent),
      g = (p = (0, r.__decorate)([(0, o.wxComponent)()], p));
    exports.default = g;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/indexes/indexes.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/indexes/indexes.js");
