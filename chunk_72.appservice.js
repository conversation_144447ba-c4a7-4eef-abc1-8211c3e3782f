__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/service-info/service-info": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || !!Z(U.serviceData, "name") || undefined)
                  O(N, "title", X(D.serviceData).name || "服务详情");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          d,
          i = (C, T) => {
            C || K || !!Z(U.serviceData, "description") || undefined
              ? T(Y(X(D.serviceData).description || "暂无服务说明"))
              : T();
          },
          h = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "description_text");
              },
              i
            );
          },
          g = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "description_box");
              },
              h
            );
          },
          j,
          m = (C, n, o, p, q, r, T, E) => {
            var t = (C) => {},
              s = (C, T, E) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "service_image");
                    if (C || K || p) O(N, "src", n);
                    if (C) O(N, "mode", "widthFix");
                    if (C) O(N, "bindtap", "previewImage");
                    if (C || K || p) R.d(N, "src", n);
                  },
                  t
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_item");
              },
              s
            );
          },
          l = (C, T, E, B, F) => {
            F(
              X(D.serviceData).detail_images,
              "index",
              U ? Z(U.serviceData, "detail_images") : undefined,
              [0, "serviceData", "detail_images"],
              m
            );
          },
          p = (C, T) => {
            C ? T("暂无图片") : T();
          },
          o = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder_text");
              },
              p
            );
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_placeholder");
              },
              o
            );
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "image_section");
                },
                l
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "image_section");
                },
                n
              );
            }
          },
          r = (C, T) => {
            var $A = X(D.serviceData).audit_status !== "approved";
            var $B = X(D.serviceData).publish_status !== "online";
            C ||
            K ||
            !!Z(U.serviceData, "audit_status") ||
            ($A
              ? undefined
              : !!Z(U.serviceData, "publish_status") ||
                ($B ? undefined : undefined))
              ? T(Y($A ? "服务未通过审核" : $B ? "服务已下架" : "立即预约"))
              : T();
          },
          q = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                var $A =
                  X(D.serviceData).audit_status !== "approved" ||
                  X(D.serviceData).publish_status !== "online";
                if (
                  C ||
                  K ||
                  !!(
                    Z(U.serviceData, "audit_status") ||
                    Z(U.serviceData, "publish_status") ||
                    ($A ? undefined : undefined)
                  ) ||
                  undefined
                )
                  L(N, "order_button_text " + Y($A ? "disabled" : ""));
                var $B =
                  X(D.serviceData).audit_status === "approved" &&
                  X(D.serviceData).publish_status === "online";
                if (
                  C ||
                  K ||
                  !!(
                    Z(U.serviceData, "audit_status") ||
                    Z(U.serviceData, "publish_status")
                  ) ||
                  ($B ? undefined : undefined)
                )
                  O(N, "bindtap", $B ? "showOrderModal" : "");
              },
              r
            );
          },
          f = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service_description_section");
              },
              g
            );
            j =
              X(D.serviceData).detail_images &&
              X(X(D.serviceData).detail_images).length > 0
                ? 1
                : 0;
            B(j, k);
            E(
              "view",
              {},
              (N, C) => {
                var $A =
                  X(D.serviceData).audit_status !== "approved" ||
                  X(D.serviceData).publish_status !== "online";
                if (
                  C ||
                  K ||
                  !!(
                    Z(U.serviceData, "audit_status") ||
                    Z(U.serviceData, "publish_status") ||
                    ($A ? undefined : undefined)
                  ) ||
                  undefined
                )
                  L(N, "bottom_actions " + Y($A ? "disabled" : ""));
              },
              q
            );
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "service_info_container");
                },
                f
              );
            }
          },
          s,
          v = (C, T) => {
            C ? T("加载中...") : T();
          },
          u = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loading_text");
              },
              v
            );
          },
          t = (C, T, E) => {
            if (s === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_container");
                },
                u
              );
            }
          },
          w,
          B0 = (C, T) => {
            C ? T("填写订单备注") : T();
          },
          C0 = (C, T) => {
            C ? T("×") : T();
          },
          A0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "modal_title");
              },
              B0
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "modal_close");
                if (C) O(N, "bindtap", "hideOrderModal");
              },
              C0
            );
          },
          F0 = (C, T) => {
            C || K || Z(U.serviceData, "name")
              ? T(Y(X(D.serviceData).name))
              : T();
          },
          G0 = (C, T) => {
            var $A = X(D.serviceData).price;
            C ||
            K ||
            !!Z(U.serviceData, "price") ||
            ($A ? !!Z(U.serviceData, "price") || undefined : undefined)
              ? T(Y($A ? "¥" + X(D.serviceData).price : "价格面议"))
              : T();
          },
          E0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service_name");
              },
              F0
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service_price");
              },
              G0
            );
          },
          I0 = (C, T) => {
            C ? T("备注信息（选填）") : T();
          },
          J0 = (C) => {},
          K0 = (C, T) => {
            C || K || !!Z(U.orderRemark, "length") || undefined
              ? T(Y(Y(X(D.orderRemark).length) + "/200"))
              : T();
          },
          H0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "remark_label");
              },
              I0
            );
            E(
              "textarea",
              {},
              (N, C) => {
                if (C) L(N, "remark_input");
                if (C) O(N, "placeholder", "请输入您的特殊需求或备注信息...");
                if (C || K || U.orderRemark) O(N, "value", D.orderRemark);
                if (C) O(N, "bindinput", "onRemarkInput");
                if (C) O(N, "maxlength", "200");
                if (C || K || undefined) O(N, "show-confirm-bar", false);
              },
              J0
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "remark_count");
              },
              K0
            );
          },
          D0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service_info_summary");
              },
              E0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "remark_section");
              },
              H0
            );
          },
          M0 = (C, T) => {
            C ? T("取消") : T();
          },
          N0 = (C, T) => {
            C ? T("立即预约") : T();
          },
          L0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "cancel_button");
                if (C) O(N, "bindtap", "hideOrderModal");
              },
              M0
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "confirm_button");
                if (C) O(N, "bindtap", "confirmOrder");
              },
              N0
            );
          },
          z = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal_header");
              },
              A0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal_content");
              },
              D0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal_footer");
              },
              L0
            );
          },
          y = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal_container");
                if (C) O(N, "catchtap", "stopPropagation");
              },
              z
            );
          },
          x = (C, T, E) => {
            if (w === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "modal_overlay");
                  if (C) O(N, "bindtap", "hideOrderModal");
                },
                y
              );
            }
          },
          a = (C, T, E, B) => {
            E("page-meta", {}, (N, C) => {}, b);
            d = !D.loading ? 1 : 0;
            B(d, e);
            s = D.loading ? 1 : 0;
            B(s, t);
            w = D.showModal ? 1 : 0;
            B(w, x);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/service-info/service-info";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/service-info/service-info.js";
define(
  "pages/service-info/service-info.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      t = require("../../@babel/runtime/helpers/asyncToGenerator");
    Page({
      data: { serviceData: "", loading: !0, showModal: !1, orderRemark: "" },
      safeNavigateBack: function () {
        getCurrentPages().length > 1
          ? wx.navigateBack()
          : wx.switchTab({ url: "/pages/index/index" });
      },
      onLoad: function (e) {
        var t = this;
        console.log("Service Info Page Loaded", e),
          e.id
            ? this.loadServiceDetail(e.id)
            : (wx.showToast({ title: "服务ID不存在", icon: "error" }),
              setTimeout(function () {
                t.safeNavigateBack();
              }, 1500));
      },
      loadServiceDetail: function (a) {
        var r = this;
        return t(
          e().mark(function t() {
            var o, n;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return (
                        (o = getApp()),
                        r.setData({ loading: !0 }),
                        (e.prev = 2),
                        (e.next = 5),
                        o.call({
                          path: "/api/mp/services/".concat(a),
                          method: "GET",
                        })
                      );
                    case 5:
                      if (((n = e.sent), console.log(n), !n || !n.data)) {
                        e.next = 11;
                        break;
                      }
                      r.setData({ serviceData: n.data.service, loading: !1 }),
                        (e.next = 12);
                      break;
                    case 11:
                      throw new Error("服务详情获取失败");
                    case 12:
                      e.next = 19;
                      break;
                    case 14:
                      (e.prev = 14),
                        (e.t0 = e.catch(2)),
                        console.error("获取服务详情失败：", e.t0),
                        r.setData({ loading: !1 }),
                        wx.showToast({ title: "加载失败", icon: "error" });
                    case 19:
                    case "end":
                      return e.stop();
                  }
              },
              t,
              null,
              [[2, 14]]
            );
          })
        )();
      },
      refreshServiceDetail: function () {
        var a = this;
        return t(
          e().mark(function t() {
            var r;
            return e().wrap(function (e) {
              for (;;)
                switch ((e.prev = e.next)) {
                  case 0:
                    if (!(r = a.data.serviceData.id)) {
                      e.next = 4;
                      break;
                    }
                    return (e.next = 4), a.loadServiceDetail(r);
                  case 4:
                  case "end":
                    return e.stop();
                }
            }, t);
          })
        )();
      },
      onReady: function () {
        console.log("Service Info Page Ready");
      },
      onShow: function () {
        console.log("Service Info Page Show");
      },
      onHide: function () {
        console.log("Service Info Page Hide");
      },
      onUnload: function () {
        console.log("Service Info Page Unload");
      },
      onPullDownRefresh: function () {
        this.refreshServiceDetail().finally(function () {
          wx.stopPullDownRefresh();
        });
      },
      onReachBottom: function () {
        console.log("Reach Bottom");
      },
      onShareAppMessage: function () {
        var e = this.data.serviceData;
        return (
          console.log("111", e),
          {
            title: e.name || "有邻服务",
            path: "/pages/service-info/service-info?id=".concat(e.id),
            imageUrl: e.thumbnail,
          }
        );
      },
      requestSubscribeMessage: function () {
        var e = this;
        return new Promise(function (t) {
          var a = e.data.subscribeTemplateIds;
          if (0 === a.length)
            return console.log("未配置订阅消息模板ID"), void t(!0);
          wx.requestSubscribeMessage({
            tmplIds: a,
            success: function (e) {
              console.log("订阅消息请求成功:", e);
              var r = !1;
              a.forEach(function (t) {
                var a = e[t];
                switch (a) {
                  case "accept":
                    console.log("用户同意订阅模板: ".concat(t)), (r = !0);
                    break;
                  case "reject":
                    console.log("用户拒绝订阅模板: ".concat(t));
                    break;
                  case "ban":
                    console.log("模板已被封禁: ".concat(t));
                    break;
                  case "filter":
                    console.log("模板被过滤: ".concat(t));
                    break;
                  default:
                    console.log("未知状态: ".concat(t, " - ").concat(a));
                }
              }),
                wx.setStorageSync("subscribeStatus", e),
                r &&
                  wx.showToast({
                    title: "订阅成功，将及时通知您",
                    icon: "success",
                    duration: 1500,
                  }),
                t(!0);
            },
            fail: function (e) {
              switch ((console.error("订阅消息请求失败:", e), e.errCode)) {
                case 20004:
                  console.log("用户关闭了主开关"),
                    wx.showToast({
                      title: "请在设置中开启消息通知",
                      icon: "none",
                      duration: 2e3,
                    });
                  break;
                case 20005:
                  console.log("小程序被禁封");
                  break;
                case 10005:
                  console.log("无法展示UI，小程序可能在后台");
                  break;
                default:
                  console.log("其他错误:", e.errMsg);
              }
              t(!0);
            },
            complete: function () {
              console.log("订阅消息请求完成");
            },
          });
        });
      },
      showOrderModal: function () {
        var a = this;
        return t(
          e().mark(function t() {
            var r, o;
            return e().wrap(function (e) {
              for (;;)
                switch ((e.prev = e.next)) {
                  case 0:
                    if (
                      ((r = a.data.serviceData),
                      (o = getApp()).isUserLoggedIn())
                    ) {
                      e.next = 6;
                      break;
                    }
                    return (
                      wx.showToast({ title: "请先登录", icon: "none" }),
                      setTimeout(function () {
                        wx.navigateTo({ url: "/pages/login/login" });
                      }, 1500),
                      e.abrupt("return")
                    );
                  case 6:
                    if (
                      "incomplete" !==
                      (o.globalData.userInfo.data || {}).auth_status.value
                    ) {
                      e.next = 11;
                      break;
                    }
                    return (
                      wx.showToast({ title: "请先完善认证信息", icon: "none" }),
                      setTimeout(function () {
                        wx.navigateTo({ url: "/pages/auth/auth" });
                      }, 1500),
                      e.abrupt("return")
                    );
                  case 11:
                    if ("approved" === r.audit_status) {
                      e.next = 14;
                      break;
                    }
                    return (
                      wx.showToast({
                        title: "服务未通过审核，暂不可预约",
                        icon: "none",
                      }),
                      e.abrupt("return")
                    );
                  case 14:
                    if ("online" === r.publish_status) {
                      e.next = 17;
                      break;
                    }
                    return (
                      wx.showToast({
                        title: "服务已下架，暂不可预约",
                        icon: "none",
                      }),
                      e.abrupt("return")
                    );
                  case 17:
                    o.globalData.needSubscribe.orderStatus &&
                      a.requestOrderStatusSubscribe(),
                      a.setData({ showModal: !0, orderRemark: "" });
                  case 19:
                  case "end":
                    return e.stop();
                }
            }, t);
          })
        )();
      },
      confirmOrder: function () {
        var a = this;
        return t(
          e().mark(function t() {
            var r, o, n, s, i;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      if (
                        ((r = a.data.serviceData),
                        (o = a.data.orderRemark.trim()),
                        (n = getApp()),
                        a.hideOrderModal(),
                        wx.showLoading({ title: "正在预约...", mask: !0 }),
                        (e.prev = 5),
                        (s = n.globalData.userInfo.data),
                        console.log("用户信息:", s),
                        s && s.id)
                      ) {
                        e.next = 10;
                        break;
                      }
                      throw new Error("用户信息不存在，请先登录");
                    case 10:
                      return (
                        (e.next = 12),
                        n.call({
                          path: "/api/mp/serviceOrder",
                          method: "POST",
                          data: {
                            customer_id: s.id,
                            service_id: r.id,
                            remark: o || "",
                          },
                        })
                      );
                    case 12:
                      if (
                        ((i = e.sent), wx.hideLoading(), !i || 0 !== i.code)
                      ) {
                        e.next = 20;
                        break;
                      }
                      wx.showToast({
                        title: "预约成功",
                        icon: "success",
                        duration: 2e3,
                      }),
                        console.log("订单创建成功，订单ID:", i.data.order_id),
                        setTimeout(function () {
                          wx.navigateTo({
                            url: "/pages/order-detail/order-detail?orderId=".concat(
                              i.data.order_id
                            ),
                            fail: function () {
                              wx.switchTab({ url: "/pages/my/my" });
                            },
                          });
                        }, 1500),
                        (e.next = 21);
                      break;
                    case 20:
                      throw new Error(i.message || "预约失败");
                    case 21:
                      e.next = 28;
                      break;
                    case 23:
                      (e.prev = 23),
                        (e.t0 = e.catch(5)),
                        wx.hideLoading(),
                        console.error("预约失败：", e.t0),
                        wx.showToast({
                          title: e.t0.message || "预约失败，请重试",
                          icon: "error",
                          duration: 2e3,
                        });
                    case 28:
                    case "end":
                      return e.stop();
                  }
              },
              t,
              null,
              [[5, 23]]
            );
          })
        )();
      },
      hideOrderModal: function () {
        this.setData({ showModal: !1, orderRemark: "" });
      },
      stopPropagation: function () {},
      onRemarkInput: function (e) {
        this.setData({ orderRemark: e.detail.value });
      },
      onOrderClick: function () {
        this.showOrderModal();
      },
      previewImage: function (e) {
        var t = e.currentTarget.dataset.src,
          a = this.data.serviceData.detail_images || [];
        a.length > 0 && wx.previewImage({ current: t, urls: a });
      },
      requestOrderStatusSubscribe: function () {
        var e = require("../../env/index.js").subscriptionTemplates.orderStatus;
        wx.requestSubscribeMessage({
          tmplIds: [e],
          success: function (t) {
            console.log("订单状态提醒订阅请求成功", t);
            var a = getApp();
            "accept" === t[e]
              ? (wx.showToast({
                  title: "订阅成功，将及时通知您订单状态",
                  icon: "success",
                  duration: 2e3,
                }),
                (a.globalData.needSubscribe.orderStatus = !1),
                (a.globalData.subscriptionStatus.orderStatus = "accept"))
              : "reject" === t[e] &&
                ((a.globalData.needSubscribe.orderStatus = !1),
                (a.globalData.subscriptionStatus.orderStatus = "reject"));
          },
          fail: function (e) {
            console.error("订单状态提醒订阅请求失败", e);
          },
        });
      },
    });
  },
  {
    isPage: true,
    isComponent: true,
    currentFile: "pages/service-info/service-info.js",
  }
);
require("pages/service-info/service-info.js");
