__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/grid-item/grid-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/image"] ||
              {}
            )._,
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = D(
        "miniprogram_npm/tdesign-miniprogram/grid-item/grid-item#util",
        (require, exports, module) => {
          function n(nv_require, nv_exports, nv_module) {
            nv_module.nv_exports.nv_getImageSize = function (nv_column) {
              if (nv_column >= 5) return "small";
              if (nv_column == 4) return "middle";
              return "large";
            };
          }
          const t = { nv_exports: {} };
          n(
            function (n) {
              n.startsWith("p_") && (n = "/" + n.slice(2)),
                n.endsWith(".wxs") && (n = n.slice(0, n.length - 4));
              const t = gdc(require(n), "nv_", 2);
              return function () {
                return t;
              };
            },
            t.nv_exports,
            t
          ),
            Object.assign(module, gdc(t, void 0, 2));
        }
      )();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            gridItemContentStyle: new Array(1),
            customStyle: new Array(1),
            gridItemWrapperStyle: new Array(1),
            describedbyID: new Array(2),
            ariaRole: new Array(1),
            align: new Array(1),
            gridItemStyle: new Array(1),
            hover: new Array(1),
            style: new Array(1),
            ariaLabel: new Array(2),
          },
          K = U === true,
          g,
          k,
          m,
          n = (C, T, E, B, F, S, J) => {
            var $A = I(m);
            if (m && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    src: D.image,
                    shape: "round",
                    mode: "widthFix",
                    tClass:
                      P(X(b).cls)(D.classPrefix + "__image", [
                        P(X(a).getImageSize)(D.column),
                      ]) +
                      " " +
                      D.prefix +
                      "-class-image",
                  },
                  X(D.imageProps),
                  {}
                ),
                K ||
                  (U
                    ? U.imageProps === true ||
                      Object.assign(
                        {
                          src: U.image,
                          tClass:
                            !!(
                              Z(undefined, "cls") ||
                              U.classPrefix ||
                              Q.a([
                                !!(Z(undefined, "getImageSize") || U.column) ||
                                  undefined,
                              ]) ||
                              U.prefix
                            ) || undefined,
                        },
                        X(U.imageProps),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          l = (C, T, E, B, F, S, J) => {
            if (k === 1) {
              m = "image";
              B(m, n);
            }
          },
          o,
          q,
          r = (C, T, E, B, F, S, J) => {
            var $A = I(q);
            if (q && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.classPrefix + "__icon", name: D.iconName },
                  X(D.iconData),
                  {}
                ),
                K ||
                  (U
                    ? U.iconData === true ||
                      Object.assign(
                        {
                          tClass: !!U.classPrefix || undefined,
                          name: U.iconName,
                        },
                        X(U.iconData),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          p = (C, T, E, B, F, S, J) => {
            if (o === 1) {
              q = "icon";
              B(q, r);
            }
          },
          j = (C, T, E, B, F, S) => {
            k = D.image && D.image != "slot" ? 1 : 0;
            B(k, l);
            S("image");
            o = D.iconName || P(X(b).isNoEmptyObj)(D.iconData) ? 1 : 0;
            B(o, p);
          },
          i = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      !!(Z(undefined, "getImageSize") || U.column) || undefined,
                      Q.a([U.icon]),
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__image", [
                        P(X(a).getImageSize)(D.column),
                        ["icon", D.icon],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-image"
                  );
              },
              j
            );
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "t-badge",
                {},
                (N, C) => {
                  if (C || K || !!Z(U.badgeProps, "color") || undefined)
                    O(N, "color", X(D.badgeProps).color || "");
                  if (C || K || !!Z(U.badgeProps, "content") || undefined)
                    O(N, "content", X(D.badgeProps).content || "");
                  if (C || K || !!Z(U.badgeProps, "count") || undefined)
                    O(N, "count", X(D.badgeProps).count || 0);
                  if (C || K || !!Z(U.badgeProps, "dot") || undefined)
                    O(N, "dot", X(D.badgeProps).dot || false);
                  if (C || K || !!Z(U.badgeProps, "maxCount") || undefined)
                    O(N, "max-count", X(D.badgeProps).maxCount || 99);
                  if (
                    C ||
                    K ||
                    !!(Z(U.badgeProps, "offset") || Q.a([])) ||
                    undefined
                  )
                    O(N, "offset", X(D.badgeProps).offset || []);
                  if (C || K || !!Z(U.badgeProps, "shape") || undefined)
                    O(N, "shape", X(D.badgeProps).shape || "circle");
                  if (C || K || !!Z(U.badgeProps, "showZero") || undefined)
                    O(N, "show-zero", X(D.badgeProps).showZero || false);
                  if (C || K || !!Z(U.badgeProps, "size") || undefined)
                    O(N, "size", X(D.badgeProps).size || "medium");
                  if (C || K || Z(U.badgeProps, "tClass"))
                    O(N, "t-class", X(D.badgeProps).tClass);
                  if (C || K || Z(U.badgeProps, "tClassContent"))
                    O(N, "t-class-content", X(D.badgeProps).tClassContent);
                  if (C || K || Z(U.badgeProps, "tClassCount"))
                    O(N, "t-class-count", X(D.badgeProps).tClassCount);
                },
                i
              );
            }
          },
          t,
          v = (C, T) => {
            C || K || U.text ? T(Y(D.text)) : T();
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        !!(Z(undefined, "getImageSize") || U.column) ||
                          undefined,
                        U.layout,
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(b).cls)(D.classPrefix + "__text", [
                          P(X(a).getImageSize)(D.column),
                          D.layout,
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-text"
                    );
                },
                v
              );
            }
          },
          w,
          y = (C, T) => {
            C || K || U.description ? T(Y(D.description)) : T();
          },
          x = (C, T, E) => {
            if (w === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        !!(Z(undefined, "getImageSize") || U.column) ||
                          undefined,
                        U.layout,
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(b).cls)(D.classPrefix + "__description", [
                          P(X(a).getImageSize)(D.column),
                          D.layout,
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-description"
                    );
                },
                y
              );
            }
          },
          s = (C, T, E, B, F, S) => {
            t = D.text ? 1 : 0;
            B(t, u);
            S("text");
            w = D.description ? 1 : 0;
            B(w, x);
            S("description");
          },
          f = (C, T, E, B, F, S) => {
            S("");
            g = D.image || D.icon ? 1 : 0;
            B(g, h);
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "cls") || U.classPrefix || Q.a([U.layout])) ||
                  undefined
                )
                  L(N, P(X(b).cls)(D.classPrefix + "__words", [D.layout]));
                var $A = X(D.badgeProps).dot || X(D.badgeProps).count;
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    Z(U.badgeProps, "dot") ||
                    Z(U.badgeProps, "count") ||
                    ($A
                      ? !!(
                          U.text ||
                          U.description ||
                          Z(undefined, "getBadgeAriaLabel") ||
                          U.badgeProps === true ||
                          Q.b(Object.assign({}, X(U.badgeProps), {}))
                        ) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($A
                        ? D.text +
                          "," +
                          D.description +
                          "," +
                          P(X(b).getBadgeAriaLabel)(
                            Object.assign({}, X(D.badgeProps), {})
                          )
                        : "")
                  );
                A["ariaLabel"][1] = (D, E, T) => {
                  var $B = X(D.badgeProps).dot || X(D.badgeProps).count;
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($B
                        ? D.text +
                          "," +
                          D.description +
                          "," +
                          P(X(b).getBadgeAriaLabel)(
                            Object.assign({}, X(D.badgeProps), {})
                          )
                        : "")
                  );
                  E(N);
                };
                if (C || K || U.describedbyID) R.i(N, D.describedbyID);
                A["describedbyID"][1] = (D, E, T) => {
                  R.i(N, D.describedbyID);
                };
              },
              s
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.align, U.layout]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__content", [
                        D.align,
                        D.layout,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                A["align"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__content", [
                        D.align,
                        D.layout,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                };
                if (C || K || U.gridItemContentStyle)
                  R.y(N, D.gridItemContentStyle);
                A["gridItemContentStyle"][0] = (D, E, T) => {
                  R.y(N, D.gridItemContentStyle);
                };
              },
              f
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "cls") || U.classPrefix || Q.a([U.layout])) ||
                  undefined
                )
                  L(N, P(X(b).cls)(D.classPrefix + "__wrapper", [D.layout]));
                if (C || K || U.gridItemWrapperStyle)
                  R.y(N, D.gridItemWrapperStyle);
                A["gridItemWrapperStyle"][0] = (D, E, T) => {
                  R.y(N, D.gridItemWrapperStyle);
                };
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!U.column || undefined])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix, [["auto-size", D.column == 0]])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([U.gridItemStyle, U.style, U.customStyle])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(b)._style)([D.gridItemStyle, D.style, D.customStyle])
                  );
                A["gridItemStyle"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(b)._style)([
                          D.gridItemStyle,
                          D.style,
                          D.customStyle,
                        ])
                      );
                    };
                var $A = D.hover;
                if (
                  C ||
                  K ||
                  !!U.hover ||
                  ($A ? !!U.classPrefix || undefined : undefined)
                )
                  O(N, "hover-class", $A ? D.classPrefix + "--hover" : "");
                A["hover"][0] = (D, E, T) => {
                  var $B = D.hover;
                  O(N, "hover-class", $B ? D.classPrefix + "--hover" : "");
                  E(N);
                };
                if (C || K || undefined) O(N, "hover-stay-time", 200);
                if (C) O(N, "bindtap", "onClick");
                if (C || K || !!U.ariaRole || undefined)
                  O(N, "aria-role", D.ariaRole || "button");
                A["ariaRole"][0] = (D, E, T) => {
                  O(N, "aria-role", D.ariaRole || "button");
                  E(N);
                };
                if (C || K || U.ariaLabel) O(N, "aria-label", D.ariaLabel);
                A["ariaLabel"][0] = (D, E, T) => {
                  O(N, "aria-label", D.ariaLabel);
                  E(N);
                };
                if (C || K || U.describedbyID)
                  O(N, "aria-describedby", D.describedbyID);
                A["describedbyID"][0] = (D, E, T) => {
                  O(N, "aria-describedby", D.describedbyID);
                  E(N);
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/grid/grid": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            customStyle: new Array(1),
            prefix: new Array(1),
            theme: new Array(1),
          },
          K = U === true,
          d,
          f = (C, T, E, B, F, S) => {
            S("");
          },
          g = (C, T, E, B, F, S) => {
            S("");
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__content");
                  if (C || K || U.contentStyle) R.y(N, D.contentStyle);
                },
                f
              );
            } else {
              E(
                "scroll-view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__content");
                  if (C || K || !!U.contentStyle || undefined)
                    R.y(N, "white-space: nowrap;" + D.contentStyle);
                  if (C) O(N, "scroll-x", true);
                  if (C) O(N, "scroll-with-animation", true);
                },
                g
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.column > 0 ? 1 : 0;
            B(d, e);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.theme]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix, [D.theme])) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["theme"][0] = A["prefix"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix, [D.theme])) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/grid-item/grid-item";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/grid-item/grid-item.js";
define(
  "miniprogram_npm/tdesign-miniprogram/grid-item/grid-item.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      n = require("../common/src/index"),
      o = u(require("../common/config")),
      c = u(require("./props")),
      s = require("../common/utils"),
      l = require("../common/validator");
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var p,
      d,
      h = o.default.prefix,
      g = "".concat(h, "-grid-item"),
      m = (0, s.uniqueFactory)("grid_item");
    ((d = p || (p = {}))["redirect-to"] = "redirectTo"),
      (d["switch-tab"] = "switchTab"),
      (d.relaunch = "reLaunch"),
      (d["navigate-to"] = "navigateTo");
    var v = (function (a) {
        r(o, a);
        var n = i(o);
        function o() {
          var t;
          return (
            e(this, o),
            ((t = n.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
              "".concat(h, "-class-content"),
              "".concat(h, "-class-image"),
              "".concat(h, "-class-text"),
              "".concat(h, "-class-description"),
            ]),
            (t.options = { multipleSlots: !0 }),
            (t.relations = {
              "../grid/grid": {
                type: "ancestor",
                linked: function (e) {
                  (this.parent = e),
                    this.updateStyle(),
                    this.setData({ column: e.data.column });
                },
              },
            }),
            (t.properties = c.default),
            (t.data = {
              prefix: h,
              classPrefix: g,
              gridItemStyle: "",
              gridItemWrapperStyle: "",
              gridItemContentStyle: "",
              align: "center",
              column: 0,
              describedbyID: "",
            }),
            (t.observers = {
              icon: function (e) {
                var t = (0, s.setIcon)("icon", e, "");
                this.setData(Object.assign({}, t));
              },
            }),
            (t.lifetimes = {
              ready: function () {
                this.setData({ describedbyID: m() });
              },
            }),
            t
          );
        }
        return (
          t(o, [
            {
              key: "updateStyle",
              value: function () {
                var e = this.parent.properties,
                  t = e.hover,
                  r = e.align,
                  i = [],
                  a = [],
                  n = [],
                  o = this.getWidthStyle(),
                  c = this.getPaddingStyle(),
                  s = this.getBorderStyle();
                o && i.push(o),
                  c && a.push(c),
                  s && n.push(s),
                  this.setData({
                    gridItemStyle: "".concat(i.join(";")),
                    gridItemWrapperStyle: a.join(";"),
                    gridItemContentStyle: n.join(";"),
                    hover: t,
                    layout: this.properties.layout,
                    align: r,
                  });
              },
            },
            {
              key: "getWidthStyle",
              value: function () {
                var e = this.parent.properties.column;
                return e > 0 ? "width:".concat((1 / e) * 100, "%") : "";
              },
            },
            {
              key: "getPaddingStyle",
              value: function () {
                var e = this.parent.properties.gutter;
                return e
                  ? "padding-left:"
                      .concat(e, "rpx;padding-top:")
                      .concat(e, "rpx")
                  : "";
              },
            },
            {
              key: "getBorderStyle",
              value: function () {
                var e = this.parent.properties.gutter,
                  t = this.parent.properties.border;
                if (!t) return "";
                (0, l.isObject)(t) || (t = {});
                var r = t,
                  i = r.color,
                  a = void 0 === i ? "#266FE8" : i,
                  n = r.width,
                  o = void 0 === n ? 2 : n,
                  c = r.style,
                  s = void 0 === c ? "solid" : c;
                return e
                  ? "border:".concat(o, "rpx ").concat(s, " ").concat(a)
                  : "border-top:"
                      .concat(o, "rpx ")
                      .concat(s, " ")
                      .concat(a, ";border-left:")
                      .concat(o, "rpx ")
                      .concat(s, " ")
                      .concat(a);
              },
            },
            {
              key: "onClick",
              value: function (e) {
                var t = e.currentTarget.dataset.item;
                this.triggerEvent("click", t), this.jumpLink();
              },
            },
            {
              key: "jumpLink",
              value: function () {
                var e = this.properties,
                  t = e.url,
                  r = e.jumpType;
                t && r && p[r] && wx[p[r]]({ url: t });
              },
            },
          ]),
          o
        );
      })(n.SuperComponent),
      y = (v = (0, a.__decorate)([(0, n.wxComponent)()], v));
    exports.default = y;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/grid-item/grid-item.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/grid-item/grid-item.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/grid/grid";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/grid/grid.js";
define(
  "miniprogram_npm/tdesign-miniprogram/grid/grid.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      o = require("../common/src/index"),
      a = l(require("../common/config")),
      s = require("../common/validator"),
      u = l(require("./props"));
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var c = a.default.prefix,
      p = "".concat(c, "-grid"),
      d = (function (i) {
        r(a, i);
        var o = n(a);
        function a() {
          var e;
          return (
            t(this, a),
            ((e = o.apply(this, arguments)).externalClasses = ["t-class"]),
            (e.relations = {
              "../grid-item/grid-item": { type: "descendant" },
            }),
            (e.properties = u.default),
            (e.data = { prefix: c, classPrefix: p, contentStyle: "" }),
            (e.observers = {
              "column,hover,align,gutter,border": function () {
                this.updateContentStyle(),
                  this.doForChild(function (e) {
                    return e.updateStyle();
                  });
              },
            }),
            (e.lifetimes = {
              attached: function () {
                this.updateContentStyle();
              },
            }),
            (e.methods = {
              doForChild: function (e) {
                this.$children.forEach(e);
              },
              updateContentStyle: function () {
                var e = [],
                  t = this.getContentMargin();
                t && e.push(t), this.setData({ contentStyle: e.join(";") });
              },
              getContentMargin: function () {
                var e = this.properties.gutter,
                  t = this.properties.border;
                if (!t)
                  return "margin-left:-"
                    .concat(e, "rpx; margin-top:-")
                    .concat(e, "rpx");
                (0, s.isObject)(t) || (t = {});
                var r = t.width,
                  n = void 0 === r ? 2 : r;
                return "margin-left:-"
                  .concat(n, "rpx; margin-top:-")
                  .concat(n, "rpx");
              },
            }),
            e
          );
        }
        return e(a);
      })(o.SuperComponent),
      h = (d = (0, i.__decorate)([(0, o.wxComponent)()], d));
    exports.default = h;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/grid/grid.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/grid/grid.js");
