var e,
  n,
  d = require("../../../../../@babel/runtime/helpers/typeof");
(e = void 0),
  (n = function () {
    return {
      name: "en",
      weekdays:
        "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),
      months:
        "January_February_March_April_May_June_July_August_September_October_November_December".split(
          "_"
        ),
      ordinal: function (e) {
        var n = ["th", "st", "nd", "rd"],
          d = e % 100;
        return "[" + e + (n[(d - 20) % 10] || n[d] || n[0]) + "]";
      },
    };
  }),
  "object" == ("undefined" == typeof exports ? "undefined" : d(exports)) &&
  "undefined" != typeof module
    ? (module.exports = n())
    : "function" == typeof define && define.amd
    ? define(n)
    : ((e =
        "undefined" != typeof globalThis
          ? globalThis
          : e || self).dayjs_locale_en = n());
