var e = require("../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../@babel/runtime/helpers/asyncToGenerator"),
  a = require("../env/index.js");
Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#ff4d4f",
    showPublishModal: !1,
    loading: !1,
    list: [
      {
        pagePath: "/pages/index/index",
        iconPath: "/assets/icon/home.png",
        selectedIconPath: "/assets/icon/home-1.png",
        text: "首页",
      },
      { pagePath: "/pages/publish/publish", text: "发布" },
      {
        pagePath: "/pages/my/my",
        iconPath: "/assets/icon/my.png",
        selectedIconPath: "/assets/icon/my-1.png",
        text: "我的",
      },
    ],
  },
  attached: function () {},
  methods: {
    requestNewOrderSubscribe: function () {
      var e = a.subscriptionTemplates.newOrder;
      wx.requestSubscribeMessage({
        tmplIds: [e],
        success: function (t) {
          console.log("新订单提醒订阅请求成功", t);
          var a = getApp();
          "accept" === t[e]
            ? (wx.showToast({
                title: "订阅成功，将及时通知您新订单",
                icon: "success",
                duration: 2e3,
              }),
              (a.globalData.needSubscribe.newOrder = !1),
              (a.globalData.subscriptionStatus.newOrder = "accept"))
            : "reject" === t[e] &&
              ((a.globalData.needSubscribe.newOrder = !1),
              (a.globalData.subscriptionStatus.newOrder = "reject"));
        },
        fail: function (e) {
          console.error("新订单提醒订阅请求失败", e);
        },
      });
    },
    switchTab: function (a) {
      var s = this;
      return t(
        e().mark(function t() {
          var r, n, i, o, c;
          return e().wrap(function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  if (((r = a.currentTarget.dataset), 1 !== (n = r.index))) {
                    e.next = 14;
                    break;
                  }
                  if (((i = getApp()), (o = i.getUserData()).data)) {
                    e.next = 8;
                    break;
                  }
                  return (
                    wx.showToast({ title: "请先登录", icon: "error" }),
                    e.abrupt("return")
                  );
                case 8:
                  if ("incomplete" !== o.data.auth_status.value) {
                    e.next = 11;
                    break;
                  }
                  return (
                    wx.showToast({ title: "请先填写认证信息", icon: "error" }),
                    e.abrupt("return")
                  );
                case 11:
                  return (
                    i.globalData.needSubscribe.newOrder &&
                      s.requestNewOrderSubscribe(),
                    s.setData({ showPublishModal: !0 }),
                    e.abrupt("return")
                  );
                case 14:
                  s.setData({ loading: !0, selected: n }),
                    (c = r.path),
                    wx.switchTab({
                      url: c,
                      success: function () {
                        s.setData({ loading: !1 });
                      },
                      fail: function () {
                        s.setData({ loading: !1 });
                      },
                    });
                case 17:
                case "end":
                  return e.stop();
              }
          }, t);
        })
      )();
    },
    closePublishModal: function () {
      this.setData({ showPublishModal: !1 });
    },
    selectNeighborService: function () {
      var e = this;
      this.setData({ showPublishModal: !1, loading: !0 }),
        wx.navigateTo({
          url: "/pages/add-service/add-service",
          success: function () {
            e.setData({ loading: !1 });
          },
          fail: function () {
            e.setData({ loading: !1 });
          },
        });
    },
    selectPaidHelp: function () {
      this.setData({ showPublishModal: !1 }),
        wx.showToast({ title: "该功能暂未开放", icon: "none", duration: 2e3 });
    },
  },
});
