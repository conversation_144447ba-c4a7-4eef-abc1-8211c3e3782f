__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/cell-group/cell-group": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            theme: new Array(1),
            bordered: new Array(1),
            customStyle: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          c,
          e = (C, T) => {
            C || K || U.title ? T(Y(D.title)) : T();
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      "class " +
                        Y(D.classPrefix) +
                        "__title " +
                        Y(D.prefix) +
                        "-class-title"
                    );
                },
                e
              );
            }
          },
          f = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E, B) => {
            c = D.title ? 1 : 0;
            B(c, d);
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.bordered]), U.theme]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["bordered", D.bordered],
                        D.theme,
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["bordered"][0] = A["theme"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["bordered", D.bordered],
                        D.theme,
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              f
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/indexes/indexes": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/indexes/indexes"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { style: new Array(1), customStyle: new Array(1) },
          K = U === true,
          f = (C, g, h, i, j, k, T, E) => {
            var m = (C, T) => {
                C ||
                K ||
                !!(Z(undefined, "getFirstCharacter") || i) ||
                undefined
                  ? T(Y(P(X(b).getFirstCharacter)(g)))
                  : T();
              },
              n,
              p = (C, T) => {
                C || K || U.activeAnchor ? T(Y(D.activeAnchor)) : T();
              },
              o = (C, T, E) => {
                if (n === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__sidebar-tips");
                    },
                    p
                  );
                }
              },
              l = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) O(N, "aria-role", "button");
                    var $A = D.activeAnchor === g;
                    if (
                      C ||
                      K ||
                      !!(U.activeAnchor || i) ||
                      ($A ? !!i || undefined : undefined)
                    )
                      O(N, "aria-label", $A ? "已选中" + g : "");
                  },
                  m
                );
                n = D.showTips && D.activeAnchor === g ? 1 : 0;
                B(n, o);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!(U.activeAnchor || i) || undefined])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__sidebar-item", [
                        ["active", D.activeAnchor === g],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-sidebar-item"
                  );
                if (C || K || j) R.d(N, "index", h);
                if (C) R.v(N, "tap", "onClick", !1, !1, !1, !1);
              },
              l
            );
          },
          e = (C, T, E, B, F) => {
            F(
              D._indexList,
              "*this",
              U ? U._indexList : undefined,
              [0, "_indexList"],
              f
            );
          },
          d = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__sidebar " +
                      Y(D.prefix) +
                      "-class-sidebar"
                  );
                if (C) R.v(N, "touchmove", "onTouchMove", !0, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchCancel", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !0, !1, !1, !1);
                if (C || K || !!U.classPrefix || undefined)
                  R.i(N, "id-" + Y(D.classPrefix) + "__bar");
              },
              e
            );
            S("");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/cell-group/cell-group.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-cell-group{position:relative}\n.",
      [1],
      "t-cell-group__title{background-color:var(--td-cell-group-title-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));color:var(--td-cell-group-title-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-family:PingFangSC-Regular;font-size:var(--td-cell-group-title-font-size,",
      [0, 28],
      ");line-height:var(--td-cell-group-title-line-height,",
      [0, 90],
      ");padding-left:var(--td-cell-group-title-padding-left,",
      [0, 32],
      ");text-align:left}\n.",
      [1],
      "t-cell-group--bordered::before{border-top:1px solid var(--td-cell-group-border-color,var(--td-component-stroke,var(--td-gray-color-3,#e7e7e7)));top:0;-webkit-transform:scaleY(.5);transform:scaleY(.5);-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform-origin:top;transform-origin:top}\n.",
      [1],
      "t-cell-group--bordered::after,.",
      [1],
      "t-cell-group--bordered::before{box-sizing:border-box;content:\x22 \x22;left:0;pointer-events:none;position:absolute;right:0;z-index:1}\n.",
      [1],
      "t-cell-group--bordered::after{border-bottom:1px solid var(--td-cell-group-border-color,var(--td-component-stroke,var(--td-gray-color-3,#e7e7e7)));bottom:0;-webkit-transform:scaleY(.5);transform:scaleY(.5);-webkit-transform-origin:bottom;transform-origin:bottom}\n.",
      [1],
      "t-cell-group--card{border-radius:var(--td-radius-large,",
      [0, 18],
      ");margin:0 ",
      [0, 32],
      ";overflow:hidden}\n",
    ],
    undefined,
    { path: "./miniprogram_npm/tdesign-miniprogram/cell-group/cell-group.wxss" }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/indexes/indexes.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-indexes{height:100vh;position:relative}\n.",
        [1],
        "t-indexes__sidebar{color:var(--td-indexes-sidebar-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex;-webkit-flex-flow:column nowrap;flex-flow:column nowrap;font-size:var(--td-indexes-sidebar-font-size,",
        [0, 24],
        ");line-height:var(--td-indexes-sidebar-line-height,",
        [0, 40],
        ");position:fixed;right:var(--td-indexes-sidebar-right,",
        [0, 16],
        ");top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:var(--td-indexes-sidebar-item-size,",
        [0, 40],
        ");z-index:1}\n.",
        [1],
        "t-indexes__sidebar-item{border-radius:50%;position:relative;text-align:center}\n.",
        [1],
        "t-indexes__sidebar-item--active{background-color:var(--td-indexes-sidebar-active-bg-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));color:var(--td-indexes-sidebar-active-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-indexes__sidebar-item+.",
        [1],
        "t-indexes__sidebar-item{margin-top:",
        [0, 4],
        "}\n.",
        [1],
        "t-indexes__sidebar-tips{background-color:var(--td-indexes-sidebar-tips-bg-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));border-radius:var(--td-indexes-sidebar-tips-size,",
        [0, 96],
        ");bottom:0;box-sizing:border-box;color:var(--td-indexes-sidebar-tips-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));font-size:var(--td-indexes-sidebar-tips-font-size,",
        [0, 40],
        ");font-weight:700;height:var(--td-indexes-sidebar-tips-size,",
        [0, 96],
        ");line-height:var(--td-indexes-sidebar-tips-size,",
        [0, 96],
        ");max-width:",
        [0, 198],
        ";min-width:var(--td-indexes-sidebar-tips-size,",
        [0, 96],
        ");overflow:hidden;padding:0 ",
        [0, 32],
        ";position:absolute;right:var(--td-indexes-sidebar-tips-right,calc(100% + ",
        [0, 32],
        "));text-align:center;text-overflow:ellipsis;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);white-space:nowrap}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/indexes/indexes.wxss" }
    );
}
