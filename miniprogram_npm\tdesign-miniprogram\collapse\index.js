Object.defineProperty(exports, "__esModule", { value: !0 });
var e = { Collapse: !0 };
Object.defineProperty(exports, "Collapse", {
  enumerable: !0,
  get: function () {
    return t.default;
  },
});
var r,
  t = (r = require("./collapse")) && r.__esModule ? r : { default: r },
  o = require("./type");
Object.keys(o).forEach(function (r) {
  "default" !== r &&
    "__esModule" !== r &&
    (Object.prototype.hasOwnProperty.call(e, r) ||
      (r in exports && exports[r] === o[r]) ||
      Object.defineProperty(exports, r, {
        enumerable: !0,
        get: function () {
          return o[r];
        },
      }));
});
var n = require("./props");
Object.keys(n).forEach(function (r) {
  "default" !== r &&
    "__esModule" !== r &&
    (Object.prototype.hasOwnProperty.call(e, r) ||
      (r in exports && exports[r] === n[r]) ||
      Object.defineProperty(exports, r, {
        enumerable: !0,
        get: function () {
          return n[r];
        },
      }));
});
