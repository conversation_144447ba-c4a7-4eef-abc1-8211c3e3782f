Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  a = require("../../../@babel/runtime/helpers/inherits"),
  s = require("../../../@babel/runtime/helpers/createSuper"),
  i = require("tslib"),
  c = require("../common/src/index"),
  r = u(require("../common/config")),
  l = u(require("./props")),
  n = require("../common/utils"),
  o = require("../common/validator");
function u(e) {
  return e && e.__esModule ? e : { default: e };
}
var d = r.default.prefix,
  h = "".concat(d, "-tag"),
  p = (function (i) {
    a(r, i);
    var c = s(r);
    function r() {
      var e;
      return (
        t(this, r),
        ((e = c.apply(this, arguments)).data = {
          prefix: d,
          classPrefix: h,
          className: "",
          tagStyle: "",
        }),
        (e.properties = l.default),
        (e.externalClasses = ["".concat(d, "-class")]),
        (e.options = { multipleSlots: !0 }),
        (e.lifetimes = {
          attached: function () {
            this.setClass(), this.setTagStyle();
          },
        }),
        (e.observers = {
          "size, shape, theme, variant, closable, disabled": function () {
            this.setClass();
          },
          maxWidth: function () {
            this.setTagStyle();
          },
          icon: function (e) {
            this.setData({ _icon: (0, n.calcIcon)(e) });
          },
          closable: function (e) {
            this.setData({ _closable: (0, n.calcIcon)(e, "close") });
          },
        }),
        (e.methods = {
          setClass: function () {
            var e = this.data,
              t = e.prefix,
              a = e.classPrefix,
              s = this.properties,
              i = s.size,
              c = s.shape,
              r = s.theme,
              l = s.variant,
              o = s.closable,
              u = s.disabled,
              d = (0, n.classNames)([
                a,
                "".concat(a, "--").concat(r || "default"),
                "".concat(a, "--").concat(l),
                o ? "".concat(a, "--closable ").concat(t, "-is-closable") : "",
                u ? "".concat(a, "--disabled ").concat(t, "-is-disabled") : "",
                "".concat(a, "--").concat(i),
                "".concat(a, "--").concat(c),
              ]);
            this.setData({ className: d });
          },
          setTagStyle: function () {
            var e = this.properties.maxWidth;
            if (!e) return "";
            var t = (0, o.isNumber)(e) ? "".concat(e, "px") : e;
            this.setData({ tagStyle: "max-width:".concat(t, ";") });
          },
          handleClick: function (e) {
            this.data.disabled || this.triggerEvent("click", e);
          },
          handleClose: function (e) {
            this.data.disabled || this.triggerEvent("close", e);
          },
        }),
        e
      );
    }
    return e(r);
  })(c.SuperComponent),
  f = (p = (0, i.__decorate)([(0, c.wxComponent)()], p));
exports.default = f;
