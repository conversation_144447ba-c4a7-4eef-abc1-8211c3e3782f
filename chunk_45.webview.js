__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/row/row": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/row/row"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            prefix: new Array(1),
            gutter: new Array(1),
            customStyle: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.prefix || undefined)
                  L(N, "class " + Y(D.prefix) + "-row");
                A["prefix"][0] = (D, E, T) => {
                  L(N, "class " + Y(D.prefix) + "-row");
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getRowStyles") ||
                    U.gutter ||
                    U.style ||
                    U.customStyle
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a).getRowStyles)(D.gutter, D.style, D.customStyle)
                  );
                A["gutter"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a).getRowStyles)(D.gutter, D.style, D.customStyle)
                      );
                    };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/row/row.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-row{box-sizing:border-box;display:-webkit-flex;display:flex;-webkit-flex-direction:row;flex-direction:row;-webkit-flex-wrap:wrap;flex-wrap:wrap}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/row/row.wxss" }
    );
}
