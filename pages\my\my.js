var e,
  r = require("../../@babel/runtime/helpers/defineProperty"),
  t = require("../../@babel/runtime/helpers/objectSpread2"),
  a = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  n = require("../../@babel/runtime/helpers/asyncToGenerator");
Page(
  (r(
    (e = {
      data: {
        activeTab: 0,
        myInfo: {},
        userServices: [],
        userOrders: [],
        orderPagination: {
          current_page: 1,
          total_pages: 1,
          total_count: 0,
          limit: 10,
          has_next: !1,
          has_prev: !1,
        },
        pagination: {
          current_page: 1,
          total_pages: 1,
          total_count: 0,
          limit: 10,
          has_next: !1,
          has_prev: !1,
        },
        loading: !1,
      },
      onLoad: function (e) {
        this.loadUserInfo(), this.loadUserServices(), this.loadUserOrders();
      },
      onShow: function () {
        "function" == typeof this.getTabBar &&
          this.getTabBar() &&
          this.getTabBar().setData({ selected: 2 }),
          this.loadUserInfo(),
          this.loadUserServices(),
          this.loadUserOrders();
      },
      loadUserInfo: function () {
        var e = getApp();
        if (e.isUserLoggedIn()) {
          var r = e.globalData.userInfo.data || {};
          console.log("个人信息:", r), this.setData({ myInfo: r });
        } else console.log("用户未登录");
      },
      loadUserServices: function () {
        var e = arguments,
          r = this;
        return n(
          a().mark(function t() {
            var n, s, o, i, c, l, u, d;
            return a().wrap(
              function (t) {
                for (;;)
                  switch ((t.prev = t.next)) {
                    case 0:
                      if (
                        ((n = e.length > 0 && void 0 !== e[0] ? e[0] : 1),
                        (s = getApp()).isUserLoggedIn())
                      ) {
                        t.next = 5;
                        break;
                      }
                      return (
                        console.log("用户未登录，无法获取服务列表"),
                        t.abrupt("return")
                      );
                    case 5:
                      if (
                        ((o = s.globalData.userInfo.data || {}), (i = o.id))
                      ) {
                        t.next = 10;
                        break;
                      }
                      return console.log("用户ID不存在"), t.abrupt("return");
                    case 10:
                      return (
                        r.setData({ loading: !0 }),
                        (t.prev = 11),
                        (t.next = 14),
                        s.call({
                          path: "/api/mp/services/user/".concat(i),
                          method: "GET",
                          data: { page: n, limit: r.data.pagination.limit },
                        })
                      );
                    case 14:
                      (c = t.sent),
                        console.log("用户服务列表获取成功:", c),
                        0 === c.code && c.data
                          ? ((l = c.data),
                            (u = l.services),
                            (d = l.pagination),
                            r.setData({
                              userServices: u || [],
                              pagination: d || r.data.pagination,
                              loading: !1,
                            }))
                          : (console.error("获取服务列表失败:", c.message),
                            wx.showToast({
                              title: c.message || "获取服务列表失败",
                              icon: "error",
                            }),
                            r.setData({ loading: !1 })),
                        (t.next = 24);
                      break;
                    case 19:
                      (t.prev = 19),
                        (t.t0 = t.catch(11)),
                        console.error("获取用户服务列表失败:", t.t0),
                        wx.showToast({ title: "网络请求失败", icon: "error" }),
                        r.setData({ loading: !1 });
                    case 24:
                    case "end":
                      return t.stop();
                  }
              },
              t,
              null,
              [[11, 19]]
            );
          })
        )();
      },
      refreshUserServices: function () {
        var e = this;
        return n(
          a().mark(function r() {
            return a().wrap(function (r) {
              for (;;)
                switch ((r.prev = r.next)) {
                  case 0:
                    return (r.next = 2), e.loadUserServices(1);
                  case 2:
                  case "end":
                    return r.stop();
                }
            }, r);
          })
        )();
      },
      loadMoreServices: function () {
        var e = this;
        return n(
          a().mark(function r() {
            var t;
            return a().wrap(function (r) {
              for (;;)
                switch ((r.prev = r.next)) {
                  case 0:
                    if (!e.data.loading && e.data.pagination.has_next) {
                      r.next = 2;
                      break;
                    }
                    return r.abrupt("return");
                  case 2:
                    return (
                      (t = e.data.pagination.current_page + 1),
                      (r.next = 5),
                      e.loadUserServices(t)
                    );
                  case 5:
                  case "end":
                    return r.stop();
                }
            }, r);
          })
        )();
      },
      onTabsClick: function (e) {
        var r = e.detail.value;
        this.setData({ activeTab: Number(r) });
      },
      navigateToEditProfile: function () {
        wx.navigateTo({ url: "/pages/edit-profile/edit-profile" });
      },
      loadUserOrders: function () {
        var e = arguments,
          r = this;
        return n(
          a().mark(function t() {
            var n, s, o, i, c, l, u, d, p, f;
            return a().wrap(
              function (t) {
                for (;;)
                  switch ((t.prev = t.next)) {
                    case 0:
                      if (
                        ((n = e.length > 0 && void 0 !== e[0] ? e[0] : 1),
                        (s = e.length > 1 && void 0 !== e[1] ? e[1] : ""),
                        (o = getApp()).isUserLoggedIn())
                      ) {
                        t.next = 6;
                        break;
                      }
                      return (
                        console.log("用户未登录，无法获取订单列表"),
                        t.abrupt("return")
                      );
                    case 6:
                      if (
                        ((i = o.globalData.userInfo.data || {}),
                        (c = i.id),
                        console.log("111", c),
                        c)
                      ) {
                        t.next = 12;
                        break;
                      }
                      return console.log("用户ID不存在"), t.abrupt("return");
                    case 12:
                      return (
                        r.setData({ loading: !0 }),
                        (t.prev = 13),
                        (l = "/api/mp/serviceOrder/user/".concat(c)),
                        (u = []),
                        n > 1 && u.push("page=".concat(n)),
                        s && u.push("order_status=".concat(s)),
                        u.length > 0 && (l += "?" + u.join("&")),
                        (t.next = 21),
                        o.call({ path: l, method: "GET" })
                      );
                    case 21:
                      (d = t.sent),
                        console.log("用户订单列表获取成功:", d),
                        0 === d.code && d.data
                          ? ((p = d.data.services),
                            (f = d.data.service_count),
                            console.log("订单列表111:", p),
                            r.setData({
                              userOrders: p || [],
                              "orderPagination.total_count": f || 0,
                              loading: !1,
                            }))
                          : (console.error("获取订单列表失败:", d.message),
                            wx.showToast({
                              title: d.message || "获取订单列表失败",
                              icon: "error",
                            }),
                            r.setData({ loading: !1 })),
                        (t.next = 31);
                      break;
                    case 26:
                      (t.prev = 26),
                        (t.t0 = t.catch(13)),
                        console.error("获取用户订单列表失败:", t.t0),
                        wx.showToast({ title: "网络请求失败", icon: "error" }),
                        r.setData({ loading: !1 });
                    case 31:
                    case "end":
                      return t.stop();
                  }
              },
              t,
              null,
              [[13, 26]]
            );
          })
        )();
      },
      refreshUserOrders: function () {
        var e = this;
        return n(
          a().mark(function r() {
            return a().wrap(function (r) {
              for (;;)
                switch ((r.prev = r.next)) {
                  case 0:
                    return (r.next = 2), e.loadUserOrders(1);
                  case 2:
                  case "end":
                    return r.stop();
                }
            }, r);
          })
        )();
      },
      filterOrdersByStatus: function (e) {
        var r = this;
        return n(
          a().mark(function t() {
            return a().wrap(function (t) {
              for (;;)
                switch ((t.prev = t.next)) {
                  case 0:
                    return (t.next = 2), r.loadUserOrders(1, e);
                  case 2:
                  case "end":
                    return t.stop();
                }
            }, t);
          })
        )();
      },
      handleContact: function (e) {
        var r = e.detail;
        console.log("联系商家:", r);
      },
      handleConfirm: function (e) {
        var r = e.detail;
        console.log("确认送达:", r);
      },
      handleOrderStatusChanged: function (e) {
        var r = e.detail,
          a = r.orderId,
          n = r.status,
          s = r.data;
        console.log("订单状态变更:", { orderId: a, status: n, data: s });
        var o = this.data.userOrders.map(function (e) {
          return e.order_id === a
            ? t(
                t(
                  t({}, e),
                  {},
                  { order_status: n },
                  "cancelled" === n && { cancel_time: s.cancel_time }
                ),
                "completed" === n && { complete_time: s.complete_time }
              )
            : e;
        });
        this.setData({ userOrders: o });
      },
      onTabsChange: function (e) {
        var r = e.detail.value;
        this.setData({ activeTab: Number(r) }),
          0 === r
            ? this.refreshUserServices()
            : 3 === r && this.refreshUserOrders();
      },
    }),
    "onTabsClick",
    function (e) {
      var r = e.detail.value;
      this.setData({ activeTab: Number(r) });
    }
  ),
  r(e, "navigateToEditProfile", function () {
    wx.navigateTo({ url: "/pages/edit-profile/edit-profile" });
  }),
  r(e, "onPullDownRefresh", function () {
    var e = this;
    return n(
      a().mark(function r() {
        return a().wrap(
          function (r) {
            for (;;)
              switch ((r.prev = r.next)) {
                case 0:
                  if (
                    (console.log("用户触发下拉刷新"),
                    (r.prev = 1),
                    e.loadUserInfo(),
                    0 !== e.data.activeTab)
                  ) {
                    r.next = 8;
                    break;
                  }
                  return (r.next = 6), e.refreshUserServices();
                case 6:
                  r.next = 11;
                  break;
                case 8:
                  if (1 !== e.data.activeTab) {
                    r.next = 11;
                    break;
                  }
                  return (r.next = 11), e.refreshUserOrders();
                case 11:
                  wx.showToast({
                    title: "刷新成功",
                    icon: "success",
                    duration: 1500,
                  }),
                    (r.next = 18);
                  break;
                case 14:
                  (r.prev = 14),
                    (r.t0 = r.catch(1)),
                    console.error("下拉刷新失败:", r.t0),
                    wx.showToast({
                      title: "刷新失败",
                      icon: "error",
                      duration: 1500,
                    });
                case 18:
                  return (r.prev = 18), wx.stopPullDownRefresh(), r.finish(18);
                case 21:
                case "end":
                  return r.stop();
              }
          },
          r,
          null,
          [[1, 14, 18, 21]]
        );
      })
    )();
  }),
  e)
);
