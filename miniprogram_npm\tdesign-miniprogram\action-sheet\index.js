Object.defineProperty(exports, "__esModule", { value: !0 }),
  Object.defineProperty(exports, "ActionSheetTheme", {
    enumerable: !0,
    get: function () {
      return e.ActionSheetTheme;
    },
  }),
  (exports.default = void 0);
var e = require("./show"),
  t = {
    show: function (t) {
      return (0, e.show)(t);
    },
    close: function (t) {
      return (0, e.close)(t);
    },
  };
exports.default = t;
