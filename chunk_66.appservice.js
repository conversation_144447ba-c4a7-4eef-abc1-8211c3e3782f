__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/dongjie/dongjie": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b = (C, T) => {
            C ? T("您的账号已被冻结") : T();
          },
          d = (C) => {},
          c = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) O(N, "src", "/assets/icon/dongjie.png");
              },
              d
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "title");
              },
              b
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loing_img");
              },
              c
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/dongjie/dongjie";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/dongjie/dongjie.js";
define(
  "pages/dongjie/dongjie.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Page({
      data: {},
      onLoad: function (n) {},
      onReady: function () {},
      onShow: function () {},
      onHide: function () {},
      onUnload: function () {},
      onPullDownRefresh: function () {},
      onReachBottom: function () {},
      onShareAppMessage: function () {},
    });
  },
  { isPage: true, isComponent: true, currentFile: "pages/dongjie/dongjie.js" }
);
require("pages/dongjie/dongjie.js");
