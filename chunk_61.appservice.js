__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/upload/upload": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/upload/upload"]();
      var c = R["miniprogram_npm/tdesign-miniprogram/upload/drag"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            draggable: new Array(1),
            gutter: new Array(1),
            customStyle: new Array(1),
            prefix: new Array(1),
          },
          K = U === true,
          g,
          i = (C, j, k, l, m, n, T, E) => {
            var q,
              s = (C) => {},
              r = (C, T, E) => {
                if (q === 1) {
                  E(
                    "t-image",
                    {},
                    (N, C) => {
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "style")) ||
                        undefined
                      )
                        R.y(N, (D.imageProps && X(D.imageProps).style) || "");
                      if (C || K || !!U.classPrefix || undefined)
                        O(N, "t-class", Y(D.classPrefix) + "__thumbnail");
                      if (
                        C ||
                        K ||
                        !!(Z(l, "thumb") || Z(l, "url")) ||
                        undefined
                      )
                        O(N, "src", X(j).thumb || X(j).url);
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "mode")) ||
                        undefined
                      )
                        O(
                          N,
                          "mode",
                          (D.imageProps && X(D.imageProps).mode) || "aspectFill"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "error")) ||
                        undefined
                      )
                        O(
                          N,
                          "error",
                          (D.imageProps && X(D.imageProps).error) || "default"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "lazy")) ||
                        undefined
                      )
                        O(
                          N,
                          "lazy",
                          (D.imageProps && X(D.imageProps).lazy) || false
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "loading")) ||
                        undefined
                      )
                        O(
                          N,
                          "loading",
                          (D.imageProps && X(D.imageProps).loading) || "default"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "shape")) ||
                        undefined
                      )
                        O(
                          N,
                          "shape",
                          (D.imageProps && X(D.imageProps).shape) || "round"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "webp")) ||
                        undefined
                      )
                        O(
                          N,
                          "webp",
                          (D.imageProps && X(D.imageProps).webp) || false
                        );
                      if (
                        C ||
                        K ||
                        !!(
                          U.imageProps || Z(U.imageProps, "showMenuByLongpress")
                        ) ||
                        undefined
                      )
                        O(
                          N,
                          "showMenuByLongpress",
                          (D.imageProps &&
                            X(D.imageProps).showMenuByLongpress) ||
                            false
                        );
                      if (C || K || l) R.d(N, "file", j);
                      if (C || K || m) R.d(N, "index", k);
                      if (C) R.v(N, "tap", "onProofTap", !1, !1, !1, !1);
                    },
                    s
                  );
                }
              },
              t,
              v = (C) => {},
              u = (C, T, E) => {
                if (t === 1) {
                  E(
                    "video",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__thumbnail");
                      if (C || K || Z(l, "url")) O(N, "src", X(j).url);
                      if (C || K || Z(l, "thumb")) O(N, "poster", X(j).thumb);
                      if (C) O(N, "controls", true);
                      if (C || K || undefined) O(N, "autoplay", false);
                      if (C) O(N, "objectFit", "contain");
                      if (C || K || l) R.d(N, "file", j);
                      if (C) R.v(N, "tap", "onFileClick", !1, !1, !1, !1);
                    },
                    v
                  );
                }
              },
              w,
              z,
              B0 = (C) => {},
              C0 = (C, T) => {
                var $A = X(j).percent;
                C ||
                K ||
                !!Z(l, "percent") ||
                ($A ? !!Z(l, "percent") || undefined : undefined)
                  ? T(Y($A ? X(j).percent + "%" : "上传中..."))
                  : T();
              },
              D0 = (C) => {},
              A0 = (C, T, E) => {
                if (z === 1) {
                  E(
                    "t-icon",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        O(
                          N,
                          "t-class",
                          Y(D.classPrefix) + "__progress-loading"
                        );
                      if (C) O(N, "name", "loading");
                      if (C) O(N, "size", "48rpx");
                      if (C) O(N, "aria-hidden", true);
                    },
                    B0
                  );
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-text");
                    },
                    C0
                  );
                } else {
                  E(
                    "t-icon",
                    {},
                    (N, C) => {
                      var $A = X(j).status == "reload";
                      if (
                        C ||
                        K ||
                        !!Z(l, "status") ||
                        ($A ? undefined : undefined)
                      )
                        O(N, "name", $A ? "refresh" : "close-circle");
                      if (C) O(N, "size", "48rpx");
                      if (C) O(N, "aria-hidden", true);
                    },
                    D0
                  );
                }
              },
              E0,
              G0 = (C, T) => {
                var $A = X(j).status == "reload";
                C || K || !!Z(l, "status") || ($A ? undefined : undefined)
                  ? T(Y($A ? "重新上传" : "上传失败"))
                  : T();
              },
              F0 = (C, T, E) => {
                if (E0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-text");
                    },
                    G0
                  );
                }
              },
              y = (C, T, E, B) => {
                z = X(j).status == "loading" ? 1 : 0;
                B(z, A0);
                E0 = X(j).status == "reload" || X(j).status == "failed" ? 1 : 0;
                B(E0, F0);
              },
              x = (C, T, E) => {
                if (w === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-mask");
                      if (C || K || m) R.d(N, "index", k);
                      if (C || K || l) R.d(N, "file", j);
                      if (C) R.v(N, "tap", "onFileClick", !1, !1, !1, !1);
                    },
                    y
                  );
                }
              },
              H0,
              K0 = (C) => {},
              J0 = (C, T, E) => {
                E(
                  "t-icon",
                  {},
                  (N, C) => {
                    if (C) O(N, "name", "close");
                    if (C) O(N, "size", "32rpx");
                    if (C) O(N, "color", "#fff");
                  },
                  K0
                );
              },
              I0 = (C, T, E) => {
                if (H0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__close-btn hotspot-expanded");
                      if (C) O(N, "aria-role", "button");
                      if (C) O(N, "aria-label", "删除");
                      if (C) O(N, "bindtap", "onDelete");
                      if (C || K || m) R.d(N, "index", k);
                    },
                    J0
                  );
                }
              },
              p = (C, T, E, B) => {
                q = X(j).type !== "video" ? 1 : 0;
                B(q, r);
                t = X(j).type === "video" ? 1 : 0;
                B(t, u);
                w = X(j).status && X(j).status != "done" ? 1 : 0;
                B(w, x);
                var $A = P(X(a).isBoolean)(X(j).removeBtn);
                H0 = ($A ? X(j).removeBtn : D.removeBtn) ? 1 : 0;
                B(H0, I0);
              },
              o = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    var $A = D.disabled;
                    if (
                      C ||
                      K ||
                      !!(
                        U.classPrefix ||
                        U.disabled ||
                        ($A ? !!U.classPrefix || undefined : undefined)
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__wrapper " +
                          Y($A ? D.classPrefix + "__wrapper--disabled" : "")
                      );
                    if (C || K || U.gridItemStyle) R.y(N, D.gridItemStyle);
                    if (
                      C ||
                      K ||
                      !!(
                        U.ariaRole ||
                        Z(undefined, "getWrapperAriaRole") ||
                        l
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "aria-role",
                        D.ariaRole || P(X(b).getWrapperAriaRole)(j)
                      );
                    if (
                      C ||
                      K ||
                      !!(
                        U.ariaLabel ||
                        Z(undefined, "getWrapperAriaLabel") ||
                        l
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "aria-label",
                        D.ariaLabel || P(X(b).getWrapperAriaLabel)(j)
                      );
                  },
                  p
                );
              };
            E(
              "t-grid-item",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                  O(
                    N,
                    "t-class",
                    Y(D.classPrefix) +
                      "__grid " +
                      Y(D.classPrefix) +
                      "__grid-file"
                  );
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class-content", Y(D.classPrefix) + "__grid-content");
                if (C) O(N, "aria-role", "presentation");
              },
              o
            );
          },
          j,
          n,
          q = (C) => {},
          p = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C) O(N, "name", "add");
              },
              q
            );
          },
          o = (C, T, E) => {
            if (n === 1) {
              C || K || U.addContent ? T(Y(D.addContent)) : T();
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.disabled;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.disabled ||
                      ($A ? !!U.classPrefix || undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__add-icon " +
                        Y($A ? D.classPrefix + "__add-icon--disabled" : "")
                    );
                },
                p
              );
            }
          },
          m = (C, T, E, B, F, S) => {
            S("add-content");
            n = D.addContent ? 1 : 0;
            B(n, o);
          },
          l = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrapper");
                if (C || K || U.gridItemStyle) R.y(N, D.gridItemStyle);
              },
              m
            );
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "t-grid-item",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    O(N, "t-class", Y(D.classPrefix) + "__grid");
                  if (C || K || !!U.classPrefix || undefined)
                    O(
                      N,
                      "t-class-content",
                      Y(D.classPrefix) + "__grid-content"
                    );
                  if (C) O(N, "aria-label", "上传");
                  if (C) O(N, "bindclick", "onAddTap");
                },
                l
              );
            }
          },
          s = (C, t, u, v, w, x, T, E) => {
            var B0,
              D0 = (C) => {},
              C0 = (C, T, E) => {
                if (B0 === 1) {
                  E(
                    "t-image",
                    {},
                    (N, C) => {
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "style")) ||
                        undefined
                      )
                        R.y(N, (D.imageProps && X(D.imageProps).style) || "");
                      if (C || K || !!U.classPrefix || undefined)
                        O(N, "t-class", Y(D.classPrefix) + "__thumbnail");
                      if (
                        C ||
                        K ||
                        !!(Z(v, "thumb") || Z(v, "url")) ||
                        undefined
                      )
                        O(N, "src", X(t).thumb || X(t).url);
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "mode")) ||
                        undefined
                      )
                        O(
                          N,
                          "mode",
                          (D.imageProps && X(D.imageProps).mode) || "aspectFill"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "error")) ||
                        undefined
                      )
                        O(
                          N,
                          "error",
                          (D.imageProps && X(D.imageProps).error) || "default"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "lazy")) ||
                        undefined
                      )
                        O(
                          N,
                          "lazy",
                          (D.imageProps && X(D.imageProps).lazy) || false
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "loading")) ||
                        undefined
                      )
                        O(
                          N,
                          "loading",
                          (D.imageProps && X(D.imageProps).loading) || "default"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "shape")) ||
                        undefined
                      )
                        O(
                          N,
                          "shape",
                          (D.imageProps && X(D.imageProps).shape) || "round"
                        );
                      if (
                        C ||
                        K ||
                        !!(U.imageProps || Z(U.imageProps, "webp")) ||
                        undefined
                      )
                        O(
                          N,
                          "webp",
                          (D.imageProps && X(D.imageProps).webp) || false
                        );
                      if (
                        C ||
                        K ||
                        !!(
                          U.imageProps || Z(U.imageProps, "showMenuByLongpress")
                        ) ||
                        undefined
                      )
                        O(
                          N,
                          "showMenuByLongpress",
                          (D.imageProps &&
                            X(D.imageProps).showMenuByLongpress) ||
                            false
                        );
                      if (C || K || v) R.d(N, "file", t);
                      if (C || K || w) R.d(N, "index", u);
                      if (C) R.v(N, "tap", "onProofTap", !1, !1, !1, !1);
                    },
                    D0
                  );
                }
              },
              E0,
              G0 = (C) => {},
              F0 = (C, T, E) => {
                if (E0 === 1) {
                  E(
                    "video",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__thumbnail");
                      if (C || K || Z(v, "url")) O(N, "src", X(t).url);
                      if (C || K || Z(v, "thumb")) O(N, "poster", X(t).thumb);
                      if (C) O(N, "controls", true);
                      if (C || K || undefined) O(N, "autoplay", false);
                      if (C) O(N, "objectFit", "contain");
                      if (C || K || v) R.d(N, "file", t);
                      if (C) R.v(N, "tap", "onFileClick", !1, !1, !1, !1);
                    },
                    G0
                  );
                }
              },
              H0,
              K0,
              M0 = (C) => {},
              N0 = (C, T) => {
                var $A = X(t).percent;
                C ||
                K ||
                !!Z(v, "percent") ||
                ($A ? !!Z(v, "percent") || undefined : undefined)
                  ? T(Y($A ? X(t).percent + "%" : "上传中..."))
                  : T();
              },
              O0 = (C) => {},
              L0 = (C, T, E) => {
                if (K0 === 1) {
                  E(
                    "t-icon",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        O(
                          N,
                          "t-class",
                          Y(D.classPrefix) + "__progress-loading"
                        );
                      if (C) O(N, "name", "loading");
                      if (C) O(N, "size", "48rpx");
                      if (C) O(N, "aria-hidden", true);
                    },
                    M0
                  );
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-text");
                    },
                    N0
                  );
                } else {
                  E(
                    "t-icon",
                    {},
                    (N, C) => {
                      var $A = X(t).status == "reload";
                      if (
                        C ||
                        K ||
                        !!Z(v, "status") ||
                        ($A ? undefined : undefined)
                      )
                        O(N, "name", $A ? "refresh" : "close-circle");
                      if (C) O(N, "size", "48rpx");
                      if (C) O(N, "aria-hidden", true);
                    },
                    O0
                  );
                }
              },
              P0,
              R0 = (C, T) => {
                var $A = X(t).status == "reload";
                C || K || !!Z(v, "status") || ($A ? undefined : undefined)
                  ? T(Y($A ? "重新上传" : "上传失败"))
                  : T();
              },
              Q0 = (C, T, E) => {
                if (P0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-text");
                    },
                    R0
                  );
                }
              },
              J0 = (C, T, E, B) => {
                K0 = X(t).status == "loading" ? 1 : 0;
                B(K0, L0);
                P0 = X(t).status == "reload" || X(t).status == "failed" ? 1 : 0;
                B(P0, Q0);
              },
              I0 = (C, T, E) => {
                if (H0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__progress-mask");
                      if (C || K || w) R.d(N, "index", u);
                      if (C || K || v) R.d(N, "file", t);
                      if (C) R.v(N, "tap", "onFileClick", !1, !1, !1, !1);
                    },
                    J0
                  );
                }
              },
              S0,
              V0 = (C) => {},
              U0 = (C, T, E) => {
                E(
                  "t-icon",
                  {},
                  (N, C) => {
                    if (C) O(N, "name", "close");
                    if (C) O(N, "size", "32rpx");
                    if (C) O(N, "color", "#fff");
                  },
                  V0
                );
              },
              T0 = (C, T, E) => {
                if (S0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__close-btn hotspot-expanded");
                      if (C) O(N, "aria-role", "button");
                      if (C) O(N, "aria-label", "删除");
                      if (C) O(N, "bindtap", "onDelete");
                      if (C || K || w) R.d(N, "index", u);
                      if (C || K || Z(v, "url")) R.d(N, "url", X(t).url);
                    },
                    U0
                  );
                }
              },
              A0 = (C, T, E, B) => {
                B0 = X(t).type !== "video" ? 1 : 0;
                B(B0, C0);
                E0 = X(t).type === "video" ? 1 : 0;
                B(E0, F0);
                H0 = X(t).status && X(t).status != "done" ? 1 : 0;
                B(H0, I0);
                var $A = P(X(a).isBoolean)(X(t).removeBtn);
                S0 = ($A ? X(t).removeBtn : D.removeBtn) ? 1 : 0;
                B(S0, T0);
              },
              z = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    var $A = D.disabled;
                    if (
                      C ||
                      K ||
                      !!(
                        U.classPrefix ||
                        U.disabled ||
                        ($A ? !!U.classPrefix || undefined : undefined)
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__wrapper " +
                          Y($A ? D.classPrefix + "__wrapper--disabled" : "")
                      );
                    if (C || K || !!U.gridItemStyle || undefined)
                      R.y(N, Y(D.gridItemStyle) + ";");
                    if (
                      C ||
                      K ||
                      !!(
                        U.ariaRole ||
                        Z(undefined, "getWrapperAriaRole") ||
                        v
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "aria-role",
                        D.ariaRole || P(X(b).getWrapperAriaRole)(t)
                      );
                    if (
                      C ||
                      K ||
                      !!(
                        U.ariaLabel ||
                        Z(undefined, "getWrapperAriaLabel") ||
                        v
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "aria-label",
                        D.ariaLabel || P(X(b).getWrapperAriaLabel)(t)
                      );
                  },
                  A0
                );
              },
              y = (C, T, E) => {
                E(
                  "t-grid-item",
                  {},
                  (N, C) => {
                    if (C) R.y(N, "width:100%");
                    if (
                      C ||
                      K ||
                      !!(U.classPrefix || U.classPrefix) ||
                      undefined
                    )
                      O(
                        N,
                        "t-class",
                        Y(D.classPrefix) +
                          "__grid " +
                          Y(D.classPrefix) +
                          "__grid-file"
                      );
                    if (C || K || !!U.classPrefix || undefined)
                      O(
                        N,
                        "t-class-content",
                        Y(D.classPrefix) + "__grid-content"
                      );
                    if (C) O(N, "aria-role", "presentation");
                  },
                  z
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__drag-item");
                if (
                  C ||
                  K ||
                  !!(
                    U.column ||
                    Z(U.transition, "duration") ||
                    Z(U.transition, "timingFunction")
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    "width:" +
                      Y(100 / D.column) +
                      "%;--td-upload-drag-transition-duration:" +
                      Y(X(D.transition).duration) +
                      "ms;--td-upload-drag-transition-timing-function:" +
                      Y(X(D.transition).timingFunction)
                  );
                if (C || K || w) R.d(N, "index", u);
                if (C || K || Z(undefined, "longPress"))
                  R.v(N, "longpress", X(c).longPress, !1, !1, !1, !0, [
                    1,
                    "miniprogram_npm/tdesign-miniprogram/upload/drag",
                    "longPress",
                  ]);
                var $A = D.dragging;
                if (
                  C ||
                  K ||
                  !!U.dragging ||
                  ($A ? Z(undefined, "touchMove") : undefined)
                )
                  R.v(
                    N,
                    "touchmove",
                    $A ? X(c).touchMove : "",
                    !0,
                    !1,
                    !1,
                    !0,
                    $A
                      ? [
                          1,
                          "miniprogram_npm/tdesign-miniprogram/upload/drag",
                          "touchMove",
                        ]
                      : null
                  );
                var $B = D.dragging;
                if (
                  C ||
                  K ||
                  !!U.dragging ||
                  ($B ? Z(undefined, "touchEnd") : undefined)
                )
                  R.v(
                    N,
                    "touchend",
                    $B ? X(c).touchEnd : "",
                    !0,
                    !1,
                    !1,
                    !0,
                    $B
                      ? [
                          1,
                          "miniprogram_npm/tdesign-miniprogram/upload/drag",
                          "touchEnd",
                        ]
                      : null
                  );
              },
              y
            );
          },
          t,
          y,
          B0 = (C) => {},
          A0 = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C) O(N, "name", "add");
              },
              B0
            );
          },
          z = (C, T, E) => {
            if (y === 1) {
              C || K || U.addContent ? T(Y(D.addContent)) : T();
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.disabled;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.disabled ||
                      ($A ? !!U.classPrefix || undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__add-icon " +
                        Y($A ? D.classPrefix + "__add-icon--disabled" : "")
                    );
                },
                A0
              );
            }
          },
          x = (C, T, E, B, F, S) => {
            S("add-content");
            y = D.addContent ? 1 : 0;
            B(y, z);
          },
          w = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrapper");
                if (C || K || U.gridItemStyle) R.y(N, D.gridItemStyle);
              },
              x
            );
          },
          v = (C, T, E) => {
            E(
              "t-grid-item",
              {},
              (N, C) => {
                if (C) R.y(N, "width:100%");
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class", Y(D.classPrefix) + "__grid");
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class-content", Y(D.classPrefix) + "__grid-content");
                if (C) O(N, "aria-label", "上传");
                if (C) O(N, "bindclick", "onAddTap");
              },
              w
            );
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__drag-item");
                  if (C || K || !!U.column || undefined)
                    R.y(N, "width:" + Y(100 / D.column) + "%");
                },
                v
              );
            }
          },
          r = (C, T, E, B, F) => {
            F(
              D.customFiles,
              "url",
              U ? U.customFiles : undefined,
              [0, "customFiles"],
              s
            );
            t = D.addBtn && D.customLimit > 0 ? 1 : 0;
            B(t, u);
          },
          h = (C, T, E, B, F) => {
            if (g === 1) {
              F(
                D.customFiles,
                "url",
                U ? U.customFiles : undefined,
                [0, "customFiles"],
                i
              );
              j = D.addBtn && D.customLimit > 0 ? 1 : 0;
              B(j, k);
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__drag");
                  if (C || K || !!U.dragWrapStyle || undefined)
                    R.y(N, Y(D.dragWrapStyle) + ";");
                  if (C || K || Z(undefined, "listObserver"))
                    R.p(N, "list", X(c).listObserver, [
                      1,
                      "miniprogram_npm/tdesign-miniprogram/upload/drag",
                      "listObserver",
                    ]);
                  if (C || K || Z(undefined, "baseDataObserver"))
                    R.p(N, "dragBaseData", X(c).baseDataObserver, [
                      1,
                      "miniprogram_npm/tdesign-miniprogram/upload/drag",
                      "baseDataObserver",
                    ]);
                  if (C || K || U.dragList) O(N, "list", D.dragList);
                  if (C || K || U.dragBaseData)
                    O(N, "dragBaseData", D.dragBaseData);
                },
                r
              );
            }
          },
          f = (C, T, E, B) => {
            g = !D.dragLayout ? 1 : 0;
            B(g, h);
          },
          e = (C, T, E) => {
            E(
              "t-grid",
              {},
              (N, C) => {
                var $A = D.draggable;
                if (C || K || !!U.draggable || ($A ? undefined : undefined))
                  R.y(N, $A ? "overflow: visible" : "");
                A["draggable"][0] = (D, E, T) => {
                  var $B = D.draggable;
                  R.y(N, $B ? "overflow: visible" : "");
                };
                if (C || K || U.gutter) O(N, "gutter", D.gutter);
                A["gutter"][0] = (D, E, T) => {
                  O(N, "gutter", D.gutter);
                  E(N);
                };
                if (C || K || undefined) O(N, "border", false);
                if (C) O(N, "align", "center");
                if (C || K || U.column) O(N, "column", D.column);
              },
              f
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["prefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              e
            );
          };
        return { C: d, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/upload/upload";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/upload/upload.js";
define(
  "miniprogram_npm/tdesign-miniprogram/upload/upload.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/slicedToArray");
    require("../../../@babel/runtime/helpers/Arrayincludes");
    var t = require("../../../@babel/runtime/helpers/toConsumableArray"),
      i = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/createClass"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      s = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      o = require("../common/src/index"),
      l = g(require("./props")),
      u = g(require("../common/config")),
      c = require("../common/utils"),
      d = require("../common/validator");
    function g(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var h = u.default.prefix,
      f = "".concat(h, "-upload"),
      m = (function (o) {
        a(g, o);
        var u = s(g);
        function g() {
          var e;
          return (
            i(this, g),
            ((e = u.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
            ]),
            (e.options = { multipleSlots: !0 }),
            (e.data = {
              classPrefix: f,
              prefix: h,
              current: !1,
              proofs: [],
              customFiles: [],
              customLimit: 0,
              column: 4,
              dragBaseData: {},
              rows: 0,
              dragWrapStyle: "",
              dragList: [],
              dragging: !0,
              dragLayout: !1,
            }),
            (e.properties = l.default),
            (e.controlledProps = [{ key: "files", event: "success" }]),
            (e.observers = {
              "files, max, draggable": function (e, t) {
                this.handleLimit(e, t);
              },
              gridConfig: function () {
                this.updateGrid();
              },
            }),
            (e.lifetimes = {
              ready: function () {
                this.handleLimit(this.data.customFiles, this.data.max),
                  this.updateGrid();
              },
            }),
            (e.methods = {
              uploadFiles: function (e) {
                var t = this;
                return new Promise(function (i) {
                  var r = t.data.requestMethod(e);
                  if (r instanceof Promise) return r;
                  i({});
                });
              },
              startUpload: function (e) {
                var t = this;
                return "function" == typeof this.data.requestMethod
                  ? this.uploadFiles(e)
                      .then(function () {
                        e.forEach(function (e) {
                          e.percent = 100;
                        }),
                          t.triggerSuccessEvent(e);
                      })
                      .catch(function (e) {
                        t.triggerFailEvent(e);
                      })
                  : (this.triggerSuccessEvent(e),
                    this.handleLimit(this.data.customFiles, this.data.max),
                    Promise.resolve());
              },
              onAddTap: function () {
                var e = this.properties,
                  t = e.disabled,
                  i = e.mediaType,
                  r = e.source;
                t ||
                  ("media" === r
                    ? this.chooseMedia(i)
                    : this.chooseMessageFile(i));
              },
              chooseMedia: function (e) {
                var t = this,
                  i = this.data,
                  r = i.config,
                  a = i.sizeLimit,
                  s = i.customLimit;
                wx.chooseMedia(
                  Object.assign(Object.assign({ count: s, mediaType: e }, r), {
                    success: function (i) {
                      var r = [];
                      i.tempFiles.forEach(function (i) {
                        var s = i.size,
                          o = i.fileType,
                          l = i.tempFilePath,
                          u = i.width,
                          d = i.height,
                          g = i.duration,
                          h = i.thumbTempFilePath,
                          f = (0, n.__rest)(i, [
                            "size",
                            "fileType",
                            "tempFilePath",
                            "width",
                            "height",
                            "duration",
                            "thumbTempFilePath",
                          ]);
                        if ((0, c.isOverSize)(s, a)) {
                          var m =
                            ("image" === o ? "图片" : "视频") + "大小超过限制";
                          return (
                            "number" != typeof a &&
                              (m = a.message.replace(
                                "{sizeLimit}",
                                null == a ? void 0 : a.size
                              )),
                            void wx.showToast({ icon: "none", title: m })
                          );
                        }
                        var p = t.getRandFileName(l);
                        r.push(
                          Object.assign(
                            {
                              name: p,
                              type: t.getFileType(e, l, o),
                              url: l,
                              size: s,
                              width: u,
                              height: d,
                              duration: g,
                              thumb: h,
                              percent: 0,
                            },
                            f
                          )
                        );
                      }),
                        t.afterSelect(r);
                    },
                    fail: function (e) {
                      t.triggerFailEvent(e);
                    },
                    complete: function (e) {
                      t.triggerEvent("complete", e);
                    },
                  })
                );
              },
              chooseMessageFile: function (e) {
                var t = this,
                  i = this.properties,
                  r = i.max,
                  a = i.config,
                  s = i.sizeLimit;
                wx.chooseMessageFile(
                  Object.assign(
                    Object.assign(
                      { count: r, type: Array.isArray(e) ? "all" : e },
                      a
                    ),
                    {
                      success: function (i) {
                        var r = [];
                        i.tempFiles.forEach(function (i) {
                          var a = i.size,
                            o = i.type,
                            l = i.path,
                            u = (0, n.__rest)(i, ["size", "type", "path"]);
                          if ((0, c.isOverSize)(a, s)) {
                            var d =
                              ("image" === o ? "图片" : "视频") +
                              "大小超过限制";
                            return (
                              "number" != typeof s &&
                                (d = s.message.replace(
                                  "{sizeLimit}",
                                  null == s ? void 0 : s.size
                                )),
                              void wx.showToast({ icon: "none", title: d })
                            );
                          }
                          var g = t.getRandFileName(l);
                          r.push(
                            Object.assign(
                              {
                                name: g,
                                type: t.getFileType(e, l, o),
                                url: l,
                                size: a,
                                percent: 0,
                              },
                              u
                            )
                          );
                        }),
                          t.afterSelect(r);
                      },
                      fail: function (e) {
                        return t.triggerFailEvent(e);
                      },
                      complete: function (e) {
                        return t.triggerEvent("complete", e);
                      },
                    }
                  )
                );
              },
              afterSelect: function (e) {
                this._trigger("select-change", {
                  files: t(this.data.customFiles),
                  currentSelectedFiles: [e],
                }),
                  this._trigger("add", { files: e }),
                  this.startUpload(e);
              },
              dragVibrate: function (e) {
                var t,
                  i = e.vibrateType,
                  r = this.data.draggable,
                  a =
                    null === (t = null == r ? void 0 : r.vibrate) ||
                    void 0 === t ||
                    t,
                  s = null == r ? void 0 : r.collisionVibrate;
                ((a && "longPress" === i) || (s && "touchMove" === i)) &&
                  wx.vibrateShort({ type: "light" });
              },
              dragStatusChange: function (e) {
                var t = e.dragging;
                this.setData({ dragging: t });
              },
              dragEnd: function (e) {
                var t,
                  i = e.dragCollisionList;
                (t =
                  0 === i.length
                    ? this.data.customFiles
                    : i.reduce(function (e, t) {
                        var i = t.realKey,
                          r = t.data;
                        return t.fixed || (e[i] = Object.assign({}, r)), e;
                      }, [])),
                  this.triggerDropEvent(t);
              },
              triggerDropEvent: function (e) {
                var t = this,
                  i = this.properties.transition;
                if (i.backTransition)
                  var r = setTimeout(function () {
                    t.triggerEvent("drop", { files: e }), clearTimeout(r);
                  }, i.duration);
                else this.triggerEvent("drop", { files: e });
              },
            }),
            e
          );
        }
        return (
          r(g, [
            {
              key: "onProofTap",
              value: function (e) {
                var t;
                this.onFileClick(e);
                var i = e.currentTarget.dataset.index;
                wx.previewImage({
                  urls: this.data.customFiles
                    .filter(function (e) {
                      return -1 !== e.percent;
                    })
                    .map(function (e) {
                      return e.url;
                    }),
                  current:
                    null === (t = this.data.customFiles[i]) || void 0 === t
                      ? void 0
                      : t.url,
                });
              },
            },
            {
              key: "handleLimit",
              value: function (e, t) {
                0 === t && (t = 20),
                  this.setData({
                    customFiles: e.length > t ? e.slice(0, t) : e,
                    customLimit: t - e.length,
                    dragging: !0,
                  }),
                  this.initDragLayout();
              },
            },
            {
              key: "triggerSuccessEvent",
              value: function (e) {
                this._trigger("success", {
                  files: [].concat(t(this.data.customFiles), t(e)),
                });
              },
            },
            {
              key: "triggerFailEvent",
              value: function (e) {
                this.triggerEvent("fail", e);
              },
            },
            {
              key: "onFileClick",
              value: function (e) {
                var t = e.currentTarget.dataset.file;
                this.triggerEvent("click", { file: t });
              },
            },
            {
              key: "getFileType",
              value: function (e, t, i) {
                if (i) return i;
                if (1 === e.length) return e[0];
                var r = t.split("."),
                  a = r[r.length - 1];
                return [
                  "avi",
                  "wmv",
                  "mkv",
                  "mp4",
                  "mov",
                  "rm",
                  "3gp",
                  "flv",
                  "mpg",
                  "rmvb",
                ].includes(a.toLocaleLowerCase())
                  ? "video"
                  : "image";
              },
            },
            {
              key: "getRandFileName",
              value: function (e) {
                var t = e.lastIndexOf("."),
                  i = -1 === t ? "" : e.substr(t);
                return (
                  parseInt(
                    ""
                      .concat(Date.now())
                      .concat(Math.floor(900 * Math.random() + 100)),
                    10
                  ).toString(36) + i
                );
              },
            },
            {
              key: "onDelete",
              value: function (e) {
                var t = e.currentTarget.dataset.index;
                this.deleteHandle(t);
              },
            },
            {
              key: "deleteHandle",
              value: function (e) {
                var t = this.data.customFiles[e];
                this.triggerEvent("remove", { index: e, file: t });
              },
            },
            {
              key: "updateGrid",
              value: function () {
                var e = this.properties.gridConfig,
                  t = void 0 === e ? {} : e;
                (0, d.isObject)(t) || (t = {});
                var i = t,
                  r = i.column,
                  a = void 0 === r ? 4 : r,
                  s = i.width,
                  n = void 0 === s ? 160 : s,
                  o = i.height,
                  l = void 0 === o ? 160 : o;
                this.setData({
                  gridItemStyle: "width:"
                    .concat(n, "rpx;height:")
                    .concat(l, "rpx"),
                  column: a,
                });
              },
            },
            {
              key: "initDragLayout",
              value: function () {
                var e = this.properties,
                  t = e.draggable,
                  i = e.disabled;
                t && !i && (this.initDragList(), this.initDragBaseData());
              },
            },
            {
              key: "initDragList",
              value: function () {
                var e = 0,
                  t = this.data,
                  i = t.column,
                  r = t.customFiles,
                  a = t.customLimit,
                  s = [];
                if (
                  (r.forEach(function (t, r) {
                    s.push({
                      realKey: e,
                      sortKey: r,
                      tranX: (r % i) * 100 + "%",
                      tranY: 100 * Math.floor(r / i) + "%",
                      data: Object.assign({}, t),
                    }),
                      (e += 1);
                  }),
                  a > 0)
                ) {
                  var n = s.length;
                  s.push({
                    realKey: n,
                    sortKey: n,
                    tranX: (n % i) * 100 + "%",
                    tranY: 100 * Math.floor(n / i) + "%",
                    fixed: !0,
                  });
                }
                (this.data.rows = Math.ceil(s.length / i)),
                  this.setData({ dragList: s });
              },
            },
            {
              key: "initDragBaseData",
              value: function () {
                var t = this,
                  i = this.data,
                  r = i.classPrefix,
                  a = i.rows,
                  s = i.column;
                if (0 !== i.customFiles.length) {
                  var n = this.createSelectorQuery(),
                    o = ".".concat(r, " >>> .t-grid-item"),
                    l = ".".concat(r, " >>> .t-grid");
                  n.select(o).boundingClientRect(),
                    n.select(l).boundingClientRect(),
                    n.selectViewport().scrollOffset(),
                    n.exec(function (i) {
                      var n = e(i, 3),
                        o = n[0],
                        l = o.width,
                        u = o.height,
                        c = n[1],
                        d = c.left,
                        g = c.top,
                        h = n[2].scrollTop,
                        f = {
                          rows: a,
                          classPrefix: r,
                          itemWidth: l,
                          itemHeight: u,
                          wrapLeft: d,
                          wrapTop: g + h,
                          columns: s,
                        },
                        m = "height: ".concat(a * u, "px");
                      t.setData(
                        { dragBaseData: f, dragWrapStyle: m, dragLayout: !0 },
                        function () {
                          var e = setTimeout(function () {
                            t.setData({ dragging: !1 }), clearTimeout(e);
                          }, 0);
                        }
                      );
                    });
                } else
                  this.setData({
                    dragBaseData: {},
                    dragWrapStyle: "",
                    dragLayout: !1,
                  });
              },
            },
          ]),
          g
        );
      })(o.SuperComponent),
      p = (m = (0, n.__decorate)([(0, o.wxComponent)()], m));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/upload/upload.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/upload/upload.js");
