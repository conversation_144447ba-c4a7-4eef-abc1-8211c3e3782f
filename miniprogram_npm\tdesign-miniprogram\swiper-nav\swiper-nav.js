Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e,
  r = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  a = require("../../../@babel/runtime/helpers/inherits"),
  n = require("../../../@babel/runtime/helpers/createSuper"),
  i = require("tslib"),
  o = require("../common/src/index");
var s = ((e = require("../common/config")) && e.__esModule ? e : { default: e })
    .default.prefix,
  u = "".concat(s, "-swiper-nav"),
  l = (function (e) {
    a(o, e);
    var i = n(o);
    function o() {
      var e;
      return (
        t(this, o),
        ((e = i.apply(this, arguments)).externalClasses = [
          "".concat(s, "-class"),
        ]),
        (e.properties = {
          current: { type: Number, value: 0 },
          total: { type: Number, value: 0 },
          type: { type: String, value: "dots" },
          minShowNum: { type: Number, value: 2 },
          showControls: { type: Boolean, value: !1 },
          direction: { type: String, value: "horizontal" },
          paginationPosition: { type: String, value: "bottom" },
        }),
        (e.relations = { "../swiper/swiper": { type: "parent" } }),
        (e.data = { prefix: s, classPrefix: u }),
        (e.methods = {
          nav: function (e) {
            var r,
              t = e.target.dataset.dir;
            this.triggerEvent("nav-btn-change", { dir: t, source: "nav" }),
              this.$parent &&
                (null === (r = this.$parent) ||
                  void 0 === r ||
                  r.doNavBtnChange(t, "nav"));
          },
        }),
        e
      );
    }
    return r(o);
  })(o.SuperComponent),
  p = (l = (0, i.__decorate)([(0, o.wxComponent)()], l));
exports.default = p;
