Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = {
  backButtonProps: { type: Object },
  counter: { type: null },
  current: { type: Number, value: null },
  defaultCurrent: { type: Number },
  finishButtonProps: { type: Object },
  hideBack: { type: Boolean, value: !1 },
  hideCounter: { type: Boolean, value: !1 },
  hideSkip: { type: Boolean, value: !1 },
  highlightPadding: { type: Number, value: 16 },
  mode: { type: String, value: "popover" },
  nextButtonProps: { type: Object },
  showOverlay: { type: <PERSON>olean, value: !0 },
  skipButtonProps: { type: Object },
  steps: { type: Array },
  usingCustomNavbar: { type: Boolean, value: !1 },
  zIndex: { type: Number, value: 999999 },
};
exports.default = e;
