__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/indexes-anchor/indexes-anchor":
      (() => {
        var H = {};
        var S;
        var I = (P) => {
          if (!S) S = Object.assign({}, H);
          return S[P];
        };
        var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
        H[""] = (R, C, D, U) => {
          R.setFnFilter(Q.A, Q.B);
          if (typeof R.setEventListenerWrapper === "function")
            R.setEventListenerWrapper(Q.C);
          var L = R.c,
            M = R.m,
            O = R.r,
            A = {
              prefix: new Array(1),
              active: new Array(2),
              classPrefix: new Array(4),
              customStyle: new Array(1),
              style: new Array(1),
              index: new Array(1),
              sticky: new Array(1),
              anchorStyle: new Array(1),
            },
            K = U === true,
            e = (C, T, E, B, F, S) => {
              S("");
            },
            f = (C, T) => {
              C || K || U.index
                ? T(Y(D.index), (N) => {
                    A["index"][0] = (D, E, T) => {
                      T(N, Y(D.index));
                    };
                  })
                : T();
            },
            d = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__slot");
                  A["classPrefix"][2] = (D, E, T) => {
                    L(N, Y(D.classPrefix) + "__slot");
                  };
                },
                e
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([U.active])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__header", [
                        ["active", D.active],
                      ])
                    );
                  A["classPrefix"][3] = A["active"][1] = (D, E, T) => {
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__header", [
                        ["active", D.active],
                      ])
                    );
                  };
                },
                f
              );
            },
            c = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([U.sticky]), Q.a([U.active])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__wrapper", [
                        ["sticky", D.sticky],
                        ["active", D.active],
                      ])
                    );
                  A["classPrefix"][1] =
                    A["sticky"][0] =
                    A["active"][0] =
                      (D, E, T) => {
                        L(
                          N,
                          P(X(a).cls)(D.classPrefix + "__wrapper", [
                            ["sticky", D.sticky],
                            ["active", D.active],
                          ])
                        );
                      };
                  if (C || K || U.anchorStyle) R.y(N, D.anchorStyle);
                  A["anchorStyle"][0] = (D, E, T) => {
                    R.y(N, D.anchorStyle);
                  };
                },
                d
              );
            },
            b = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  A["classPrefix"][0] = A["prefix"][0] = (D, E, T) => {
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  };
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  };
                },
                c
              );
            };
          return { C: b, B: A };
        };
        return Object.assign(
          function (R) {
            return H[R];
          },
          { _: H }
        );
      })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/indexes-anchor/indexes-anchor.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-indexes-anchor{color:var(--td-indexes-anchor-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:var(--td-indexes-anchor-font-size,",
      [0, 28],
      ");line-height:var(--td-indexes-anchor-line-height,",
      [0, 44],
      ")}\n.",
      [1],
      "t-indexes-anchor__header{background-color:var(--td-indexes-anchor-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));display:none;padding:",
      [0, 8],
      " ",
      [0, 32],
      "}\n.",
      [1],
      "t-indexes-anchor__header--active{background-color:var(--td-indexes-anchor-active-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));position:relative}\n.",
      [1],
      "t-indexes-anchor__header--active::after{background-color:var(--td-component-border,var(--td-gray-color-4,#dcdcdc));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
      [1],
      "t-indexes-anchor__slot{overflow:hidden}\n.",
      [1],
      "t-indexes-anchor__slot:empty+.",
      [1],
      "t-indexes-anchor__header{display:block}\n.",
      [1],
      "t-indexes-anchor__wrapper{will-change:transform}\n.",
      [1],
      "t-indexes-anchor__wrapper--sticky{left:0;position:fixed;top:0;width:100%;z-index:1}\n.",
      [1],
      "t-indexes-anchor__wrapper--active{color:var(--td-indexes-anchor-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));font-weight:var(--td-indexes-anchor-active-font-weight,600)}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/indexes-anchor/indexes-anchor.wxss",
    }
  );
}
