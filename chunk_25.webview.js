__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/dropdown-menu/dropdown-menu": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { style: new Array(1), customStyle: new Array(1) },
          K = U === true,
          d = (C, e, f, g, h, i, T, E) => {
            var k = (C, T) => {
                C || K || Z(g, "label") ? T(Y(X(e).label)) : T();
              },
              l,
              m = (C, T, E, B, F, S, J) => {
                var $A = I(l);
                var $B = D.activeIdx == f;
                if (l && $A)
                  $A(
                    R,
                    C,
                    Object.assign({}, X(D._arrowIcon), {
                      ariaHidden: true,
                      tClass:
                        D.classPrefix +
                        "__icon " +
                        D.classPrefix +
                        "__icon--" +
                        ($B ? "active " : " ") +
                        D.prefix +
                        "-class-icon",
                    }),
                    K ||
                      (U
                        ? U._arrowIcon === true ||
                          Object.assign({}, X(U._arrowIcon), {
                            tClass:
                              !!(
                                U.classPrefix ||
                                U.classPrefix ||
                                U.activeIdx ||
                                h ||
                                ($B ? undefined : undefined) ||
                                U.prefix
                              ) || undefined,
                          })
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              },
              j = (C, T, E, B, F, S, J) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__title " +
                          Y(D.prefix) +
                          "-class-label"
                      );
                  },
                  k
                );
                l = "icon";
                B(l, m);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([!!(U.activeIdx || h) || undefined]),
                      Q.a([Z(g, "disabled")]),
                      Q.a([h]),
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__item", [
                        ["active", D.activeIdx == f],
                        ["disabled", X(e).disabled],
                        [f, true],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-item"
                  );
                if (C) O(N, "bindtap", "handleToggle");
                if (C || K || Z(g, "disabled"))
                  O(N, "aria-disabled", X(e).disabled);
                if (C) O(N, "aria-role", "button");
                if (C || K || !!(U.activeIdx || h) || undefined)
                  O(N, "aria-expanded", D.activeIdx === f);
                if (C) O(N, "aria-haspopup", "menu");
                if (C || K || h) R.d(N, "index", f);
              },
              j
            );
          },
          c = (C, T, E, B, F, S) => {
            F(D.menus, "index", U ? U.menus : undefined, [0, "menus"], d);
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                var $A = D.activeIdx === -1;
                if (C || K || !!U.activeIdx || ($A ? undefined : undefined))
                  O(N, "catchtouchmove", $A ? "" : "noop");
                if (C) R.i(N, "t-bar");
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/dropdown-menu/dropdown-menu.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-dropdown-menu{background:var(--td-dropdown-menu-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));display:-webkit-flex;display:flex;height:var(--td-dropdown-menu-height,",
      [0, 96],
      ");position:relative}\n.",
      [1],
      "t-dropdown-menu::after{background-color:var(--td-component-border,var(--td-gray-color-4,#dcdcdc));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
      [1],
      "t-dropdown-menu:after{height:var(--td-dropdown-menu-border-width,1px)}\n.",
      [1],
      "t-dropdown-menu__item{-webkit-align-items:center;align-items:center;color:var(--td-dropdown-menu-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-justify-content:center;justify-content:center;overflow:hidden;padding:0 var(--td-spacer,",
      [0, 16],
      ");position:relative}\n.",
      [1],
      "t-dropdown-menu__item--active{color:var(--td-dropdown-menu-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-dropdown-menu__item--disabled{color:var(--td-dropdown-menu-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
      [1],
      "t-dropdown-menu__icon{box-sizing:border-box;font-size:var(--td-dropdown-menu-icon-size,",
      [0, 40],
      ");padding:",
      [0, 4],
      ";transition:-webkit-transform .24s ease;transition:transform .24s ease;transition:transform .24s ease,-webkit-transform .24s ease}\n.",
      [1],
      "t-dropdown-menu__icon--active{-webkit-transform:rotate(180deg);transform:rotate(180deg)}\n.",
      [1],
      "t-dropdown-menu__icon:not(:empty){margin-left:",
      [0, 8],
      "}\n.",
      [1],
      "t-dropdown-menu__title{font-size:var(--td-dropdown-menu-font-size,",
      [0, 28],
      ");overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/dropdown-menu/dropdown-menu.wxss",
    }
  );
}
