var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/toConsumableArray"),
  a = require("../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../env/index.js");
Page({
  data: {
    categoryId: "",
    categoryName: "",
    serviceList: [],
    serviceLoading: !1,
    servicePage: 1,
    serviceLimit: 10,
    serviceHasMore: !0,
  },
  onLoad: function (e) {
    var t = e.categoryId,
      a = e.categoryName;
    this.setData({ categoryId: t || "", categoryName: a || "服务列表" }),
      wx.setNavigationBarTitle({ title: a || "服务列表" }),
      this.fetchServiceList(!0);
  },
  fetchServiceList: function () {
    var r = arguments,
      i = this;
    return a(
      e().mark(function a() {
        var o, n, c, s, d, v, l;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  if (
                    ((o = r.length > 0 && void 0 !== r[0] && r[0]),
                    !i.data.serviceLoading)
                  ) {
                    e.next = 3;
                    break;
                  }
                  return e.abrupt("return");
                case 3:
                  return (
                    (n = getApp()),
                    i.setData({ serviceLoading: !0 }),
                    (e.prev = 5),
                    (c = o ? 1 : i.data.servicePage),
                    (s = { page: c, limit: i.data.serviceLimit }),
                    i.data.categoryId && (s.category_id = i.data.categoryId),
                    (e.next = 11),
                    n.call({ path: "/api/mp/services", method: "GET", data: s })
                  );
                case 11:
                  if (0 !== (d = e.sent).code) {
                    e.next = 19;
                    break;
                  }
                  (v = d.data.services || []),
                    (l = o ? v : [].concat(t(i.data.serviceList), t(v))),
                    i.setData({
                      serviceList: l,
                      servicePage: c + 1,
                      serviceHasMore: d.data.pagination.has_next,
                      serviceLoading: !1,
                    }),
                    console.log("获取服务列表成功:", l),
                    (e.next = 20);
                  break;
                case 19:
                  throw new Error(d.message || "获取服务列表失败");
                case 20:
                  e.next = 27;
                  break;
                case 22:
                  (e.prev = 22),
                    (e.t0 = e.catch(5)),
                    console.error("获取服务列表失败:", e.t0),
                    i.setData({ serviceLoading: !1 }),
                    wx.showToast({ title: "加载失败", icon: "error" });
                case 27:
                case "end":
                  return e.stop();
              }
          },
          a,
          null,
          [[5, 22]]
        );
      })
    )();
  },
  loadMoreServices: function () {
    this.data.serviceHasMore &&
      !this.data.serviceLoading &&
      this.fetchServiceList(!1);
  },
  handleContact: function (e) {
    var t = e.detail;
    console.log("联系服务提供者:", t);
  },
  handleConfirm: function (e) {
    var t = e.detail;
    console.log("确认服务:", t);
  },
  onReady: function () {},
  onShow: function () {},
  onHide: function () {},
  onUnload: function () {},
  onPullDownRefresh: function () {
    this.fetchServiceList(!0).then(function () {
      wx.stopPullDownRefresh();
    });
  },
  onReachBottom: function () {
    this.loadMoreServices();
  },
  onShareAppMessage: function () {
    return {
      title: "".concat(this.data.categoryName, " - ").concat(r.appName),
      path: "/pages/service-list/service-list?categoryId="
        .concat(this.data.categoryId, "&categoryName=")
        .concat(this.data.categoryName),
    };
  },
});
