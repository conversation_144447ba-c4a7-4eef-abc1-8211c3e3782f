__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/navbar/navbar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            boxStyle: new Array(1),
            hideCenter: new Array(1),
            style: new Array(1),
            visibleClass: new Array(1),
            customStyle: new Array(1),
            hideLeft: new Array(1),
          },
          K = U === true,
          d,
          f = (C) => {},
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__placeholder " +
                        Y(D.prefix) +
                        "-class-placeholder"
                    );
                },
                f
              );
            }
          },
          i,
          l = (C) => {},
          k = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__left-arrow");
                if (C) O(N, "name", "chevron-left");
              },
              l
            );
          },
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__btn");
                  if (C) O(N, "aria-role", "button");
                  if (C) O(N, "aria-label", "返回");
                  if (C) R.v(N, "tap", "goBack", !1, !1, !1, !1);
                },
                k
              );
            }
          },
          m = (C, T, E, B, F, S) => {
            S("capsule");
          },
          h = (C, T, E, B, F, S) => {
            i = D.leftArrow ? 1 : 0;
            B(i, j);
            S("left");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__capsule " +
                      Y(D.prefix) +
                      "-class-capsule"
                  );
              },
              m
            );
          },
          o,
          q = (C, T) => {
            C || K || U.showTitle ? T(Y(D.showTitle)) : T();
          },
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__center-title " +
                        Y(D.prefix) +
                        "-class-title"
                    );
                },
                q
              );
            }
          },
          n = (C, T, E, B, F, S) => {
            S("title");
            o = D.title ? 1 : 0;
            B(o, p);
          },
          g = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.hideLeft;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.hideLeft ||
                    ($A ? !!U.classPrefix || undefined : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__left " +
                      Y($A ? D.classPrefix + "__left--hide" : "") +
                      " " +
                      Y(D.prefix) +
                      "-class-left"
                  );
                A["hideLeft"][0] = (D, E, T) => {
                  var $B = D.hideLeft;
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__left " +
                      Y($B ? D.classPrefix + "__left--hide" : "") +
                      " " +
                      Y(D.prefix) +
                      "-class-left"
                  );
                };
              },
              h
            );
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.hideCenter;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.hideCenter ||
                    ($A ? !!U.classPrefix || undefined : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__center " +
                      Y($A ? D.classPrefix + "__center--hide" : "") +
                      " " +
                      Y(D.prefix) +
                      "-class-center"
                  );
                A["hideCenter"][0] = (D, E, T) => {
                  var $B = D.hideCenter;
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__center " +
                      Y($B ? D.classPrefix + "__center--hide" : "") +
                      " " +
                      Y(D.prefix) +
                      "-class-center"
                  );
                };
              },
              n
            );
          },
          c = (C, T, E, B) => {
            d = D.fixed ? 1 : 0;
            B(d, e);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
              },
              g
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.fixed])]) ||
                    U.visibleClass ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix, [["fixed", D.fixed]])) +
                      " " +
                      Y(D.visibleClass) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["visibleClass"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix, [["fixed", D.fixed]])) +
                      " " +
                      Y(D.visibleClass) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([U.boxStyle, U.style, U.customStyle])
                  ) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.boxStyle, D.style, D.customStyle]));
                A["boxStyle"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([D.boxStyle, D.style, D.customStyle])
                      );
                    };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/navbar/navbar.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-navbar--fixed .",
        [1],
        "t-navbar__content{left:0;position:fixed;top:0;z-index:5001}\n.",
        [1],
        "t-navbar--visible{display:\x22\x22}\n.",
        [1],
        "t-navbar--visible-animation{opacity:1;transition:opacity .3s cubic-bezier(.645,.045,.355,1)}\n.",
        [1],
        "t-navbar--hide-animation{opacity:0;transition:opacity .3s cubic-bezier(.645,.045,.355,1)}\n.",
        [1],
        "t-navbar--hide{display:none}\n.",
        [1],
        "t-navbar__placeholder{box-sizing:initial;height:var(--td-navbar-height,48px);padding-top:var(--td-navbar-padding-top,20px);position:relative;visibility:hidden}\n.",
        [1],
        "t-navbar__content{background-color:var(--td-navbar-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));box-sizing:initial;color:var(--td-navbar-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));height:var(--td-navbar-height,48px);padding-right:var(--td-navbar-right,95px);padding-top:var(--td-navbar-padding-top,20px);width:calc(100% - var(--td-navbar-right,95px));z-index:1}\n.",
        [1],
        "t-navbar__content,.",
        [1],
        "t-navbar__left{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;position:relative}\n.",
        [1],
        "t-navbar__left{box-sizing:border-box;margin-left:var(--td-spacer-1,",
        [0, 24],
        ");max-width:var(--td-navbar-left-max-width);overflow:hidden;transition:opacity .3s cubic-bezier(.645,.045,.355,1)}\n.",
        [1],
        "t-navbar__left-arrow{font-size:var(--td-navbar-left-arrow-size,24px)}\n.",
        [1],
        "t-navbar__left--hide{opacity:0}\n.",
        [1],
        "t-navbar__capsule{-webkit-align-items:center;align-items:center;box-sizing:border-box;display:-webkit-flex;display:flex;height:var(--td-navbar-capsule-height,32px);width:var(--td-navbar-capsule-width,88px)}\n.",
        [1],
        "t-navbar__capsule::before{border:",
        [0, 2],
        " solid var(--td-navbar-capsule-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));border-radius:calc(var(--td-navbar-capsule-border-radius,16px) * 2);box-sizing:border-box;content:\x22\x22;height:200%;left:0;position:absolute;top:0;-webkit-transform:scale(.5);transform:scale(.5);-webkit-transform-origin:0 0;transform-origin:0 0;width:200%;z-index:-1}\n.",
        [1],
        "t-navbar__capsule:empty{display:none}\n.",
        [1],
        "t-navbar__center{-webkit-align-items:center;align-items:center;bottom:0;display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;font-size:18px;height:var(--td-navbar-height,48px);-webkit-justify-content:center;justify-content:center;left:var(--td-navbar-center-left,var(--td-navbar-right,95px));line-height:var(--td-navbar-height,48px);overflow:hidden;position:absolute;text-align:center;transition:opacity .3s cubic-bezier(.645,.045,.355,1);width:var(--td-navbar-center-width)}\n.",
        [1],
        "t-navbar__center:empty{display:none}\n.",
        [1],
        "t-navbar__center-title{font-size:var(--td-navbar-title-font-size,18px);font-weight:var(--td-navbar-title-font-weight,600);overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\n.",
        [1],
        "t-navbar__center--hide{opacity:0}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/navbar/navbar.wxss" }
    );
}
