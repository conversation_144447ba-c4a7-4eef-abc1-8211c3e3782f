Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/slicedToArray"),
  l = require("../../../@babel/runtime/helpers/toConsumableArray"),
  t = require("../../../@babel/runtime/helpers/createClass"),
  r = require("../../../@babel/runtime/helpers/classCallCheck"),
  a = require("../../../@babel/runtime/helpers/inherits"),
  i = require("../../../@babel/runtime/helpers/createSuper"),
  n = require("tslib"),
  u = require("../common/src/index"),
  o = require("../common/validator"),
  s = v(require("../common/config")),
  c = v(require("./props"));
function v(e) {
  return e && e.__esModule ? e : { default: e };
}
var h = s.default.prefix,
  d = "".concat(h, "-tree-select"),
  p = (function (n) {
    a(s, n);
    var u = i(s);
    function s() {
      var t;
      return (
        r(this, s),
        ((t = u.apply(this, arguments)).externalClasses = [
          "".concat(h, "-class"),
          "".concat(h, "-class-left-column"),
          "".concat(h, "-class-left-item"),
          "".concat(h, "-class-middle-item"),
          "".concat(h, "-class-right-column"),
          "".concat(h, "-class-right-item"),
          "".concat(h, "-class-right-item-label"),
        ]),
        (t.options = { multipleSlots: !0 }),
        (t.data = { prefix: h, classPrefix: d, scrollIntoView: null }),
        (t.properties = Object.assign(Object.assign({}, c.default), {
          customValue: { type: null, value: null },
        })),
        (t.controlledProps = [{ key: "value", event: "change" }]),
        (t.observers = {
          "value, customValue, options, keys, multiple": function () {
            this.buildTreeOptions();
          },
        }),
        (t.lifetimes = {
          ready: function () {
            this.getScrollIntoView("init");
          },
        }),
        (t.methods = {
          buildTreeOptions: function () {
            var t = this.data,
              r = t.options,
              a = t.value,
              i = t.defaultValue,
              n = t.customValue,
              u = t.multiple,
              o = t.keys,
              s = [],
              c = -1,
              v = { children: r };
            if (0 !== r.length) {
              for (
                var h = function () {
                  c += 1;
                  var t = v.children.map(function (e) {
                      return {
                        label: e[(null == o ? void 0 : o.label) || "label"],
                        value: e[(null == o ? void 0 : o.value) || "value"],
                        children: e.children,
                      };
                    }),
                    r =
                      (null == n ? void 0 : n[c]) ||
                      (null == a ? void 0 : a[c]);
                  if ((s.push(l(t)), null == r)) {
                    var i = e(t, 1)[0];
                    v = i;
                  } else {
                    var u = t.find(function (e) {
                      return e.value === r;
                    });
                    v = null != u ? u : t[0];
                  }
                };
                v && v.children;

              )
                h();
              var d = Math.max(0, c);
              if (u) {
                var p = n || a || i;
                if (null != p[d] && !Array.isArray(p[d]))
                  throw TypeError("应传入数组类型的 value");
              }
              this.setData({
                innerValue:
                  n ||
                  (null == s
                    ? void 0
                    : s.map(function (e, l) {
                        var t =
                          l === s.length - 1 && u ? [e[0].value] : e[0].value;
                        return (null == a ? void 0 : a[l]) || t;
                      })),
                leafLevel: d,
                treeOptions: s,
              });
            }
          },
          getScrollIntoView: function (e) {
            var l = this.data,
              t = l.value,
              r = l.customValue,
              a = l.scrollIntoView;
            if ("init" === e) {
              var i = r || t,
                n = Array.isArray(i)
                  ? i.map(function (e) {
                      return Array.isArray(e) ? e[0] : e;
                    })
                  : [i];
              this.setData({ scrollIntoView: n });
            } else {
              if (null === a) return;
              this.setData({ scrollIntoView: null });
            }
          },
          onRootChange: function (e) {
            var l = this.data.innerValue,
              t = e.detail.value;
            this.getScrollIntoView("none"),
              (l[0] = t),
              this._trigger("change", { value: l, level: 0 });
          },
          handleTreeClick: function (e) {
            var l = e.currentTarget.dataset,
              t = l.level,
              r = l.value,
              a = this.data.innerValue;
            (a[t] = r),
              this.getScrollIntoView("none"),
              this._trigger("change", { value: a, level: 1 });
          },
          handleChange: function (e) {
            var l = this.data.innerValue,
              t = e.target.dataset,
              r = t.level,
              a = t.type,
              i = ("multiple" === a ? e.detail.context : e.detail).value;
            if ("multiple" === a) {
              (0, o.isDef)(l[r]) || (l[r] = []);
              var n = l[r].indexOf(i);
              -1 === n ? l[r].push(i) : l[r].splice(n, 1);
            } else l[r] = i;
            this.getScrollIntoView("none"),
              this._trigger("change", { value: l, level: r });
          },
        }),
        t
      );
    }
    return t(s);
  })(u.SuperComponent),
  f = (p = (0, n.__decorate)([(0, u.wxComponent)()], p));
exports.default = f;
