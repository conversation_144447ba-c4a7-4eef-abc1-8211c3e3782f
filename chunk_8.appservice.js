__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/button/button": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            className: new Array(1),
            hoverStopPropagation: new Array(1),
            sendMessageTitle: new Array(1),
            showMessageCard: new Array(1),
            hoverStayTime: new Array(1),
            hoverClass: new Array(1),
            tId: new Array(1),
            hoverStartTime: new Array(1),
            sendMessageImg: new Array(1),
            type: new Array(1),
            lang: new Array(1),
            openType: new Array(1),
            appParameter: new Array(1),
            sessionFrom: new Array(1),
            disabled: new Array(3),
            ariaLabel: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            customDataset: new Array(1),
            sendMessagePath: new Array(1),
          },
          K = U === true,
          d,
          f,
          g = (C, T, E, B, F, S, J) => {
            var $A = I(f);
            if (f && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix + "__icon " + D.prefix + "-class-icon",
                    ariaHidden: true,
                    name: D.iconName,
                  },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        {
                          tClass: !!(U.classPrefix || U.prefix) || undefined,
                          name: U.iconName,
                        },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          e = (C, T, E, B, F, S, J) => {
            if (d === 1) {
              f = "icon";
              B(f, g);
            }
          },
          h,
          j = (C) => {},
          i = (C, T, E) => {
            if (h === 1) {
              E(
                "t-loading",
                {},
                (N, C) => {
                  if (C || K || !!Z(U.loadingProps, "delay") || undefined)
                    O(N, "delay", X(D.loadingProps).delay || 0);
                  if (C || K || !!Z(U.loadingProps, "duration") || undefined)
                    O(N, "duration", X(D.loadingProps).duration || 800);
                  if (C || K || !!Z(U.loadingProps, "indicator") || undefined)
                    O(N, "indicator", X(D.loadingProps).indicator || true);
                  if (
                    C ||
                    K ||
                    !!Z(U.loadingProps, "inheritColor") ||
                    undefined
                  )
                    O(
                      N,
                      "inheritColor",
                      X(D.loadingProps).inheritColor || true
                    );
                  if (C || K || !!Z(U.loadingProps, "layout") || undefined)
                    O(N, "layout", X(D.loadingProps).layout || "horizontal");
                  if (C || K || !!Z(U.loadingProps, "pause") || undefined)
                    O(N, "pause", X(D.loadingProps).pause || false);
                  if (C || K || !!Z(U.loadingProps, "progress") || undefined)
                    O(N, "progress", X(D.loadingProps).progress || 0);
                  if (C || K || !!Z(U.loadingProps, "reverse") || undefined)
                    O(N, "reverse", X(D.loadingProps).reverse || false);
                  if (C || K || !!Z(U.loadingProps, "size") || undefined)
                    O(N, "size", X(D.loadingProps).size || "40rpx");
                  if (C || K || !!Z(U.loadingProps, "text") || undefined)
                    O(N, "text", X(D.loadingProps).text || "");
                  if (C || K || !!Z(U.loadingProps, "theme") || undefined)
                    O(N, "theme", X(D.loadingProps).theme || "circular");
                  if (C) O(N, "loading", true);
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    O(
                      N,
                      "t-class",
                      Y(D.classPrefix) +
                        "__loading " +
                        Y(D.classPrefix) +
                        "__loading--wrapper"
                    );
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    O(
                      N,
                      "t-class-indicator",
                      Y(D.classPrefix) +
                        "__loading--indicator " +
                        Y(D.prefix) +
                        "-class-loading"
                    );
                },
                j
              );
            }
          },
          l,
          m = (C, T) => {
            if (l === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          k = (C, T, E, B, F, S) => {
            S("content");
            l = D.content ? 1 : 0;
            B(l, m);
            S("");
          },
          c = (C, T, E, B, F, S) => {
            d = D._icon ? 1 : 0;
            B(d, e);
            h = D.loading ? 1 : 0;
            B(h, i);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__content");
              },
              k
            );
            S("suffix");
          },
          b = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C || K || !!U.className || undefined)
                  L(N, "class " + Y(D.className));
                A["className"][0] = (D, E, T) => {
                  L(N, "class " + Y(D.className));
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                var $A = D.disabled || D.loading;
                if (
                  C ||
                  K ||
                  !!(U.disabled || U.loading) ||
                  ($A ? undefined : U.type)
                )
                  O(N, "form-type", $A ? "" : D.type);
                A["disabled"][0] = A["type"][0] = (D, E, T) => {
                  var $B = D.disabled || D.loading;
                  O(N, "form-type", $B ? "" : D.type);
                  E(N);
                };
                var $B = D.disabled || D.loading;
                if (
                  C ||
                  K ||
                  !!(U.disabled || U.loading) ||
                  ($B ? undefined : U.openType)
                )
                  O(N, "open-type", $B ? "" : D.openType);
                A["disabled"][1] = A["openType"][0] = (D, E, T) => {
                  var $C = D.disabled || D.loading;
                  O(N, "open-type", $C ? "" : D.openType);
                  E(N);
                };
                if (C || K || U.hoverStopPropagation)
                  O(N, "hover-stop-propagation", D.hoverStopPropagation);
                A["hoverStopPropagation"][0] = (D, E, T) => {
                  O(N, "hover-stop-propagation", D.hoverStopPropagation);
                  E(N);
                };
                if (C || K || U.hoverStartTime)
                  O(N, "hover-start-time", D.hoverStartTime);
                A["hoverStartTime"][0] = (D, E, T) => {
                  O(N, "hover-start-time", D.hoverStartTime);
                  E(N);
                };
                if (C || K || U.hoverStayTime)
                  O(N, "hover-stay-time", D.hoverStayTime);
                A["hoverStayTime"][0] = (D, E, T) => {
                  O(N, "hover-stay-time", D.hoverStayTime);
                  E(N);
                };
                if (C || K || U.lang) O(N, "lang", D.lang);
                A["lang"][0] = (D, E, T) => {
                  O(N, "lang", D.lang);
                  E(N);
                };
                if (C || K || U.sessionFrom)
                  O(N, "session-from", D.sessionFrom);
                A["sessionFrom"][0] = (D, E, T) => {
                  O(N, "session-from", D.sessionFrom);
                  E(N);
                };
                var $C = D.disabled || D.loading;
                if (
                  C ||
                  K ||
                  !!(U.disabled || U.loading) ||
                  ($C
                    ? undefined
                    : !!(U.hoverClass || U.classPrefix) || undefined)
                )
                  O(
                    N,
                    "hover-class",
                    $C ? "" : D.hoverClass || D.classPrefix + "--hover"
                  );
                A["disabled"][2] = A["hoverClass"][0] = (D, E, T) => {
                  var $D = D.disabled || D.loading;
                  O(
                    N,
                    "hover-class",
                    $D ? "" : D.hoverClass || D.classPrefix + "--hover"
                  );
                  E(N);
                };
                if (C || K || U.sendMessageTitle)
                  O(N, "send-message-title", D.sendMessageTitle);
                A["sendMessageTitle"][0] = (D, E, T) => {
                  O(N, "send-message-title", D.sendMessageTitle);
                  E(N);
                };
                if (C || K || U.sendMessagePath)
                  O(N, "send-message-path", D.sendMessagePath);
                A["sendMessagePath"][0] = (D, E, T) => {
                  O(N, "send-message-path", D.sendMessagePath);
                  E(N);
                };
                if (C || K || U.sendMessageImg)
                  O(N, "send-message-img", D.sendMessageImg);
                A["sendMessageImg"][0] = (D, E, T) => {
                  O(N, "send-message-img", D.sendMessageImg);
                  E(N);
                };
                if (C || K || U.appParameter)
                  O(N, "app-parameter", D.appParameter);
                A["appParameter"][0] = (D, E, T) => {
                  O(N, "app-parameter", D.appParameter);
                  E(N);
                };
                if (C || K || U.showMessageCard)
                  O(N, "show-message-card", D.showMessageCard);
                A["showMessageCard"][0] = (D, E, T) => {
                  O(N, "show-message-card", D.showMessageCard);
                  E(N);
                };
                if (C || K || U.ariaLabel) O(N, "aria-label", D.ariaLabel);
                A["ariaLabel"][0] = (D, E, T) => {
                  O(N, "aria-label", D.ariaLabel);
                  E(N);
                };
                if (C || K || U.customDataset)
                  R.d(N, "custom", D.customDataset);
                A["customDataset"][0] = (D, E, T) => {
                  R.d(N, "custom", D.customDataset);
                  E(N);
                };
                if (C) R.v(N, "tap", "handleTap", !0, !1, !1, !1);
                if (C) R.v(N, "getuserinfo", "getuserinfo", !1, !1, !1, !1);
                if (C) R.v(N, "contact", "contact", !1, !1, !1, !1);
                if (C)
                  R.v(N, "getphonenumber", "getphonenumber", !1, !1, !1, !1);
                if (C) R.v(N, "error", "error", !1, !1, !1, !1);
                if (C) R.v(N, "opensetting", "opensetting", !1, !1, !1, !1);
                if (C) R.v(N, "launchapp", "launchapp", !1, !1, !1, !1);
                if (C) R.v(N, "chooseavatar", "chooseavatar", !1, !1, !1, !1);
                if (C)
                  R.v(
                    N,
                    "agreeprivacyauthorization",
                    "agreeprivacyauthorization",
                    !1,
                    !1,
                    !1,
                    !1
                  );
                if (C || K || U.tId) R.i(N, D.tId);
                A["tId"][0] = (D, E, T) => {
                  R.i(N, D.tId);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/button/button";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/button/button.js";
define(
  "miniprogram_npm/tdesign-miniprogram/button/button.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var t = require("../../../@babel/runtime/helpers/createClass"),
      e = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      r = require("../common/src/index"),
      s = u(require("../common/config")),
      o = u(require("./props")),
      c = require("../common/version"),
      l = require("../common/utils");
    function u(t) {
      return t && t.__esModule ? t : { default: t };
    }
    var h = s.default.prefix,
      d = "".concat(h, "-button"),
      p = (function (n) {
        a(s, n);
        var r = i(s);
        function s() {
          var t;
          return (
            e(this, s),
            ((t = r.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
              "".concat(h, "-class-icon"),
              "".concat(h, "-class-loading"),
            ]),
            (t.behaviors = (0, c.canIUseFormFieldButton)()
              ? ["wx://form-field-button"]
              : []),
            (t.properties = o.default),
            (t.options = { multipleSlots: !0 }),
            (t.data = { prefix: h, className: "", classPrefix: d }),
            (t.observers = {
              "theme, size, plain, block, shape, disabled, loading, variant":
                function () {
                  this.setClass();
                },
              icon: function (t) {
                this.setData({ _icon: (0, l.calcIcon)(t, "") });
              },
            }),
            (t.lifetimes = {
              attached: function () {
                this.setClass();
              },
            }),
            (t.methods = {
              setClass: function () {
                var t = [
                  d,
                  "".concat(h, "-class"),
                  "".concat(d, "--").concat(this.data.variant || "base"),
                  "".concat(d, "--").concat(this.data.theme || "default"),
                  "".concat(d, "--").concat(this.data.shape || "rectangle"),
                  "".concat(d, "--size-").concat(this.data.size || "medium"),
                ];
                this.data.block && t.push("".concat(d, "--block")),
                  this.data.disabled && t.push("".concat(d, "--disabled")),
                  this.data.ghost && t.push("".concat(d, "--ghost")),
                  this.setData({ className: t.join(" ") });
              },
              getuserinfo: function (t) {
                this.triggerEvent("getuserinfo", t.detail);
              },
              contact: function (t) {
                this.triggerEvent("contact", t.detail);
              },
              getphonenumber: function (t) {
                this.triggerEvent("getphonenumber", t.detail);
              },
              error: function (t) {
                this.triggerEvent("error", t.detail);
              },
              opensetting: function (t) {
                this.triggerEvent("opensetting", t.detail);
              },
              launchapp: function (t) {
                this.triggerEvent("launchapp", t.detail);
              },
              chooseavatar: function (t) {
                this.triggerEvent("chooseavatar", t.detail);
              },
              agreeprivacyauthorization: function (t) {
                this.triggerEvent("agreeprivacyauthorization", t.detail);
              },
              handleTap: function (t) {
                this.data.disabled ||
                  this.data.loading ||
                  this.triggerEvent("tap", t);
              },
            }),
            t
          );
        }
        return t(s);
      })(r.SuperComponent),
      g = (p = (0, n.__decorate)([(0, r.wxComponent)()], p));
    exports.default = g;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/button/button.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/button/button.js");
