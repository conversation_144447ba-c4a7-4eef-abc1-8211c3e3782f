var o,
  e = (o = require("../../env/index.js")) && o.__esModule ? o : { default: o };
Page({
  data: {
    isLoading: !0,
    showPrivacy: !1,
    privacyChecked: !1,
    privacyContractName: "《隐私保护指引》",
    appName: e.default.appName,
  },
  onLoad: function (o) {
    console.log("登录页面加载"), this.checkPrivacyAuthorization();
  },
  checkPrivacyAuthorization: function () {
    var o = this;
    wx.getPrivacySetting({
      success: function (e) {
        console.log("隐私授权状态:", e),
          o.setData({
            privacyChecked: !0,
            privacyContractName: e.privacyContractName || "《隐私保护指引》",
          }),
          e.needAuthorization
            ? o.setData({ showPrivacy: !0, isLoading: !1 })
            : o.checkLoginStatus();
      },
      fail: function (e) {
        console.error("获取隐私设置失败:", e),
          o.setData({ privacyChecked: !0 }),
          o.checkLoginStatus();
      },
    });
  },
  openPrivacyContract: function () {
    wx.openPrivacyContract({
      success: function () {
        console.log("打开隐私协议成功");
      },
      fail: function (o) {
        console.error("打开隐私协议失败:", o),
          wx.showToast({ title: "无法打开隐私协议", icon: "error" });
      },
    });
  },
  handleAgreePrivacy: function () {
    console.log("用户同意隐私协议"),
      this.setData({ showPrivacy: !1, privacyChecked: !0 });
    var o = getApp();
    if (o.isUserLoggedIn()) {
      var e = o.getUserData();
      this.performUserRedirect(e);
    } else this.checkLoginStatus();
  },
  exitMiniProgram: function () {
    wx.showModal({
      title: "提示",
      content: "需要您同意隐私协议才能继续使用小程序",
      showCancel: !1,
      confirmText: "我知道了",
    });
  },
  checkLoginStatus: function () {
    var o = getApp();
    o.isUserLoggedIn()
      ? (console.log("用户已登录，直接处理跳转"),
        this.handleUserRedirect(o.getUserData()))
      : console.log("用户未登录，等待登录完成");
  },
  onUserLoginSuccess: function () {
    console.log("收到登录成功通知");
    var o = getApp().getUserData();
    o
      ? this.handleUserRedirect(o)
      : (console.error("登录成功但未获取到用户信息"),
        wx.showToast({ title: "获取用户信息失败", icon: "error" }));
  },
  handleUserRedirect: function (o) {
    var e = this;
    console.log("处理用户跳转逻辑:", o),
      this.setData({ isLoading: !1 }),
      wx.getPrivacySetting({
        success: function (t) {
          if ((console.log("跳转前检查隐私授权状态:", t), t.needAuthorization))
            return (
              console.log("用户未签署隐私协议，显示隐私协议弹窗"),
              void e.setData({ showPrivacy: !0, privacyChecked: !0 })
            );
          e.performUserRedirect(o);
        },
        fail: function (t) {
          console.error("检查隐私设置失败:", t), e.performUserRedirect(o);
        },
      });
  },
  performUserRedirect: function (o) {
    console.log("执行用户跳转逻辑:", o),
      o.data && "frozen" === o.data.account_status.value
        ? (console.log("用户账号已冻结，跳转到冻结页面"),
          wx.redirectTo({
            url: "/pages/dongjie/dongjie",
            fail: function (o) {
              console.error("跳转冻结页面失败:", o),
                wx.showToast({ title: "页面跳转失败", icon: "error" });
            },
          }))
        : (console.log("用户账号正常，跳转到首页"),
          wx.switchTab({
            url: "/pages/index/index",
            fail: function (o) {
              console.error("跳转首页失败:", o),
                wx.showToast({ title: "页面跳转失败", icon: "error" });
            },
          }));
  },
  onShow: function () {
    this.data.privacyChecked ||
      this.data.showPrivacy ||
      this.checkPrivacyAuthorization();
  },
  onReady: function () {},
  onHide: function () {},
  onUnload: function () {},
  onPullDownRefresh: function () {},
  onReachBottom: function () {},
  onShareAppMessage: function () {},
});
