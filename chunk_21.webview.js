__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/dialog/dialog": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/button"] ||
              {}
            )._,
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/dialog/dialog"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            closeOnOverlayClick: new Array(1),
            visible: new Array(1),
            buttonVariant: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            showOverlay: new Array(1),
            zIndex: new Array(1),
            preventScrollThrough: new Array(1),
            overlayProps: new Array(1),
            usingCustomNavbar: new Array(1),
          },
          K = U === true,
          f,
          i,
          k,
          l = (C, T, E, B, F, S, J) => {
            var $A = I(k);
            if (k && $A)
              $A(
                R,
                C,
                Object.assign({ name: "close", size: 22 }, X(D.closeBtn), {}),
                K ||
                  (U
                    ? U.closeBtn === true ||
                      Object.assign({}, X(U.closeBtn), {})
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          m = (C) => {},
          j = (C, T, E, B, F, S, J) => {
            if (i === 1) {
              k = "icon";
              B(k, l);
            } else {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "name", "close");
                  if (C) O(N, "size", "44rpx");
                },
                m
              );
            }
          },
          h = (C, T, E, B) => {
            i = P(X(a).isObject)(D.closeBtn) ? 1 : 0;
            B(i, j);
          },
          g = (C, T, E) => {
            if (f === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__close-btn");
                  if (C) R.v(N, "tap", "onClose", !1, !1, !1, !1);
                },
                h
              );
            }
          },
          o,
          q = (C, T) => {
            C || K || U.title ? T(Y(D.title)) : T();
          },
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__header");
                },
                q
              );
            }
          },
          r,
          u = (C, T) => {
            C || K || U.content ? T(Y(D.content)) : T();
          },
          t = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__body-text");
              },
              u
            );
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__body");
                },
                t
              );
            }
          },
          n = (C, T, E, B, F, S) => {
            o = D.title ? 1 : 0;
            B(o, p);
            S("title");
            r = D.content ? 1 : 0;
            B(r, s);
            S("content");
          },
          w,
          y = (C, z, A0, B0, C0, D0, T, E, B, F, S, J) => {
            var E0,
              F0 = (C, T, E, B, F, S, J) => {
                var $A = I(E0);
                if (E0 && $A)
                  $A(
                    R,
                    C,
                    Object.assign(
                      {
                        block: true,
                        type: "action",
                        extra: A0,
                        tClass: D.prefix + "-class-action",
                        rootClass: P(X(b).getActionClass)(
                          D.classPrefix,
                          D.buttonLayout
                        ),
                      },
                      X(z),
                      {}
                    ),
                    K ||
                      (U
                        ? B0 === true ||
                          Object.assign(
                            {
                              extra: C0,
                              tClass: !!U.prefix || undefined,
                              rootClass:
                                !!(
                                  Z(undefined, "getActionClass") ||
                                  U.classPrefix ||
                                  U.buttonLayout
                                ) || undefined,
                            },
                            X(B0),
                            {}
                          )
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              };
            E0 = "button";
            B(E0, F0);
          },
          x = (C, T, E, B, F) => {
            if (w === 1) {
              F(
                D.actions,
                "index",
                U ? U.actions : undefined,
                [0, "actions"],
                y
              );
            }
          },
          z,
          B0,
          C0 = (C, T, E, B, F, S, J) => {
            var $A = I(B0);
            if (B0 && $A)
              $A(
                R,
                C,
                Object.assign({ type: "cancel" }, X(D._cancel), {}),
                K ||
                  (U
                    ? U._cancel === true || Object.assign({}, X(U._cancel), {})
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          A0 = (C, T, E, B, F, S, J) => {
            if (z === 1) {
              B0 = "button";
              B(B0, C0);
            }
          },
          D0,
          F0,
          G0 = (C, T, E, B, F, S, J) => {
            var $A = I(F0);
            if (F0 && $A)
              $A(
                R,
                C,
                Object.assign(
                  { type: "confirm", theme: "primary" },
                  X(D._confirm),
                  {}
                ),
                K ||
                  (U
                    ? U._confirm === true ||
                      Object.assign({}, X(U._confirm), {})
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          E0 = (C, T, E, B, F, S, J) => {
            if (D0 === 1) {
              F0 = "button";
              B(F0, G0);
            }
          },
          v = (C, T, E, B, F, S) => {
            w = D.actions ? 1 : 0;
            B(w, x);
            S("actions");
            z = D._cancel ? 1 : 0;
            B(z, A0);
            S("cancel-btn");
            D0 = D._confirm ? 1 : 0;
            B(D0, E0);
            S("confirm-btn");
          },
          e = (C, T, E, B, F, S) => {
            S("top");
            f = D.closeBtn ? 1 : 0;
            B(f, g);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
              },
              n
            );
            S("middle");
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([!!U.buttonLayout || undefined]),
                      Q.a([
                        !!(U.buttonVariant || Z(U.actions, "length")) ||
                          undefined,
                      ]),
                    ])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__footer", [
                      ["column", D.buttonLayout === "vertical"],
                      [
                        "full",
                        D.buttonVariant == "text" && X(D.actions).length == 0,
                      ],
                    ])
                  );
                A["buttonVariant"][0] = (D, E, T) => {
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__footer", [
                      ["column", D.buttonLayout === "vertical"],
                      [
                        "full",
                        D.buttonVariant == "text" && X(D.actions).length == 0,
                      ],
                    ])
                  );
                };
              },
              v
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " " + Y(D.prefix) + "-class");
              },
              e,
              "content"
            );
          },
          c = (C, T, E) => {
            E(
              "t-popup",
              {},
              (N, C) => {
                if (C) L(N, "class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "name", "dialog");
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class", Y(D.classPrefix) + "__wrapper");
                if (C || K || U.visible) O(N, "visible", D.visible);
                A["visible"][0] = (D, E, T) => {
                  O(N, "visible", D.visible);
                  E(N);
                };
                if (C || K || U.showOverlay) O(N, "showOverlay", D.showOverlay);
                A["showOverlay"][0] = (D, E, T) => {
                  O(N, "showOverlay", D.showOverlay);
                  E(N);
                };
                if (C || K || U.closeOnOverlayClick)
                  O(N, "closeOnOverlayClick", D.closeOnOverlayClick);
                A["closeOnOverlayClick"][0] = (D, E, T) => {
                  O(N, "closeOnOverlayClick", D.closeOnOverlayClick);
                  E(N);
                };
                if (C || K || U.preventScrollThrough)
                  O(N, "preventScrollThrough", D.preventScrollThrough);
                A["preventScrollThrough"][0] = (D, E, T) => {
                  O(N, "preventScrollThrough", D.preventScrollThrough);
                  E(N);
                };
                if (C || K || U.overlayProps)
                  O(N, "overlayProps", D.overlayProps);
                A["overlayProps"][0] = (D, E, T) => {
                  O(N, "overlayProps", D.overlayProps);
                  E(N);
                };
                if (C || K || U.zIndex) O(N, "zIndex", D.zIndex);
                A["zIndex"][0] = (D, E, T) => {
                  O(N, "zIndex", D.zIndex);
                  E(N);
                };
                if (C) O(N, "placement", "center");
                if (C || K || U.usingCustomNavbar)
                  O(N, "usingCustomNavbar", D.usingCustomNavbar);
                A["usingCustomNavbar"][0] = (D, E, T) => {
                  O(N, "usingCustomNavbar", D.usingCustomNavbar);
                  E(N);
                };
                if (C) R.v(N, "visible-change", "overlayClick", !1, !1, !1, !1);
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/dialog/dialog.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-dialog{background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff));border-radius:var(--td-dialog-border-radius,var(--td-radius-extra-large,",
        [0, 24],
        "));overflow:hidden;width:var(--td-dialog-width,",
        [0, 622],
        ")}\n.",
        [1],
        "t-dialog__wrapper{--td-popup-border-radius:var(--td-dialog-border-radius,var(--td-radius-extra-large,",
        [0, 24],
        "))}\n.",
        [1],
        "t-dialog__close-btn{color:var(--td-dialog-close-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));position:absolute;right:var(--td-spacer,",
        [0, 16],
        ");top:var(--td-spacer,",
        [0, 16],
        ");z-index:1}\n.",
        [1],
        "t-dialog__content{box-sizing:border-box;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;font-size:var(--td-font-size-m,",
        [0, 32],
        ");-webkit-justify-content:center;justify-content:center;max-height:var(--td-dialog-body-max-height,",
        [0, 912],
        ");padding-bottom:0;padding-left:var(--td-spacer-3,",
        [0, 48],
        ");padding-right:var(--td-spacer-3,",
        [0, 48],
        ");padding-top:var(--td-spacer-3,",
        [0, 48],
        ")}\n.",
        [1],
        "t-dialog__content:empty{display:none}\n.",
        [1],
        "t-dialog__header{color:var(--td-dialog-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:var(--td-dialog-title-font-size,",
        [0, 36],
        ");font-weight:700;line-height:var(--td-dialog-title-line-height,",
        [0, 52],
        ");text-align:center}\n.",
        [1],
        "t-dialog__header+.",
        [1],
        "t-dialog__body{margin-top:var(--td-spacer,",
        [0, 16],
        ")}\n.",
        [1],
        "t-dialog__body{-webkit-overflow-scrolling:touch;color:var(--td-dialog-content-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))));font-size:var(--td-dialog-content-font-size,",
        [0, 32],
        ");line-height:var(--td-dialog-content-line-height,",
        [0, 48],
        ");overflow-y:scroll;text-align:center}\n.",
        [1],
        "t-dialog__body-text{word-wrap:break-word}\n.",
        [1],
        "t-dialog__body--left{text-align:left}\n.",
        [1],
        "t-dialog__body--right{text-align:right}\n.",
        [1],
        "t-dialog__footer{display:-webkit-flex;display:flex;padding:var(--td-spacer-3,",
        [0, 48],
        ")}\n.",
        [1],
        "t-dialog__footer--column{-webkit-flex-flow:column-reverse;flex-flow:column-reverse}\n.",
        [1],
        "t-dialog__footer--column .",
        [1],
        "t-dialog__button{width:100%}\n.",
        [1],
        "t-dialog__footer--full{padding:var(--td-spacer-4,",
        [0, 64],
        ") 0 0}\n.",
        [1],
        "t-dialog__button{-webkit-flex:1;flex:1;overflow:hidden;position:relative;text-overflow:ellipsis;white-space:nowrap}\n.",
        [1],
        "t-dialog__button--horizontal+.",
        [1],
        "t-dialog__button--horizontal{margin-left:var(--td-spacer-1,",
        [0, 24],
        ")}\n.",
        [1],
        "t-dialog__button--vertical+.",
        [1],
        "t-dialog__button--vertical{margin-bottom:var(--td-spacer-1,",
        [0, 24],
        ")}\n.",
        [1],
        "t-dialog__button--text{--td-button-border-radius:0;--td-button-medium-height:",
        [0, 112],
        ";border-radius:0;-webkit-flex:1;flex:1}\n.",
        [1],
        "t-dialog__button--text:before{border-left:1px solid var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7));border-radius:0;border-top:1px solid var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7));box-sizing:border-box;content:\x22 \x22;height:200%;left:0;position:absolute;top:0;-webkit-transform:scale(.5);transform:scale(.5);-webkit-transform-origin:0 0;transform-origin:0 0;width:200%}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/dialog/dialog.wxss" }
    );
}
