Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/toConsumableArray"),
  t = require("../../../@babel/runtime/helpers/slicedToArray"),
  i = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
  r = require("../../../@babel/runtime/helpers/classCallCheck"),
  a = require("../../../@babel/runtime/helpers/createClass"),
  n = require("../../../@babel/runtime/helpers/inherits"),
  s = require("../../../@babel/runtime/helpers/createSuper"),
  u = require("tslib"),
  l = require("../common/src/index"),
  o = f(require("../common/config")),
  c = require("./tool"),
  h = f(require("./props")),
  d = require("../common/utils"),
  v = f(require("../common/bus"));
function f(e) {
  return e && e.__esModule ? e : { default: e };
}
var p = o.default.prefix,
  g = "".concat(p, "-slider"),
  m = (function (l) {
    n(f, l);
    var o = s(f);
    function f() {
      var e;
      return (
        r(this, f),
        ((e = o.apply(this, arguments)).externalClasses = [
          "".concat(p, "-class"),
          "".concat(p, "-class-bar"),
          "".concat(p, "-class-bar-active"),
          "".concat(p, "-class-bar-disabled"),
          "".concat(p, "-class-cursor"),
        ]),
        (e.options = { pureDataPattern: /^__/ }),
        (e.properties = h.default),
        (e.controlledProps = [{ key: "value", event: "change" }]),
        (e.data = {
          sliderStyles: "",
          classPrefix: g,
          initialLeft: null,
          initialRight: null,
          activeLeft: 0,
          activeRight: 0,
          maxRange: 0,
          lineLeft: 0,
          lineRight: 0,
          dotTopValue: [0, 0],
          _value: 0,
          blockSize: 20,
          isScale: !1,
          scaleArray: [],
          scaleTextArray: [],
          prefix: p,
          isVisibleToScreenReader: !1,
          identifier: [-1, -1],
          __inited: !1,
        }),
        (e.observers = {
          value: function (e) {
            this.handlePropsChange(e);
          },
          _value: function (e) {
            var t = this;
            this.bus.on("initial", function () {
              return t.renderLine(e);
            }),
              this.toggleA11yTips();
          },
          marks: function (e) {
            var t = this;
            this.bus.on("initial", function () {
              return t.handleMark(e);
            });
          },
        }),
        (e.lifetimes = {
          created: function () {
            this.bus = new v.default();
          },
          attached: function () {
            this.properties.value || this.handlePropsChange(0),
              this.init(),
              this.injectPageScroll();
          },
        }),
        e
      );
    }
    return (
      a(f, [
        {
          key: "injectPageScroll",
          value: function () {
            var e = this,
              t = this.properties,
              i = t.range,
              r = t.vertical;
            if (i && r) {
              var a = getCurrentPages() || [],
                n = null;
              if ((a && a.length - 1 >= 0 && (n = a[a.length - 1]), n)) {
                var s = null == n ? void 0 : n.onPageScroll;
                n.onPageScroll = function (t) {
                  null == s || s.call(e, t), e.observerScrollTop(t);
                };
              }
            }
          },
        },
        {
          key: "observerScrollTop",
          value: function (e) {
            var t = (e || {}).scrollTop;
            this.pageScrollTop = t;
          },
        },
        {
          key: "toggleA11yTips",
          value: function () {
            var e = this;
            this.setData({ isVisibleToScreenReader: !0 }),
              setTimeout(function () {
                e.setData({ isVisibleToScreenReader: !1 });
              }, 2e3);
          },
        },
        {
          key: "renderLine",
          value: function (e) {
            var t = this.properties,
              i = t.min,
              r = t.max,
              a = t.range,
              n = this.data.maxRange;
            if (a) {
              var s = (n * (e[0] - Number(i))) / (Number(r) - Number(i)),
                u = (n * (Number(r) - e[1])) / (Number(r) - Number(i));
              this.setLineStyle(s, u);
            } else this.setSingleBarWidth(e);
          },
        },
        {
          key: "triggerValue",
          value: function (e) {
            this.preval !== e &&
              ((this.preval = e),
              this._trigger("change", {
                value: (0, c.trimValue)(e, this.properties),
              }));
          },
        },
        {
          key: "handlePropsChange",
          value: function (e) {
            var t = this,
              i = (0, c.trimValue)(e, this.properties),
              r = function () {
                t.setData({ _value: i });
              };
            0 !== this.data.maxRange ? r() : this.init().then(r);
          },
        },
        {
          key: "valueToPosition",
          value: function (e) {
            var t = this.properties,
              i = t.min,
              r = t.max,
              a = t.theme,
              n = this.data,
              s = n.blockSize,
              u = n.maxRange,
              l = "capsule" === a ? Number(s) / 2 : 0;
            return (
              Math.round(
                ((Number(e) - Number(i)) / (Number(r) - Number(i))) * u
              ) + l
            );
          },
        },
        {
          key: "handleMark",
          value: function (e) {
            var t = this,
              i = function (e) {
                return e.map(function (e) {
                  return { val: e, left: t.valueToPosition(e) };
                });
              };
            if (
              ((null == e ? void 0 : e.length) &&
                Array.isArray(e) &&
                this.setData({
                  isScale: !0,
                  scaleArray: i(e),
                  scaleTextArray: [],
                }),
              "[object Object]" === Object.prototype.toString.call(e))
            ) {
              var r = Object.keys(e).map(function (e) {
                  return Number(e);
                }),
                a = r.map(function (t) {
                  return e[t];
                });
              this.setData({
                isScale: r.length > 0,
                scaleArray: i(r),
                scaleTextArray: a,
              });
            }
          },
        },
        {
          key: "setSingleBarWidth",
          value: function (e) {
            var t = this.valueToPosition(e);
            this.setData({ lineBarWidth: "".concat(t, "px") });
          },
        },
        {
          key: "init",
          value: function () {
            return (0, u.__awaiter)(
              this,
              void 0,
              void 0,
              i().mark(function e() {
                var t, r, a, n, s, u, l, o, c, h, v, f, p;
                return i().wrap(
                  function (e) {
                    for (;;)
                      switch ((e.prev = e.next)) {
                        case 0:
                          if (!this.data.__inited) {
                            e.next = 2;
                            break;
                          }
                          return e.abrupt("return");
                        case 2:
                          return (
                            (e.next = 4), (0, d.getRect)(this, "#sliderLine")
                          );
                        case 4:
                          (t = e.sent),
                            (r = this.data.blockSize),
                            (a = this.properties),
                            (n = a.theme),
                            (s = a.vertical),
                            (u = Number(r) / 2),
                            (l = t.top),
                            (o = t.bottom),
                            (c = t.right),
                            (h = t.left),
                            (v = s ? o - l : c - h),
                            (p = s ? o : c),
                            (0 === (f = s ? l : h) && 0 === p) ||
                              ("capsule" === n &&
                                ((v = v - Number(r) - 6), (f -= u), (p -= u)),
                              this.setData({
                                maxRange: v,
                                initialLeft: f,
                                initialRight: p,
                                __inited: !0,
                              }),
                              this.bus.emit("initial"));
                        case 16:
                        case "end":
                          return e.stop();
                      }
                  },
                  e,
                  this
                );
              })
            );
          },
        },
        {
          key: "stepValue",
          value: function (e) {
            var t = this.properties,
              i = t.step,
              r = t.min,
              a = t.max,
              n =
                String(i).indexOf(".") > -1
                  ? String(i).length - String(i).indexOf(".") - 1
                  : 0;
            return (0, c.trimSingleValue)(
              Number((Math.round(e / Number(i)) * Number(i)).toFixed(n)),
              Number(r),
              Number(a)
            );
          },
        },
        {
          key: "onSingleLineTap",
          value: function (e) {
            if (!this.properties.disabled) {
              var i = -1 === this.data.identifier[0];
              if (i) {
                var r = t(e.changedTouches, 1)[0];
                this.data.identifier[0] = r.identifier;
              }
              var a = this.getSingleChangeValue(e);
              i && (this.data.identifier[0] = -1), this.triggerValue(a);
            }
          },
        },
        {
          key: "getSingleChangeValue",
          value: function (e) {
            var t = this,
              i = this.properties,
              r = i.min,
              a = i.max,
              n = i.theme,
              s = i.vertical,
              u = this.data,
              l = u.initialLeft,
              o = u.maxRange,
              c = u.blockSize,
              h = e.changedTouches.find(function (e) {
                return e.identifier === t.data.identifier[0];
              }),
              d = this.getPagePosition(h),
              v = 0;
            "capsule" === n
              ? ((v = Number(c)), s && (v *= 2), (v += 6))
              : s && (v = Number(c));
            var f,
              p = d - l - v;
            return (
              (f =
                p <= 0
                  ? Number(r)
                  : p >= o
                  ? Number(a)
                  : (p / o) * (Number(a) - Number(r)) + Number(r)),
              this.stepValue(f)
            );
          },
        },
        {
          key: "convertPosToValue",
          value: function (e, t) {
            var i = this.data.maxRange,
              r = this.properties,
              a = r.max,
              n = r.min;
            return 0 === t
              ? (e / i) * (Number(a) - Number(n)) + Number(n)
              : Number(a) - (e / i) * (Number(a) - Number(n));
          },
        },
        {
          key: "onLineTap",
          value: function (e) {
            var i = this,
              r = this.properties,
              a = r.disabled,
              n = r.theme,
              s = r.vertical,
              u = this.data,
              l = u.initialLeft,
              o = u.initialRight,
              c = u.maxRange,
              h = u.blockSize;
            if (!a) {
              var v = t(e.changedTouches, 1)[0],
                f = this.getPagePosition(v),
                p = "capsule" === n ? Number(h) / 2 : 0;
              f - l < 0 ||
                -(f - o) > c + Number(h) ||
                Promise.all([
                  (0, d.getRect)(this, "#leftDot"),
                  (0, d.getRect)(this, "#rightDot"),
                ]).then(function (e) {
                  var r = t(e, 2),
                    a = r[0],
                    u = r[1],
                    c = i.pageScrollTop || 0,
                    d = s ? a.top + c : a.left,
                    v = Math.abs(f - d - p),
                    g = s ? u.top + c : u.left,
                    m = v < Math.abs(g - f + p),
                    b = 0;
                  if (
                    ("capsule" === n
                      ? ((b = Number(h)), s && (b *= 2), (b += 6))
                      : s && (b = Number(h)),
                    m)
                  ) {
                    var y = f - l - b,
                      N = i.convertPosToValue(y, 0);
                    i.triggerValue([i.stepValue(N), i.data._value[1]]);
                  } else {
                    var T = -(f - o);
                    s && (T += b / 2);
                    var S = i.convertPosToValue(T, 1);
                    i.triggerValue([i.data._value[0], i.stepValue(S)]);
                  }
                });
            }
          },
        },
        {
          key: "onTouchStart",
          value: function (e) {
            this.triggerEvent("dragstart", { e: e });
            var i = t(e.changedTouches, 1)[0];
            "rightDot" === e.currentTarget.id
              ? (this.data.identifier[1] = i.identifier)
              : (this.data.identifier[0] = i.identifier);
          },
        },
        {
          key: "onTouchMoveLeft",
          value: function (t) {
            var i = this,
              r = this.properties,
              a = r.disabled,
              n = r.theme,
              s = r.vertical,
              u = this.data,
              l = u.initialLeft,
              o = u._value,
              c = u.blockSize;
            if (!a) {
              var h = t.changedTouches.find(function (e) {
                  return e.identifier === i.data.identifier[0];
                }),
                d = this.getPagePosition(h),
                v = 0;
              "capsule" === n && (v += Number(c)), s && (v += Number(c) + 6);
              var f = d - l - v,
                p = e(o),
                g = this.convertPosToValue(f, 0);
              (p[0] = this.stepValue(g)), this.triggerValue(p);
            }
          },
        },
        {
          key: "onTouchMoveRight",
          value: function (t) {
            var i = this,
              r = this.properties,
              a = r.disabled,
              n = r.vertical,
              s = this.data,
              u = s.initialRight,
              l = s._value,
              o = s.blockSize;
            if (!a) {
              var c = t.changedTouches.find(function (e) {
                  return e.identifier === i.data.identifier[1];
                }),
                h = this.getPagePosition(c),
                d = 0;
              n && (d += Number(o) / 2 + 6);
              var v = -(h - u - d),
                f = e(l),
                p = this.convertPosToValue(v, 1);
              (f[1] = this.stepValue(p)), this.triggerValue(f);
            }
          },
        },
        {
          key: "setLineStyle",
          value: function (e, i) {
            var r = this.properties.theme,
              a = this.data,
              n = a.blockSize,
              s = a.maxRange,
              u = "capsule" === r ? Number(n) / 2 : 0,
              l = t(this.data._value, 2),
              o = l[0],
              c = l[1],
              h = function (e) {
                return parseInt(e, 10);
              };
            this.setData({ dotTopValue: [o, c] }),
              e + i <= s
                ? this.setData({ lineLeft: h(e + u), lineRight: h(i + u) })
                : this.setData({
                    lineLeft: h(s + u - i),
                    lineRight: h(s - e + 1.5 * u),
                  });
          },
        },
        {
          key: "onTouchEnd",
          value: function (e) {
            this.triggerEvent("dragend", { e: e, value: this.data._value }),
              "rightDot" === e.currentTarget.id
                ? (this.data.identifier[1] = -1)
                : (this.data.identifier[0] = -1);
          },
        },
        {
          key: "getPagePosition",
          value: function (e) {
            var t = e.pageX,
              i = e.pageY;
            return this.properties.vertical ? i : t;
          },
        },
      ]),
      f
    );
  })(l.SuperComponent),
  b = (m = (0, u.__decorate)([(0, l.wxComponent)()], m));
exports.default = b;
