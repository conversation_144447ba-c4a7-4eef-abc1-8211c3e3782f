__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/sticky/sticky": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            containerStyle: new Array(1),
            classPrefix: new Array(2),
            prefix: new Array(2),
            style: new Array(1),
            customStyle: new Array(1),
            contentStyle: new Array(1),
            zIndex: new Array(2),
          },
          K = U === true,
          d = (C, T, E, B, F, S) => {
            S("");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                A["classPrefix"][1] = A["prefix"][1] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([!!U.zIndex || undefined, U.contentStyle])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)(["z-index:" + D.zIndex, D.contentStyle])
                  );
                A["zIndex"][1] = A["contentStyle"][0] = (D, E, T) => {
                  R.y(
                    N,
                    P(X(a)._style)(["z-index:" + D.zIndex, D.contentStyle])
                  );
                };
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["classPrefix"][0] = A["prefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([
                      !!U.zIndex || undefined,
                      U.containerStyle,
                      U.style,
                      U.customStyle,
                    ])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([
                      "z-index:" + D.zIndex,
                      D.containerStyle,
                      D.style,
                      D.customStyle,
                    ])
                  );
                A["zIndex"][0] =
                  A["containerStyle"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([
                          "z-index:" + D.zIndex,
                          D.containerStyle,
                          D.style,
                          D.customStyle,
                        ])
                      );
                    };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/tab-panel/tab-panel": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          f,
          h = (C, T) => {
            C || K || U.panel ? T(Y(D.panel)) : T();
          },
          g = (C, T, E) => {
            if (f === 1) {
              E("view", {}, (N, C) => {}, h);
            }
          },
          e = (C, T, E, B, F, S) => {
            f = D.panel ? 1 : 0;
            B(f, g);
            S("");
            S("panel");
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.active;
                  if (
                    C ||
                    K ||
                    !!(
                      U.prefix ||
                      U.classPrefix ||
                      U.active ||
                      ($A
                        ? !!U.classPrefix || undefined
                        : !!U.classPrefix || undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      "class " +
                        Y(D.prefix) +
                        "-class " +
                        Y(D.classPrefix) +
                        " " +
                        Y(
                          $A
                            ? D.classPrefix + "--active"
                            : D.classPrefix + "--inactive"
                        )
                    );
                  var $B = D.hide;
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        U.style,
                        U.customStyle,
                        !!U.hide || ($B ? undefined : undefined),
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        D.style,
                        D.customStyle,
                        $B ? "display: none" : "",
                      ])
                    );
                  if (C) O(N, "aria-role", "tabpanel");
                  if (C || K || U.id) R.i(N, D.id);
                },
                e
              );
            }
          },
          b = (C, T, E, B) => {
            c = !D.lazy || D.hasActivated ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/tabs/tabs": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/badge"] ||
              {}
            )._,
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/tabs/tabs"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            stickyProps: new Array(3),
            customStyle: new Array(1),
            offset: new Array(1),
            style: new Array(1),
            sticky: new Array(1),
            split: new Array(1),
            animation: new Array(2),
          },
          K = U === true,
          i = (C, j, k, l, m, n, T, E) => {
            var q,
              s,
              t = (C, T, E, B, F, S, J) => {
                var $A = I(s);
                if (s && $A)
                  $A(
                    R,
                    C,
                    Object.assign(
                      { tClass: D.classPrefix + "__icon" },
                      X(X(j).icon),
                      {}
                    ),
                    K ||
                      (U
                        ? Z(l, "icon") === true ||
                          Object.assign(
                            { tClass: !!U.classPrefix || undefined },
                            X(Z(l, "icon")),
                            {}
                          )
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              },
              r = (C, T, E, B, F, S, J) => {
                if (q === 1) {
                  s = "icon";
                  B(s, t);
                }
              },
              u,
              w,
              x = (C, T, E, B, F, S, J) => {
                var $A = I(w);
                if (w && $A)
                  $A(
                    R,
                    C,
                    Object.assign({}, X(X(j).badgeProps), {
                      content: X(j).label,
                      tClass: P(X(b).cls)(D.classPrefix + "__badge", [
                        ["disabled", X(j).disabled],
                        ["active", D.currentIndex === k],
                      ]),
                    }),
                    K ||
                      (U
                        ? Z(l, "badgeProps") === true ||
                          Object.assign({}, X(Z(l, "badgeProps")), {
                            content: Z(l, "label"),
                            tClass:
                              !!(
                                Z(undefined, "cls") ||
                                U.classPrefix ||
                                Q.a([
                                  Q.a([Z(l, "disabled")]),
                                  Q.a([!!(U.currentIndex || m) || undefined]),
                                ])
                              ) || undefined,
                          })
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              },
              v = (C, T, E, B, F, S, J) => {
                if (u === 1) {
                  w = "badge";
                  B(w, x);
                } else {
                  C || K || Z(l, "label") ? T(Y(X(j).label)) : T();
                }
              },
              p = (C, T, E, B) => {
                q = X(j).icon ? 1 : 0;
                B(q, r);
                u = X(j).badgeProps ? 1 : 0;
                B(u, v);
              },
              y,
              A0 = (C) => {},
              z = (C, T, E) => {
                if (y === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__item-prefix");
                    },
                    A0
                  );
                }
              },
              B0,
              D0 = (C) => {},
              C0 = (C, T, E) => {
                if (B0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__item-suffix");
                    },
                    D0
                  );
                }
              },
              o = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (
                      C ||
                      K ||
                      !!(
                        Z(undefined, "cls") ||
                        U.classPrefix ||
                        Q.a([
                          U.theme,
                          Q.a([!!(U.currentIndex || m) || undefined]),
                        ])
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        P(X(b).cls)(D.classPrefix + "__item-inner", [
                          D.theme,
                          ["active", D.currentIndex === k],
                        ])
                      );
                    if (
                      C ||
                      K ||
                      !!(
                        Z(Z(l, "badgeProps"), "dot") ||
                        Z(Z(l, "badgeProps"), "count")
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "aria-hidden",
                        X(X(j).badgeProps).dot || X(X(j).badgeProps).count
                      );
                  },
                  p
                );
                y = D.theme == "card" && D.currentIndex - 1 == k ? 1 : 0;
                B(y, z);
                B0 = D.theme == "card" && D.currentIndex + 1 == k ? 1 : 0;
                B(B0, C0);
              };
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.currentIndex === k;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      U.theme,
                      Q.a([U.spaceEvenly]),
                      U.placement,
                      Q.a([Z(l, "disabled")]),
                      Q.a([!!(U.currentIndex || m) || undefined]),
                    ]) ||
                    U.currentIndex ||
                    m ||
                    ($A ? !!U.prefix || undefined : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__item", [
                        D.theme,
                        ["evenly", D.spaceEvenly],
                        D.placement,
                        ["disabled", X(j).disabled],
                        ["active", D.currentIndex === k],
                      ])
                    ) +
                      " " +
                      Y($A ? D.prefix + "-class-active" : "") +
                      " " +
                      Y(D.prefix) +
                      "-class-item"
                  );
                if (C) O(N, "aria-role", "tab");
                if (C || K || !!(U.tabID || m) || undefined)
                  O(N, "aria-controls", D.tabID + "_panel_" + k);
                if (C || K || !!(U.currentIndex || m) || undefined)
                  O(N, "aria-selected", D.currentIndex === k);
                if (C || K || Z(l, "disabled"))
                  O(N, "aria-disabled", X(j).disabled);
                var $B = X(X(j).badgeProps).dot || X(X(j).badgeProps).count;
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    Z(Z(l, "badgeProps"), "dot") ||
                    Z(Z(l, "badgeProps"), "count") ||
                    ($B
                      ? !!(
                          Z(l, "label") ||
                          Z(undefined, "getBadgeAriaLabel") ||
                          Z(l, "badgeProps") === true ||
                          Q.b(Object.assign({}, X(Z(l, "badgeProps")), {}))
                        ) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($B
                        ? X(j).label +
                          P(X(b).getBadgeAriaLabel)(
                            Object.assign({}, X(X(j).badgeProps), {})
                          )
                        : "")
                  );
                if (C || K || m) R.d(N, "index", k);
                if (C) R.v(N, "tap", "onTabTap", !1, !1, !1, !1);
              },
              o
            );
          },
          j,
          l = (C) => {},
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.placement]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(P(X(b).cls)(D.classPrefix + "__track", [D.placement])) +
                        " " +
                        Y(D.prefix) +
                        "-class-track"
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "trackStyle") || U.trackOption) ||
                    undefined
                  )
                    R.y(N, P(X(a).trackStyle)(D.trackOption));
                },
                l
              );
            }
          },
          h = (C, T, E, B, F) => {
            F(D.tabs, "index", U ? U.tabs : undefined, [0, "tabs"], i);
            j = D.theme == "line" && D.showBottomLine ? 1 : 0;
            B(j, k);
          },
          g = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.placement, Q.a([U.spaceEvenly])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__nav", [
                      D.placement,
                      ["evenly", D.spaceEvenly],
                    ])
                  );
                if (C) O(N, "aria-role", "tablist");
              },
              h
            );
          },
          f = (C, T, E) => {
            E(
              "scroll-view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.placement, Q.a([U.split])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__scroll", [
                      D.placement,
                      ["split", D.split],
                    ])
                  );
                A["split"][0] = (D, E, T) => {
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__scroll", [
                      D.placement,
                      ["split", D.split],
                    ])
                  );
                };
                if (C) O(N, "enhanced", true);
                if (C) O(N, "enable-flex", true);
                if (C || K || U.offset) O(N, "scroll-left", D.offset);
                A["offset"][0] = (D, E, T) => {
                  O(N, "scroll-left", D.offset);
                  E(N);
                };
                if (C || K || undefined) O(N, "scroll-x", true);
                if (C) O(N, "scroll-anchoring", true);
                if (C) O(N, "scroll-with-animation", true);
                if (C) O(N, "enable-passive", true);
                if (C || K || undefined) O(N, "show-scrollbar", false);
                if (C) O(N, "type", "list");
                if (C) R.v(N, "scroll", "onScroll", !1, !1, !1, !1);
              },
              g
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "cls") || U.classPrefix || Q.a([U.theme])) ||
                  undefined
                )
                  L(N, P(X(b).cls)(D.classPrefix + "__wrapper", [D.theme]));
              },
              f
            );
          },
          n = (C, T, E, B, F, S) => {
            S("");
          },
          m = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content-inner " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "animate") ||
                    Q.b({
                      duration: Z(U.animation, "duration"),
                      currentIndex: U.currentIndex,
                    })
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a).animate)({
                      duration: X(D.animation).duration,
                      currentIndex: D.currentIndex,
                    })
                  );
                A["animation"][1] = (D, E, T) => {
                  R.y(
                    N,
                    P(X(a).animate)({
                      duration: X(D.animation).duration,
                      currentIndex: D.currentIndex,
                    })
                  );
                };
              },
              n
            );
          },
          d = (C, T, E, B, F, S) => {
            E(
              "t-sticky",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.placement])
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "t-class",
                    P(X(b).cls)(D.classPrefix + "__sticky", [D.placement])
                  );
                if (C || K || !!U.sticky || undefined)
                  O(N, "disabled", !D.sticky);
                A["sticky"][0] = (D, E, T) => {
                  O(N, "disabled", !D.sticky);
                  E(N);
                };
                if (C || K || !!Z(U.stickyProps, "zIndex") || undefined)
                  O(N, "z-index", X(D.stickyProps).zIndex || "1");
                A["stickyProps"][0] = (D, E, T) => {
                  O(N, "z-index", X(D.stickyProps).zIndex || "1");
                  E(N);
                };
                if (C || K || !!Z(U.stickyProps, "offsetTop") || undefined)
                  O(N, "offset-top", X(D.stickyProps).offsetTop || 0);
                A["stickyProps"][1] = (D, E, T) => {
                  O(N, "offset-top", X(D.stickyProps).offsetTop || 0);
                  E(N);
                };
                if (C || K || Z(U.stickyProps, "container"))
                  O(N, "container", X(D.stickyProps).container);
                A["stickyProps"][2] = (D, E, T) => {
                  O(N, "container", X(D.stickyProps).container);
                  E(N);
                };
                if (C) R.v(N, "scroll", "onTouchScroll", !1, !1, !1, !1);
              },
              e
            );
            S("middle");
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.animation])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__content", [
                      ["animated", D.animation],
                    ])
                  );
                A["animation"][0] = (D, E, T) => {
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__content", [
                      ["animated", D.animation],
                    ])
                  );
                };
                if (C) R.v(N, "touchstart", "onTouchStart", !1, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onTouchMove", !1, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !1, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchEnd", !1, !1, !1, !1);
              },
              m
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.placement]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(b).cls)(D.classPrefix, [D.placement])) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/sticky/sticky.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-sticky{position:relative}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/sticky/sticky.wxss" }
    );
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/tab-panel/tab-panel.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-tab-panel{-webkit-overflow-scrolling:touch;box-sizing:border-box;-webkit-flex-shrink:0;flex-shrink:0;height:100%;overflow-y:auto;width:100%}\n.",
      [1],
      "t-tab-panel--active{height:auto}\n.",
      [1],
      "t-tab-panel--inactive{height:0;overflow:visible}\n",
    ],
    undefined,
    { path: "./miniprogram_npm/tdesign-miniprogram/tab-panel/tab-panel.wxss" }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/tabs/tabs.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-tabs{-webkit-flex-wrap:wrap;flex-wrap:wrap;font-size:var(--td-tab-font-size,",
        [0, 28],
        ");position:relative}\n.",
        [1],
        "t-tabs,.",
        [1],
        "t-tabs__wrapper{background:var(--td-tab-nav-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-tabs__wrapper{display:-webkit-flex;display:flex;overflow:hidden}\n.",
        [1],
        "t-tabs__wrapper--card{--td-tab-border-color:transparent;background:var(--td-tab-item-tag-bg,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)))}\n.",
        [1],
        "t-tabs__item{-webkit-align-items:center;align-items:center;box-sizing:border-box;color:var(--td-tab-item-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex;-webkit-flex:none;flex:none;font-weight:400;height:var(--td-tab-item-height,",
        [0, 96],
        ");-webkit-justify-content:center;justify-content:center;overflow:hidden;padding:0 var(--td-spacer-2,",
        [0, 32],
        ");position:relative;white-space:nowrap}\n.",
        [1],
        "t-tabs__item--active{color:var(--td-tab-item-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));font-weight:600}\n.",
        [1],
        "t-tabs__item--disabled{color:var(--td-tab-item-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-tabs__item--evenly{-webkit-flex:1 0 auto;flex:1 0 auto}\n.",
        [1],
        "t-tabs__item-inner{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
        [1],
        "t-tabs__item-inner--tag{background-color:var(--td-tab-item-tag-bg,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));border-radius:calc(var(--td-tab-item-tag-height,",
        [0, 64],
        ")/ 2);line-height:var(--td-tab-item-tag-height,",
        [0, 64],
        ");padding:0 var(--td-spacer-2,",
        [0, 32],
        ");text-align:center;width:100%}\n.",
        [1],
        "t-tabs__item-inner--active.",
        [1],
        "t-tabs__item-inner--tag{background-color:var(--td-tab-item-tag-active-bg,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)))}\n.",
        [1],
        "t-tabs__item--tag:not(.",
        [1],
        "t-tabs__item--evenly){padding:0 calc(var(--td-spacer,",
        [0, 16],
        ")/ 2)}\n.",
        [1],
        "t-tabs__item--tag:not(.",
        [1],
        "t-tabs__item--evenly):first-child{margin-left:var(--td-spacer,",
        [0, 16],
        ")}\n.",
        [1],
        "t-tabs__item--tag:not(.",
        [1],
        "t-tabs__item--evenly):last-child{padding-right:var(--td-spacer-1,",
        [0, 24],
        ")}\n.",
        [1],
        "t-tabs__item--tag{padding:0 var(--td-spacer,",
        [0, 16],
        ")}\n.",
        [1],
        "t-tabs__item--card.",
        [1],
        "t-tabs__item--active{background-color:var(--td-tab-nav-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));border-radius:var(--td-radius-large,",
        [0, 18],
        ") var(--td-radius-large,",
        [0, 18],
        ") 0 0}\n.",
        [1],
        "t-tabs__item--card.",
        [1],
        "t-tabs__item--active:first-child{border-top-left-radius:0}\n.",
        [1],
        "t-tabs__item--card.",
        [1],
        "t-tabs__item--active:last-child{border-top-right-radius:0}\n.",
        [1],
        "t-tabs__item--card.",
        [1],
        "t-tabs__item--pre{border-bottom-right-radius:var(--td-radius-large,",
        [0, 18],
        ")}\n.",
        [1],
        "t-tabs__item-prefix,.",
        [1],
        "t-tabs__item-suffix{background-color:var(--td-tab-nav-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));bottom:0;height:",
        [0, 18],
        ";position:absolute;width:",
        [0, 18],
        "}\n.",
        [1],
        "t-tabs__item-prefix::after,.",
        [1],
        "t-tabs__item-suffix::after{background-color:var(--td-tab-item-tag-bg,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));content:\x22\x22;display:block;height:100%;width:100%}\n.",
        [1],
        "t-tabs__item-prefix{right:0}\n.",
        [1],
        "t-tabs__item-prefix::after{border-bottom-right-radius:var(--td-radius-large,",
        [0, 18],
        ")}\n.",
        [1],
        "t-tabs__item-suffix{left:0}\n.",
        [1],
        "t-tabs__item-suffix::after{border-bottom-left-radius:var(--td-radius-large,",
        [0, 18],
        ")}\n.",
        [1],
        "t-tabs__badge--active{--td-badge-content-text-color:var(--td-tab-item-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-tabs__badge--disabled{--td-badge-content-text-color:var(--td-tab-item-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-tabs__icon{font-size:var(--td-tab-icon-size,",
        [0, 36],
        ");margin-right:calc(var(--td-spacer,",
        [0, 16],
        ")/ 4)}\n.",
        [1],
        "t-tabs__content{overflow:hidden}\n.",
        [1],
        "t-tabs__nav{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-wrap:nowrap;flex-wrap:nowrap;position:relative;-webkit-user-select:none;user-select:none}\n.",
        [1],
        "t-tabs__nav.",
        [1],
        "t-tabs__nav--evenly{width:100%}\n.",
        [1],
        "t-tabs__track{background-color:var(--td-tab-track-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:var(--td-tab-track-radius,",
        [0, 8],
        ");bottom:",
        [0, 1],
        ";font-weight:600;height:var(--td-tab-track-thickness,",
        [0, 6],
        ");left:0;opacity:0;position:absolute;width:var(--td-tab-track-width,",
        [0, 32],
        ");z-index:1}\n.",
        [1],
        "t-tabs__scroll{height:var(--td-tab-item-height,",
        [0, 96],
        ")}\n.",
        [1],
        "t-tabs__scroll,.",
        [1],
        "t-tabs__scroll--split{position:relative}\n.",
        [1],
        "t-tabs__scroll--split::after{background-color:var(--td-tab-border-color,var(--td-component-stroke,var(--td-gray-color-3,#e7e7e7)));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
        [1],
        "t-tabs__scroll::-webkit-scrollbar{display:none}\n.",
        [1],
        "t-tabs__content{width:100%}\n.",
        [1],
        "t-tabs__content-inner{display:block}\n.",
        [1],
        "t-tabs__content--animated .",
        [1],
        "t-tabs__content-inner{display:-webkit-flex;display:flex;height:100%;position:relative;transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform;width:100%;will-change:left}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/tabs/tabs.wxss" }
    );
}
