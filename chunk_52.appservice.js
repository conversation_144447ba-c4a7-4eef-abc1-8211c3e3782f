__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/sticky/sticky": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            containerStyle: new Array(1),
            classPrefix: new Array(2),
            prefix: new Array(2),
            style: new Array(1),
            customStyle: new Array(1),
            contentStyle: new Array(1),
            zIndex: new Array(2),
          },
          K = U === true,
          d = (C, T, E, B, F, S) => {
            S("");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                A["classPrefix"][1] = A["prefix"][1] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([!!U.zIndex || undefined, U.contentStyle])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)(["z-index:" + D.zIndex, D.contentStyle])
                  );
                A["zIndex"][1] = A["contentStyle"][0] = (D, E, T) => {
                  R.y(
                    N,
                    P(X(a)._style)(["z-index:" + D.zIndex, D.contentStyle])
                  );
                };
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["classPrefix"][0] = A["prefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([
                      !!U.zIndex || undefined,
                      U.containerStyle,
                      U.style,
                      U.customStyle,
                    ])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([
                      "z-index:" + D.zIndex,
                      D.containerStyle,
                      D.style,
                      D.customStyle,
                    ])
                  );
                A["zIndex"][0] =
                  A["containerStyle"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([
                          "z-index:" + D.zIndex,
                          D.containerStyle,
                          D.style,
                          D.customStyle,
                        ])
                      );
                    };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/tab-panel/tab-panel": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          f,
          h = (C, T) => {
            C || K || U.panel ? T(Y(D.panel)) : T();
          },
          g = (C, T, E) => {
            if (f === 1) {
              E("view", {}, (N, C) => {}, h);
            }
          },
          e = (C, T, E, B, F, S) => {
            f = D.panel ? 1 : 0;
            B(f, g);
            S("");
            S("panel");
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.active;
                  if (
                    C ||
                    K ||
                    !!(
                      U.prefix ||
                      U.classPrefix ||
                      U.active ||
                      ($A
                        ? !!U.classPrefix || undefined
                        : !!U.classPrefix || undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      "class " +
                        Y(D.prefix) +
                        "-class " +
                        Y(D.classPrefix) +
                        " " +
                        Y(
                          $A
                            ? D.classPrefix + "--active"
                            : D.classPrefix + "--inactive"
                        )
                    );
                  var $B = D.hide;
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        U.style,
                        U.customStyle,
                        !!U.hide || ($B ? undefined : undefined),
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        D.style,
                        D.customStyle,
                        $B ? "display: none" : "",
                      ])
                    );
                  if (C) O(N, "aria-role", "tabpanel");
                  if (C || K || U.id) R.i(N, D.id);
                },
                e
              );
            }
          },
          b = (C, T, E, B) => {
            c = !D.lazy || D.hasActivated ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/tabs/tabs": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/badge"] ||
              {}
            )._,
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/tabs/tabs"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            stickyProps: new Array(3),
            customStyle: new Array(1),
            offset: new Array(1),
            style: new Array(1),
            sticky: new Array(1),
            split: new Array(1),
            animation: new Array(2),
          },
          K = U === true,
          i = (C, j, k, l, m, n, T, E) => {
            var q,
              s,
              t = (C, T, E, B, F, S, J) => {
                var $A = I(s);
                if (s && $A)
                  $A(
                    R,
                    C,
                    Object.assign(
                      { tClass: D.classPrefix + "__icon" },
                      X(X(j).icon),
                      {}
                    ),
                    K ||
                      (U
                        ? Z(l, "icon") === true ||
                          Object.assign(
                            { tClass: !!U.classPrefix || undefined },
                            X(Z(l, "icon")),
                            {}
                          )
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              },
              r = (C, T, E, B, F, S, J) => {
                if (q === 1) {
                  s = "icon";
                  B(s, t);
                }
              },
              u,
              w,
              x = (C, T, E, B, F, S, J) => {
                var $A = I(w);
                if (w && $A)
                  $A(
                    R,
                    C,
                    Object.assign({}, X(X(j).badgeProps), {
                      content: X(j).label,
                      tClass: P(X(b).cls)(D.classPrefix + "__badge", [
                        ["disabled", X(j).disabled],
                        ["active", D.currentIndex === k],
                      ]),
                    }),
                    K ||
                      (U
                        ? Z(l, "badgeProps") === true ||
                          Object.assign({}, X(Z(l, "badgeProps")), {
                            content: Z(l, "label"),
                            tClass:
                              !!(
                                Z(undefined, "cls") ||
                                U.classPrefix ||
                                Q.a([
                                  Q.a([Z(l, "disabled")]),
                                  Q.a([!!(U.currentIndex || m) || undefined]),
                                ])
                              ) || undefined,
                          })
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              },
              v = (C, T, E, B, F, S, J) => {
                if (u === 1) {
                  w = "badge";
                  B(w, x);
                } else {
                  C || K || Z(l, "label") ? T(Y(X(j).label)) : T();
                }
              },
              p = (C, T, E, B) => {
                q = X(j).icon ? 1 : 0;
                B(q, r);
                u = X(j).badgeProps ? 1 : 0;
                B(u, v);
              },
              y,
              A0 = (C) => {},
              z = (C, T, E) => {
                if (y === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__item-prefix");
                    },
                    A0
                  );
                }
              },
              B0,
              D0 = (C) => {},
              C0 = (C, T, E) => {
                if (B0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__item-suffix");
                    },
                    D0
                  );
                }
              },
              o = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (
                      C ||
                      K ||
                      !!(
                        Z(undefined, "cls") ||
                        U.classPrefix ||
                        Q.a([
                          U.theme,
                          Q.a([!!(U.currentIndex || m) || undefined]),
                        ])
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        P(X(b).cls)(D.classPrefix + "__item-inner", [
                          D.theme,
                          ["active", D.currentIndex === k],
                        ])
                      );
                    if (
                      C ||
                      K ||
                      !!(
                        Z(Z(l, "badgeProps"), "dot") ||
                        Z(Z(l, "badgeProps"), "count")
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "aria-hidden",
                        X(X(j).badgeProps).dot || X(X(j).badgeProps).count
                      );
                  },
                  p
                );
                y = D.theme == "card" && D.currentIndex - 1 == k ? 1 : 0;
                B(y, z);
                B0 = D.theme == "card" && D.currentIndex + 1 == k ? 1 : 0;
                B(B0, C0);
              };
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.currentIndex === k;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      U.theme,
                      Q.a([U.spaceEvenly]),
                      U.placement,
                      Q.a([Z(l, "disabled")]),
                      Q.a([!!(U.currentIndex || m) || undefined]),
                    ]) ||
                    U.currentIndex ||
                    m ||
                    ($A ? !!U.prefix || undefined : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__item", [
                        D.theme,
                        ["evenly", D.spaceEvenly],
                        D.placement,
                        ["disabled", X(j).disabled],
                        ["active", D.currentIndex === k],
                      ])
                    ) +
                      " " +
                      Y($A ? D.prefix + "-class-active" : "") +
                      " " +
                      Y(D.prefix) +
                      "-class-item"
                  );
                if (C) O(N, "aria-role", "tab");
                if (C || K || !!(U.tabID || m) || undefined)
                  O(N, "aria-controls", D.tabID + "_panel_" + k);
                if (C || K || !!(U.currentIndex || m) || undefined)
                  O(N, "aria-selected", D.currentIndex === k);
                if (C || K || Z(l, "disabled"))
                  O(N, "aria-disabled", X(j).disabled);
                var $B = X(X(j).badgeProps).dot || X(X(j).badgeProps).count;
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    Z(Z(l, "badgeProps"), "dot") ||
                    Z(Z(l, "badgeProps"), "count") ||
                    ($B
                      ? !!(
                          Z(l, "label") ||
                          Z(undefined, "getBadgeAriaLabel") ||
                          Z(l, "badgeProps") === true ||
                          Q.b(Object.assign({}, X(Z(l, "badgeProps")), {}))
                        ) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($B
                        ? X(j).label +
                          P(X(b).getBadgeAriaLabel)(
                            Object.assign({}, X(X(j).badgeProps), {})
                          )
                        : "")
                  );
                if (C || K || m) R.d(N, "index", k);
                if (C) R.v(N, "tap", "onTabTap", !1, !1, !1, !1);
              },
              o
            );
          },
          j,
          l = (C) => {},
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.placement]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(P(X(b).cls)(D.classPrefix + "__track", [D.placement])) +
                        " " +
                        Y(D.prefix) +
                        "-class-track"
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "trackStyle") || U.trackOption) ||
                    undefined
                  )
                    R.y(N, P(X(a).trackStyle)(D.trackOption));
                },
                l
              );
            }
          },
          h = (C, T, E, B, F) => {
            F(D.tabs, "index", U ? U.tabs : undefined, [0, "tabs"], i);
            j = D.theme == "line" && D.showBottomLine ? 1 : 0;
            B(j, k);
          },
          g = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.placement, Q.a([U.spaceEvenly])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__nav", [
                      D.placement,
                      ["evenly", D.spaceEvenly],
                    ])
                  );
                if (C) O(N, "aria-role", "tablist");
              },
              h
            );
          },
          f = (C, T, E) => {
            E(
              "scroll-view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.placement, Q.a([U.split])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__scroll", [
                      D.placement,
                      ["split", D.split],
                    ])
                  );
                A["split"][0] = (D, E, T) => {
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__scroll", [
                      D.placement,
                      ["split", D.split],
                    ])
                  );
                };
                if (C) O(N, "enhanced", true);
                if (C) O(N, "enable-flex", true);
                if (C || K || U.offset) O(N, "scroll-left", D.offset);
                A["offset"][0] = (D, E, T) => {
                  O(N, "scroll-left", D.offset);
                  E(N);
                };
                if (C || K || undefined) O(N, "scroll-x", true);
                if (C) O(N, "scroll-anchoring", true);
                if (C) O(N, "scroll-with-animation", true);
                if (C) O(N, "enable-passive", true);
                if (C || K || undefined) O(N, "show-scrollbar", false);
                if (C) O(N, "type", "list");
                if (C) R.v(N, "scroll", "onScroll", !1, !1, !1, !1);
              },
              g
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "cls") || U.classPrefix || Q.a([U.theme])) ||
                  undefined
                )
                  L(N, P(X(b).cls)(D.classPrefix + "__wrapper", [D.theme]));
              },
              f
            );
          },
          n = (C, T, E, B, F, S) => {
            S("");
          },
          m = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content-inner " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "animate") ||
                    Q.b({
                      duration: Z(U.animation, "duration"),
                      currentIndex: U.currentIndex,
                    })
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a).animate)({
                      duration: X(D.animation).duration,
                      currentIndex: D.currentIndex,
                    })
                  );
                A["animation"][1] = (D, E, T) => {
                  R.y(
                    N,
                    P(X(a).animate)({
                      duration: X(D.animation).duration,
                      currentIndex: D.currentIndex,
                    })
                  );
                };
              },
              n
            );
          },
          d = (C, T, E, B, F, S) => {
            E(
              "t-sticky",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.placement])
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "t-class",
                    P(X(b).cls)(D.classPrefix + "__sticky", [D.placement])
                  );
                if (C || K || !!U.sticky || undefined)
                  O(N, "disabled", !D.sticky);
                A["sticky"][0] = (D, E, T) => {
                  O(N, "disabled", !D.sticky);
                  E(N);
                };
                if (C || K || !!Z(U.stickyProps, "zIndex") || undefined)
                  O(N, "z-index", X(D.stickyProps).zIndex || "1");
                A["stickyProps"][0] = (D, E, T) => {
                  O(N, "z-index", X(D.stickyProps).zIndex || "1");
                  E(N);
                };
                if (C || K || !!Z(U.stickyProps, "offsetTop") || undefined)
                  O(N, "offset-top", X(D.stickyProps).offsetTop || 0);
                A["stickyProps"][1] = (D, E, T) => {
                  O(N, "offset-top", X(D.stickyProps).offsetTop || 0);
                  E(N);
                };
                if (C || K || Z(U.stickyProps, "container"))
                  O(N, "container", X(D.stickyProps).container);
                A["stickyProps"][2] = (D, E, T) => {
                  O(N, "container", X(D.stickyProps).container);
                  E(N);
                };
                if (C) R.v(N, "scroll", "onTouchScroll", !1, !1, !1, !1);
              },
              e
            );
            S("middle");
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.animation])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__content", [
                      ["animated", D.animation],
                    ])
                  );
                A["animation"][0] = (D, E, T) => {
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__content", [
                      ["animated", D.animation],
                    ])
                  );
                };
                if (C) R.v(N, "touchstart", "onTouchStart", !1, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onTouchMove", !1, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !1, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchEnd", !1, !1, !1, !1);
              },
              m
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.placement]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(b).cls)(D.classPrefix, [D.placement])) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/sticky/sticky";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/sticky/sticky.js";
define(
  "miniprogram_npm/tdesign-miniprogram/sticky/sticky.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/slicedToArray"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/createClass"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      o = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      s = require("../common/src/index"),
      a = u(require("./props")),
      c = u(require("../common/config")),
      l = u(require("../mixins/page-scroll")),
      f = require("../common/utils");
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var h = c.default.prefix,
      p = "".concat(h, "-sticky"),
      d = ".".concat(p),
      m = (function (n) {
        i(c, n);
        var s = o(c);
        function c() {
          var r;
          return (
            t(this, c),
            ((r = s.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
              "".concat(h, "-class-content"),
            ]),
            (r.properties = a.default),
            (r.behaviors = [(0, l.default)()]),
            (r.observers = {
              "offsetTop, disabled, container": function () {
                this.onScroll();
              },
            }),
            (r.data = {
              prefix: h,
              classPrefix: p,
              containerStyle: "",
              contentStyle: "",
            }),
            (r.methods = {
              onScroll: function (t) {
                var r = this,
                  i = (t || {}).scrollTop,
                  o = this.properties,
                  n = o.container,
                  s = o.offsetTop;
                o.disabled
                  ? this.setDataAfterDiff({ isFixed: !1, transform: 0 })
                  : ((this.scrollTop = i || this.scrollTop),
                    "function" != typeof n
                      ? (0, f.getRect)(this, d).then(function (e) {
                          e &&
                            (s >= e.top
                              ? (r.setDataAfterDiff({
                                  isFixed: !0,
                                  height: e.height,
                                }),
                                (r.transform = 0))
                              : r.setDataAfterDiff({ isFixed: !1 }));
                        })
                      : Promise.all([
                          (0, f.getRect)(this, d),
                          this.getContainerRect(),
                        ]).then(function (t) {
                          var i = e(t, 2),
                            o = i[0],
                            n = i[1];
                          o &&
                            n &&
                            (s + o.height > n.height + n.top
                              ? r.setDataAfterDiff({
                                  isFixed: !1,
                                  transform: n.height - o.height,
                                })
                              : s >= o.top
                              ? r.setDataAfterDiff({
                                  isFixed: !0,
                                  height: o.height,
                                  transform: 0,
                                })
                              : r.setDataAfterDiff({
                                  isFixed: !1,
                                  transform: 0,
                                }));
                        }));
              },
              setDataAfterDiff: function (e) {
                var t = this,
                  r = this.properties.offsetTop,
                  i = this.data,
                  o = i.containerStyle,
                  n = i.contentStyle,
                  s = e.isFixed,
                  a = e.height,
                  c = e.transform;
                wx.nextTick(function () {
                  var e = "",
                    i = "";
                  if (
                    (s &&
                      ((e += "height:".concat(a, "px;")),
                      (i += "position:fixed;top:".concat(
                        r,
                        "px;left:0;right:0;"
                      ))),
                    c)
                  ) {
                    var l = "translate3d(0, ".concat(c, "px, 0)");
                    i += "-webkit-transform:"
                      .concat(l, ";transform:")
                      .concat(l, ";");
                  }
                  (o === e && n === i) ||
                    t.setData({ containerStyle: e, contentStyle: i }),
                    t.triggerEvent("scroll", {
                      scrollTop: t.scrollTop,
                      isFixed: s,
                    });
                });
              },
              getContainerRect: function () {
                var e = this.properties.container();
                return new Promise(function (t) {
                  return e.boundingClientRect(t).exec();
                });
              },
            }),
            r
          );
        }
        return (
          r(c, [
            {
              key: "ready",
              value: function () {
                this.onScroll();
              },
            },
          ]),
          c
        );
      })(s.SuperComponent),
      x = (m = (0, n.__decorate)([(0, s.wxComponent)()], m));
    exports.default = x;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/sticky/sticky.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/sticky/sticky.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/tab-panel/tab-panel";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/tab-panel/tab-panel.js";
define(
  "miniprogram_npm/tdesign-miniprogram/tab-panel/tab-panel.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/createClass"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      r = require("tslib"),
      s = require("../common/src/index"),
      n = l(require("./props"));
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = l(require("../common/config")).default.prefix,
      o = "".concat(u, "-tab-panel"),
      c = (function (r) {
        a(l, r);
        var s = i(l);
        function l() {
          var t;
          return (
            e(this, l),
            ((t = s.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
            ]),
            (t.relations = { "../tabs/tabs": { type: "ancestor" } }),
            (t.options = { multipleSlots: !0 }),
            (t.properties = n.default),
            (t.data = {
              prefix: u,
              classPrefix: o,
              active: !1,
              hide: !0,
              id: "",
              hasActivated: !1,
            }),
            (t.observers = {
              "label, badgeProps, disabled, icon, panel, value, lazy":
                function () {
                  this.update();
                },
            }),
            t
          );
        }
        return (
          t(l, [
            {
              key: "setId",
              value: function (e) {
                this.setData({ id: e });
              },
            },
            {
              key: "getComputedName",
              value: function () {
                return null != this.properties.value
                  ? "".concat(this.properties.value)
                  : "".concat(this.index);
              },
            },
            {
              key: "update",
              value: function () {
                var e;
                null === (e = this.$parent) || void 0 === e || e.updateTabs();
              },
            },
            {
              key: "render",
              value: function (e, t) {
                (this.initialized = this.initialized || e),
                  e &&
                    !this.data.hasActivated &&
                    this.setData({ hasActivated: !0 }),
                  this.setData({ active: e, hide: !t.data.animation && !e });
              },
            },
          ]),
          l
        );
      })(s.SuperComponent),
      p = (c = (0, r.__decorate)([(0, s.wxComponent)()], c));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/tab-panel/tab-panel.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/tab-panel/tab-panel.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/tabs/tabs";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/tabs/tabs.js";
define(
  "miniprogram_npm/tdesign-miniprogram/tabs/tabs.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var t = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
      e = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/createClass"),
      n = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      s = require("../common/src/index"),
      c = d(require("./props")),
      o = d(require("../common/config")),
      h = d(require("../mixins/touch")),
      u = require("../common/utils"),
      l = require("../common/wechat");
    function d(t) {
      return t && t.__esModule ? t : { default: t };
    }
    var f = o.default.prefix,
      p = "".concat(f, "-tabs"),
      b = (0, u.uniqueFactory)("tabs"),
      v = (function (s) {
        n(d, s);
        var o = r(d);
        function d() {
          var i;
          return (
            e(this, d),
            ((i = o.apply(this, arguments)).options = {
              pureDataPattern: /^currentLabels$/,
            }),
            (i.behaviors = [h.default]),
            (i.externalClasses = [
              "".concat(f, "-class"),
              "".concat(f, "-class-item"),
              "".concat(f, "-class-active"),
              "".concat(f, "-class-track"),
              "".concat(f, "-class-content"),
            ]),
            (i.relations = {
              "../tab-panel/tab-panel": {
                type: "descendant",
                linked: function (t) {
                  this.children.push(t),
                    this.initChildId(),
                    (t.index = this.children.length - 1),
                    this.updateTabs();
                },
                unlinked: function (t) {
                  var e = this;
                  (this.children = this.children.filter(function (e) {
                    return e.index !== t.index;
                  })),
                    this.updateTabs(function () {
                      return e.setTrack();
                    }),
                    this.initChildId();
                },
              },
            }),
            (i.properties = c.default),
            (i.controlledProps = [{ key: "value", event: "change" }]),
            (i.observers = {
              value: function (t) {
                t !== this.getCurrentName() && this.setCurrentIndexByName(t);
              },
            }),
            (i.data = {
              prefix: f,
              classPrefix: p,
              tabs: [],
              currentLabels: [],
              currentIndex: -1,
              trackOption: { lineWidth: 0, distance: 0, isInit: !0 },
              offset: 0,
              scrollLeft: 0,
              tabID: "",
              placement: "top",
            }),
            (i.lifetimes = {
              created: function () {
                this.children = this.children || [];
              },
              attached: function () {
                var t = this;
                wx.nextTick(function () {
                  t.setTrack();
                }),
                  (0, u.getRect)(this, ".".concat(p)).then(function (e) {
                    t.containerWidth = e.width;
                  }),
                  this.setData({ tabID: b() });
              },
            }),
            (i.methods = {
              onScroll: function (t) {
                var e = t.detail.scrollLeft;
                this.setData({ scrollLeft: e });
              },
              updateTabs: function (t) {
                var e = this.children.map(function (t) {
                  return t.data;
                });
                e.forEach(function (t) {
                  "string" == typeof t.icon && (t.icon = { name: t.icon });
                }),
                  this.setData({ tabs: e }, t),
                  this.setCurrentIndexByName(this.properties.value);
              },
              setCurrentIndexByName: function (t) {
                var e = this.children.findIndex(function (e) {
                  return e.getComputedName() === "".concat(t);
                });
                e > -1 && this.setCurrentIndex(e);
              },
              setCurrentIndex: function (t) {
                var e = this;
                if (!(t <= -1 || t >= this.children.length)) {
                  var i = [];
                  this.children.forEach(function (n, r) {
                    var a = t === r;
                    (a === n.data.active && n.initialized) || n.render(a, e),
                      i.push(n.data.label);
                  });
                  var n = this.data,
                    r = n.currentIndex,
                    a = n.currentLabels;
                  (r === t && a.join("") === i.join("")) ||
                    this.setData(
                      { currentIndex: t, currentLabels: i },
                      function () {
                        e.setTrack();
                      }
                    );
                }
              },
              getCurrentName: function () {
                if (this.children) {
                  var t = this.children[this.data.currentIndex];
                  if (t) return t.getComputedName();
                }
              },
              calcScrollOffset: function (t, e, i, n) {
                return n + e - 0.5 * t + i / 2;
              },
              getTabHeight: function () {
                return (0, u.getRect)(this, ".".concat(p));
              },
              getTrackSize: function () {
                var t = this,
                  e = this.properties.bottomLineMode,
                  i = {
                    fixed: ".".concat(f, "-tabs__track"),
                    auto: "."
                      .concat(f, "-tabs__item--active .")
                      .concat(f, "-tabs__item-inner"),
                    full: ".".concat(f, "-tabs__item--active"),
                  };
                return new Promise(function (n, r) {
                  t.trackWidth
                    ? n(t.trackWidth)
                    : (0, u.getRect)(t, i[e] || i.fixed)
                        .then(function (t) {
                          t && n(t.width);
                        })
                        .catch(r);
                });
              },
              setTrack: function () {
                return (0, a.__awaiter)(
                  this,
                  void 0,
                  void 0,
                  t().mark(function e() {
                    var i,
                      n,
                      r,
                      a,
                      s,
                      c,
                      o,
                      h,
                      d,
                      b,
                      v = this;
                    return t().wrap(
                      function (t) {
                        for (;;)
                          switch ((t.prev = t.next)) {
                            case 0:
                              if (this.children) {
                                t.next = 3;
                                break;
                              }
                              return t.abrupt("return");
                            case 3:
                              if ((i = this.data.currentIndex) <= -1) {
                                t.next = 25;
                                break;
                              }
                              return (
                                (t.prev = 5),
                                (t.next = 8),
                                (0, u.getRect)(
                                  this,
                                  ".".concat(f, "-tabs__item"),
                                  !0
                                )
                              );
                            case 8:
                              if (((n = t.sent), (r = n[i]))) {
                                t.next = 12;
                                break;
                              }
                              return t.abrupt("return");
                            case 12:
                              return (
                                (a = 0),
                                (s = 0),
                                (c = 0),
                                n.forEach(function (t) {
                                  a < i && ((s += t.width), (a += 1)),
                                    (c += t.width);
                                }),
                                this.containerWidth
                                  ? ((o = this.calcScrollOffset(
                                      this.containerWidth,
                                      r.left,
                                      r.width,
                                      this.data.scrollLeft
                                    )),
                                    (h = c - this.containerWidth),
                                    this.setData({
                                      offset: Math.min(Math.max(o, 0), h),
                                    }))
                                  : this._hasObserved ||
                                    ((this._hasObserved = !0),
                                    (0, l.getObserver)(
                                      this,
                                      ".".concat(p)
                                    ).then(function () {
                                      return v.setTrack();
                                    })),
                                (t.next = 16),
                                this.getTrackSize()
                              );
                            case 16:
                              (d = t.sent),
                                "line" === this.data.theme &&
                                  (s += (r.width - d) / 2),
                                ((b = void 0 === this.previousIndex) ||
                                  this.previousIndex !== i) &&
                                  ((this.previousIndex = i),
                                  this.setData({
                                    trackOption: {
                                      lineWidth: d,
                                      distance: s,
                                      isInit: b,
                                    },
                                  })),
                                (t.next = 25);
                              break;
                            case 22:
                              (t.prev = 22),
                                (t.t0 = t.catch(5)),
                                this.triggerEvent("error", t.t0);
                            case 25:
                            case "end":
                              return t.stop();
                          }
                      },
                      e,
                      this,
                      [[5, 22]]
                    );
                  })
                );
              },
              onTabTap: function (t) {
                var e = t.currentTarget.dataset.index;
                this.changeIndex(e);
              },
              onTouchStart: function (t) {
                this.properties.swipeable && this.touchStart(t);
              },
              onTouchMove: function (t) {
                this.properties.swipeable && this.touchMove(t);
              },
              onTouchEnd: function () {
                if (this.properties.swipeable) {
                  var t = this.direction,
                    e = this.deltaX,
                    i = this.offsetX;
                  if ("horizontal" === t && i >= 50) {
                    var n = this.getAvailableTabIndex(e);
                    -1 !== n && this.changeIndex(n);
                  }
                }
              },
              onTouchScroll: function (t) {
                this._trigger("scroll", t.detail);
              },
              changeIndex: function (t) {
                var e = this.data.tabs[t],
                  i = e.value,
                  n = e.label;
                (null == e ? void 0 : e.disabled) ||
                  t === this.data.currentIndex ||
                  this._trigger("change", { value: i, label: n }),
                  this._trigger("click", { value: i, label: n });
              },
              getAvailableTabIndex: function (t) {
                for (
                  var e = t > 0 ? -1 : 1,
                    i = this.data,
                    n = i.currentIndex,
                    r = i.tabs,
                    a = r.length,
                    s = e;
                  n + e >= 0 && n + e < a;
                  s += e
                ) {
                  var c = n + s;
                  if (!(c >= 0 && c < a && r[c])) return n;
                  if (!r[c].disabled) return c;
                }
                return -1;
              },
            }),
            i
          );
        }
        return (
          i(d, [
            {
              key: "initChildId",
              value: function () {
                var t = this;
                this.children.forEach(function (e, i) {
                  e.setId("".concat(t.data.tabID, "_panel_").concat(i));
                });
              },
            },
          ]),
          d
        );
      })(s.SuperComponent),
      x = (v = (0, a.__decorate)([(0, s.wxComponent)()], v));
    exports.default = x;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/tabs/tabs.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/tabs/tabs.js");
