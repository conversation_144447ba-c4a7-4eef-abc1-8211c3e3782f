Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  r = require("../../../@babel/runtime/helpers/classCallCheck"),
  t = require("../../../@babel/runtime/helpers/inherits"),
  i = require("../../../@babel/runtime/helpers/createSuper"),
  a = require("tslib"),
  s = require("../common/src/index"),
  n = l(require("../common/config")),
  u = l(require("./props"));
function l(e) {
  return e && e.__esModule ? e : { default: e };
}
var o = n.default.prefix,
  c = "".concat(o, "-indexes-anchor"),
  p = (function (a) {
    t(n, a);
    var s = i(n);
    function n() {
      var e;
      return (
        r(this, n),
        ((e = s.apply(this, arguments)).externalClasses = [
          "".concat(o, "-class"),
        ]),
        (e.properties = u.default),
        (e.data = {
          prefix: o,
          classPrefix: c,
          anchorStyle: "",
          sticky: !1,
          active: !1,
        }),
        (e.relations = { "../indexes/indexes": { type: "parent" } }),
        e
      );
    }
    return e(n);
  })(s.SuperComponent),
  d = (p = (0, a.__decorate)([(0, s.wxComponent)()], p));
exports.default = d;
