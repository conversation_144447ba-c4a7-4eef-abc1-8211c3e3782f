__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/checkbox-group/checkbox-group":
      (() => {
        var H = {};
        var S;
        var I = (P) => {
          if (!S) S = Object.assign({}, H);
          return S[P];
        };
        var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
        H[""] = (R, C, D, U) => {
          R.setFnFilter(Q.A, Q.B);
          if (typeof R.setEventListenerWrapper === "function")
            R.setEventListenerWrapper(Q.C);
          var L = R.c,
            M = R.m,
            O = R.r,
            A = {
              classPrefix: new Array(1),
              style: new Array(1),
              customStyle: new Array(1),
            },
            K = U === true,
            d = (C, e, f, g, h, i, T, E) => {
              var j = (C) => {};
              E(
                "t-checkbox",
                {},
                (N, C) => {
                  if (C || K || !!U.prefix || undefined)
                    L(N, Y(D.prefix) + "-checkbox-option");
                  if (C || K || !!(Z(g, "label") || Z(g, "text")) || undefined)
                    O(N, "label", X(e).label || X(e).text || "");
                  var $A = X(e).value == null;
                  if (
                    C ||
                    K ||
                    !!Z(g, "value") ||
                    ($A ? undefined : Z(g, "value"))
                  )
                    O(N, "value", $A ? "" : X(e).value);
                  if (C || K || !!Z(g, "block") || undefined)
                    O(N, "block", X(e).block || true);
                  if (C || K || !!Z(g, "checkAll") || undefined)
                    O(N, "check-all", X(e).checkAll || false);
                  if (C || K || !!Z(g, "checked") || undefined)
                    O(N, "checked", X(e).checked || false);
                  if (C || K || !!Z(g, "content") || undefined)
                    O(N, "content", X(e).content || "");
                  if (C || K || !!Z(g, "contentDisabled") || undefined)
                    O(N, "content-disabled", X(e).contentDisabled || false);
                  if (C || K || !!Z(g, "icon") || undefined)
                    O(N, "icon", X(e).icon || "circle");
                  if (C || K || !!Z(g, "indeterminate") || undefined)
                    O(N, "indeterminate", X(e).indeterminate || false);
                  var $B = X(e).disabled == null;
                  if (
                    C ||
                    K ||
                    !!Z(g, "disabled") ||
                    ($B ? U.disabled : Z(g, "disabled"))
                  )
                    O(N, "disabled", $B ? D.disabled : X(e).disabled);
                  if (C || K || !!Z(g, "maxContentRow") || undefined)
                    O(N, "max-content-row", X(e).maxContentRow || 5);
                  if (C || K || !!Z(g, "maxLabelRow") || undefined)
                    O(N, "max-label-row", X(e).maxLabelRow || 3);
                  if (C || K || !!Z(g, "name") || undefined)
                    O(N, "name", X(e).name || "");
                  if (C || K || U.borderless) O(N, "borderless", D.borderless);
                  if (C || K || !!Z(g, "readonly") || undefined)
                    O(N, "readonly", X(e).readonly || false);
                  if (C || K || !!Z(g, "placement") || undefined)
                    O(N, "placement", X(e).placement || "left");
                  if (C || K || g) R.d(N, "item", e);
                  if (C)
                    R.v(N, "change", "handleInnerChildChange", !1, !1, !1, !1);
                },
                j
              );
            },
            c = (C, T, E, B, F, S) => {
              S("");
              F(
                D.checkboxOptions,
                "value",
                U ? U.checkboxOptions : undefined,
                [0, "checkboxOptions"],
                d
              );
            },
            b = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  A["classPrefix"][0] = (D, E, T) => {
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  };
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  };
                },
                c
              );
            };
          return { C: b, B: A };
        };
        return Object.assign(
          function (R) {
            return H[R];
          },
          { _: H }
        );
      })(),
    "miniprogram_npm/tdesign-miniprogram/checkbox/checkbox": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            maxContentRow: new Array(1),
            customStyle: new Array(1),
            tabindex: new Array(1),
            maxLabelRow: new Array(1),
            block: new Array(1),
            tId: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          d,
          g,
          j = (C) => {},
          i = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon-image");
                var $A = D.checked;
                var $C = 2;
                var $B = D.indeterminate && X(D.icon)[$C];
                var $D = 2;
                var $E = 0;
                var $F = 1;
                if (
                  C ||
                  K ||
                  !!U.checked ||
                  ($A
                    ? !!(U.indeterminate || Z(U.icon, $C)) ||
                      ($B ? Z(U.icon, $D) : Z(U.icon, $E))
                    : Z(U.icon, $F))
                )
                  O(
                    N,
                    "src",
                    $A ? ($B ? X(D.icon)[$D] : X(D.icon)[$E]) : X(D.icon)[$F]
                  );
                if (C) O(N, "webp", true);
              },
              j
            );
          },
          k,
          m = (C) => {},
          l = (C, T, E) => {
            if (k === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "cls") || U.classPrefix || Q.a([])) ||
                    undefined
                  )
                    L(N, P(X(a).cls)(D.classPrefix + "__icon-wrapper", []));
                  var $A = D.indeterminate;
                  if (
                    C ||
                    K ||
                    !!U.indeterminate ||
                    ($A ? !!U.icon || undefined : !!U.icon || undefined)
                  )
                    O(
                      N,
                      "name",
                      $A
                        ? "minus-" + D.icon + "-filled"
                        : "check-" + D.icon + "-filled"
                    );
                },
                m
              );
            }
          },
          n,
          p = (C) => {},
          q = (C) => {},
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "cls") || U.classPrefix || Q.a([])) ||
                    undefined
                  )
                    L(N, P(X(a).cls)(D.classPrefix + "__icon-wrapper", []));
                  var $A = D.indeterminate;
                  if (
                    C ||
                    K ||
                    !!U.indeterminate ||
                    ($A ? !!U.icon || undefined : undefined)
                  )
                    O(N, "name", $A ? "minus-" + D.icon + "-filled" : "check");
                },
                p
              );
            } else if (n === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      U.icon ||
                      Q.a([Q.a([U._disabled])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__icon-" + D.icon, [
                        ["disabled", D._disabled],
                      ])
                    );
                },
                q
              );
            }
          },
          r,
          t = (C) => {},
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "placeholder");
                },
                t
              );
            }
          },
          h = (C, T, E, B, F, S) => {
            if (g === 1) {
              S("icon");
            } else if (g === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__icon");
                },
                i
              );
            } else {
              k =
                D.checked && (D.icon == "circle" || D.icon == "rectangle")
                  ? 1
                  : 0;
              B(k, l);
              n =
                D.checked && D.icon == "line"
                  ? 1
                  : !D.checked && (D.icon == "circle" || D.icon == "rectangle")
                  ? 2
                  : 0;
              B(n, o);
              r = !D.checked && D.icon == "line" ? 1 : 0;
              B(r, s);
            }
          },
          f = (C, T, E, B) => {
            g = D.icon === "slot" ? 1 : P(X(a).isArray)(D.icon) ? 2 : 0;
            B(g, h);
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        U.placement,
                        Q.a([U.checked]),
                        Q.a([U._disabled]),
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__icon", [
                          D.placement,
                          ["checked", D.checked],
                          ["disabled", D._disabled],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-icon"
                    );
                },
                f
              );
            }
          },
          w,
          x = (C, T) => {
            if (w === 1) {
              C || K || U.label ? T(Y(D.label)) : T();
            }
          },
          v = (C, T, E, B, F, S) => {
            w = D.label ? 1 : 0;
            B(w, x);
            S("");
            S("label");
          },
          z,
          A0 = (C, T) => {
            if (z === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          y = (C, T, E, B, F, S) => {
            z = D.content ? 1 : 0;
            B(z, A0);
            S("content");
          },
          u = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U._disabled]), Q.a([U.checked])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__title", [
                        ["disabled", D._disabled],
                        ["checked", D.checked],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-label"
                  );
                if (C || K || !!U.maxLabelRow || undefined)
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxLabelRow));
                A["maxLabelRow"][0] = (D, E, T) => {
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxLabelRow));
                };
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U._disabled])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__description", [
                        ["disabled", D._disabled],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C || K || !!U.maxContentRow || undefined)
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxContentRow));
                A["maxContentRow"][0] = (D, E, T) => {
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxContentRow));
                };
              },
              y
            );
          },
          B0,
          D0 = (C) => {},
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.placement]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__border", [D.placement])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-border"
                    );
                },
                D0
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.theme == "default" ? 1 : 0;
            B(d, e);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__content");
                if (C) R.d(N, "target", "text");
                if (C) R.v(N, "tap", "handleTap", !1, !0, !1, !1);
              },
              u
            );
            B0 = D.theme == "default" && !D.borderless ? 1 : 0;
            B(B0, C0);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      U.placement,
                      U.theme,
                      Q.a([U.checked]),
                      Q.a([U.block]),
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D.placement,
                        D.theme,
                        ["checked", D.checked],
                        ["block", D.block],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["block"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D.placement,
                        D.theme,
                        ["checked", D.checked],
                        ["block", D.block],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-role", "checkbox");
                var $A = D.checked;
                var $B = D.indeterminate;
                if (
                  C ||
                  K ||
                  !!U.checked ||
                  ($A
                    ? !!U.indeterminate || ($B ? undefined : undefined)
                    : undefined)
                )
                  O(N, "aria-checked", $A ? ($B ? "mixed" : true) : false);
                var $C = D._disabled;
                if (C || K || !!U._disabled || ($C ? undefined : undefined))
                  O(N, "aria-disabled", $C ? true : false);
                if (C || K || U.tabindex) O(N, "tabindex", D.tabindex);
                A["tabindex"][0] = (D, E, T) => {
                  O(N, "tabindex", D.tabindex);
                  E(N);
                };
                if (C) R.v(N, "tap", "handleTap", !1, !0, !1, !1);
                if (C || K || U.tId) R.i(N, D.tId);
                A["tId"][0] = (D, E, T) => {
                  R.i(N, D.tId);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/checkbox-group/checkbox-group";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/checkbox-group/checkbox-group.js";
define(
  "miniprogram_npm/tdesign-miniprogram/checkbox-group/checkbox-group.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0),
      require("../../../@babel/runtime/helpers/Arrayincludes");
    var e = require("../../../@babel/runtime/helpers/typeof"),
      t = require("../../../@babel/runtime/helpers/createClass"),
      i = require("../../../@babel/runtime/helpers/classCallCheck"),
      n = require("../../../@babel/runtime/helpers/inherits"),
      l = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      r = require("../common/src/index"),
      c = s(require("../common/config")),
      u = s(require("./props"));
    function s(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = c.default.prefix,
      d = "".concat(o, "-checkbox-group"),
      h = (function (a) {
        n(c, a);
        var r = l(c);
        function c() {
          var t;
          return (
            i(this, c),
            ((t = r.apply(this, arguments)).externalClasses = [
              "".concat(o, "-class"),
            ]),
            (t.relations = { "../checkbox/checkbox": { type: "descendant" } }),
            (t.data = { prefix: o, classPrefix: d, checkboxOptions: [] }),
            (t.properties = u.default),
            (t.observers = {
              value: function () {
                this.updateChildren();
              },
              options: function () {
                this.initWithOptions();
              },
              disabled: function (e) {
                var t;
                (
                  null === (t = this.data.options) || void 0 === t
                    ? void 0
                    : t.length
                )
                  ? this.initWithOptions()
                  : this.getChildren().forEach(function (t) {
                      t.setDisabled(e);
                    });
              },
            }),
            (t.lifetimes = {
              ready: function () {
                this.setCheckall();
              },
            }),
            (t.controlledProps = [{ key: "value", event: "change" }]),
            (t.$checkAll = null),
            (t.methods = {
              getChildren: function () {
                var e = this.$children;
                return (
                  e.length ||
                    (e = this.selectAllComponents(
                      ".".concat(o, "-checkbox-option")
                    )),
                  e || []
                );
              },
              updateChildren: function () {
                var e = this.getChildren(),
                  t = this.data.value;
                e.length > 0 &&
                  (e.forEach(function (e) {
                    !e.data.checkAll &&
                      e.setData({
                        checked: null == t ? void 0 : t.includes(e.data.value),
                      });
                  }),
                  e.some(function (e) {
                    return e.data.checkAll;
                  }) && this.setCheckall());
              },
              updateValue: function (e) {
                var t = e.value,
                  i = e.checked,
                  n = e.checkAll,
                  l = e.item,
                  a = e.indeterminate,
                  r = this.data.value,
                  c = this.data.max,
                  u = new Set(
                    this.getChildren().map(function (e) {
                      return e.data.value;
                    })
                  );
                if (
                  ((r = r.filter(function (e) {
                    return u.has(e);
                  })),
                  !c || !i || r.length !== c)
                ) {
                  if (n) {
                    var s = this.getChildren();
                    r =
                      !i && a
                        ? s
                            .filter(function (e) {
                              var t = e.data;
                              return !(t.disabled && !r.includes(t.value));
                            })
                            .map(function (e) {
                              return e.data.value;
                            })
                        : s
                            .filter(function (e) {
                              var t = e.data;
                              return t.disabled
                                ? r.includes(t.value)
                                : i && !t.checkAll;
                            })
                            .map(function (e) {
                              return e.data.value;
                            });
                  } else if (i) r = r.concat(t);
                  else {
                    var o = r.findIndex(function (e) {
                      return e === t;
                    });
                    r.splice(o, 1);
                  }
                  this._trigger("change", { value: r, context: l });
                }
              },
              initWithOptions: function () {
                var t = this.data,
                  i = t.options,
                  n = t.value,
                  l = t.keys;
                if ((null == i ? void 0 : i.length) && Array.isArray(i)) {
                  var a = i.map(function (t) {
                    var i, a, r;
                    return ["number", "string"].includes(e(t))
                      ? {
                          label: "".concat(t),
                          value: t,
                          checked: null == n ? void 0 : n.includes(t),
                        }
                      : Object.assign(Object.assign({}, t), {
                          label:
                            t[
                              null !== (i = null == l ? void 0 : l.label) &&
                              void 0 !== i
                                ? i
                                : "label"
                            ],
                          value:
                            t[
                              null !== (a = null == l ? void 0 : l.value) &&
                              void 0 !== a
                                ? a
                                : "value"
                            ],
                          checked:
                            null == n
                              ? void 0
                              : n.includes(
                                  t[
                                    null !==
                                      (r = null == l ? void 0 : l.value) &&
                                    void 0 !== r
                                      ? r
                                      : "value"
                                  ]
                                ),
                        });
                  });
                  this.setData({ checkboxOptions: a });
                }
              },
              handleInnerChildChange: function (e) {
                var t,
                  i = e.target.dataset.item,
                  n = e.detail.checked,
                  l = {};
                i.checkAll &&
                  (l.indeterminate =
                    null === (t = this.$checkAll) || void 0 === t
                      ? void 0
                      : t.data.indeterminate),
                  this.updateValue(
                    Object.assign(
                      Object.assign(Object.assign({}, i), {
                        checked: n,
                        item: i,
                      }),
                      l
                    )
                  );
              },
              setCheckall: function () {
                var e = this,
                  t = this.getChildren();
                if (
                  (this.$checkAll ||
                    (this.$checkAll = t.find(function (e) {
                      return e.data.checkAll;
                    })),
                  this.$checkAll)
                ) {
                  var i = this.data.value,
                    n = new Set(
                      null == i
                        ? void 0
                        : i.filter(function (t) {
                            return t !== e.$checkAll.data.value;
                          })
                    ),
                    l = t.every(function (e) {
                      return !!e.data.checkAll || n.has(e.data.value);
                    });
                  this.$checkAll.setData({
                    checked: n.size > 0,
                    indeterminate: !l,
                  });
                }
              },
            }),
            t
          );
        }
        return t(c);
      })(r.SuperComponent),
      v = (h = (0, a.__decorate)([(0, r.wxComponent)()], h));
    exports.default = v;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/checkbox-group/checkbox-group.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/checkbox-group/checkbox-group.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/checkbox/checkbox";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/checkbox/checkbox.js";
define(
  "miniprogram_npm/tdesign-miniprogram/checkbox/checkbox.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      s = require("../../../@babel/runtime/helpers/createSuper"),
      r = require("tslib"),
      i = require("../common/src/index"),
      c = d(require("../common/config")),
      l = d(require("./props"));
    function d(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var n = c.default.prefix,
      o = "".concat(n, "-checkbox"),
      u = (function (r) {
        a(c, r);
        var i = s(c);
        function c() {
          var e;
          return (
            t(this, c),
            ((e = i.apply(this, arguments)).externalClasses = [
              "".concat(n, "-class"),
              "".concat(n, "-class-label"),
              "".concat(n, "-class-icon"),
              "".concat(n, "-class-content"),
              "".concat(n, "-class-border"),
            ]),
            (e.behaviors = ["wx://form-field"]),
            (e.relations = {
              "../checkbox-group/checkbox-group": {
                type: "ancestor",
                linked: function (e) {
                  var t = e.data,
                    a = t.value,
                    s = t.disabled,
                    r = t.borderless,
                    i = new Set(a),
                    c = i.has(this.data.value),
                    l = {
                      _disabled:
                        null == this.data.disabled ? s : this.data.disabled,
                    };
                  r && (l.borderless = !0),
                    (l.checked = this.data.checked || c),
                    this.data.checked && e.updateValue(this.data),
                    this.data.checkAll && (l.checked = i.size > 0),
                    this.setData(l);
                },
              },
            }),
            (e.options = { multipleSlots: !0 }),
            (e.properties = Object.assign(Object.assign({}, l.default), {
              theme: { type: String, value: "default" },
              tId: { type: String },
            })),
            (e.data = { prefix: n, classPrefix: o, _disabled: !1 }),
            (e.observers = {
              disabled: function (e) {
                this.setData({ _disabled: e });
              },
            }),
            (e.controlledProps = [{ key: "checked", event: "change" }]),
            (e.methods = {
              handleTap: function (e) {
                var t = this.data,
                  a = t._disabled,
                  s = t.readonly,
                  r = t.contentDisabled,
                  i = e.currentTarget.dataset.target;
                if (!(a || s || ("text" === i && r))) {
                  var c = this.data,
                    l = c.value,
                    d = c.label,
                    n = !this.data.checked,
                    o = this.$parent;
                  o
                    ? o.updateValue(
                        Object.assign(Object.assign({}, this.data), {
                          checked: n,
                          item: { label: d, value: l, checked: n },
                        })
                      )
                    : this._trigger("change", {
                        context: { value: l, label: d },
                        checked: n,
                      });
                }
              },
              setDisabled: function (e) {
                this.setData({ _disabled: this.data.disabled || e });
              },
            }),
            e
          );
        }
        return e(c);
      })(i.SuperComponent),
      h = (u = (0, r.__decorate)([(0, i.wxComponent)()], u));
    exports.default = h;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/checkbox/checkbox.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/checkbox/checkbox.js");
