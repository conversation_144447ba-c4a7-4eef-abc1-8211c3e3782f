@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-swiper {
  position: relative;
}
.t-swiper-host {
  border-radius: var(--td-swiper-radius, var(--td-radius-large, 18rpx));
  overflow: hidden;
  transform: translateY(0);
}
.t-swiper__item {
  align-items: center;
  box-sizing: border-box;
  display: -webkit-flex;
  display: flex;
  padding: var(--td-swiper-item-padding, 0);
}
.t-swiper__image {
  transition: all 0.3s ease;
  width: 100%;
}
.t-swiper__image-host {
  width: 100%;
}
