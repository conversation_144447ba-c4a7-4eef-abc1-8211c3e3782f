__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/input/input": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/input/input"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            safePasswordNonce: new Array(1),
            customStyle: new Array(1),
            layout: new Array(1),
            placeholderClass: new Array(1),
            cursorColor: new Array(1),
            safePasswordTimeStamp: new Array(1),
            type: new Array(3),
            holdKeyboard: new Array(1),
            placeholder: new Array(1),
            confirmHold: new Array(1),
            focus: new Array(1),
            borderless: new Array(1),
            autoFocus: new Array(1),
            disabled: new Array(3),
            status: new Array(1),
            placeholderStyle: new Array(1),
            cursor: new Array(1),
            selectionStart: new Array(1),
            maxlength: new Array(1),
            safePasswordSalt: new Array(1),
            readonly: new Array(1),
            selectionEnd: new Array(1),
            safePasswordCustomHash: new Array(1),
            alwaysEmbed: new Array(1),
            cursorSpacing: new Array(1),
            confirmType: new Array(1),
            allowInputOverMax: new Array(1),
            safePasswordLength: new Array(1),
            style: new Array(1),
            safePasswordCertPath: new Array(1),
            adjustPosition: new Array(1),
          },
          K = U === true,
          g,
          i,
          j = (C, T, E, B, F, S, J) => {
            var $A = I(i);
            if (i && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-prefix-icon", ariaHidden: true },
                  X(D._prefixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._prefixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._prefixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          h = (C, T, E, B, F, S, J) => {
            if (g === 1) {
              i = "icon";
              B(i, j);
            }
          },
          f = (C, T, E, B, F, S) => {
            S("prefix-icon");
            g = D._prefixIcon ? 1 : 0;
            B(g, h);
          },
          l,
          m = (C, T) => {
            if (l === 1) {
              C || K || U.label ? T(Y(D.label)) : T();
            }
          },
          k = (C, T, E, B, F, S) => {
            S("label");
            l = D.label ? 1 : 0;
            B(l, m);
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon--prefix");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__label " + Y(D.prefix) + "-class-label"
                  );
                if (C) O(N, "aria-hidden", true);
              },
              k
            );
          },
          p = (C) => {},
          q,
          t,
          u = (C, T, E, B, F, S, J) => {
            var $A = I(t);
            if (t && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass: D.prefix + "-class-clearable",
                    ariaRole: "button",
                    ariaLabel: "清除",
                  },
                  X(D._clearIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._clearIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._clearIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          s = (C, T, E, B, F, S, J) => {
            t = "icon";
            B(t, u);
          },
          r = (C, T, E) => {
            if (q === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__wrap--clearable-icon");
                  if (C) R.v(N, "tap", "clearInput", !1, !1, !1, !1);
                },
                s
              );
            }
          },
          w,
          y = (C, T) => {
            C || K || U.suffix ? T(Y(D.suffix)) : T();
          },
          x = (C, T, E) => {
            if (w === 1) {
              E("text", {}, (N, C) => {}, y);
            }
          },
          v = (C, T, E, B, F, S) => {
            w = D.suffix ? 1 : 0;
            B(w, x);
            S("suffix");
          },
          A0,
          C0,
          D0 = (C, T, E, B, F, S, J) => {
            var $A = I(C0);
            if (C0 && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass: D.prefix + "-class-suffix-icon",
                    ariaRole: "button",
                  },
                  X(D._suffixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._suffixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._suffixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          B0 = (C, T, E, B, F, S, J) => {
            if (A0 === 1) {
              C0 = "icon";
              B(C0, D0);
            }
          },
          z = (C, T, E, B, F, S) => {
            S("suffix-icon");
            A0 = D._suffixIcon ? 1 : 0;
            B(A0, B0);
          },
          o = (C, T, E, B) => {
            E(
              "input",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getInputClass") ||
                    U.classPrefix ||
                    U.suffix ||
                    U.align ||
                    U.disabled ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).getInputClass)(
                        D.classPrefix,
                        D.suffix,
                        D.align,
                        D.disabled
                      )
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-input"
                  );
                A["disabled"][2] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(b).getInputClass)(
                        D.classPrefix,
                        D.suffix,
                        D.align,
                        D.disabled
                      )
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-input"
                  );
                };
                var $A = D.allowInputOverMax;
                if (
                  C ||
                  K ||
                  !!U.allowInputOverMax ||
                  ($A ? undefined : U.maxlength)
                )
                  O(N, "maxlength", $A ? -1 : D.maxlength);
                A["allowInputOverMax"][0] = A["maxlength"][0] = (D, E, T) => {
                  var $B = D.allowInputOverMax;
                  O(N, "maxlength", $B ? -1 : D.maxlength);
                  E(N);
                };
                if (C || K || !!(U.disabled || U.readonly) || undefined)
                  O(N, "disabled", D.disabled || D.readonly);
                A["disabled"][0] = A["readonly"][0] = (D, E, T) => {
                  O(N, "disabled", D.disabled || D.readonly);
                  E(N);
                };
                if (C || K || U.placeholder) O(N, "placeholder", D.placeholder);
                A["placeholder"][0] = (D, E, T) => {
                  O(N, "placeholder", D.placeholder);
                  E(N);
                };
                if (C || K || U.placeholderStyle)
                  O(N, "placeholder-style", D.placeholderStyle);
                A["placeholderStyle"][0] = (D, E, T) => {
                  O(N, "placeholder-style", D.placeholderStyle);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.disabled])]) ||
                    U.placeholderClass
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "placeholder-class",
                    Y(
                      P(X(a).cls)(D.classPrefix + "__placeholder", [
                        ["disabled", D.disabled],
                      ])
                    ) +
                      " " +
                      Y(D.placeholderClass)
                  );
                A["disabled"][1] = A["placeholderClass"][0] = (D, E, T) => {
                  O(
                    N,
                    "placeholder-class",
                    Y(
                      P(X(a).cls)(D.classPrefix + "__placeholder", [
                        ["disabled", D.disabled],
                      ])
                    ) +
                      " " +
                      Y(D.placeholderClass)
                  );
                  E(N);
                };
                if (C || K || U.value) O(N, "value", D.value);
                if (C || K || !!U.type || undefined)
                  O(N, "password", D.type === "password");
                A["type"][0] = (D, E, T) => {
                  O(N, "password", D.type === "password");
                  E(N);
                };
                var $B = D.type === "password";
                if (C || K || !!U.type || ($B ? undefined : U.type))
                  O(N, "type", $B ? "text" : D.type);
                A["type"][1] = A["type"][2] = (D, E, T) => {
                  var $C = D.type === "password";
                  O(N, "type", $C ? "text" : D.type);
                  E(N);
                };
                if (C || K || U.focus) O(N, "focus", D.focus);
                A["focus"][0] = (D, E, T) => {
                  O(N, "focus", D.focus);
                  E(N);
                };
                if (C || K || U.confirmType)
                  O(N, "confirm-type", D.confirmType);
                A["confirmType"][0] = (D, E, T) => {
                  O(N, "confirm-type", D.confirmType);
                  E(N);
                };
                if (C || K || U.confirmHold)
                  O(N, "confirm-hold", D.confirmHold);
                A["confirmHold"][0] = (D, E, T) => {
                  O(N, "confirm-hold", D.confirmHold);
                  E(N);
                };
                if (C || K || U.cursor) O(N, "cursor", D.cursor);
                A["cursor"][0] = (D, E, T) => {
                  O(N, "cursor", D.cursor);
                  E(N);
                };
                if (C || K || U.cursorColor)
                  O(N, "cursor-color", D.cursorColor);
                A["cursorColor"][0] = (D, E, T) => {
                  O(N, "cursor-color", D.cursorColor);
                  E(N);
                };
                if (C || K || U.cursorSpacing)
                  O(N, "cursor-spacing", D.cursorSpacing);
                A["cursorSpacing"][0] = (D, E, T) => {
                  O(N, "cursor-spacing", D.cursorSpacing);
                  E(N);
                };
                if (C || K || U.adjustPosition)
                  O(N, "adjust-position", D.adjustPosition);
                A["adjustPosition"][0] = (D, E, T) => {
                  O(N, "adjust-position", D.adjustPosition);
                  E(N);
                };
                if (C || K || U.autoFocus) O(N, "auto-focus", D.autoFocus);
                A["autoFocus"][0] = (D, E, T) => {
                  O(N, "auto-focus", D.autoFocus);
                  E(N);
                };
                if (C || K || U.alwaysEmbed)
                  O(N, "always-embed", D.alwaysEmbed);
                A["alwaysEmbed"][0] = (D, E, T) => {
                  O(N, "always-embed", D.alwaysEmbed);
                  E(N);
                };
                if (C || K || U.selectionStart)
                  O(N, "selection-start", D.selectionStart);
                A["selectionStart"][0] = (D, E, T) => {
                  O(N, "selection-start", D.selectionStart);
                  E(N);
                };
                if (C || K || U.selectionEnd)
                  O(N, "selection-end", D.selectionEnd);
                A["selectionEnd"][0] = (D, E, T) => {
                  O(N, "selection-end", D.selectionEnd);
                  E(N);
                };
                if (C || K || U.holdKeyboard)
                  O(N, "hold-keyboard", D.holdKeyboard);
                A["holdKeyboard"][0] = (D, E, T) => {
                  O(N, "hold-keyboard", D.holdKeyboard);
                  E(N);
                };
                if (C || K || U.safePasswordCertPath)
                  O(N, "safe-password-cert-path", D.safePasswordCertPath);
                A["safePasswordCertPath"][0] = (D, E, T) => {
                  O(N, "safe-password-cert-path", D.safePasswordCertPath);
                  E(N);
                };
                if (C || K || U.safePasswordLength)
                  O(N, "safe-password-length", D.safePasswordLength);
                A["safePasswordLength"][0] = (D, E, T) => {
                  O(N, "safe-password-length", D.safePasswordLength);
                  E(N);
                };
                if (C || K || U.safePasswordTimeStamp)
                  O(N, "safe-password-time-stamp", D.safePasswordTimeStamp);
                A["safePasswordTimeStamp"][0] = (D, E, T) => {
                  O(N, "safe-password-time-stamp", D.safePasswordTimeStamp);
                  E(N);
                };
                if (C || K || U.safePasswordNonce)
                  O(N, "safe-password-nonce", D.safePasswordNonce);
                A["safePasswordNonce"][0] = (D, E, T) => {
                  O(N, "safe-password-nonce", D.safePasswordNonce);
                  E(N);
                };
                if (C || K || U.safePasswordSalt)
                  O(N, "safe-password-salt", D.safePasswordSalt);
                A["safePasswordSalt"][0] = (D, E, T) => {
                  O(N, "safe-password-salt", D.safePasswordSalt);
                  E(N);
                };
                if (C || K || U.safePasswordCustomHash)
                  O(N, "safe-password-custom-hash", D.safePasswordCustomHash);
                A["safePasswordCustomHash"][0] = (D, E, T) => {
                  O(N, "safe-password-custom-hash", D.safePasswordCustomHash);
                  E(N);
                };
                if (C) O(N, "aria-role", "textbox");
                if (C || K || U.label) O(N, "aria-label", D.label);
                if (C || K || U.label) O(N, "aria-roledescription", D.label);
                if (C) O(N, "bindinput", "onInput");
                if (C) O(N, "bindfocus", "onFocus");
                if (C) O(N, "bindblur", "onBlur");
                if (C) O(N, "bindconfirm", "onConfirm");
                if (C)
                  O(N, "bindkeyboardheightchange", "onKeyboardHeightChange");
                if (C) O(N, "bindnicknamereview", "onNickNameReview");
              },
              p
            );
            q =
              D._clearIcon && X(D.value).length > 0 && D.showClearIcon ? 1 : 0;
            B(q, r);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__wrap--suffix " +
                      Y(D.prefix) +
                      "-class-suffix"
                  );
                if (C) R.v(N, "tap", "onSuffixClick", !1, !1, !1, !1);
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrap--suffix-icon");
                if (C) R.v(N, "tap", "onSuffixIconClick", !1, !1, !1, !1);
              },
              z
            );
          },
          E0,
          G0 = (C, T) => {
            C || K || U.tips ? T(Y(D.tips)) : T();
          },
          F0 = (C, T, E) => {
            if (E0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(U.classPrefix || U.classPrefix || U.align || U.prefix) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__tips " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.align) +
                        " " +
                        Y(D.prefix) +
                        "-class-tips"
                    );
                },
                G0
              );
            }
          },
          n = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.status) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.status)
                  );
                A["status"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.status)
                  );
                };
              },
              o
            );
            E0 = D.tips && X(D.tips).length > 0 ? 1 : 0;
            B(E0, F0);
            S("tips");
          },
          d = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrap--prefix");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrap");
              },
              n
            );
            S("extra");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!U.borderless || undefined])]) ||
                    U.classPrefix ||
                    U.layout ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix, [["border", !D.borderless]])) +
                      " " +
                      Y(D.classPrefix) +
                      "--layout-" +
                      Y(D.layout) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["borderless"][0] = A["layout"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix, [["border", !D.borderless]])) +
                      " " +
                      Y(D.classPrefix) +
                      "--layout-" +
                      Y(D.layout) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-describedby", true);
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/input/input.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-input{-webkit-align-items:var(--td-input-align-items,center);align-items:var(--td-input-align-items,center);background-color:var(--td-input-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;overflow:hidden;padding:var(--td-input-vertical-padding,",
        [0, 32],
        ")}\n.",
        [1],
        "t-input--border{position:relative}\n.",
        [1],
        "t-input--border::after{background-color:var(--td-input-border-color,var(--td-component-stroke,var(--td-gray-color-3,#e7e7e7)));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
        [1],
        "t-input--border:after{left:var(--td-input-border-left-space,",
        [0, 32],
        ");right:var(--td-input-border-right-space,0)}\n.",
        [1],
        "t-input--layout-vertical{-webkit-align-items:start;align-items:start;-webkit-flex-direction:column;flex-direction:column}\n.",
        [1],
        "t-input__wrap--prefix{display:-webkit-flex;display:flex}\n.",
        [1],
        "t-input__icon--prefix{color:var(--td-input-prefix-icon-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:",
        [0, 48],
        "}\n.",
        [1],
        "t-input__label:not(:empty){word-wrap:break-word;color:var(--td-input-label-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:var(--td-font-size-m,",
        [0, 32],
        ");line-height:",
        [0, 48],
        ";margin-right:var(--td-spacer-2,",
        [0, 32],
        ");max-width:var(--td-input-label-max-width,5em);min-width:var(--td-input-label-min-width,2em)}\n.",
        [1],
        "t-input--layout-vertical .",
        [1],
        "t-input__label:not(:empty){font-size:var(--td-font-size-base,",
        [0, 28],
        ");padding-bottom:",
        [0, 8],
        "}\n.",
        [1],
        "t-input__icon--prefix:not(:empty)+.",
        [1],
        "t-input__label:not(:empty){padding-left:",
        [0, 8],
        "}\n.",
        [1],
        "t-input__label:not(:empty)+.",
        [1],
        "t-input__wrap{margin-left:var(--td-spacer-2,",
        [0, 32],
        ")}\n.",
        [1],
        "t-input__icon--prefix:not(:empty)+.",
        [1],
        "t-input__label:empty{margin-right:var(--td-spacer-2,",
        [0, 32],
        ")}\n.",
        [1],
        "t-input__wrap{display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-flex-shrink:1;flex-shrink:1;-webkit-flex:1;flex:1;-webkit-flex-wrap:wrap;flex-wrap:wrap;-webkit-justify-content:center;justify-content:center;width:100%}\n.",
        [1],
        "t-input__wrap .",
        [1],
        "t-input__content{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;font-size:var(--td-font-size-m,",
        [0, 32],
        ");line-height:",
        [0, 48],
        ";width:100%}\n.",
        [1],
        "t-input__wrap--clearable-icon,.",
        [1],
        "t-input__wrap--suffix,.",
        [1],
        "t-input__wrap--suffix-icon{-webkit-flex:0 0 auto;flex:0 0 auto;padding-left:var(--td-spacer-1,",
        [0, 24],
        ")}\n.",
        [1],
        "t-input__wrap--clearable-icon:empty,.",
        [1],
        "t-input__wrap--suffix-icon:empty,.",
        [1],
        "t-input__wrap--suffix:empty{display:none}\n.",
        [1],
        "t-input__wrap--clearable-icon,.",
        [1],
        "t-input__wrap--suffix-icon{color:var(--td-input-suffix-icon-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:",
        [0, 48],
        "}\n.",
        [1],
        "t-input__wrap--suffix{color:var(--td-input-suffix-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:var(--td-font-size-m,",
        [0, 32],
        ")}\n.",
        [1],
        "t-input__icon--prefix:empty,.",
        [1],
        "t-input__tips:empty,.",
        [1],
        "t-input__wrap--clearable-icon:empty,.",
        [1],
        "t-input__wrap--suffix-icon:empty,.",
        [1],
        "t-input__wrap--suffix:empty{display:none}\n.",
        [1],
        "t-input__control{background-color:initial;border:0;box-sizing:border-box;color:var(--td-input-default-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:block;font-size:inherit;line-height:inherit;margin:0;min-height:",
        [0, 48],
        ";min-width:0;padding:0;resize:none;width:100%}\n.",
        [1],
        "t-input__control--disabled{-webkit-text-fill-color:currentColor;color:var(--td-input-disabled-text-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))));cursor:not-allowed;opacity:1}\n.",
        [1],
        "t-input__control--read-only{cursor:default}\n.",
        [1],
        "t-input--left{text-align:left}\n.",
        [1],
        "t-input--right{text-align:right}\n.",
        [1],
        "t-input--center{text-align:center}\n.",
        [1],
        "t-input__placeholder{color:var(--td-input-placeholder-text-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-input-placeholder-text-font-size,var(--td-font-size-m,",
        [0, 32],
        "))}\n.",
        [1],
        "t-input__placeholder--disabled{color:var(--td-input-disabled-text-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-input__tips{font-size:var(--td-font-size-s,",
        [0, 24],
        ");line-height:",
        [0, 40],
        ";padding-top:",
        [0, 8],
        "}\n.",
        [1],
        "t-input--default+.",
        [1],
        "t-input__tips{color:var(--td-input-default-tips-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))))}\n.",
        [1],
        "t-input--success+.",
        [1],
        "t-input__tips{color:var(--td-input-success-tips-color,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
        [1],
        "t-input--warning+.",
        [1],
        "t-input__tips{color:var(--td-input-warning-tips-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
        [1],
        "t-input--error+.",
        [1],
        "t-input__tips{color:var(--td-input-error-tips-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/input/input.wxss" }
    );
}
