page {
  background-color: #eff2f5;
}
.nr_box {
  margin: 10rpx auto;
  width: 95%;
}
.tags_box {
  margin: 0 auto;
  width: 100%;
}
.custom-tabs {
  background-color: #eff2f5 !important;
  justify-content: flex-start !important;
}
.custom-tabs .t-tabs__item {
  color: #1e1e1e !important;
}
.custom-tabs .t-tabs__item--active {
  color: #ff4d4f !important;
}
.custom-track {
  background-color: #ff4d4f !important;
}
.order_card {
  background-color: #fff;
  border: 2rpx solid #f0f0f0;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  padding: 30rpx;
}
.order_header {
  border-bottom: 2rpx solid #f5f5f5;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
}
.order_header,
.user_info {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.user_avatar {
  border-radius: 50%;
  height: 80rpx;
  margin-right: 20rpx;
  width: 80rpx;
}
.user_name {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}
.order_status {
  padding: 10rpx 20rpx;
}
.status_text {
  color: #666;
  font-size: 24rpx;
}
.order_details {
  margin-bottom: 30rpx;
}
.detail_row {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  margin-bottom: 15rpx;
}
.detail_row:last-child {
  margin-bottom: 0;
}
.label {
  color: #666;
  flex-shrink: 0;
  font-size: 28rpx;
  width: 160rpx;
}
.value {
  color: #333;
}
.price,
.value {
  flex: 1;
  font-size: 28rpx;
}
.price {
  color: #ff4d4f;
  font-weight: 500;
}
.order_content {
  margin-bottom: 30rpx;
}
.content_box {
  background-color: #f8f8f8;
  border: 2rpx solid #f0f0f0;
  border-radius: 15rpx;
  padding: 25rpx;
}
.content_text {
  color: #333;
  font-size: 26rpx;
  line-height: 1.5;
}
.order_actions {
  display: -webkit-flex;
  display: flex;
  gap: 20rpx;
  justify-content: space-between;
}
.action_btn {
  border: 5rpx solid;
  border-radius: 20rpx;
  font-size: 28rpx;
  padding: 15rpx 0;
  text-align: center;
  transition: all 0.3s ease;
}
.contact_btn {
  background-color: #fff;
  border: none;
  color: #195abf;
  flex: 1;
}
.contact_btn:active {
  background-color: #f0f7ff;
}
.confirm_btn {
  background-color: #fff;
  border-color: #e03131;
  color: #e03131;
  flex: 0.5;
}
.confirm_btn:active {
  background-color: #fff5f5;
}
.action_btn text {
  font-weight: 500;
}
.fwdd_list_bottom {
  align-items: center;
  border-top: 2rpx solid #f5f5f5;
  display: -webkit-flex;
  display: flex;
  gap: 10rpx;
  justify-content: space-between;
  padding-top: 30rpx;
}
.quxiao-btn {
  border: 5rpx solid #ff4d4f;
  border-radius: 20rpx;
  color: #ff4d4f;
  font-size: 24rpx;
  margin-left: 10rpx;
  padding: 8rpx 32rpx;
}
.contact-btn {
  color: #2b5ee3;
  font-size: 28rpx;
  margin-left: 0;
}
.accept-btn {
  margin-left: 10rpx;
}
.accept-btn,
.confirm-btn {
  background-color: #ff4d4f;
  border: 5rpx solid #ff4d4f;
  border-radius: 20rpx;
  color: #f0f7ff;
  font-size: 24rpx;
  padding: 8rpx 32rpx;
}
.loading_more {
  padding: 20rpx 0;
  text-align: center;
}
.loading_more .loading_text {
  color: #999;
  font-size: 24rpx;
}
.empty_container {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 100rpx 0;
}
.empty_text {
  color: #999;
  font-size: 28rpx;
}
.user_type {
  background-color: #f2f3ff;
  border-radius: 10rpx;
  color: #1971c2;
  display: inline-block;
  font-size: 24rpx;
  margin-left: 20rpx;
  padding: 8rpx 16rpx;
}
