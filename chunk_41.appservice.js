__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/pull-down-refresh/pull-down-refresh":
      (() => {
        var H = {};
        var S;
        var I = (P) => {
          if (!S) S = Object.assign({}, H);
          return S[P];
        };
        var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
        H[""] = (R, C, D, U) => {
          R.setFnFilter(Q.A, Q.B);
          if (typeof R.setEventListenerWrapper === "function")
            R.setEventListenerWrapper(Q.C);
          var L = R.c,
            M = R.m,
            O = R.r,
            A = {
              lowerThreshold: new Array(1),
              showScrollbar: new Array(1),
              loosing: new Array(2),
              upperThreshold: new Array(1),
              tipsHeight: new Array(1),
              scrollIntoView: new Array(1),
              style: new Array(1),
              scrollTop: new Array(1),
              enablePassive: new Array(1),
              distanceTop: new Array(1),
              barHeight: new Array(2),
              customStyle: new Array(1),
              enableBackToTop: new Array(1),
            },
            K = U === true,
            f,
            h = (C) => {},
            i = (C, T) => {
              var $A = D.refreshStatus;
              C || K || !!U.refreshStatus || Z(U.loadingTexts, $A)
                ? T(Y(X(D.loadingTexts)[$A]))
                : T();
            },
            g = (C, T, E) => {
              if (f === 1) {
                E(
                  "t-loading",
                  {},
                  (N, C) => {
                    if (C || K || !!Z(U.loadingProps, "delay") || undefined)
                      O(N, "delay", X(D.loadingProps).delay || 0);
                    if (C || K || !!Z(U.loadingProps, "duration") || undefined)
                      O(N, "duration", X(D.loadingProps).duration || 800);
                    if (C || K || !!Z(U.loadingProps, "indicator") || undefined)
                      O(N, "indicator", X(D.loadingProps).indicator || true);
                    if (C || K || !!Z(U.loadingProps, "layout") || undefined)
                      O(N, "layout", X(D.loadingProps).layout || "horizontal");
                    if (C || K || !!Z(U.loadingProps, "loading") || undefined)
                      O(N, "loading", X(D.loadingProps).loading || true);
                    if (C || K || !!Z(U.loadingProps, "pause") || undefined)
                      O(N, "pause", X(D.loadingProps).pause || false);
                    if (C || K || !!Z(U.loadingProps, "progress") || undefined)
                      O(N, "progress", X(D.loadingProps).progress || 0);
                    if (C || K || !!Z(U.loadingProps, "reverse") || undefined)
                      O(N, "reverse", X(D.loadingProps).reverse || false);
                    if (C || K || !!Z(U.loadingProps, "size") || undefined)
                      O(N, "size", X(D.loadingProps).size || "50rpx");
                    var $A = D.refreshStatus;
                    if (
                      C ||
                      K ||
                      !!(
                        Z(U.loadingProps, "text") ||
                        U.refreshStatus ||
                        Z(U.loadingTexts, $A)
                      ) ||
                      undefined
                    )
                      O(
                        N,
                        "text",
                        X(D.loadingProps).text || X(D.loadingTexts)[$A]
                      );
                    if (C || K || !!Z(U.loadingProps, "theme") || undefined)
                      O(N, "theme", X(D.loadingProps).theme || "circular");
                    if (C || K || !!U.prefix || undefined)
                      O(
                        N,
                        "t-class-indicator",
                        Y(D.prefix) + "-class-indicator"
                      );
                  },
                  h
                );
              } else if (f === 2) {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__text " +
                          Y(D.prefix) +
                          "-class-text"
                      );
                  },
                  i
                );
              }
            },
            e = (C, T, E, B) => {
              f = D.refreshStatus === 2 ? 1 : D.refreshStatus >= 0 ? 2 : 0;
              B(f, g);
            },
            d = (C, T, E, B, F, S) => {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.loosing;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.loosing ||
                      ($A ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__tips " +
                        Y(D.classPrefix + "__tips--" + ($A ? "loosing" : ""))
                    );
                  A["loosing"][1] = (D, E, T) => {
                    var $B = D.loosing;
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__tips " +
                        Y(D.classPrefix + "__tips--" + ($B ? "loosing" : ""))
                    );
                  };
                  if (C || K || !!U.tipsHeight || undefined)
                    R.y(N, "height:" + Y(D.tipsHeight) + "px");
                  A["tipsHeight"][0] = (D, E, T) => {
                    R.y(N, "height:" + Y(D.tipsHeight) + "px");
                  };
                  if (C) O(N, "aria-live", "polite");
                },
                e
              );
              S("");
            },
            c = (C, T, E, B, F, S) => {
              S("header");
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.loosing;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.loosing ||
                      ($A ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__track " +
                        Y(D.classPrefix + "__track--" + ($A ? "loosing" : ""))
                    );
                  A["loosing"][0] = (D, E, T) => {
                    var $B = D.loosing;
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__track " +
                        Y(D.classPrefix + "__track--" + ($B ? "loosing" : ""))
                    );
                  };
                  var $B = D.barHeight > 0;
                  if (
                    C ||
                    K ||
                    !!U.barHeight ||
                    ($B ? !!U.barHeight || undefined : undefined)
                  )
                    R.y(
                      N,
                      $B
                        ? "transform: translate3d(0, " + D.barHeight + "px, 0);"
                        : ""
                    );
                  A["barHeight"][0] = A["barHeight"][1] = (D, E, T) => {
                    var $C = D.barHeight > 0;
                    R.y(
                      N,
                      $C
                        ? "transform: translate3d(0, " + D.barHeight + "px, 0);"
                        : ""
                    );
                  };
                },
                d
              );
            },
            b = (C, T, E) => {
              E(
                "scroll-view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        U.style,
                        U.customStyle,
                        !!U.distanceTop || undefined,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        D.style,
                        D.customStyle,
                        "max-height: calc(100vh - " + D.distanceTop + "px)",
                      ])
                    );
                  A["style"][0] =
                    A["customStyle"][0] =
                    A["distanceTop"][0] =
                      (D, E, T) => {
                        R.y(
                          N,
                          P(X(a)._style)([
                            D.style,
                            D.customStyle,
                            "max-height: calc(100vh - " + D.distanceTop + "px)",
                          ])
                        );
                      };
                  if (C) O(N, "type", "list");
                  if (C || K || U.scrollTop) O(N, "scroll-top", D.scrollTop);
                  A["scrollTop"][0] = (D, E, T) => {
                    O(N, "scroll-top", D.scrollTop);
                    E(N);
                  };
                  if (C) O(N, "scroll-y", true);
                  if (C || K || U.enableBackToTop)
                    O(N, "enable-back-to-top", D.enableBackToTop);
                  A["enableBackToTop"][0] = (D, E, T) => {
                    O(N, "enable-back-to-top", D.enableBackToTop);
                    E(N);
                  };
                  if (C || K || U.enablePassive)
                    O(N, "enable-passive", D.enablePassive);
                  A["enablePassive"][0] = (D, E, T) => {
                    O(N, "enable-passive", D.enablePassive);
                    E(N);
                  };
                  if (C || K || U.lowerThreshold)
                    O(N, "lower-threshold", D.lowerThreshold);
                  A["lowerThreshold"][0] = (D, E, T) => {
                    O(N, "lower-threshold", D.lowerThreshold);
                    E(N);
                  };
                  if (C || K || U.upperThreshold)
                    O(N, "upper-threshold", D.upperThreshold);
                  A["upperThreshold"][0] = (D, E, T) => {
                    O(N, "upper-threshold", D.upperThreshold);
                    E(N);
                  };
                  if (C || K || U.scrollIntoView)
                    O(N, "scroll-into-view", D.scrollIntoView);
                  A["scrollIntoView"][0] = (D, E, T) => {
                    O(N, "scroll-into-view", D.scrollIntoView);
                    E(N);
                  };
                  if (C || K || U.showScrollbar)
                    O(N, "show-scrollbar", D.showScrollbar);
                  A["showScrollbar"][0] = (D, E, T) => {
                    O(N, "show-scrollbar", D.showScrollbar);
                    E(N);
                  };
                  if (C) O(N, "enhanced", true);
                  if (C) O(N, "scroll-with-animation", true);
                  if (C || K || undefined) O(N, "bounces", false);
                  if (C) O(N, "binddragstart", "onDragStart");
                  if (C) O(N, "binddragging", "onDragging");
                  if (C) O(N, "binddragend", "onDragEnd");
                  if (C) O(N, "bindscrolltoupper", "onScrollToTop");
                  if (C) O(N, "bindscrolltolower", "onScrollToBottom");
                  if (C || K || undefined) O(N, "throttle", false);
                  if (C) R.v(N, "touchstart", "onTouchStart", !1, !1, !1, !1);
                  if (C) R.v(N, "touchmove", "onTouchMove", !1, !1, !1, !1);
                  if (C) R.v(N, "touchend", "onTouchEnd", !1, !1, !1, !1);
                  if (C) R.v(N, "scroll", "onScroll", !1, !1, !1, !1);
                },
                c
              );
            };
          return { C: b, B: A };
        };
        return Object.assign(
          function (R) {
            return H[R];
          },
          { _: H }
        );
      })(),
  };
});
__wxRoute =
  "miniprogram_npm/tdesign-miniprogram/pull-down-refresh/pull-down-refresh";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/pull-down-refresh/pull-down-refresh.js";
define(
  "miniprogram_npm/tdesign-miniprogram/pull-down-refresh/pull-down-refresh.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var t = require("../../../@babel/runtime/helpers/createClass"),
      e = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      s = require("../common/src/index"),
      o = c(require("../common/config")),
      n = c(require("./props")),
      l = require("../common/utils"),
      h = require("../common/version");
    function c(t) {
      return t && t.__esModule ? t : { default: t };
    }
    var u = o.default.prefix,
      g = "".concat(u, "-pull-down-refresh"),
      f = ["下拉刷新", "松手刷新", "正在刷新", "刷新完成"],
      p = (function (a) {
        i(o, a);
        var s = r(o);
        function o() {
          var t;
          return (
            e(this, o),
            ((t = s.apply(this, arguments)).pixelRatio = 1),
            (t.startPoint = null),
            (t.isPulling = !1),
            (t.maxRefreshAnimateTimeFlag = 0),
            (t.closingAnimateTimeFlag = 0),
            (t.refreshStatusTimer = null),
            (t.externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-loading"),
              "".concat(u, "-class-text"),
              "".concat(u, "-class-indicator"),
            ]),
            (t.behaviors = (0, h.canUseProxyScrollView)()
              ? ["wx://proxy-scroll-view"]
              : []),
            (t.options = { multipleSlots: !0, pureDataPattern: /^_/ }),
            (t.relations = { "../back-top/back-top": { type: "descendant" } }),
            (t.properties = n.default),
            (t.data = {
              prefix: u,
              classPrefix: g,
              distanceTop: 0,
              barHeight: 0,
              tipsHeight: 0,
              refreshStatus: -1,
              loosing: !1,
              enableToRefresh: !0,
              scrollTop: 0,
              _maxBarHeight: 0,
              _loadingBarHeight: 0,
            }),
            (t.lifetimes = {
              attached: function () {
                var t = this,
                  e = l.systemInfo.screenWidth,
                  i = this.properties,
                  r = i.loadingTexts,
                  a = i.maxBarHeight,
                  s = i.loadingBarHeight,
                  o = Array.isArray(r) && r.length >= 4;
                this.setData({
                  _maxBarHeight: (0, l.unitConvert)(a),
                  _loadingBarHeight: (0, l.unitConvert)(s),
                  loadingTexts: o ? r : f,
                }),
                  (this.pixelRatio = 750 / e),
                  (0, l.getRect)(this, ".".concat(g)).then(function (e) {
                    t.setData({ distanceTop: e.top });
                  });
              },
              detached: function () {
                clearTimeout(this.maxRefreshAnimateTimeFlag),
                  clearTimeout(this.closingAnimateTimeFlag),
                  this.resetTimer();
              },
            }),
            (t.observers = {
              value: function (t) {
                t
                  ? this.doRefresh()
                  : (clearTimeout(this.maxRefreshAnimateTimeFlag),
                    this.data.refreshStatus > 0 &&
                      this.setData({ refreshStatus: 3 }),
                    this.setData({ barHeight: 0 }));
              },
              barHeight: function (t) {
                var e = this;
                this.resetTimer(),
                  0 === t &&
                    -1 !== this.data.refreshStatus &&
                    (this.refreshStatusTimer = setTimeout(function () {
                      e.setData({ refreshStatus: -1 });
                    }, 240)),
                  this.setData({
                    tipsHeight: Math.min(t, this.data._loadingBarHeight),
                  });
              },
              maxBarHeight: function (t) {
                this.setData({ _maxBarHeight: (0, l.unitConvert)(t) });
              },
              loadingBarHeight: function (t) {
                this.setData({ _loadingBarHeight: (0, l.unitConvert)(t) });
              },
            }),
            (t.methods = {
              resetTimer: function () {
                this.refreshStatusTimer &&
                  (clearTimeout(this.refreshStatusTimer),
                  (this.refreshStatusTimer = null));
              },
              onScrollToBottom: function () {
                this.triggerEvent("scrolltolower");
              },
              onScrollToTop: function () {
                this.setData({ enableToRefresh: !0 });
              },
              onScroll: function (t) {
                var e = t.detail.scrollTop;
                this.setData({ enableToRefresh: 0 === e }),
                  this.triggerEvent("scroll", { scrollTop: e });
              },
              onTouchStart: function (t) {
                if (
                  !this.isPulling &&
                  this.data.enableToRefresh &&
                  !this.properties.disabled
                ) {
                  var e = t.touches;
                  if (1 === e.length) {
                    var i = e[0],
                      r = i.pageX,
                      a = i.pageY;
                    this.setData({ loosing: !1 }),
                      (this.startPoint = { pageX: r, pageY: a }),
                      (this.isPulling = !0);
                  }
                }
              },
              onTouchMove: function (t) {
                if (this.startPoint && !this.properties.disabled) {
                  var e = t.touches;
                  if (1 === e.length) {
                    var i = e[0].pageY - this.startPoint.pageY;
                    i > 0 && this.setRefreshBarHeight(i);
                  }
                }
              },
              onTouchEnd: function (t) {
                if (this.startPoint && !this.properties.disabled) {
                  var e = t.changedTouches;
                  if (1 === e.length) {
                    var i = e[0].pageY - this.startPoint.pageY;
                    (this.startPoint = null),
                      (this.isPulling = !1),
                      this.setData({ loosing: !0 }),
                      i > this.data._loadingBarHeight
                        ? (this._trigger("change", { value: !0 }),
                          this.triggerEvent("refresh"))
                        : this.setData({ barHeight: 0 });
                  }
                }
              },
              onDragStart: function (t) {
                var e = t.detail,
                  i = e.scrollTop,
                  r = e.scrollLeft;
                this.triggerEvent("dragstart", { scrollTop: i, scrollLeft: r });
              },
              onDragging: function (t) {
                var e = t.detail,
                  i = e.scrollTop,
                  r = e.scrollLeft;
                this.triggerEvent("dragging", { scrollTop: i, scrollLeft: r });
              },
              onDragEnd: function (t) {
                var e = t.detail,
                  i = e.scrollTop,
                  r = e.scrollLeft;
                this.triggerEvent("dragend", { scrollTop: i, scrollLeft: r });
              },
              doRefresh: function () {
                var t = this;
                this.properties.disabled ||
                  (this.setData({
                    barHeight: this.data._loadingBarHeight,
                    refreshStatus: 2,
                    loosing: !0,
                  }),
                  (this.maxRefreshAnimateTimeFlag = setTimeout(function () {
                    (t.maxRefreshAnimateTimeFlag = null),
                      2 === t.data.refreshStatus &&
                        (t.triggerEvent("timeout"),
                        t._trigger("change", { value: !1 }));
                  }, this.properties.refreshTimeout)));
              },
              setRefreshBarHeight: function (t) {
                var e = this,
                  i = Math.min(t, this.data._maxBarHeight),
                  r = { barHeight: i };
                return (
                  i >= this.data._loadingBarHeight
                    ? (r.refreshStatus = 1)
                    : (r.refreshStatus = 0),
                  new Promise(function (t) {
                    e.setData(r, function () {
                      return t(i);
                    });
                  })
                );
              },
              setScrollTop: function (t) {
                this.setData({ scrollTop: t });
              },
              scrollToTop: function () {
                this.setScrollTop(0);
              },
            }),
            t
          );
        }
        return t(o);
      })(s.SuperComponent),
      d = (p = (0, a.__decorate)([(0, s.wxComponent)()], p));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/pull-down-refresh/pull-down-refresh.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/pull-down-refresh/pull-down-refresh.js");
