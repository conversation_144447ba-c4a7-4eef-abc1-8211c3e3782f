__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/tab-bar-item/tab-bar-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            theme: new Array(1),
            shape: new Array(1),
            ariaLabel: new Array(1),
            isChecked: new Array(2),
            crowded: new Array(1),
            style: new Array(1),
            split: new Array(1),
            customStyle: new Array(1),
          },
          K = U === true,
          f,
          i,
          j = (C, T, E, B, F, S, J) => {
            var $A = I(i);
            var $B = D.iconOnly;
            if (i && $A)
              $A(
                R,
                C,
                Object.assign({ size: $B ? 24 : 20 }, X(D._icon), {}),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { size: !!U.iconOnly || ($B ? undefined : undefined) },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          h = (C, T, E, B, F, S, J) => {
            i = "icon";
            B(i, j);
          },
          k,
          l = (C, T, E, B, F, S, J) => {
            var $A = I(k);
            var $B = D.iconOnly;
            if (k && $A)
              $A(
                R,
                C,
                Object.assign(
                  { ariaHidden: !D.iconOnly, size: $B ? 24 : 20 },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        {
                          ariaHidden: !!U.iconOnly || undefined,
                          size: !!U.iconOnly || ($B ? undefined : undefined),
                        },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          g = (C, T, E, B, F, S, J) => {
            if (f === 1) {
              E(
                "t-badge",
                {},
                (N, C) => {
                  if (C || K || !!Z(U.badgeProps, "count") || undefined)
                    O(N, "count", X(D.badgeProps).count || 0);
                  if (C || K || !!Z(U.badgeProps, "maxCount") || undefined)
                    O(N, "max-count", X(D.badgeProps).maxCount || 99);
                  if (C || K || !!Z(U.badgeProps, "dot") || undefined)
                    O(N, "dot", X(D.badgeProps).dot || false);
                  if (C || K || !!Z(U.badgeProps, "content") || undefined)
                    O(N, "content", X(D.badgeProps).content || "");
                  if (C || K || !!Z(U.badgeProps, "size") || undefined)
                    O(N, "size", X(D.badgeProps).size || "medium");
                  if (C || K || Z(U.badgeProps, "visible"))
                    O(N, "visible", X(D.badgeProps).visible);
                  if (
                    C ||
                    K ||
                    !!(Z(U.badgeProps, "offset") || Q.a([])) ||
                    undefined
                  )
                    O(N, "offset", X(D.badgeProps).offset || [0, 0]);
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class-count", D.prefix + "-badge-class");
                },
                h
              );
            } else if (f === 2) {
              k = "icon";
              B(k, l);
            }
          },
          e = (C, T, E, B, F, S) => {
            f =
              X(D.badgeProps).dot || X(D.badgeProps).count
                ? 1
                : !!D.icon
                ? 2
                : 0;
            B(f, g);
            S("icon");
          },
          n,
          p = (C) => {},
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "name", "view-list");
                  if (C) O(N, "size", "32rpx");
                  if (C || K || !!U.classPrefix || undefined)
                    O(N, "t-class", Y(D.classPrefix) + "__icon-menu");
                },
                p
              );
            }
          },
          m = (C, T, E, B, F, S) => {
            n = D.hasChildren ? 1 : 0;
            B(n, o);
            S("");
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon");
                var $A = D.iconOnly;
                if (
                  C ||
                  K ||
                  !!(U.iconOnly || ($A ? undefined : undefined)) ||
                  undefined
                )
                  R.y(N, "height:" + Y($A ? 24 : 20) + "px");
                if (
                  C ||
                  K ||
                  !!(Z(U.badgeProps, "dot") || Z(U.badgeProps, "count")) ||
                  undefined
                )
                  O(
                    N,
                    "aria-hidden",
                    X(D.badgeProps).dot || X(D.badgeProps).count
                  );
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!U.icon || undefined])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__text", [["small", !!D.icon]])
                  );
              },
              m
            );
          },
          q,
          t = (C, u, v, w, x, y, T, E) => {
            var A0,
              C0 = (C) => {},
              B0 = (C, T, E) => {
                if (A0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__spread-item-split");
                    },
                    C0
                  );
                }
              },
              D0 = (C, T) => {
                C || K || Z(w, "label") ? T(Y(X(u).label)) : T();
              },
              z = (C, T, E, B) => {
                A0 = v !== 0 ? 1 : 0;
                B(A0, B0);
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!U.classPrefix || undefined)
                      L(N, Y(D.classPrefix) + "__spread-item-text");
                    if (C || K || !!(Z(w, "value") || x) || undefined)
                      R.d(N, "value", X(u).value || v);
                  },
                  D0
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__spread-item");
                if (C || K || !!U.classPrefix || undefined)
                  O(
                    N,
                    "hover-class",
                    Y(D.classPrefix) + "__spread-item--active"
                  );
                if (C || K || undefined) O(N, "hover-stay-time", 200);
                if (C) O(N, "aria-role", "tab");
                if (C || K || !!(Z(w, "value") || x) || undefined)
                  R.d(N, "value", X(u).value || v);
                if (C) R.v(N, "tap", "selectChild", !1, !1, !1, !1);
              },
              z
            );
          },
          s = (C, T, E, B, F) => {
            F(
              D.subTabBar,
              "value",
              U ? U.subTabBar : undefined,
              [0, "subTabBar"],
              t
            );
          },
          r = (C, T, E) => {
            if (q === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__spread");
                },
                s
              );
            }
          },
          c = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.isChecked]), U.theme])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__content", [
                      ["checked", D.isChecked],
                      D.theme,
                    ])
                  );
                A["isChecked"][1] = A["theme"][0] = (D, E, T) => {
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__content", [
                      ["checked", D.isChecked],
                      D.theme,
                    ])
                  );
                };
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "hover-class", Y(D.classPrefix) + "__content--active");
                if (C || K || undefined) O(N, "hover-stay-time", 200);
                if (C) O(N, "bindtap", "toggle");
                var $A = (!D.hasChildren || !D.isSpread) && D.isChecked;
                if (
                  C ||
                  K ||
                  !!(U.hasChildren || U.isSpread || U.isChecked) ||
                  ($A ? undefined : undefined)
                )
                  O(N, "aria-selected", $A ? true : false);
                A["isChecked"][0] = (D, E, T) => {
                  var $B = (!D.hasChildren || !D.isSpread) && D.isChecked;
                  O(N, "aria-selected", $B ? true : false);
                  E(N);
                };
                var $B = D.hasChildren && D.isSpread;
                if (
                  C ||
                  K ||
                  !!(U.hasChildren || U.isSpread) ||
                  ($B ? undefined : undefined)
                )
                  O(N, "aria-expanded", $B ? true : "");
                var $C = D.hasChildren;
                if (C || K || !!U.hasChildren || ($C ? undefined : undefined))
                  O(N, "aria-role", $C ? "button" : "tab");
                var $D = X(D.badgeProps).dot || X(D.badgeProps).count;
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    Z(U.badgeProps, "dot") ||
                    Z(U.badgeProps, "count") ||
                    ($D
                      ? !!(
                          Z(undefined, "getBadgeAriaLabel") ||
                          U.badgeProps === true ||
                          Q.b(Object.assign({}, X(U.badgeProps), {}))
                        ) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($D
                        ? P(X(a).getBadgeAriaLabel)(
                            Object.assign({}, X(D.badgeProps), {})
                          )
                        : "")
                  );
                A["ariaLabel"][0] = (D, E, T) => {
                  var $E = X(D.badgeProps).dot || X(D.badgeProps).count;
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($E
                        ? P(X(a).getBadgeAriaLabel)(
                            Object.assign({}, X(D.badgeProps), {})
                          )
                        : "")
                  );
                  E(N);
                };
              },
              d
            );
            q = D.hasChildren && D.isSpread ? 1 : 0;
            B(q, r);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([U.split]),
                      Q.a([!!U.icon || undefined]),
                      Q.a([U.crowded]),
                      U.shape,
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["split", D.split],
                        ["text-only", !D.icon],
                        ["crowded", D.crowded],
                        D.shape,
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["split"][0] =
                  A["crowded"][0] =
                  A["shape"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(
                          P(X(a).cls)(D.classPrefix, [
                            ["split", D.split],
                            ["text-only", !D.icon],
                            ["crowded", D.crowded],
                            D.shape,
                          ])
                        ) +
                          " class " +
                          Y(D.prefix) +
                          "-class"
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/tab-bar-item/tab-bar-item";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/tab-bar-item/tab-bar-item.js";
define(
  "miniprogram_npm/tdesign-miniprogram/tab-bar-item/tab-bar-item.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
      t = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      n = require("../common/src/index"),
      u = l(require("../common/config")),
      o = l(require("./props")),
      c = require("../common/utils");
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var h = u.default.prefix,
      p = "".concat(h, "-tab-bar-item"),
      d = (function (n) {
        a(l, n);
        var u = i(l);
        function l() {
          var t;
          return (
            r(this, l),
            ((t = u.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
            ]),
            (t.parent = null),
            (t.relations = {
              "../tab-bar/tab-bar": {
                type: "ancestor",
                linked: function (e) {
                  var t = e.data,
                    r = t.theme,
                    a = t.split,
                    i = t.shape;
                  this.setData({
                    theme: r,
                    split: a,
                    shape: i,
                    currentName: this.properties.value
                      ? this.properties.value
                      : e.initName(),
                  }),
                    e.updateChildren();
                },
              },
            }),
            (t.options = { multipleSlots: !0 }),
            (t.data = {
              prefix: h,
              classPrefix: p,
              isSpread: !1,
              isChecked: !1,
              hasChildren: !1,
              currentName: "",
              split: !0,
              iconOnly: !1,
              theme: "",
              crowded: !1,
              shape: "normal",
            }),
            (t.properties = o.default),
            (t.observers = {
              subTabBar: function (e) {
                this.setData({ hasChildren: e.length > 0 });
              },
              icon: function (e) {
                this.setData({ _icon: (0, c.calcIcon)(e) });
              },
            }),
            (t.lifetimes = {
              attached: function () {
                return (0, s.__awaiter)(
                  this,
                  void 0,
                  void 0,
                  e().mark(function t() {
                    var r;
                    return e().wrap(
                      function (e) {
                        for (;;)
                          switch ((e.prev = e.next)) {
                            case 0:
                              return (
                                (e.next = 2),
                                (0, c.getRect)(this, ".".concat(p, "__text"))
                              );
                            case 2:
                              (r = e.sent),
                                this.setData({ iconOnly: 0 === r.height });
                            case 4:
                            case "end":
                              return e.stop();
                          }
                      },
                      t,
                      this
                    );
                  })
                );
              },
            }),
            (t.methods = {
              showSpread: function () {
                this.setData({ isSpread: !0 });
              },
              toggle: function () {
                var e = this.data,
                  t = e.currentName,
                  r = e.hasChildren,
                  a = e.isSpread;
                r && this.setData({ isSpread: !a }),
                  this.$parent.updateValue(t),
                  this.$parent.changeOtherSpread(t);
              },
              selectChild: function (e) {
                var t = e.target.dataset.value;
                this.$parent.updateValue(t), this.setData({ isSpread: !1 });
              },
              checkActive: function (e) {
                var t = this.data,
                  r = t.currentName,
                  a = t.subTabBar,
                  i =
                    (null == a
                      ? void 0
                      : a.some(function (t) {
                          return t.value === e;
                        })) || r === e;
                this.setData({ isChecked: i });
              },
              closeSpread: function () {
                this.setData({ isSpread: !1 });
              },
            }),
            t
          );
        }
        return t(l);
      })(n.SuperComponent),
      m = (d = (0, s.__decorate)([(0, n.wxComponent)()], d));
    exports.default = m;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/tab-bar-item/tab-bar-item.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/tab-bar-item/tab-bar-item.js");
