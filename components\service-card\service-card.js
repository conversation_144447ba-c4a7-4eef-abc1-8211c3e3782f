Component({
  properties: {
    service: {
      type: Object,
      value: {
        id: 0,
        name: "",
        description: "",
        thumbnail: "",
        price: 0,
        order_count: 0,
        positive_rate: 0,
        category: { id: 0, name: "" },
        publisher: {
          id: 0,
          nickname: "",
          avatar: "",
          auth_status: { value: "", label: "" },
          neighbor_type: { value: "", label: "" },
        },
        createdAt: "",
      },
    },
  },
  methods: {
    onServiceClick: function () {
      var e = this;
      if (!this.data.clicking) {
        this.setData({ clicking: !0 }),
          setTimeout(function () {
            e.setData({ clicking: !1 });
          }, 1e3);
        var i = this.properties.service.id;
        i
          ? wx.navigateTo({
              url: "/pages/service-info/service-info?id=".concat(i),
              fail: function (e) {
                console.error("跳转失败：", e),
                  wx.showToast({ title: "跳转失败", icon: "none" });
              },
            })
          : wx.showToast({ title: "服务信息异常", icon: "none" });
      }
    },
    onContactCustomer: function () {
      var e = this.properties.service.publisher.phone;
      e
        ? wx.makePhoneCall({
            phoneNumber: e,
            fail: function (e) {
              wx.showToast({ title: "拨号失败", icon: "error" });
            },
          })
        : wx.showToast({ title: "暂无联系方式", icon: "none" });
    },
  },
});
