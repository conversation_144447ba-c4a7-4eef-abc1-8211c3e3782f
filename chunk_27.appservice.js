__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/footer/footer": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            customStyle: new Array(1),
            prefix: new Array(1),
          },
          K = U === true,
          d,
          g,
          i = (C) => {},
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    O(N, "t-class", Y(D.classPrefix) + "__icon");
                  if (C || K || Z(U.logo, "icon")) O(N, "src", X(D.logo).icon);
                },
                i
              );
            }
          },
          j,
          l = (C, T) => {
            C || K || Z(U.logo, "title") ? T(Y(X(D.logo).title)) : T();
          },
          m = (C) => {},
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__title");
                },
                l
              );
            } else if (j === 2) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    O(N, "t-class", Y(D.classPrefix) + "__title-url");
                  if (C || K || Z(U.logo, "url")) O(N, "src", X(D.logo).url);
                  if (C) O(N, "mode", "widthFix");
                },
                m
              );
            }
          },
          f = (C, T, E, B) => {
            g = X(D.logo).icon ? 1 : 0;
            B(g, h);
            j = X(D.logo).title ? 1 : X(D.logo).url ? 2 : 0;
            B(j, k);
          },
          n,
          q = (C, r, s, t, u, v, T, E, B) => {
            var w = (C, T) => {
                C || K || Z(t, "name") ? T(Y(X(r).name)) : T();
              },
              x,
              z = (C, T) => {
                C ? T("|") : T();
              },
              y = (C, T, E) => {
                if (x === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__link-line");
                      if (C || K || undefined) O(N, "aria-hidden", true);
                    },
                    z
                  );
                }
              };
            E(
              "navigator",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__link-item");
                if (C || K || Z(t, "url")) O(N, "url", X(r).url);
                if (C || K || Z(t, "openType"))
                  O(N, "open-type", X(r).openType);
                if (C) O(N, "hover-class", "none");
              },
              w
            );
            x = s !== X(D.links).length - 1 ? 1 : 0;
            B(x, y);
          },
          p = (C, T, E, B, F) => {
            F(D.links, "name", U ? U.links : undefined, [0, "links"], q);
          },
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__link-list");
                },
                p
              );
            }
          },
          r = (C, T) => {
            C || K || U.text ? T(Y(D.text)) : T();
          },
          e = (C, T, E, B) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__logo");
                },
                f
              );
            } else {
              n = X(D.links).length > 0 ? 1 : 0;
              B(n, o);
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__text");
                },
                r
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.logo ? 1 : 0;
            B(d, e);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["prefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/footer/footer";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/footer/footer.js";
define(
  "miniprogram_npm/tdesign-miniprogram/footer/footer.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      s = require("../common/src/index"),
      u = n(require("../common/config")),
      l = n(require("./props"));
    function n(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = u.default.prefix,
      c = "".concat(o, "-footer"),
      p = (function (i) {
        t(u, i);
        var s = a(u);
        function u() {
          var e;
          return (
            r(this, u),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(o, "-class"),
            ]),
            (e.properties = l.default),
            (e.data = { prefix: o, classPrefix: c }),
            e
          );
        }
        return e(u);
      })(s.SuperComponent),
      f = (p = (0, i.__decorate)([(0, s.wxComponent)()], p));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/footer/footer.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/footer/footer.js");
