Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  r = require("../../../@babel/runtime/helpers/inherits"),
  n = require("../../../@babel/runtime/helpers/createSuper"),
  a = require("tslib"),
  i = require("../common/src/index"),
  s = c(require("../common/config")),
  o = c(require("./props"));
function c(e) {
  return e && e.__esModule ? e : { default: e };
}
var u = s.default.prefix,
  l = "".concat(u, "-swiper"),
  p = (function (a) {
    r(s, a);
    var i = n(s);
    function s() {
      var e;
      return (
        t(this, s),
        ((e = i.apply(this, arguments)).externalClasses = [
          "".concat(u, "-class"),
          "".concat(u, "-class-nav"),
          "".concat(u, "-class-image"),
          "".concat(u, "-class-prev-image"),
          "".concat(u, "-class-next-image"),
        ]),
        (e.options = { multipleSlots: !0 }),
        (e.properties = o.default),
        (e.observers = {
          navCurrent: function (e) {
            this.updateNav(e);
          },
        }),
        (e.$nav = null),
        (e.relations = { "../swiper-nav/swiper-nav": { type: "child" } }),
        (e.data = { prefix: u, classPrefix: l }),
        (e.lifetimes = {
          ready: function () {
            var e = this.properties.current;
            this.setData({ navCurrent: e });
          },
        }),
        (e.methods = {
          updateNav: function (e) {
            var t;
            if (!this.data.navigation) {
              var r =
                null === (t = this.getRelationNodes("./swiper-nav")) ||
                void 0 === t
                  ? void 0
                  : t[0];
              if (r) {
                var n = this.properties,
                  a = n.direction,
                  i = n.paginationPosition,
                  s = n.list;
                r.setData({
                  current: e,
                  total: s.length,
                  direction: a,
                  paginationPosition: i,
                });
              }
            }
          },
          onTap: function (e) {
            var t = e.currentTarget.dataset.index;
            this.triggerEvent("click", { index: t });
          },
          onChange: function (e) {
            var t = e.detail,
              r = t.current,
              n = t.source;
            this.setData({ navCurrent: r }),
              this.triggerEvent("change", { current: r, source: n });
          },
          onNavBtnChange: function (e) {
            var t = e.detail,
              r = t.dir,
              n = t.source;
            this.doNavBtnChange(r, n);
          },
          doNavBtnChange: function (e, t) {
            var r = this.data,
              n = r.current,
              a = r.list,
              i = r.loop,
              s = a.length,
              o = "next" === e ? n + 1 : n - 1;
            (o = i
              ? "next" === e
                ? (n + 1) % s
                : (n - 1 + s) % s
              : o < 0 || o >= s
              ? n
              : o) !== n &&
              (this.setData({ current: o }),
              this.triggerEvent("change", { current: o, source: t }));
          },
          onImageLoad: function (e) {
            this.triggerEvent("image-load", { index: e.target.dataset.custom });
          },
        }),
        e
      );
    }
    return e(s);
  })(i.SuperComponent),
  d = (p = (0, a.__decorate)([(0, i.wxComponent)()], p));
exports.default = d;
