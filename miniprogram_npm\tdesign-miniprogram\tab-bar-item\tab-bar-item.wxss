@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-tab-bar-item {
  background-color: var(
    --td-tab-bar-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  box-sizing: border-box;
  flex: 1;
  height: var(--td-tab-bar-height, 80rpx);
  margin: 16rpx 0;
  padding: 0 24rpx;
  position: relative;
  user-select: none;
}
.t-tab-bar-item--text-only {
  font-size: 32rpx;
}
.t-tab-bar-item--split + .t-tab-bar-item--split::before {
  border-left: 1px solid
    var(
      --td-tab-bar-border-color,
      var(--td-border-color, var(--td-gray-color-3, #e7e7e7))
    );
  bottom: 0;
  bottom: 16rpx;
  box-sizing: border-box;
  content: " ";
  left: 0;
  pointer-events: none;
  position: absolute;
  top: 0;
  top: 16rpx;
  transform: scaleX(0.5);
}
.t-tab-bar-item--crowded {
  padding: 0 16rpx;
}
.t-tab-bar-item--round {
  border-radius: 99px;
}
.t-tab-bar-item__content {
  align-items: center;
  border-radius: 16rpx;
  color: var(
    --td-tab-bar-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  width: 100%;
}
.t-tab-bar-item__content--checked {
  color: var(
    --td-tab-bar-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  font-weight: 600;
}
.t-tab-bar-item__content--tag {
  border-radius: 99px;
}
.t-tab-bar-item__content--tag.t-tab-bar-item__content--checked {
  background-color: var(
    --td-tab-bar-active-bg,
    var(--td-brand-color-light, var(--td-primary-color-1, #f2f3ff))
  );
}
.t-tab-bar-item__icon {
  display: contents;
}
.t-tab-bar-item__icon:empty {
  display: none;
}
.t-tab-bar-item__text {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.t-tab-bar-item__text--small {
  font-size: 20rpx;
  line-height: 32rpx;
}
.t-tab-bar-item__icon-menu {
  margin-right: 8rpx;
}
.t-tab-bar-item__spread {
  background-color: var(
    --td-tab-bar-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  border-radius: 12rpx;
  box-shadow: var(
    --td-tab-bar-spread-shadow,
    var(
      --td-shadow-3,
      0 6px 30px 5px rgba(0, 0, 0, 0.05),
      0 16px 24px 2px rgba(0, 0, 0, 0.04),
      0 8px 10px -5px rgba(0, 0, 0, 0.08)
    )
  );
  color: var(
    --td-tab-bar-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  left: 7%;
  position: absolute;
  top: 0;
  transform: translate3d(0, calc(-100% - 32rpx), 0);
  width: 86%;
  z-index: 1;
}
.t-tab-bar-item__spread::before {
  border: 16rpx solid transparent;
  border-top: 16rpx solid
    var(
      --td-tab-bar-bg-color,
      var(--td-bg-color-container, var(--td-font-white-1, #fff))
    );
  bottom: 0;
  content: "";
  display: block;
  height: 0;
  left: 50%;
  position: absolute;
  transform: translate3d(-50%, 32rpx, 0);
  width: 0;
}
.t-tab-bar-item__spread-item {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  height: 96rpx;
  justify-content: flex-start;
  position: relative;
  width: 100%;
}
.t-tab-bar-item__spread-item--active {
  background-color: var(--td-tab-bar-hover-bg-color, rgba(0, 0, 0, 0.05));
}
.t-tab-bar-item__spread-item-split {
  background-color: var(
    --td-tab-bar-spread-border-color,
    var(--td-border-color, var(--td-gray-color-3, #e7e7e7))
  );
  box-sizing: border-box;
  content: " ";
  height: 1px;
  pointer-events: none;
  transform: translateY(0.5);
  width: 80%;
}
.t-tab-bar-item__spread-item-text {
  padding-top: 24rpx;
}
