__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "components/service-card/service-card": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          e = (C) => {},
          f = (C, T) => {
            C || K || Z(Z(U.service, "publisher"), "nickname")
              ? T(Y(X(X(D.service).publisher).nickname))
              : T();
          },
          d = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) L(N, "avatar");
                if (C || K || Z(Z(U.service, "publisher"), "avatar"))
                  O(N, "src", X(X(D.service).publisher).avatar);
              },
              e
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "name");
              },
              f
            );
          },
          h,
          j = (C, T) => {
            C || K || Z(Z(Z(U.service, "publisher"), "auth_status"), "label")
              ? T(Y(X(X(X(D.service).publisher).auth_status).label))
              : T();
          },
          i = (C, T, E) => {
            if (h === 1) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  if (C) O(N, "theme", "default");
                },
                j
              );
            }
          },
          k,
          m = (C, T) => {
            C || K || Z(Z(Z(U.service, "publisher"), "neighbor_type"), "label")
              ? T(Y(X(X(X(D.service).publisher).neighbor_type).label))
              : T();
          },
          l = (C, T, E) => {
            if (k === 1) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  if (C) O(N, "theme", "primary");
                },
                m
              );
            }
          },
          g = (C, T, E, B) => {
            h =
              X(X(X(D.service).publisher).auth_status).value !== "verified"
                ? 1
                : 0;
            B(h, i);
            k =
              X(X(X(D.service).publisher).auth_status).value === "verified"
                ? 1
                : 0;
            B(k, l);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top_left");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top_right");
              },
              g
            );
          },
          p = (C) => {},
          o = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) L(N, "service-icon");
                if (C || K || Z(U.service, "thumbnail"))
                  O(N, "src", X(D.service).thumbnail);
              },
              p
            );
          },
          r = (C, T) => {
            C || K || Z(U.service, "name") ? T(Y(X(D.service).name)) : T();
          },
          s = (C, T) => {
            C || K || Z(U.service, "description")
              ? T(Y(X(D.service).description))
              : T();
          },
          q = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service-title");
              },
              r
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service-description");
              },
              s
            );
          },
          u,
          x = (C, T) => {
            C || K || Z(U.service, "price") ? T(Y(X(D.service).price)) : T();
          },
          w = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "price-value");
              },
              x
            );
          },
          z = (C, T) => {
            C ? T("面议") : T();
          },
          y = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "price-value");
              },
              z
            );
          },
          v = (C, T, E) => {
            if (u === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "price-box");
                },
                w
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "price-box");
                },
                y
              );
            }
          },
          t = (C, T, E, B) => {
            u = X(D.service).price ? 1 : 0;
            B(u, v);
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_left");
              },
              o
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_midle");
              },
              q
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_right");
              },
              t
            );
          },
          C0 = (C, T) => {
            C || K || !!Z(U.service, "order_count") || undefined
              ? T(Y("订单数量：" + Y(X(D.service).order_count)))
              : T();
          },
          B0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "order-count");
              },
              C0
            );
          },
          E0 = (C, T) => {
            C || K || Z(Z(U.service, "category"), "name")
              ? T(Y(X(X(D.service).category).name))
              : T();
          },
          D0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "category");
              },
              E0
            );
          },
          A0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service-stats");
              },
              B0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service-stats");
              },
              D0
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top");
              },
              c
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info");
                if (C) R.v(N, "tap", "onServiceClick", !1, !1, !1, !1);
              },
              n
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_bottom");
              },
              A0
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list");
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/empty/empty": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { customStyle: new Array(1), style: new Array(1) },
          K = U === true,
          e,
          g = (C) => {},
          h,
          i = (C, T, E, B, F, S, J) => {
            var $A = I(h);
            if (h && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.classPrefix + "__icon", name: D.iconName },
                  X(D.iconData),
                  {}
                ),
                K ||
                  (U
                    ? U.iconData === true ||
                      Object.assign(
                        {
                          tClass: !!U.classPrefix || undefined,
                          name: U.iconName,
                        },
                        X(U.iconData),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class", Y(D.prefix) + "-class-image");
                  if (C || K || U.image) O(N, "src", D.image);
                  if (C) O(N, "mode", "aspectFit");
                },
                g
              );
            } else if (e === 2) {
              h = "icon";
              B(h, i);
            } else {
              S("image");
            }
          },
          d = (C, T, E, B) => {
            e = D.image
              ? 1
              : D.iconName || P(X(a).isNoEmptyObj)(D.iconData)
              ? 2
              : 0;
            B(e, f);
          },
          k,
          l = (C, T) => {
            if (k === 1) {
              C || K || U.description ? T(Y(D.description)) : T();
            }
          },
          j = (C, T, E, B, F, S) => {
            k = D.description ? 1 : 0;
            B(k, l);
            S("description");
          },
          m = (C, T, E, B, F, S) => {
            S("action");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__thumb");
                if (C) O(N, "aria-hidden", "true");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__description " +
                      Y(D.prefix) +
                      "-class-description"
                  );
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__actions " +
                      Y(D.prefix) +
                      "-class-actions"
                  );
              },
              m
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "components/service-card/service-card";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "components/service-card/service-card.js";
define(
  "components/service-card/service-card.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Component({
      properties: {
        service: {
          type: Object,
          value: {
            id: 0,
            name: "",
            description: "",
            thumbnail: "",
            price: 0,
            order_count: 0,
            positive_rate: 0,
            category: { id: 0, name: "" },
            publisher: {
              id: 0,
              nickname: "",
              avatar: "",
              auth_status: { value: "", label: "" },
              neighbor_type: { value: "", label: "" },
            },
            createdAt: "",
          },
        },
      },
      methods: {
        onServiceClick: function () {
          var e = this;
          if (!this.data.clicking) {
            this.setData({ clicking: !0 }),
              setTimeout(function () {
                e.setData({ clicking: !1 });
              }, 1e3);
            var i = this.properties.service.id;
            i
              ? wx.navigateTo({
                  url: "/pages/service-info/service-info?id=".concat(i),
                  fail: function (e) {
                    console.error("跳转失败：", e),
                      wx.showToast({ title: "跳转失败", icon: "none" });
                  },
                })
              : wx.showToast({ title: "服务信息异常", icon: "none" });
          }
        },
        onContactCustomer: function () {
          var e = this.properties.service.publisher.phone;
          e
            ? wx.makePhoneCall({
                phoneNumber: e,
                fail: function (e) {
                  wx.showToast({ title: "拨号失败", icon: "error" });
                },
              })
            : wx.showToast({ title: "暂无联系方式", icon: "none" });
        },
      },
    });
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "components/service-card/service-card.js",
  }
);
require("components/service-card/service-card.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/empty/empty";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/empty/empty.js";
define(
  "miniprogram_npm/tdesign-miniprogram/empty/empty.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      s = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      a = require("../common/src/index"),
      n = u(require("./props")),
      o = u(require("../common/config")),
      c = require("../common/utils");
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var l = o.default.prefix,
      p = "".concat(l, "-empty"),
      m = (function (i) {
        t(o, i);
        var a = s(o);
        function o() {
          var e;
          return (
            r(this, o),
            ((e = a.apply(this, arguments)).options = { multipleSlots: !0 }),
            (e.externalClasses = [
              "".concat(l, "-class"),
              "".concat(l, "-class-description"),
              "".concat(l, "-class-image"),
            ]),
            (e.properties = n.default),
            (e.data = { prefix: l, classPrefix: p }),
            (e.observers = {
              icon: function (e) {
                var r = (0, c.setIcon)("icon", e, "");
                this.setData(Object.assign({}, r));
              },
            }),
            e
          );
        }
        return e(o);
      })(a.SuperComponent),
      f = (m = (0, i.__decorate)([(0, a.wxComponent)()], m));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/empty/empty.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/empty/empty.js");
