.container {
  background-color: #fff;
  min-height: 100vh;
  padding-top: 160rpx;
}
.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx auto;
  padding: 40rpx 30rpx;
}
.form-item {
  margin-bottom: 30rpx;
  position: relative;
}
.form-label {
  align-items: center;
  color: #333;
  display: -webkit-flex;
  display: flex;
  font-size: 28rpx;
  margin-bottom: 15rpx;
}
.form-label.required::before {
  color: #ff4d4f;
  content: "*";
  font-size: 28rpx;
  margin-right: 8rpx;
}
.form-input {
  background-color: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-sizing: border-box;
  color: #333;
  font-size: 26rpx;
  height: 80rpx;
  padding: 0 20rpx;
  width: 100%;
}
.form-input:focus {
  border-color: #195abf;
}
.form-input::-webkit-input-placeholder {
  color: #999;
}
.form-input::placeholder {
  color: #999;
}
.picker-input {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  height: 80rpx;
  line-height: 80rpx;
}
.textarea-wrapper {
  position: relative;
}
.form-textarea {
  background-color: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-sizing: border-box;
  color: #333;
  font-size: 26rpx;
  min-height: 160rpx;
  padding: 20rpx;
  resize: none;
  width: 100%;
}
.form-textarea:focus {
  border-color: #195abf;
}
.form-textarea::-webkit-input-placeholder {
  color: #999;
}
.form-textarea::placeholder {
  color: #999;
}
.char-count-inner {
  background-color: #fff;
  bottom: 20rpx;
  color: #999;
  font-size: 22rpx;
  padding: 0 5rpx;
  position: absolute;
  right: 20rpx;
}
.textarea-count {
  bottom: 20rpx;
  top: auto;
  transform: none;
}
.button-group {
  gap: 20rpx;
  justify-content: space-between;
  margin-top: 60rpx;
}
.btn,
.button-group {
  display: -webkit-flex;
  display: flex;
}
.btn {
  align-items: center;
  border: none;
  border-radius: 20rpx;
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  justify-content: center;
}
.btn-cancel {
  background-color: #fff;
  border: 3rpx solid #1e1e1e;
  color: #666;
}
.btn-submit {
  background-color: #a5d8ff;
  border: 3rpx solid #1971c2;
  color: #1971c2;
}
.btn:active {
  opacity: 0.8;
}
.avatar-section {
  justify-content: flex-start;
  margin-top: 15rpx;
}
.avatar-preview,
.avatar-section {
  display: -webkit-flex;
  display: flex;
}
.avatar-preview {
  align-items: center;
  background-color: #f8f9fa;
  border: 2rpx dashed #e0e0e0;
  border-radius: 50%;
  height: 150rpx;
  justify-content: center;
  overflow: hidden;
  position: relative;
  width: 150rpx;
}
.selected-avatar {
  border-radius: 50%;
  height: 100%;
  width: 100%;
}
.avatar-placeholder {
  align-items: center;
  color: #1971c2;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.placeholder-text {
  color: #1971c2;
  font-size: 28rpx;
}
.avatar-modal {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  position: fixed;
  z-index: 9999;
}
.avatar-modal,
.modal-mask {
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
}
.modal-mask {
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
}
.modal-content {
  background-color: #fff;
  border-radius: 20rpx;
  max-width: 600rpx;
  padding: 40rpx;
  position: relative;
  width: 90%;
}
.modal-header {
  margin-bottom: 30rpx;
  text-align: center;
}
.modal-title {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}
.avatar-grid {
  display: -webkit-flex;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  justify-content: space-around;
  margin-bottom: 30rpx;
}
.avatar-item {
  border-radius: 50%;
  height: 120rpx;
  padding: 10rpx;
  position: relative;
  transition: all 0.3s ease;
  width: 120rpx;
}
.avatar-item:active {
  transform: scale(0.95);
}
.avatar-img {
  border: 2rpx solid #e0e0e0;
  border-radius: 50%;
  height: 100%;
  width: 100%;
}
.selected-mark {
  align-items: center;
  background-color: #195abf;
  border-radius: 50%;
  color: #fff;
  font-size: 24rpx;
  font-weight: 700;
  height: 40rpx;
  position: absolute;
  right: 5rpx;
  top: 5rpx;
  width: 40rpx;
}
.modal-footer,
.selected-mark {
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.modal-footer {
  margin: 0 auto;
  width: 50%;
}
