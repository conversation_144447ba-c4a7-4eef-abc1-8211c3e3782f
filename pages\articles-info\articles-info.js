var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator");
Page({
  data: {
    articlesData: {
      article_id: null,
      title: "",
      content: "",
      detail_images: "",
    },
    loading: !0,
  },
  onLoad: function (e) {
    var t = this,
      a = e.id;
    a
      ? this.loadArticleDetail(a)
      : (wx.showToast({ title: "文章ID不存在", icon: "error" }),
        setTimeout(function () {
          t.safeNavigateBack();
        }, 1500));
  },
  safeNavigateBack: function () {
    getCurrentPages().length > 1
      ? wx.navigateBack()
      : wx.reLaunch({ url: "/pages/index/index" });
  },
  loadArticleDetail: function (a) {
    var n = this;
    return t(
      e().mark(function t() {
        var r, i;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  return (
                    (r = getApp()),
                    n.setData({ loading: !0 }),
                    (e.prev = 2),
                    (e.next = 5),
                    r.call({
                      path: "/api/mp/articles/".concat(a),
                      method: "GET",
                    })
                  );
                case 5:
                  if (0 !== (i = e.sent).code) {
                    e.next = 11;
                    break;
                  }
                  console.log("文章详情获取成功:", i.data),
                    n.setData({ articlesData: i.data, loading: !1 }),
                    (e.next = 12);
                  break;
                case 11:
                  throw new Error(i.message || "获取文章详情失败");
                case 12:
                  e.next = 19;
                  break;
                case 14:
                  (e.prev = 14),
                    (e.t0 = e.catch(2)),
                    console.error("获取文章详情失败：", e.t0),
                    n.setData({ loading: !1 }),
                    wx.showToast({ title: "加载失败", icon: "error" });
                case 19:
                case "end":
                  return e.stop();
              }
          },
          t,
          null,
          [[2, 14]]
        );
      })
    )();
  },
  refreshArticleDetail: function () {
    var a = this;
    return t(
      e().mark(function t() {
        var n;
        return e().wrap(function (e) {
          for (;;)
            switch ((e.prev = e.next)) {
              case 0:
                if (!(n = a.data.articlesData.article_id)) {
                  e.next = 4;
                  break;
                }
                return (e.next = 4), a.loadArticleDetail(n);
              case 4:
              case "end":
                return e.stop();
            }
        }, t);
      })
    )();
  },
  onReady: function () {},
  onShow: function () {},
  onHide: function () {},
  onUnload: function () {},
  onPullDownRefresh: function () {
    this.refreshArticleDetail().then(function () {
      wx.stopPullDownRefresh();
    });
  },
  onReachBottom: function () {},
  onShareAppMessage: function () {
    var e = this.data.articlesData;
    return {
      title: e.title || "精彩文章分享",
      path: "/pages/articles-info/articles-info?id=".concat(e.article_id),
      imageUrl: e.detail_images || "",
    };
  },
});
