__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/dropdown-menu/dropdown-menu": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { style: new Array(1), customStyle: new Array(1) },
          K = U === true,
          d = (C, e, f, g, h, i, T, E) => {
            var k = (C, T) => {
                C || K || Z(g, "label") ? T(Y(X(e).label)) : T();
              },
              l,
              m = (C, T, E, B, F, S, J) => {
                var $A = I(l);
                var $B = D.activeIdx == f;
                if (l && $A)
                  $A(
                    R,
                    C,
                    Object.assign({}, X(D._arrowIcon), {
                      ariaHidden: true,
                      tClass:
                        D.classPrefix +
                        "__icon " +
                        D.classPrefix +
                        "__icon--" +
                        ($B ? "active " : " ") +
                        D.prefix +
                        "-class-icon",
                    }),
                    K ||
                      (U
                        ? U._arrowIcon === true ||
                          Object.assign({}, X(U._arrowIcon), {
                            tClass:
                              !!(
                                U.classPrefix ||
                                U.classPrefix ||
                                U.activeIdx ||
                                h ||
                                ($B ? undefined : undefined) ||
                                U.prefix
                              ) || undefined,
                          })
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              },
              j = (C, T, E, B, F, S, J) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__title " +
                          Y(D.prefix) +
                          "-class-label"
                      );
                  },
                  k
                );
                l = "icon";
                B(l, m);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([!!(U.activeIdx || h) || undefined]),
                      Q.a([Z(g, "disabled")]),
                      Q.a([h]),
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__item", [
                        ["active", D.activeIdx == f],
                        ["disabled", X(e).disabled],
                        [f, true],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-item"
                  );
                if (C) O(N, "bindtap", "handleToggle");
                if (C || K || Z(g, "disabled"))
                  O(N, "aria-disabled", X(e).disabled);
                if (C) O(N, "aria-role", "button");
                if (C || K || !!(U.activeIdx || h) || undefined)
                  O(N, "aria-expanded", D.activeIdx === f);
                if (C) O(N, "aria-haspopup", "menu");
                if (C || K || h) R.d(N, "index", f);
              },
              j
            );
          },
          c = (C, T, E, B, F, S) => {
            F(D.menus, "index", U ? U.menus : undefined, [0, "menus"], d);
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                var $A = D.activeIdx === -1;
                if (C || K || !!U.activeIdx || ($A ? undefined : undefined))
                  O(N, "catchtouchmove", $A ? "" : "noop");
                if (C) R.i(N, "t-bar");
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/dropdown-menu/dropdown-menu";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/dropdown-menu/dropdown-menu.js";
define(
  "miniprogram_npm/tdesign-miniprogram/dropdown-menu/dropdown-menu.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      i = require("../common/src/index"),
      o = c(require("../common/config")),
      s = c(require("./props")),
      l = require("../common/utils");
    function c(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = o.default.prefix,
      d = "".concat(u, "-dropdown-menu"),
      p = (function (a) {
        r(o, a);
        var i = n(o);
        function o() {
          var e;
          return (
            t(this, o),
            ((e = i.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-item"),
              "".concat(u, "-class-label"),
              "".concat(u, "-class-icon"),
            ]),
            (e.properties = s.default),
            (e.nodes = null),
            (e.data = {
              prefix: u,
              classPrefix: d,
              menus: null,
              activeIdx: -1,
              bottom: 0,
              _arrowIcon: { name: s.default.arrowIcon.value },
            }),
            (e.relations = {
              "../dropdown-item/dropdown-item": { type: "child" },
            }),
            (e.lifetimes = {
              ready: function () {
                this.getAllItems();
              },
            }),
            (e.observers = {
              arrowIcon: function (e) {
                this.setData({ _arrowIcon: (0, l.calcIcon)(e) });
              },
              activeIdx: function (e) {
                this.triggerEvent(-1 === e ? "close" : "open");
              },
            }),
            (e.methods = {
              toggle: function (e) {
                var t = this.data,
                  r = t.activeIdx,
                  n = t.duration,
                  a = this.$children[r],
                  i = this.$children[e];
                (null == i ? void 0 : i.data.disabled) ||
                  (-1 !== r &&
                    (a.triggerEvent("close"),
                    a.setData({ show: !1 }, function () {
                      setTimeout(function () {
                        a.triggerEvent("closed");
                      }, n);
                    })),
                  null == e || r === e
                    ? this.setData({ activeIdx: -1 })
                    : (i.triggerEvent("open"),
                      this.setData({ activeIdx: e }),
                      i.setData({ show: !0 }, function () {
                        setTimeout(function () {
                          i.triggerEvent("opened");
                        }, n);
                      })));
              },
              getAllItems: function () {
                var e = this.$children.map(function (e) {
                  var t = e.data;
                  return {
                    label: t.label || t.computedLabel,
                    disabled: t.disabled,
                  };
                });
                this.setData({ menus: e });
              },
              handleToggle: function (e) {
                var t = e.currentTarget.dataset.index;
                this.toggle(t);
              },
              noop: function () {},
            }),
            e
          );
        }
        return e(o);
      })(i.SuperComponent),
      m = (p = (0, a.__decorate)([(0, i.wxComponent)()], p));
    exports.default = m;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/dropdown-menu/dropdown-menu.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/dropdown-menu/dropdown-menu.js");
