var _ = require("../../../../../@babel/runtime/helpers/typeof");
!(function (e, t) {
  "object" == ("undefined" == typeof exports ? "undefined" : _(exports)) &&
  "undefined" != typeof module
    ? (module.exports = t(require("dayjs")))
    : "function" == typeof define && define.amd
    ? define(["dayjs"], t)
    : ((e =
        "undefined" != typeof globalThis
          ? globalThis
          : e || self).dayjs_locale_ru = t(e.dayjs));
})(void 0, function (e) {
  var t = (function (e) {
      return e && "object" == _(e) && "default" in e ? e : { default: e };
    })(e),
    n =
      "января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря".split(
        "_"
      ),
    r =
      "январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split(
        "_"
      ),
    i = "янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.".split(
      "_"
    ),
    s = "янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.".split(
      "_"
    ),
    o = /D[oD]?(\[[^[\]]*\]|\s)+MMMM?/;
  function d(_, e, t) {
    var n, r;
    return "m" === t
      ? e
        ? "минута"
        : "минуту"
      : _ +
          " " +
          ((n = +_),
          (r = {
            mm: e ? "минута_минуты_минут" : "минуту_минуты_минут",
            hh: "час_часа_часов",
            dd: "день_дня_дней",
            MM: "месяц_месяца_месяцев",
            yy: "год_года_лет",
          }[t].split("_")),
          n % 10 == 1 && n % 100 != 11
            ? r[0]
            : n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 10 || n % 100 >= 20)
            ? r[1]
            : r[2]);
  }
  var u = function (_, e) {
    return o.test(e) ? n[_.month()] : r[_.month()];
  };
  (u.s = r), (u.f = n);
  var a = function (_, e) {
    return o.test(e) ? i[_.month()] : s[_.month()];
  };
  (a.s = s), (a.f = i);
  var f = {
    name: "ru",
    weekdays:
      "воскресенье_понедельник_вторник_среда_четверг_пятница_суббота".split(
        "_"
      ),
    weekdaysShort: "вск_пнд_втр_срд_чтв_птн_сбт".split("_"),
    weekdaysMin: "вс_пн_вт_ср_чт_пт_сб".split("_"),
    months: u,
    monthsShort: a,
    weekStart: 1,
    yearStart: 4,
    formats: {
      LT: "H:mm",
      LTS: "H:mm:ss",
      L: "DD.MM.YYYY",
      LL: "D MMMM YYYY г.",
      LLL: "D MMMM YYYY г., H:mm",
      LLLL: "dddd, D MMMM YYYY г., H:mm",
    },
    relativeTime: {
      future: "через %s",
      past: "%s назад",
      s: "несколько секунд",
      m: d,
      mm: d,
      h: "час",
      hh: d,
      d: "день",
      dd: d,
      M: "месяц",
      MM: d,
      y: "год",
      yy: d,
    },
    ordinal: function (_) {
      return _;
    },
    meridiem: function (_) {
      return _ < 4 ? "ночи" : _ < 12 ? "утра" : _ < 17 ? "дня" : "вечера";
    },
  };
  return t.default.locale(f, null, !0), f;
});
