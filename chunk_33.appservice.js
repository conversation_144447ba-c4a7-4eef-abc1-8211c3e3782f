__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/indexes-anchor/indexes-anchor":
      (() => {
        var H = {};
        var S;
        var I = (P) => {
          if (!S) S = Object.assign({}, H);
          return S[P];
        };
        var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
        H[""] = (R, C, D, U) => {
          R.setFnFilter(Q.A, Q.B);
          if (typeof R.setEventListenerWrapper === "function")
            R.setEventListenerWrapper(Q.C);
          var L = R.c,
            M = R.m,
            O = R.r,
            A = {
              prefix: new Array(1),
              active: new Array(2),
              classPrefix: new Array(4),
              customStyle: new Array(1),
              style: new Array(1),
              index: new Array(1),
              sticky: new Array(1),
              anchorStyle: new Array(1),
            },
            K = U === true,
            e = (C, T, E, B, F, S) => {
              S("");
            },
            f = (C, T) => {
              C || K || U.index
                ? T(Y(D.index), (N) => {
                    A["index"][0] = (D, E, T) => {
                      T(N, Y(D.index));
                    };
                  })
                : T();
            },
            d = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__slot");
                  A["classPrefix"][2] = (D, E, T) => {
                    L(N, Y(D.classPrefix) + "__slot");
                  };
                },
                e
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([U.active])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__header", [
                        ["active", D.active],
                      ])
                    );
                  A["classPrefix"][3] = A["active"][1] = (D, E, T) => {
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__header", [
                        ["active", D.active],
                      ])
                    );
                  };
                },
                f
              );
            },
            c = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([U.sticky]), Q.a([U.active])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__wrapper", [
                        ["sticky", D.sticky],
                        ["active", D.active],
                      ])
                    );
                  A["classPrefix"][1] =
                    A["sticky"][0] =
                    A["active"][0] =
                      (D, E, T) => {
                        L(
                          N,
                          P(X(a).cls)(D.classPrefix + "__wrapper", [
                            ["sticky", D.sticky],
                            ["active", D.active],
                          ])
                        );
                      };
                  if (C || K || U.anchorStyle) R.y(N, D.anchorStyle);
                  A["anchorStyle"][0] = (D, E, T) => {
                    R.y(N, D.anchorStyle);
                  };
                },
                d
              );
            },
            b = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  A["classPrefix"][0] = A["prefix"][0] = (D, E, T) => {
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  };
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  };
                },
                c
              );
            };
          return { C: b, B: A };
        };
        return Object.assign(
          function (R) {
            return H[R];
          },
          { _: H }
        );
      })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/indexes-anchor/indexes-anchor";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/indexes-anchor/indexes-anchor.js";
define(
  "miniprogram_npm/tdesign-miniprogram/indexes-anchor/indexes-anchor.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      s = require("../common/src/index"),
      n = l(require("../common/config")),
      u = l(require("./props"));
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = n.default.prefix,
      c = "".concat(o, "-indexes-anchor"),
      p = (function (a) {
        t(n, a);
        var s = i(n);
        function n() {
          var e;
          return (
            r(this, n),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(o, "-class"),
            ]),
            (e.properties = u.default),
            (e.data = {
              prefix: o,
              classPrefix: c,
              anchorStyle: "",
              sticky: !1,
              active: !1,
            }),
            (e.relations = { "../indexes/indexes": { type: "parent" } }),
            e
          );
        }
        return e(n);
      })(s.SuperComponent),
      d = (p = (0, a.__decorate)([(0, s.wxComponent)()], p));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/indexes-anchor/indexes-anchor.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/indexes-anchor/indexes-anchor.js");
