__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/color-picker/color-picker": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          f = (C, T, E, B, F, S, J) => {
            var $A =
              G["miniprogram_npm/tdesign-miniprogram/color-picker/template"];
            if ($A) $A("")(R, C, D, U).C(C, T, E, B, F, S, J);
          },
          e = (C, T, E, B, F, S, J) => {
            S("header");
            J(f);
            S("footer");
          },
          g = (C, T, E, B, F, S, J) => {
            var $A =
              G["miniprogram_npm/tdesign-miniprogram/color-picker/template"];
            if ($A) $A("")(R, C, D, U).C(C, T, E, B, F, S, J);
          },
          d = (C, T, E, B, F, S, J) => {
            if (c === 1) {
              E(
                "t-popup",
                {},
                (N, C) => {
                  if (C) L(N, "class");
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (
                    C ||
                    K ||
                    !!Z(U.popupProps, "usingCustomNavbar") ||
                    undefined
                  )
                    O(
                      N,
                      "using-custom-navbar",
                      X(D.popupProps).usingCustomNavbar || false
                    );
                  if (C || K || !!Z(U.popupProps, "showOverlay") || undefined)
                    O(N, "show-overlay", X(D.popupProps).showOverlay || true);
                  if (C || K || !!Z(U.popupProps, "zIndex") || undefined)
                    O(N, "z-index", X(D.popupProps).zIndex || 11500);
                  if (
                    C ||
                    K ||
                    !!(
                      Z(U.popupProps, "overlayProps") || U.defaultOverlayProps
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "overlay-props",
                      X(D.popupProps).overlayProps || D.defaultOverlayProps
                    );
                  if (C) O(N, "placement", "bottom");
                  if (C)
                    R.v(N, "visible-change", "onVisibleChange", !1, !1, !1, !1);
                },
                e
              );
            } else {
              J(g);
            }
          },
          b = (C, T, E, B) => {
            c = D.usePopup ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/color-picker/color-picker.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-color-picker__panel{background:var(--td-color-picker-background,#fff);border-top-left-radius:var(--td-color-picker-panel-radius,",
      [0, 24],
      ");border-top-right-radius:var(--td-color-picker-panel-radius,",
      [0, 24],
      ");padding:0;-webkit-user-select:none;user-select:none;width:var(--td-color-picker-panel-width,",
      [0, 750],
      ")}\n.",
      [1],
      "t-color-picker__body{padding:var(--td-color-picker-panel-padding,",
      [0, 32],
      ");padding-bottom:",
      [0, 56],
      "}\n.",
      [1],
      "t-color-picker__thumb{box-shadow:var(--td-shadow-1,0 1px 10px rgba(0,0,0,.05),0 4px 5px rgba(0,0,0,.08),0 2px 4px -1px rgba(0,0,0,.12));color:var(--td-text-color-brand,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));height:var(--td-color-picker-slider-thumb-size,",
      [0, 48],
      ");outline:0;width:var(--td-color-picker-slider-thumb-size,",
      [0, 48],
      ");z-index:1}\n.",
      [1],
      "t-color-picker__thumb,.",
      [1],
      "t-color-picker__thumb::after,.",
      [1],
      "t-color-picker__thumb::before{border-radius:var(--td-color-picker-border-radius-circle,50%);box-sizing:border-box;position:absolute}\n.",
      [1],
      "t-color-picker__thumb::after,.",
      [1],
      "t-color-picker__thumb::before{border:1px solid #dcdcdc;content:\x22\x22;display:block}\n.",
      [1],
      "t-color-picker__thumb::before{background-color:#fff;height:100%;left:0;top:0;width:100%}\n.",
      [1],
      "t-color-picker__thumb::after{background:currentcolor;height:calc(100% - 6px);left:3px;padding:var(--td-color-picker-slider-thumb-padding,",
      [0, 6],
      ");top:3px;width:calc(100% - 6px)}\n.",
      [1],
      "t-color-picker__saturation{background:0 0;border-radius:var(--td-color-picker-saturation-radius,",
      [0, 12],
      ");height:var(--td-color-picker-saturation-height,",
      [0, 288],
      ");overflow:hidden;position:relative}\n.",
      [1],
      "t-color-picker__saturation::after,.",
      [1],
      "t-color-picker__saturation::before{content:\x22\x22;height:100%;left:0;position:absolute;top:0;width:100%}\n.",
      [1],
      "t-color-picker__saturation::before{background:linear-gradient(90deg,#fff,transparent)}\n.",
      [1],
      "t-color-picker__saturation::after{background:linear-gradient(0deg,#000,transparent)}\n.",
      [1],
      "t-color-picker__saturation .",
      [1],
      "t-color-picker__thumb{border-radius:var(--td-color-picker-border-radius-circle,50%);height:var(--td-color-picker-saturation-thumb-size,",
      [0, 48],
      ");-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:var(--td-color-picker-saturation-thumb-size,",
      [0, 48],
      ")}\n.",
      [1],
      "t-color-picker__slider-wrapper{border-radius:calc(var(--td-color-picker-slider-height,",
      [0, 16],
      ")/ 2);padding:var(--td-color-picker-slider-wrapper-padding,0 ",
      [0, 18],
      ");position:relative}\n.",
      [1],
      "t-color-picker__slider-wrapper--hue-type{background:linear-gradient(90deg,red,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red);margin:",
      [0, 16],
      " 0}\n.",
      [1],
      "t-color-picker__slider-wrapper--alpha-type{background:var(--td-text-color-anti,var(--td-font-white-1,#fff));background-image:linear-gradient(45deg,#c5c5c5 25%,transparent 0,transparent 75%,#c5c5c5 0,#c5c5c5),linear-gradient(45deg,#c5c5c5 25%,transparent 0,transparent 75%,#c5c5c5 0,#c5c5c5);background-position:0 0,3px 3px;background-size:6px 6px;margin:",
      [0, 40],
      " 0 ",
      [0, 16],
      "}\n.",
      [1],
      "t-color-picker__slider-wrapper--alpha-type .",
      [1],
      "t-color-picker__rail{background:linear-gradient(90deg,transparent,currentcolor)}\n.",
      [1],
      "t-color-picker__slider-padding{left:0;position:absolute;top:0;width:100%}\n.",
      [1],
      "t-color-picker__slider,.",
      [1],
      "t-color-picker__slider-padding{border-radius:calc(var(--td-color-picker-slider-height,",
      [0, 16],
      ")/ 2);height:var(--td-color-picker-slider-height,",
      [0, 16],
      ")}\n.",
      [1],
      "t-color-picker__slider{color:transparent;outline:0;position:relative;z-index:1}\n.",
      [1],
      "t-color-picker__slider .",
      [1],
      "t-color-picker__thumb{top:50%;-webkit-transform:translate(var(--td-color-picker-slider-thumb-transform-x,",
      [0, -18],
      "),-50%);transform:translate(var(--td-color-picker-slider-thumb-transform-x,",
      [0, -18],
      "),-50%)}\n.",
      [1],
      "t-color-picker__slider .",
      [1],
      "t-color-picker__rail{border-radius:inherit;height:100%;left:0;overflow:hidden;position:absolute;top:0;width:100%}\n.",
      [1],
      "t-color-picker__sliders-wrapper{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;margin:",
      [0, 32],
      " 0 ",
      [0, 40],
      "}\n.",
      [1],
      "t-color-picker__sliders{width:100%}\n.",
      [1],
      "t-color-picker__sliders-preview{background:var(--td-text-color-anti,var(--td-font-white-1,#fff));background-image:linear-gradient(45deg,#c5c5c5 25%,transparent 0,transparent 75%,#c5c5c5 0,#c5c5c5),linear-gradient(45deg,#c5c5c5 25%,transparent 0,transparent 75%,#c5c5c5 0,#c5c5c5);background-position:0 0,3px 3px;background-size:6px 6px;border-radius:var(--td-color-picker-gradient-preview-radius,",
      [0, 6],
      ");-webkit-flex-shrink:0;flex-shrink:0;height:var(--td-color-picker-gradient-preview-height,",
      [0, 56],
      ");margin-left:var(--td-color-picker-margin,",
      [0, 24],
      ");overflow:hidden;width:var(--td-color-picker-gradient-preview-width,",
      [0, 56],
      ")}\n.",
      [1],
      "t-color-picker__sliders-preview-inner{display:block;height:100%;width:100%}\n.",
      [1],
      "t-color-picker__format{-webkit-align-items:center;align-items:center;color:rgba(0,0,0,.4);display:-webkit-flex;display:flex;font-size:",
      [0, 28],
      ";height:",
      [0, 56],
      ";-webkit-justify-content:space-between;justify-content:space-between;line-height:",
      [0, 56],
      ";margin-top:",
      [0, 40],
      ";text-align:center}\n.",
      [1],
      "t-color-picker__format-item{background:var(--td-color-picker-format-background-color,var(--td-gray-color-1,#f3f3f3))}\n.",
      [1],
      "t-color-picker__format-item--first{border:1px solid #dcdcdc;border-radius:",
      [0, 12],
      ";-webkit-flex-shrink:0;flex-shrink:0;margin-right:",
      [0, 24],
      ";width:",
      [0, 136],
      "}\n.",
      [1],
      "t-color-picker__format-item--second{-webkit-flex:1;flex:1}\n.",
      [1],
      "t-color-picker__format-inputs{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:space-around;justify-content:space-around}\n.",
      [1],
      "t-color-picker__format-input{border:1px solid #dcdcdc;border-radius:",
      [0, 12],
      ";-webkit-flex:1;flex:1;margin-left:-1px;width:0}\n.",
      [1],
      "t-color-picker__format-input:not(:first-child):not(:last-child){border-radius:0}\n.",
      [1],
      "t-color-picker__format-input:first-child:not(:last-child){border-bottom-right-radius:0;border-top-right-radius:0}\n.",
      [1],
      "t-color-picker__format-input:last-child:not(:first-child){border-bottom-left-radius:0;border-top-left-radius:0}\n.",
      [1],
      "t-color-picker__format-input--fixed{-webkit-flex-basis:",
      [0, 133],
      ";flex-basis:",
      [0, 133],
      ";-webkit-flex-grow:0;flex-grow:0;-webkit-flex-shrink:0;flex-shrink:0}\n.",
      [1],
      "t-color-picker__swatches-wrap{margin-top:",
      [0, 56],
      ";position:relative}\n.",
      [1],
      "t-color-picker__swatches+.",
      [1],
      "t-color-picker__swatches{margin-top:var(--td-color-picker-margin,",
      [0, 24],
      ")}\n.",
      [1],
      "t-color-picker__swatches-title{-webkit-align-items:center;align-items:center;color:rgba(0,0,0,.9);display:-webkit-flex;display:flex;font:var(--td-color-picker-swatches-title-font,",
      [0, 32],
      ");height:",
      [0, 48],
      ";-webkit-justify-content:space-between;justify-content:space-between;line-height:",
      [0, 48],
      ";padding:0}\n.",
      [1],
      "t-color-picker__swatches-items{display:-webkit-flex;display:flex;list-style:none;margin-top:",
      [0, 24],
      ";overflow-x:auto;overflow-y:auto;width:100%}\n.",
      [1],
      "t-color-picker__swatches-items::-webkit-scrollbar{color:transparent;display:none;height:0;width:0}\n.",
      [1],
      "t-color-picker__swatches-item{-webkit-align-items:center;align-items:center;border-radius:",
      [0, 6],
      ";border-radius:var(--td-color-picker-swatch-border-radius,",
      [0, 6],
      ");box-sizing:border-box;display:-webkit-flex;display:flex;-webkit-flex-shrink:0;flex-shrink:0;height:var(--td-color-picker-swatch-height,",
      [0, 48],
      ");-webkit-justify-content:center;justify-content:center;margin-right:",
      [0, 24],
      ";overflow:hidden;padding:var(--td-color-picker-swatch-padding,0);position:relative;-webkit-transform-origin:center;transform-origin:center;transition:all var(--td-anim-duration-base,.2s) var(--td-anim-time-fn-easing,cubic-bezier(.38,0,.24,1));width:var(--td-color-picker-swatch-width,",
      [0, 48],
      ")}\n.",
      [1],
      "t-color-picker__swatches-item::after{background:rgba(0,0,0,.2);content:\x22\x22;height:100%;left:0;opacity:0;position:absolute;top:0;width:100%}\n.",
      [1],
      "t-color-picker__swatches-item:active::after{opacity:1}\n.",
      [1],
      "t-color-picker__swatches-inner{border-radius:var(--td-color-picker-swatch-border-radius,",
      [0, 6],
      ");display:block;height:100%;position:relative;width:100%}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/color-picker/color-picker.wxss",
    }
  );
}
