__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/switch/switch": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { customStyle: new Array(1), style: new Array(1) },
          K = U === true,
          f,
          i,
          k = (C) => {},
          l = (C, T) => {
            var $A = D.checked;
            var $B = 0;
            var $C = 1;
            C || K || !!U.checked || ($A ? Z(U.label, $B) : Z(U.label, $C))
              ? T(Y($A ? X(D.label)[$B] : X(D.label)[$C]))
              : T();
          },
          m = (C) => {},
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "t-loading",
                {},
                (N, C) => {
                  if (C) O(N, "inherit-color", true);
                  if (C) O(N, "size", "32rpx");
                },
                k
              );
            } else if (i === 2) {
              E("text", {}, (N, C) => {}, l);
            } else if (i === 3) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  var $A = D.checked;
                  var $B = 0;
                  var $C = 1;
                  if (
                    C ||
                    K ||
                    !!U.checked ||
                    ($A ? Z(U.icon, $B) : Z(U.icon, $C))
                  )
                    O(N, "name", $A ? X(D.icon)[$B] : X(D.icon)[$C]);
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([U.checked]), U.size])
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "t-class",
                      P(X(a).cls)(D.classPrefix + "__icon", [
                        ["checked", D.checked],
                        D.size,
                      ])
                    );
                },
                m
              );
            }
          },
          h = (C, T, E, B) => {
            i = D.loading
              ? 1
              : X(D.label).length == 2
              ? 2
              : X(D.icon).length == 2
              ? 3
              : 0;
            B(i, j);
          },
          g = (C, T, E) => {
            if (f === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([U.checked]), Q.a([U.disabled]), U.size]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__label", [
                          ["checked", D.checked],
                          ["disabled", D.disabled],
                          D.size,
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-label"
                    );
                },
                h
              );
            }
          },
          e = (C, T, E, B) => {
            f = D.label ? 1 : 0;
            B(f, g);
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([U.checked]),
                      Q.a([U.disabled]),
                      Q.a([
                        !!(
                          Z(U.label, "length") ||
                          Z(U.icon, "length") ||
                          U.loading
                        ) || undefined,
                      ]),
                      U.size,
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__dot", [
                        ["checked", D.checked],
                        ["disabled", D.disabled],
                        [
                          "plain",
                          X(D.label).length != 2 &&
                            X(D.icon).length != 2 &&
                            !D.loading,
                        ],
                        D.size,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-dot"
                  );
                if (C || K || undefined) O(N, "aria-hidden", true);
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([U.checked]),
                      Q.a([!!(U.disabled || U.loading) || undefined]),
                      U.size,
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__body", [
                        ["checked", D.checked],
                        ["disabled", D.disabled || D.loading],
                        D.size,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-body"
                  );
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C || K || U.checked) O(N, "aria-checked", D.checked);
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                if (C) O(N, "aria-role", "switch");
                if (C) R.v(N, "tap", "handleSwitch", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/switch/switch.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-switch,.",
        [1],
        "t-switch__label{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;overflow:hidden}\n.",
        [1],
        "t-switch__label{bottom:0;color:var(--td-switch-label-color,var(--td-bg-color-secondarycontainer-active,var(--td-gray-color-4,#dcdcdc)));-webkit-flex-wrap:nowrap;flex-wrap:nowrap;font-size:var(--td-switch-label-font-size,",
        [0, 28],
        ");-webkit-justify-content:center;justify-content:center;left:0;position:absolute;top:0;width:100%}\n.",
        [1],
        "t-switch__label--checked{color:var(--td-switch-label-checked-color,var(--td-switch-checked-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9))))}\n.",
        [1],
        "t-switch__label--disabled{color:var(--td-switch-unchecked-disabled-color,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)))}\n.",
        [1],
        "t-switch__label--checked.",
        [1],
        "t-switch__label--disabled{color:var(--td-switch-checked-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-switch__label--large{font-size:var(--td-switch-label-large-font-size,",
        [0, 32],
        ")}\n.",
        [1],
        "t-switch__label--small{font-size:var(--td-switch-label-small-font-size,",
        [0, 24],
        ")}\n.",
        [1],
        "t-switch__label:empty{display:none}\n.",
        [1],
        "t-switch__icon{font-size:var(--td-switch-icon-size,",
        [0, 40],
        ")}\n.",
        [1],
        "t-switch__icon--large{font-size:var(--td-switch-icon-large-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-switch__icon--small{font-size:var(--td-switch-icon-small-size,",
        [0, 32],
        ")}\n.",
        [1],
        "t-switch__loading{color:var(--td-switch-label-checked-color,var(--td-switch-checked-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9))))}\n.",
        [1],
        "t-switch__body{background-color:var(--td-switch-unchecked-color,var(--td-bg-color-secondarycontainer-active,var(--td-gray-color-4,#dcdcdc)));border-radius:var(--td-switch-radius,calc(var(--td-switch-height,",
        [0, 56],
        ")/ 2));height:var(--td-switch-height,",
        [0, 56],
        ");overflow:hidden;position:relative;transition:all .3s ease;vertical-align:middle;width:var(--td-switch-width,",
        [0, 90],
        ")}\n.",
        [1],
        "t-switch__body--checked{background-color:var(--td-switch-checked-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-switch__body--disabled{background-color:var(--td-switch-unchecked-disabled-color,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)))}\n.",
        [1],
        "t-switch__body--checked.",
        [1],
        "t-switch__body--disabled{background-color:var(--td-switch-checked-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-switch__body--large{border-radius:var(--td-switch-large-radius,calc(var(--td-switch-large-height,",
        [0, 64],
        ")/ 2));height:var(--td-switch-large-height,",
        [0, 64],
        ");width:var(--td-switch-large-width,",
        [0, 104],
        ")}\n.",
        [1],
        "t-switch__body--small{border-radius:var(--td-switch-small-radius,calc(var(--td-switch-small-height,",
        [0, 48],
        ")/ 2));height:var(--td-switch-small-height,",
        [0, 48],
        ");width:var(--td-switch-small-width,",
        [0, 78],
        ")}\n.",
        [1],
        "t-switch__dot{background-color:var(--td-text-color-anti,var(--td-font-white-1,#fff));border-radius:50%;box-shadow:var(--td-switch-dot-shadow,var(--td-shadow-1,0 1px 10px rgba(0,0,0,.05),0 4px 5px rgba(0,0,0,.08),0 2px 4px -1px rgba(0,0,0,.12)));height:var(--td-switch-dot-size,",
        [0, 44],
        ");left:var(--td-switch-dot-horizontal-margin,",
        [0, 6],
        ");position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);transition:all .3s;width:var(--td-switch-dot-size,",
        [0, 44],
        ")}\n.",
        [1],
        "t-switch__dot--disabled{background-color:var(--td-switch-dot-disabled-color,var(--td-font-white-1,#fff))}\n.",
        [1],
        "t-switch__dot--large{height:var(--td-switch-dot-large-size,",
        [0, 52],
        ");width:var(--td-switch-dot-large-size,",
        [0, 52],
        ")}\n.",
        [1],
        "t-switch__dot--small{height:var(--td-switch-dot-small-size,",
        [0, 36],
        ");width:var(--td-switch-dot-small-size,",
        [0, 36],
        ")}\n.",
        [1],
        "t-switch__dot--checked{left:calc(var(--td-switch-width,",
        [0, 90],
        ") - var(--td-switch-dot-size,",
        [0, 44],
        ") - var(--td-switch-dot-horizontal-margin,",
        [0, 6],
        "))}\n.",
        [1],
        "t-switch__dot--large.",
        [1],
        "t-switch__dot--checked{left:calc(var(--td-switch-large-width,",
        [0, 104],
        ") - var(--td-switch-dot-large-size,",
        [0, 52],
        ") - var(--td-switch-dot-horizontal-margin,",
        [0, 6],
        "))}\n.",
        [1],
        "t-switch__dot--small.",
        [1],
        "t-switch__dot--checked{left:calc(var(--td-switch-small-width,",
        [0, 78],
        ") - var(--td-switch-dot-small-size,",
        [0, 36],
        ") - var(--td-switch-dot-horizontal-margin,",
        [0, 6],
        "))}\n.",
        [1],
        "t-switch__dot--plain:not(.",
        [1],
        "t-switch__dot--checked){height:var(--td-switch-dot-plain-size,",
        [0, 36],
        ");left:var(--td-switch-dot-plain-horizontal-margin,",
        [0, 10],
        ");width:var(--td-switch-dot-plain-size,",
        [0, 36],
        ")}\n.",
        [1],
        "t-switch__dot--large.",
        [1],
        "t-switch__dot--plain:not(.",
        [1],
        "t-switch__dot--checked){height:var(--td-switch-dot-plain-large-size,",
        [0, 44],
        ");width:var(--td-switch-dot-plain-large-size,",
        [0, 44],
        ")}\n.",
        [1],
        "t-switch__dot--small.",
        [1],
        "t-switch__dot--plain:not(.",
        [1],
        "t-switch__dot--checked){height:var(--td-switch-dot-plain-small-size,",
        [0, 28],
        ");width:var(--td-switch-dot-plain-small-size,",
        [0, 28],
        ")}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/switch/switch.wxss" }
    );
}
