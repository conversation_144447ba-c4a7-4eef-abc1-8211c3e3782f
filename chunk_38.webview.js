__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/overlay/overlay": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          e = (C, T, E, B, F, S) => {
            S("");
          },
          f = (C, T, E, B, F, S) => {
            S("");
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.prefix || U.transitionClass) || undefined)
                    L(
                      N,
                      Y(D.prefix) +
                        "-overlay " +
                        Y(D.transitionClass) +
                        " class"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!U.duration || undefined,
                        !!U._zIndex || undefined,
                        !!U.distanceTop || undefined,
                        U.computedStyle,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        "--td-overlay-transition-duration:" + D.duration + "ms",
                        "z-index:" + D._zIndex,
                        "top:" + D.distanceTop + "px",
                        D.computedStyle,
                        D.style,
                        D.customStyle,
                      ])
                    );
                  if (C) O(N, "catchtouchmove", "noop");
                  if (C || K || !!U.ariaRole || undefined)
                    O(N, "aria-role", D.ariaRole || "button");
                  if (C || K || !!U.ariaLabel || undefined)
                    O(N, "aria-label", D.ariaLabel || "关闭");
                  if (C) R.v(N, "tap", "handleClick", !1, !1, !1, !1);
                  if (C)
                    R.v(N, "transitionend", "onTransitionEnd", !1, !1, !1, !1);
                },
                e
              );
            } else if (c === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.prefix || U.transitionClass) || undefined)
                    L(
                      N,
                      Y(D.prefix) +
                        "-overlay " +
                        Y(D.transitionClass) +
                        " class"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!U._zIndex || undefined,
                        !!U.distanceTop || undefined,
                        U.computedStyle,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        "z-index:" + D._zIndex,
                        "top:" + D.distanceTop + "px",
                        D.computedStyle,
                        D.style,
                        D.customStyle,
                      ])
                    );
                  if (C || K || !!U.ariaRole || undefined)
                    O(N, "aria-role", D.ariaRole || "button");
                  if (C || K || !!U.ariaLabel || undefined)
                    O(N, "aria-label", D.ariaLabel || "关闭");
                  if (C) R.v(N, "tap", "handleClick", !1, !1, !1, !1);
                  if (C)
                    R.v(N, "transitionend", "onTransitionEnd", !1, !1, !1, !1);
                },
                f
              );
            }
          },
          b = (C, T, E, B) => {
            c =
              D.realVisible && D.preventScrollThrough
                ? 1
                : D.realVisible
                ? 2
                : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/overlay/overlay.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-overlay{background-color:var(--td-overlay-bg-color,var(--td-font-gray-2,rgba(0,0,0,.6)));bottom:0;left:0;position:fixed;top:0;transition-duration:var(--td-overlay-transition-duration,.3s);transition-property:opacity;transition-timing-function:ease;width:100%}\n.",
        [1],
        "t-fade-enter,.",
        [1],
        "t-fade-leave-to{opacity:0}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/overlay/overlay.wxss" }
    );
}
