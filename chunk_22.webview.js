__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/divider/divider": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            align: new Array(1),
            prefix: new Array(2),
            dashed: new Array(1),
            layout: new Array(2),
            customStyle: new Array(1),
            style: new Array(1),
            classPrefix: new Array(6),
            dividerStyle: new Array(1),
          },
          K = U === true,
          f,
          h = (C, T) => {
            C || K || U.content ? T(Y(D.content)) : T();
          },
          g = (C, T, E, B, F, S) => {
            if (f === 1) {
              E("view", {}, (N, C) => {}, h);
            } else {
              S("content");
            }
          },
          e = (C, T, E, B) => {
            f = D.content ? 1 : 0;
            B(f, g);
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(
                    N,
                    Y(D.prefix) +
                      "-class-content " +
                      Y(D.classPrefix) +
                      "__content"
                  );
                A["prefix"][1] = A["classPrefix"][5] = (D, E, T) => {
                  L(
                    N,
                    Y(D.prefix) +
                      "-class-content " +
                      Y(D.classPrefix) +
                      "__content"
                  );
                };
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.dashed;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.prefix ||
                    U.classPrefix ||
                    U.layout ||
                    U.classPrefix ||
                    U.align ||
                    U.dashed ||
                    ($A ? !!U.classPrefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      " class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.layout) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.align) +
                      " " +
                      Y($A ? D.classPrefix + "--dashed" : "")
                  );
                A["classPrefix"][1] =
                  A["prefix"][0] =
                  A["classPrefix"][2] =
                  A["layout"][1] =
                  A["classPrefix"][3] =
                  A["align"][0] =
                  A["dashed"][0] =
                  A["classPrefix"][4] =
                    (D, E, T) => {
                      var $B = D.dashed;
                      L(
                        N,
                        Y(D.classPrefix) +
                          " class " +
                          Y(D.prefix) +
                          "-class " +
                          Y(D.classPrefix) +
                          "--" +
                          Y(D.layout) +
                          " " +
                          Y(D.classPrefix) +
                          "--" +
                          Y(D.align) +
                          " " +
                          Y($B ? D.classPrefix + "--dashed" : "")
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([U.dividerStyle, U.style, U.customStyle])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([D.dividerStyle, D.style, D.customStyle])
                  );
                A["dividerStyle"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([D.dividerStyle, D.style, D.customStyle])
                      );
                    };
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.layout === "vertical";
                if (
                  C ||
                  K ||
                  !!U.layout ||
                  ($A ? !!U.classPrefix || undefined : undefined)
                )
                  L(N, $A ? D.classPrefix + "--vertical-center" : "");
                A["layout"][0] = A["classPrefix"][0] = (D, E, T) => {
                  var $B = D.layout === "vertical";
                  L(N, $B ? D.classPrefix + "--vertical-center" : "");
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/divider/divider.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-divider{border-color:var(--td-divider-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));border-style:var(--td-divider-content-line-style,solid);border-width:0;color:var(--td-divider-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));display:-webkit-flex;display:flex}\n.",
        [1],
        "t-divider::after,.",
        [1],
        "t-divider::before{border:inherit;border-color:inherit;border-style:inherit;box-sizing:border-box;content:\x22\x22;display:block;-webkit-flex:1;flex:1}\n.",
        [1],
        "t-divider--horizontal{-webkit-align-items:center;align-items:center;margin:var(--td-divider-horizontal-margin,",
        [0, 20],
        ") 0}\n.",
        [1],
        "t-divider--horizontal::after,.",
        [1],
        "t-divider--horizontal::before{border-top-width:var(--td-divider-border-width,",
        [0, 2],
        ");-webkit-transform:scaleY(.5);transform:scaleY(.5);-webkit-transform-origin:center;transform-origin:center}\n.",
        [1],
        "t-divider--horizontal .",
        [1],
        "t-divider__content:not(:empty){margin:0 var(--td-divider-content-margin,var(--td-spacer-1,",
        [0, 24],
        "))}\n.",
        [1],
        "t-divider--vertical{-webkit-flex-direction:column;flex-direction:column;height:",
        [0, 28],
        ";margin:0 var(--td-divider-vertical-margin,var(--td-spacer,",
        [0, 16],
        "))}\n.",
        [1],
        "t-divider--vertical::after,.",
        [1],
        "t-divider--vertical::before{border-left-width:var(--td-divider-border-width,",
        [0, 2],
        ");-webkit-transform:scaleX(.5);transform:scaleX(.5);-webkit-transform-origin:center;transform-origin:center}\n.",
        [1],
        "t-divider--vertical-center{-webkit-align-items:center;align-items:center;height:100%}\n.",
        [1],
        "t-divider--dashed{border-style:dashed}\n.",
        [1],
        "t-divider__content{color:var(--td-divider-content-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-divider-content-font-size,var(--td-font-size-s,",
        [0, 24],
        "));line-height:var(--td-divider-content-line-height,",
        [0, 40],
        ")}\n.",
        [1],
        "t-divider--left::before,.",
        [1],
        "t-divider--right::after{max-width:",
        [0, 60],
        "}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/divider/divider.wxss" }
    );
}
