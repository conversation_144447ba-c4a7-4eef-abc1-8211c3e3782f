__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/popup/popup": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/popup/popup"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          d,
          i,
          k = (C) => {},
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "name", "close");
                  if (C) O(N, "size", "64rpx");
                },
                k
              );
            }
          },
          h = (C, T, E, B, F, S) => {
            i = D.closeBtn ? 1 : 0;
            B(i, j);
            S("close-btn", (N) => {
              if (C || K || !!U.classPrefix || undefined)
                R.l(N, "class", Y(D.classPrefix) + "-slot");
            });
          },
          g = (C, T, E, B, F, S) => {
            S("content");
            S("");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__close");
                if (C) R.v(N, "tap", "handleClose", !1, !1, !1, !1);
              },
              h
            );
          },
          f = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                var $A = D.overlayProps;
                if (
                  C ||
                  K ||
                  !!(
                    U.preventScrollThrough ||
                    U.overlayProps ||
                    ($A
                      ? !!Z(U.overlayProps, "preventScrollThrough") || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  R.d(
                    N,
                    "prevention",
                    D.preventScrollThrough ||
                      ($A ? !!X(D.overlayProps).preventScrollThrough : false)
                  );
                if (C || K || Z(undefined, "onContentTouchMove"))
                  R.v(N, "touchmove", X(a).onContentTouchMove, !1, !1, !1, !0, [
                    1,
                    "miniprogram_npm/tdesign-miniprogram/popup/popup",
                    "onContentTouchMove",
                  ]);
              },
              g
            );
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.placement]) ||
                      U.transitionClass ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(P(X(b).cls)(D.classPrefix, [D.placement])) +
                        " " +
                        Y(D.transitionClass) +
                        " class " +
                        Y(D.prefix) +
                        "-class"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!(
                          Z(undefined, "getPopupStyles") ||
                          U.zIndex ||
                          U.distanceTop ||
                          U.placement
                        ) || undefined,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(b)._style)([
                        P(X(a).getPopupStyles)(
                          D.zIndex,
                          D.distanceTop,
                          D.placement
                        ),
                        D.style,
                        D.customStyle,
                      ])
                    );
                  if (C)
                    R.v(N, "transitionend", "onTransitionEnd", !1, !1, !1, !1);
                },
                f
              );
            }
          },
          l,
          n = (C) => {},
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "t-overlay",
                {},
                (N, C) => {
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C || K || U.usingCustomNavbar)
                    O(N, "usingCustomNavbar", D.usingCustomNavbar);
                  if (
                    C ||
                    K ||
                    !!(U.overlayProps || Z(U.overlayProps, "zIndex")) ||
                    undefined
                  )
                    O(
                      N,
                      "z-index",
                      (D.overlayProps && X(D.overlayProps).zIndex) || 11000
                    );
                  if (
                    C ||
                    K ||
                    !!(U.overlayProps || Z(U.overlayProps, "duration")) ||
                    undefined
                  )
                    O(
                      N,
                      "duration",
                      (D.overlayProps && X(D.overlayProps).duration) || 300
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      U.overlayProps || Z(U.overlayProps, "backgroundColor")
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "background-color",
                      (D.overlayProps && X(D.overlayProps).backgroundColor) ||
                        ""
                    );
                  var $A = D.overlayProps;
                  if (
                    C ||
                    K ||
                    !!(
                      U.preventScrollThrough ||
                      U.overlayProps ||
                      ($A
                        ? !!Z(U.overlayProps, "preventScrollThrough") ||
                          undefined
                        : undefined)
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "prevent-scroll-through",
                      D.preventScrollThrough ||
                        ($A ? !!X(D.overlayProps).preventScrollThrough : false)
                    );
                  if (
                    C ||
                    K ||
                    !!(U.overlayProps || Z(U.overlayProps, "style")) ||
                    undefined
                  )
                    O(
                      N,
                      "custom-style",
                      (D.overlayProps && X(D.overlayProps).style) || ""
                    );
                  if (C) R.v(N, "tap", "handleOverlayClick", !1, !1, !1, !1);
                  if (C) R.i(N, "popup-overlay");
                },
                n
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.realVisible ? 1 : 0;
            B(d, e);
            l = D.showOverlay ? 1 : 0;
            B(l, m);
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/popup/popup.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-popup{background-color:var(--td-popup-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));max-height:calc(100vh - var(--td-popup-distance-top,0));position:fixed;transition:all .3s ease;z-index:11500}\n.",
        [1],
        "t-popup__content{height:100%;position:relative;z-index:1}\n.",
        [1],
        "t-popup__close{color:var(--td-popup-close-btn-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));line-height:1;padding:",
        [0, 20],
        ";position:absolute;right:0;top:0}\n.",
        [1],
        "t-popup--top{border-bottom-left-radius:var(--td-popup-border-radius,var(--td-radius-extra-large,",
        [0, 24],
        "));border-bottom-right-radius:var(--td-popup-border-radius,var(--td-radius-extra-large,",
        [0, 24],
        "));left:0;top:0;width:100%}\n.",
        [1],
        "t-popup--bottom{border-top-left-radius:var(--td-popup-border-radius,var(--td-radius-extra-large,",
        [0, 24],
        "));border-top-right-radius:var(--td-popup-border-radius,var(--td-radius-extra-large,",
        [0, 24],
        "));bottom:0;left:0;padding-bottom:env(safe-area-inset-bottom);width:100%}\n.",
        [1],
        "t-popup--left{height:100%;left:0;top:0}\n.",
        [1],
        "t-popup--right{height:100%;right:0;top:0}\n.",
        [1],
        "t-popup--center{border-radius:var(--td-popup-border-radius,var(--td-radius-extra-large,",
        [0, 24],
        "));left:50%;top:50%;-webkit-transform:scale(1) translate3d(-50%,-50%,0);transform:scale(1) translate3d(-50%,-50%,0);-webkit-transform-origin:0 0;transform-origin:0 0}\n.",
        [1],
        "t-popup.",
        [1],
        "t-fade-enter.",
        [1],
        "t-popup--top,.",
        [1],
        "t-popup.",
        [1],
        "t-fade-leave-to.",
        [1],
        "t-popup--top{-webkit-transform:translateY(-100%);transform:translateY(-100%)}\n.",
        [1],
        "t-popup.",
        [1],
        "t-fade-enter.",
        [1],
        "t-popup--bottom,.",
        [1],
        "t-popup.",
        [1],
        "t-fade-leave-to.",
        [1],
        "t-popup--bottom{-webkit-transform:translateY(100%);transform:translateY(100%)}\n.",
        [1],
        "t-popup.",
        [1],
        "t-fade-enter.",
        [1],
        "t-popup--left,.",
        [1],
        "t-popup.",
        [1],
        "t-fade-leave-to.",
        [1],
        "t-popup--left{-webkit-transform:translateX(-100%);transform:translateX(-100%)}\n.",
        [1],
        "t-popup.",
        [1],
        "t-fade-enter.",
        [1],
        "t-popup--right,.",
        [1],
        "t-popup.",
        [1],
        "t-fade-leave-to.",
        [1],
        "t-popup--right{-webkit-transform:translateX(100%);transform:translateX(100%)}\n.",
        [1],
        "t-popup.",
        [1],
        "t-dialog-enter.",
        [1],
        "t-popup--center,.",
        [1],
        "t-popup.",
        [1],
        "t-dialog-leave-to.",
        [1],
        "t-popup--center,.",
        [1],
        "t-popup.",
        [1],
        "t-fade-enter.",
        [1],
        "t-popup--center,.",
        [1],
        "t-popup.",
        [1],
        "t-fade-leave-to.",
        [1],
        "t-popup--center{opacity:0;-webkit-transform:scale(.6) translate3d(-50%,-50%,0);transform:scale(.6) translate3d(-50%,-50%,0)}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/popup/popup.wxss" }
    );
}
