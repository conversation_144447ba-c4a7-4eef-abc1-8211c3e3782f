__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/dongjie/dongjie": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b = (C, T) => {
            C ? T("您的账号已被冻结") : T();
          },
          d = (C) => {},
          c = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) O(N, "src", "/assets/icon/dongjie.png");
              },
              d
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "title");
              },
              b
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loing_img");
              },
              c
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/dongjie/dongjie.wxss"] = setCssToHead(
    [
      "body{-webkit-align-items:center;align-items:center;background-color:#fff;box-sizing:border-box;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;height:100vh;-webkit-justify-content:center;justify-content:center;padding:0 ",
      [0, 60],
      "}\n.",
      [1],
      "title{color:#000;font-size:",
      [0, 48],
      ";font-weight:700;margin-bottom:",
      [0, 80],
      ";text-align:center}\n.",
      [1],
      "loing_img{margin-bottom:",
      [0, 60],
      ";text-align:center}\n.",
      [1],
      "loing_img wx-image{border-radius:",
      [0, 20],
      ";height:",
      [0, 300],
      ";width:",
      [0, 300],
      "}\n.",
      [1],
      "miaoshu{color:#ff9500;font-size:",
      [0, 28],
      ";line-height:1.5;text-align:center}\n",
    ],
    "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/dongjie/dongjie.wxss:1:413)",
    { path: "./pages/dongjie/dongjie.wxss" }
  );
}
