__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/footer/footer": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            customStyle: new Array(1),
            prefix: new Array(1),
          },
          K = U === true,
          d,
          g,
          i = (C) => {},
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    O(N, "t-class", Y(D.classPrefix) + "__icon");
                  if (C || K || Z(U.logo, "icon")) O(N, "src", X(D.logo).icon);
                },
                i
              );
            }
          },
          j,
          l = (C, T) => {
            C || K || Z(U.logo, "title") ? T(Y(X(D.logo).title)) : T();
          },
          m = (C) => {},
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__title");
                },
                l
              );
            } else if (j === 2) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    O(N, "t-class", Y(D.classPrefix) + "__title-url");
                  if (C || K || Z(U.logo, "url")) O(N, "src", X(D.logo).url);
                  if (C) O(N, "mode", "widthFix");
                },
                m
              );
            }
          },
          f = (C, T, E, B) => {
            g = X(D.logo).icon ? 1 : 0;
            B(g, h);
            j = X(D.logo).title ? 1 : X(D.logo).url ? 2 : 0;
            B(j, k);
          },
          n,
          q = (C, r, s, t, u, v, T, E, B) => {
            var w = (C, T) => {
                C || K || Z(t, "name") ? T(Y(X(r).name)) : T();
              },
              x,
              z = (C, T) => {
                C ? T("|") : T();
              },
              y = (C, T, E) => {
                if (x === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__link-line");
                      if (C || K || undefined) O(N, "aria-hidden", true);
                    },
                    z
                  );
                }
              };
            E(
              "navigator",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__link-item");
                if (C || K || Z(t, "url")) O(N, "url", X(r).url);
                if (C || K || Z(t, "openType"))
                  O(N, "open-type", X(r).openType);
                if (C) O(N, "hover-class", "none");
              },
              w
            );
            x = s !== X(D.links).length - 1 ? 1 : 0;
            B(x, y);
          },
          p = (C, T, E, B, F) => {
            F(D.links, "name", U ? U.links : undefined, [0, "links"], q);
          },
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__link-list");
                },
                p
              );
            }
          },
          r = (C, T) => {
            C || K || U.text ? T(Y(D.text)) : T();
          },
          e = (C, T, E, B) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__logo");
                },
                f
              );
            } else {
              n = X(D.links).length > 0 ? 1 : 0;
              B(n, o);
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__text");
                },
                r
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.logo ? 1 : 0;
            B(d, e);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["prefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/footer/footer.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-footer{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:flex-start;justify-content:flex-start}\n.",
        [1],
        "t-footer__text{color:var(--td-footer-text-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-footer-text-font-size,var(--td-font-size-s,",
        [0, 24],
        "));line-height:var(--td-footer-text-line-height,",
        [0, 40],
        ")}\n.",
        [1],
        "t-footer__link-list+.",
        [1],
        "t-footer__text:not(:empty){margin-top:var(--td-footer-text-margin-top,",
        [0, 8],
        ")}\n.",
        [1],
        "t-footer__link-list{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
        [1],
        "t-footer__link-item{color:var(--td-footer-link-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));font-size:var(--td-footer-link-font-size,var(--td-font-size-s,",
        [0, 24],
        "));line-height:var(--td-footer-link-line-height,",
        [0, 40],
        ");text-decoration:underline}\n.",
        [1],
        "t-footer__link-line{color:var(--td-footer-link-dividing-line-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));display:inline-block;font-size:",
        [0, 24],
        ";padding:0 var(--td-footer-link-dividing-line-padding,var(--td-spacer-1,",
        [0, 24],
        "))}\n.",
        [1],
        "t-footer__logo{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
        [1],
        "t-footer__icon{height:var(--td-footer-logo-icon-height,",
        [0, 48],
        ");margin-right:var(--td-footer-logo-icon-margin-right,var(--td-spacer,",
        [0, 16],
        "));width:var(--td-footer-logo-icon-width,",
        [0, 48],
        ")}\n.",
        [1],
        "t-footer__title{color:var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9)));font-size:var(--td-footer-logo-title-font-size,var(--td-font-size-m,",
        [0, 32],
        "));font-style:italic;font-weight:700;line-height:var(--td-footer-logo-title-line-height,",
        [0, 48],
        ")}\n.",
        [1],
        "t-footer__title-url{width:var(--td-footer-logo-title-url-width,",
        [0, 256],
        ")}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/footer/footer.wxss" }
    );
}
