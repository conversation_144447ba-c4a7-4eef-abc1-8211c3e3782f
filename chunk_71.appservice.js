__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/my-order-list/my-order-list": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["pages/my-order-list/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { serviceInfo: new Array(1) },
          K = U === true,
          d = (C) => {},
          c = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || !!Z(U.serviceInfo, "service_name") || undefined)
                  O(N, "title", X(D.serviceInfo).service_name || "预约记录");
                A["serviceInfo"][0] = (D, E, T) => {
                  O(N, "title", X(D.serviceInfo).service_name || "预约记录");
                  E(N);
                };
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              d
            );
          },
          h = (C) => {},
          i = (C) => {},
          j = (C) => {},
          k = (C) => {},
          g = (C, T, E) => {
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已下单");
                if (C) O(N, "value", "0");
              },
              h
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已接单");
                if (C) O(N, "value", "1");
              },
              i
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已完成");
                if (C) O(N, "value", "2");
              },
              j
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已取消");
                if (C) O(N, "value", "3");
              },
              k
            );
          },
          f = (C, T, E) => {
            E(
              "t-tabs",
              {},
              (N, C) => {
                if (C || K || undefined) O(N, "defaultValue", 0);
                if (C) O(N, "t-class", "custom-tabs");
                if (C) O(N, "t-class-track", "custom-track");
                if (C) R.v(N, "change", "onTabsChange", !1, !1, !1, !1);
                if (C) R.v(N, "click", "onTabsClick", !1, !1, !1, !1);
              },
              g
            );
          },
          l,
          o = (C) => {},
          n = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "40rpx");
                if (C) O(N, "text", "加载中...");
                if (C) O(N, "layout", "vertical");
              },
              o
            );
          },
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_container");
                },
                n
              );
            }
          },
          p,
          s = (C, t, u, v, w, x, T, E) => {
            var B0 = (C) => {},
              D0 = (C, T) => {
                C || K || Z(Z(v, "service_provider"), "nickname")
                  ? T(Y(X(X(t).service_provider).nickname))
                  : T();
              },
              C0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_name");
                  },
                  D0
                );
              },
              A0 = (C, T, E) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_avatar");
                    if (
                      C ||
                      K ||
                      !!Z(Z(v, "service_provider"), "avatar") ||
                      undefined
                    )
                      O(
                        N,
                        "src",
                        X(X(t).service_provider).avatar ||
                          "/assets/tx/短发职业女.png"
                      );
                  },
                  B0
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_details");
                  },
                  C0
                );
              },
              F0 = (C, T) => {
                var $A = X(t).order_status;
                C ||
                K ||
                !!Z(v, "order_status") ||
                Z(Z(U.statusMap, $A), "text")
                  ? T(Y(X(X(D.statusMap)[$A]).text))
                  : T();
              },
              E0 = (C, T, E) => {
                E(
                  "t-tag",
                  {},
                  (N, C) => {
                    if (C) O(N, "variant", "light");
                    var $A = X(t).order_status;
                    if (
                      C ||
                      K ||
                      !!Z(v, "order_status") ||
                      Z(Z(U.statusMap, $A), "theme")
                    )
                      O(N, "theme", X(X(D.statusMap)[$A]).theme);
                  },
                  F0
                );
              },
              z = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_info");
                  },
                  A0
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_status");
                  },
                  E0
                );
              },
              I0 = (C, T) => {
                C ? T("下单时间：") : T();
              },
              J0 = (C, T) => {
                C ||
                K ||
                !!(Z(undefined, "formatTime") || Z(v, "order_time")) ||
                undefined
                  ? T(Y(P(X(a).formatTime)(X(t).order_time)))
                  : T();
              },
              H0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  I0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  J0
                );
              },
              K0,
              N0 = (C, T) => {
                C ? T("地址：") : T();
              },
              O0 = (C, T) => {
                C ||
                K ||
                !!(
                  Z(Z(Z(v, "customer"), "address"), "building") ||
                  Z(Z(Z(v, "customer"), "address"), "unit") ||
                  Z(Z(Z(v, "customer"), "address"), "room")
                ) ||
                undefined
                  ? T(
                      Y(
                        Y(X(X(X(t).customer).address).building) +
                          "-" +
                          Y(X(X(X(t).customer).address).unit) +
                          "-" +
                          Y(X(X(X(t).customer).address).room)
                      )
                    )
                  : T();
              },
              M0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  N0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  O0
                );
              },
              L0 = (C, T, E) => {
                if (K0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    M0
                  );
                }
              },
              P0,
              S0 = (C, T) => {
                C ? T("接单时间：") : T();
              },
              T0 = (C, T) => {
                C || K || !!(U.formatTime || Z(v, "accept_time")) || undefined
                  ? T(Y(P(D.formatTime)(X(t).accept_time)))
                  : T();
              },
              R0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  S0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  T0
                );
              },
              Q0 = (C, T, E) => {
                if (P0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    R0
                  );
                }
              },
              U0,
              X0 = (C, T) => {
                C ? T("完成时间：") : T();
              },
              Y0 = (C, T) => {
                C || K || !!(U.formatTime || Z(v, "complete_time")) || undefined
                  ? T(Y(P(D.formatTime)(X(t).complete_time)))
                  : T();
              },
              W0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  X0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  Y0
                );
              },
              V0 = (C, T, E) => {
                if (U0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    W0
                  );
                }
              },
              G0 = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "detail_row");
                  },
                  H0
                );
                K0 = X(X(t).customer).address ? 1 : 0;
                B(K0, L0);
                P0 = X(t).accept_time ? 1 : 0;
                B(P0, Q0);
                U0 = X(t).complete_time ? 1 : 0;
                B(U0, V0);
              },
              Z0,
              d0 = (C, T) => {
                C || K || Z(v, "remark") ? T(Y(X(t).remark)) : T();
              },
              c0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "content_text");
                  },
                  d0
                );
              },
              b0 = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "content_box");
                  },
                  c0
                );
              },
              a0 = (C, T, E) => {
                if (Z0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "order_content");
                    },
                    b0
                  );
                }
              },
              e0,
              i0 = (C, T) => {
                C ? T("联系客户") : T();
              },
              h0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, i0);
              },
              j0,
              m0 = (C, T) => {
                C ? T("取消订单") : T();
              },
              l0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, m0);
              },
              k0 = (C, T, E) => {
                if (j0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "quxiao-btn");
                      if (C || K || Z(v, "order_id"))
                        R.d(N, "orderId", X(t).order_id);
                      if (C) R.v(N, "tap", "onCancelOrder", !1, !1, !1, !1);
                    },
                    l0
                  );
                }
              },
              n0,
              q0 = (C, T) => {
                C ? T("确认完成") : T();
              },
              p0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, q0);
              },
              o0 = (C, T, E) => {
                if (n0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "confirm-btn");
                      if (C || K || Z(v, "order_id"))
                        R.d(N, "orderId", X(t).order_id);
                      if (C) R.v(N, "tap", "onCompleteOrder", !1, !1, !1, !1);
                    },
                    p0
                  );
                }
              },
              g0 = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "contact-btn");
                    if (C || K || Z(v, "order_id"))
                      R.d(N, "orderId", X(t).order_id);
                    if (C || K || Z(Z(v, "customer"), "contact_info"))
                      R.d(N, "phone", X(X(t).customer).contact_info);
                    if (C) R.v(N, "tap", "onContactCustomer", !1, !1, !1, !1);
                  },
                  h0
                );
                j0 = X(t).order_status === "ordered" ? 1 : 0;
                B(j0, k0);
                n0 = X(t).order_status === "accepted" ? 1 : 0;
                B(n0, o0);
              },
              f0 = (C, T, E) => {
                if (e0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "fwdd_list_bottom");
                    },
                    g0
                  );
                }
              },
              y = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_header");
                  },
                  z
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_details");
                  },
                  G0
                );
                Z0 = X(t).remark ? 1 : 0;
                B(Z0, a0);
                e0 =
                  X(t).order_status === "ordered" ||
                  X(t).order_status === "accepted"
                    ? 1
                    : 0;
                B(e0, f0);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "order_card");
              },
              y
            );
          },
          t,
          w = (C) => {},
          v = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "40rpx");
                if (C) O(N, "text", "加载更多...");
                if (C) O(N, "layout", "horizontal");
              },
              w
            );
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_more");
                },
                v
              );
            }
          },
          x,
          A0 = (C, T) => {
            C ? T("没有更多数据了") : T();
          },
          z = (C, T, E) => {
            E("text", {}, (N, C) => {}, A0);
          },
          y = (C, T, E) => {
            if (x === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "no_more");
                },
                z
              );
            }
          },
          B0,
          E0 = (C, T) => {
            C ? T("暂无订单") : T();
          },
          D0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "empty_text");
              },
              E0
            );
          },
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty_container");
                },
                D0
              );
            }
          },
          r = (C, T, E, B, F) => {
            F(
              D.filteredOrderList,
              "order_id",
              U ? U.filteredOrderList : undefined,
              [0, "filteredOrderList"],
              s
            );
            t = D.loadingMore ? 1 : 0;
            B(t, u);
            var $A = D.currentTab;
            x =
              !X(X(D.pagination)[$A]).hasMore &&
              X(D.filteredOrderList).length > 0
                ? 1
                : 0;
            B(x, y);
            B0 = D.isEmpty ? 1 : 0;
            B(B0, C0);
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                r
              );
            }
          },
          e = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "tags_box");
              },
              f
            );
            l = D.loading ? 1 : 0;
            B(l, m);
            p = !D.loading ? 1 : 0;
            B(p, q);
          },
          b = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, c);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              e
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/my-order-list/my-order-list";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/my-order-list/my-order-list.js";
define(
  "pages/my-order-list/my-order-list.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      t = require("../../@babel/runtime/helpers/toConsumableArray"),
      r = require("../../@babel/runtime/helpers/defineProperty"),
      a = require("../../@babel/runtime/helpers/asyncToGenerator");
    Page({
      data: {
        serviceId: "",
        currentTab: "ordered",
        orderList: [],
        filteredOrderList: [],
        loading: !0,
        isEmpty: !1,
        loadingMore: !1,
        hasMore: !0,
        pagination: {
          all: { page: 1, hasMore: !0, loading: !1 },
          ordered: { page: 1, hasMore: !0, loading: !1 },
          accepted: { page: 1, hasMore: !0, loading: !1 },
          completed: { page: 1, hasMore: !0, loading: !1 },
          cancelled: { page: 1, hasMore: !0, loading: !1 },
        },
        statusOrderLists: {
          all: [],
          ordered: [],
          accepted: [],
          completed: [],
          cancelled: [],
        },
        statusMap: {
          ordered: {
            text: "已下单",
            theme: "primary",
            showActions: ["contact", "accept"],
          },
          accepted: {
            text: "已接单",
            theme: "warning",
            showActions: ["contact", "complete"],
          },
          completed: {
            text: "已完成",
            theme: "success",
            showActions: ["contact"],
          },
          cancelled: { text: "已取消", theme: "default", showActions: [] },
        },
      },
      onLoad: function (e) {
        console.log("Service Order List Page Loaded", e),
          e.serviceId
            ? (this.setData({ serviceId: e.serviceId, currentTab: "ordered" }),
              this.loadOrderList())
            : (wx.showToast({ title: "服务ID不存在", icon: "error" }),
              setTimeout(function () {
                wx.navigateBack();
              }, 1500));
      },
      loadOrderList: function () {
        var n = arguments,
          s = this;
        return a(
          e().mark(function a() {
            var o, c, i, d, l, u, p, h, g, f, w, x, m, b, v, k, L;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      (o = n.length > 0 && void 0 !== n[0] && n[0]),
                        (c = getApp()),
                        (i = s.data),
                        (d = i.currentTab),
                        (l = i.pagination),
                        (u = d),
                        (p = null),
                        (e.t0 = d),
                        (e.next =
                          "ordered" === e.t0
                            ? 8
                            : "accepted" === e.t0
                            ? 10
                            : "completed" === e.t0
                            ? 12
                            : "cancelled" === e.t0
                            ? 14
                            : 16);
                      break;
                    case 8:
                      return (p = "ordered"), e.abrupt("break", 16);
                    case 10:
                      return (p = "accepted"), e.abrupt("break", 16);
                    case 12:
                      return (p = "completed"), e.abrupt("break", 16);
                    case 14:
                      return (p = "cancelled"), e.abrupt("break", 16);
                    case 16:
                      if (!o) {
                        e.next = 22;
                        break;
                      }
                      if (l[u].hasMore) {
                        e.next = 19;
                        break;
                      }
                      return e.abrupt("return");
                    case 19:
                      s.setData(
                        r(
                          { loadingMore: !0 },
                          "pagination.".concat(u, ".loading"),
                          !0
                        )
                      ),
                        (e.next = 23);
                      break;
                    case 22:
                      s.setData(
                        (r(
                          (h = { loading: !0 }),
                          "pagination.".concat(u, ".page"),
                          1
                        ),
                        r(h, "pagination.".concat(u, ".hasMore"), !0),
                        r(h, "statusOrderLists.".concat(u), []),
                        h)
                      );
                    case 23:
                      if (
                        ((e.prev = 23), (g = c.globalData.userInfo.data.id))
                      ) {
                        e.next = 27;
                        break;
                      }
                      throw new Error("用户未登录");
                    case 27:
                      return (
                        (f = { user_id: g, page: l[u].page, pageSize: 10 }),
                        p && (f.order_status = p),
                        (e.next = 31),
                        c.call({
                          path: "/api/mp/serviceOrder/user/"
                            .concat(g, "/service/")
                            .concat(s.data.serviceId),
                          method: "GET",
                          data: f,
                        })
                      );
                    case 31:
                      if (!(w = e.sent) || 0 !== w.code) {
                        e.next = 41;
                        break;
                      }
                      (m = w.data.orders || []),
                        (b = w.data.pagination || {}),
                        (v = b.has_next_page || !1),
                        (k = o
                          ? [].concat(t(s.data.statusOrderLists[u]), t(m))
                          : m),
                        s.setData(
                          (r((x = {}), "statusOrderLists.".concat(u), k),
                          r(x, "pagination.".concat(u, ".hasMore"), v),
                          r(
                            x,
                            "pagination.".concat(u, ".page"),
                            b.current_page + 1
                          ),
                          r(x, "filteredOrderList", k),
                          r(x, "loading", !1),
                          r(x, "loadingMore", !1),
                          r(x, "pagination.".concat(u, ".loading"), !1),
                          r(x, "isEmpty", 0 === k.length),
                          x)
                        ),
                        console.log("".concat(u, " 订单列表获取成功:"), k),
                        (e.next = 42);
                      break;
                    case 41:
                      throw new Error(w.message || "获取订单列表失败");
                    case 42:
                      e.next = 49;
                      break;
                    case 44:
                      (e.prev = 44),
                        (e.t1 = e.catch(23)),
                        console.error("获取订单列表失败：", e.t1),
                        s.setData(
                          (r(
                            (L = { loading: !1, loadingMore: !1 }),
                            "pagination.".concat(u, ".loading"),
                            !1
                          ),
                          r(
                            L,
                            "isEmpty",
                            0 === s.data.statusOrderLists[u].length
                          ),
                          L)
                        ),
                        wx.showToast({
                          title: e.t1.message || "加载失败",
                          icon: "error",
                        });
                    case 49:
                    case "end":
                      return e.stop();
                  }
              },
              a,
              null,
              [[23, 44]]
            );
          })
        )();
      },
      onTabsChange: function (e) {
        var t = "all";
        switch (e.detail.value) {
          case "0":
            t = "ordered";
            break;
          case "1":
            t = "accepted";
            break;
          case "2":
            t = "completed";
            break;
          case "3":
            t = "cancelled";
        }
        this.setData({ currentTab: t }),
          0 === this.data.statusOrderLists[t].length
            ? this.loadOrderList()
            : this.setData({
                filteredOrderList: this.data.statusOrderLists[t],
                isEmpty: 0 === this.data.statusOrderLists[t].length,
              });
      },
      onTabsClick: function (e) {},
      filterOrdersByTab: function (e) {
        var t = this.data.orderList,
          r = [];
        switch (e) {
          case "all":
            r = t;
            break;
          case "ongoing":
            r = t.filter(function (e) {
              return (
                "ordered" === e.order_status || "accepted" === e.order_status
              );
            });
            break;
          case "completed":
            r = t.filter(function (e) {
              return (
                "completed" === e.order_status || "cancelled" === e.order_status
              );
            });
        }
        this.setData({ filteredOrderList: r, isEmpty: 0 === r.length });
      },
      getOrderStatusInfo: function (e) {
        return (
          this.data.statusMap[e] || {
            text: "未知状态",
            theme: "default",
            showActions: [],
          }
        );
      },
      onContactCustomer: function (e) {
        var t = e.currentTarget.dataset.phone;
        t
          ? wx.makePhoneCall({
              phoneNumber: t,
              fail: function (e) {
                console.error("拨打电话失败：", e),
                  wx.showToast({ title: "拨打失败", icon: "error" });
              },
            })
          : wx.showToast({ title: "客户电话不存在", icon: "none" });
      },
      onCompleteOrder: function (t) {
        var r = this;
        return a(
          e().mark(function n() {
            var s, o;
            return e().wrap(function (n) {
              for (;;)
                switch ((n.prev = n.next)) {
                  case 0:
                    (s = t.currentTarget.dataset.orderId),
                      (o = getApp()),
                      wx.showModal({
                        title: "确认完成",
                        content: "请输入完成备注（可选）",
                        editable: !0,
                        placeholderText: "服务已按时完成，质量满意",
                        success: (function () {
                          var t = a(
                            e().mark(function t(a) {
                              var n, c;
                              return e().wrap(
                                function (e) {
                                  for (;;)
                                    switch ((e.prev = e.next)) {
                                      case 0:
                                        if (!a.confirm) {
                                          e.next = 25;
                                          break;
                                        }
                                        if (
                                          ((e.prev = 1),
                                          wx.showLoading({
                                            title: "处理中...",
                                          }),
                                          (n = o.getUserData()) && n.data.id)
                                        ) {
                                          e.next = 6;
                                          break;
                                        }
                                        throw new Error("无法获取用户信息");
                                      case 6:
                                        return (
                                          (e.next = 8),
                                          o.call({
                                            path: "/api/mp/serviceOrder/".concat(
                                              s,
                                              "/complete"
                                            ),
                                            method: "PUT",
                                            data: {
                                              user_id: n.data.id,
                                              completion_note: a.content || "",
                                            },
                                          })
                                        );
                                      case 8:
                                        if (!(c = e.sent) || 0 !== c.code) {
                                          e.next = 15;
                                          break;
                                        }
                                        return (
                                          wx.showToast({
                                            title: "订单已完成",
                                            icon: "success",
                                          }),
                                          (e.next = 13),
                                          r.loadOrderList()
                                        );
                                      case 13:
                                        e.next = 16;
                                        break;
                                      case 15:
                                        throw new Error(
                                          c.message || "操作失败"
                                        );
                                      case 16:
                                        e.next = 22;
                                        break;
                                      case 18:
                                        (e.prev = 18),
                                          (e.t0 = e.catch(1)),
                                          console.error("完成订单失败：", e.t0),
                                          wx.showToast({
                                            title: e.t0.message || "操作失败",
                                            icon: "error",
                                          });
                                      case 22:
                                        return (
                                          (e.prev = 22),
                                          wx.hideLoading(),
                                          e.finish(22)
                                        );
                                      case 25:
                                      case "end":
                                        return e.stop();
                                    }
                                },
                                t,
                                null,
                                [[1, 18, 22, 25]]
                              );
                            })
                          );
                          return function (e) {
                            return t.apply(this, arguments);
                          };
                        })(),
                      });
                  case 3:
                  case "end":
                    return n.stop();
                }
            }, n);
          })
        )();
      },
      onCancelOrder: function (t) {
        var r = this;
        return a(
          e().mark(function n() {
            var s, o;
            return e().wrap(function (n) {
              for (;;)
                switch ((n.prev = n.next)) {
                  case 0:
                    (s = t.currentTarget.dataset.orderId),
                      (o = getApp()),
                      wx.showModal({
                        title: "确认取消",
                        content: "",
                        editable: !0,
                        placeholderText: "请输入取消原因（选填）",
                        success: (function () {
                          var t = a(
                            e().mark(function t(a) {
                              var n, c;
                              return e().wrap(
                                function (e) {
                                  for (;;)
                                    switch ((e.prev = e.next)) {
                                      case 0:
                                        if (!a.confirm) {
                                          e.next = 25;
                                          break;
                                        }
                                        if (
                                          ((e.prev = 1),
                                          wx.showLoading({
                                            title: "处理中...",
                                          }),
                                          (n = o.getUserData()) && n.data.id)
                                        ) {
                                          e.next = 6;
                                          break;
                                        }
                                        throw new Error("无法获取用户信息");
                                      case 6:
                                        return (
                                          (e.next = 8),
                                          o.call({
                                            path: "/api/mp/serviceOrder/".concat(
                                              s,
                                              "/cancel"
                                            ),
                                            method: "PUT",
                                            data: {
                                              user_id: n.data.id,
                                              cancel_reason: a.content || "",
                                            },
                                          })
                                        );
                                      case 8:
                                        if (!(c = e.sent) || 0 !== c.code) {
                                          e.next = 15;
                                          break;
                                        }
                                        return (
                                          wx.showToast({
                                            title: "订单已取消",
                                            icon: "success",
                                          }),
                                          (e.next = 13),
                                          r.loadOrderList()
                                        );
                                      case 13:
                                        e.next = 16;
                                        break;
                                      case 15:
                                        throw new Error(
                                          c.message || "取消失败"
                                        );
                                      case 16:
                                        e.next = 22;
                                        break;
                                      case 18:
                                        (e.prev = 18),
                                          (e.t0 = e.catch(1)),
                                          console.error("取消订单失败：", e.t0),
                                          wx.showToast({
                                            title: e.t0.message || "取消失败",
                                            icon: "error",
                                          });
                                      case 22:
                                        return (
                                          (e.prev = 22),
                                          wx.hideLoading(),
                                          e.finish(22)
                                        );
                                      case 25:
                                      case "end":
                                        return e.stop();
                                    }
                                },
                                t,
                                null,
                                [[1, 18, 22, 25]]
                              );
                            })
                          );
                          return function (e) {
                            return t.apply(this, arguments);
                          };
                        })(),
                      });
                  case 3:
                  case "end":
                    return n.stop();
                }
            }, n);
          })
        )();
      },
      updateOrderStatus: function (t, r, n) {
        var s = this;
        return a(
          e().mark(function a() {
            var o, c;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return (
                        (o = getApp()),
                        (e.prev = 1),
                        wx.showLoading({ title: "处理中..." }),
                        (e.next = 5),
                        o.call({
                          path: "/api/mp/serviceOrder/".concat(t, "/status"),
                          method: "PUT",
                          data: { order_status: r },
                        })
                      );
                    case 5:
                      if (!(c = e.sent) || 0 !== c.code) {
                        e.next = 12;
                        break;
                      }
                      return (
                        wx.showToast({ title: n, icon: "success" }),
                        (e.next = 10),
                        s.loadOrderList()
                      );
                    case 10:
                      e.next = 13;
                      break;
                    case 12:
                      throw new Error(c.message || "操作失败");
                    case 13:
                      e.next = 19;
                      break;
                    case 15:
                      (e.prev = 15),
                        (e.t0 = e.catch(1)),
                        console.error("更新订单状态失败：", e.t0),
                        wx.showToast({
                          title: e.t0.message || "操作失败",
                          icon: "error",
                        });
                    case 19:
                      return (e.prev = 19), wx.hideLoading(), e.finish(19);
                    case 22:
                    case "end":
                      return e.stop();
                  }
              },
              a,
              null,
              [[1, 15, 19, 22]]
            );
          })
        )();
      },
      formatTime: function (e) {
        if (!e) return "";
        var t = new Date(e),
          r = t.getFullYear(),
          a = String(t.getMonth() + 1).padStart(2, "0"),
          n = String(t.getDate()).padStart(2, "0"),
          s = String(t.getHours()).padStart(2, "0"),
          o = String(t.getMinutes()).padStart(2, "0");
        return ""
          .concat(r, "-")
          .concat(a, "-")
          .concat(n, " ")
          .concat(s, ":")
          .concat(o);
      },
      onPullDownRefresh: function () {
        this.loadOrderList().finally(function () {
          wx.stopPullDownRefresh();
        });
      },
      onReachBottom: function () {
        var e = this.data,
          t = e.currentTab,
          r = e.pagination;
        r[t].hasMore && !r[t].loading && this.loadOrderList(!0);
      },
      onShow: function () {
        this.data.serviceId && this.loadOrderList();
      },
    });
  },
  {
    isPage: true,
    isComponent: true,
    currentFile: "pages/my-order-list/my-order-list.js",
  }
);
require("pages/my-order-list/my-order-list.js");
