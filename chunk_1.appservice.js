__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "components/navigation-bar/navigation-bar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            innerPaddingRight: new Array(1),
            color: new Array(1),
            safeAreaTop: new Array(1),
            background: new Array(1),
            extClass: new Array(1),
            ios: new Array(1),
            leftWidth: new Array(1),
            displayStyle: new Array(1),
          },
          K = U === true,
          e,
          g,
          k = (C) => {},
          j = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C)
                  L(
                    N,
                    "weui-navigation-bar__button weui-navigation-bar__btn_goback"
                  );
              },
              k
            );
          },
          i = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-navigation-bar__btn_goback_wrapper");
                if (C) O(N, "bindtap", "back");
                if (C) O(N, "hover-class", "weui-active");
                if (C) O(N, "hover-stay-time", "100");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "返回");
              },
              j
            );
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C)
                    L(
                      N,
                      "weui-navigation-bar__buttons weui-navigation-bar__buttons_goback"
                    );
                },
                i
              );
            }
          },
          l,
          p = (C) => {},
          o = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C)
                  L(
                    N,
                    "weui-navigation-bar__button weui-navigation-bar__btn_home"
                  );
              },
              p
            );
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-navigation-bar__btn_home_wrapper");
                if (C) O(N, "bindtap", "home");
                if (C) O(N, "hover-class", "weui-active");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "首页");
              },
              o
            );
          },
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C)
                    L(
                      N,
                      "weui-navigation-bar__buttons weui-navigation-bar__buttons_home"
                    );
                },
                n
              );
            }
          },
          f = (C, T, E, B, F, S) => {
            if (e === 1) {
              g = D.back ? 1 : 0;
              B(g, h);
              l = D.homeButton ? 1 : 0;
              B(l, m);
            } else {
              S("left");
            }
          },
          d = (C, T, E, B) => {
            e = D.back || D.homeButton ? 1 : 0;
            B(e, f);
          },
          r,
          u = (C) => {},
          t = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-loading");
                if (C) O(N, "aria-role", "img");
                if (C) O(N, "aria-label", "加载中");
              },
              u
            );
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "weui-navigation-bar__loading");
                  if (C) O(N, "aria-role", "alert");
                },
                t
              );
            }
          },
          v,
          x = (C, T) => {
            C || K || U.title ? T(Y(D.title)) : T();
          },
          w = (C, T, E, B, F, S) => {
            if (v === 1) {
              E("text", {}, (N, C) => {}, x);
            } else {
              S("center");
            }
          },
          q = (C, T, E, B) => {
            r = D.loading ? 1 : 0;
            B(r, s);
            v = D.title ? 1 : 0;
            B(v, w);
          },
          y = (C, T, E, B, F, S) => {
            S("right");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-navigation-bar__left");
                if (C || K || !!U.leftWidth || undefined)
                  R.y(N, Y(D.leftWidth) + ";");
                A["leftWidth"][0] = (D, E, T) => {
                  R.y(N, Y(D.leftWidth) + ";");
                };
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-navigation-bar__center");
              },
              q
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-navigation-bar__right");
              },
              y
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.ios;
                if (
                  C ||
                  K ||
                  !!(U.ios || ($A ? undefined : undefined)) ||
                  undefined
                )
                  L(
                    N,
                    "weui-navigation-bar__inner " + Y($A ? "ios" : "android")
                  );
                A["ios"][0] = (D, E, T) => {
                  var $B = D.ios;
                  L(
                    N,
                    "weui-navigation-bar__inner " + Y($B ? "ios" : "android")
                  );
                };
                if (
                  C ||
                  K ||
                  !!(
                    U.color ||
                    U.background ||
                    U.displayStyle ||
                    U.innerPaddingRight ||
                    U.safeAreaTop
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    "color:" +
                      Y(D.color) +
                      ";background:" +
                      Y(D.background) +
                      ";" +
                      Y(D.displayStyle) +
                      ";;;;" +
                      Y(D.innerPaddingRight) +
                      ";;;" +
                      Y(D.safeAreaTop) +
                      ";;"
                  );
                A["color"][0] =
                  A["background"][0] =
                  A["displayStyle"][0] =
                  A["innerPaddingRight"][0] =
                  A["safeAreaTop"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        "color:" +
                          Y(D.color) +
                          ";background:" +
                          Y(D.background) +
                          ";" +
                          Y(D.displayStyle) +
                          ";;;;" +
                          Y(D.innerPaddingRight) +
                          ";;;" +
                          Y(D.safeAreaTop) +
                          ";;"
                      );
                    };
              },
              c
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.extClass || undefined)
                  L(N, "weui-navigation-bar " + Y(D.extClass));
                A["extClass"][0] = (D, E, T) => {
                  L(N, "weui-navigation-bar " + Y(D.extClass));
                };
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "components/navigation-bar/navigation-bar";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "components/navigation-bar/navigation-bar.js";
define(
  "components/navigation-bar/navigation-bar.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Component({
      options: { multipleSlots: !0 },
      properties: {
        extClass: { type: String, value: "" },
        title: { type: String, value: "" },
        background: { type: String, value: "" },
        color: { type: String, value: "" },
        back: { type: Boolean, value: !0 },
        loading: { type: Boolean, value: !1 },
        homeButton: { type: Boolean, value: !1 },
        animated: { type: Boolean, value: !0 },
        show: { type: Boolean, value: !0, observer: "_showChange" },
        delta: { type: Number, value: 1 },
      },
      data: { displayStyle: "" },
      lifetimes: {
        attached: function () {
          var t = wx.getMenuButtonBoundingClientRect(),
            e = (wx.getDeviceInfo() || wx.getSystemInfoSync()).platform,
            a = "android" === e,
            n = "devtools" === e,
            o = wx.getWindowInfo() || wx.getSystemInfoSync(),
            i = o.windowWidth,
            l = o.safeArea,
            d = void 0 === l ? {} : l,
            s = d.top,
            c = void 0 === s ? 0 : s;
          d.bottom;
          this.setData({
            ios: !a,
            innerPaddingRight: "padding-right: ".concat(i - t.left, "px"),
            leftWidth: "width: ".concat(i - t.left, "px"),
            safeAreaTop:
              n || a
                ? "height: calc(var(--height) + "
                    .concat(c, "px); padding-top: ")
                    .concat(c, "px")
                : "",
          });
        },
      },
      methods: {
        _showChange: function (t) {
          var e = "";
          (e = this.data.animated
            ? "opacity: ".concat(t ? "1" : "0", ";transition:opacity 0.5s;")
            : "display: ".concat(t ? "" : "none")),
            this.setData({ displayStyle: e });
        },
        back: function () {
          var t = this.data;
          getCurrentPages().length > 1
            ? t.delta && wx.navigateBack({ delta: t.delta })
            : wx.switchTab({ url: "/pages/index/index" }),
            this.triggerEvent("back", { delta: t.delta }, {});
        },
      },
    });
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "components/navigation-bar/navigation-bar.js",
  }
);
require("components/navigation-bar/navigation-bar.js");
