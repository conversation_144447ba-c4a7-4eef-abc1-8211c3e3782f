__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/step-item/step-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/step-item/step-item"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            prefix: new Array(5),
            ariaRole: new Array(1),
            customStyle: new Array(1),
            ariaLabel: new Array(1),
            readonly: new Array(2),
          },
          K = U === true,
          f,
          h = (C) => {},
          j,
          l = (C) => {},
          k = (C, T, E, B, F, S) => {
            if (j === 1) {
              S("icon");
            } else {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C || K || U.icon) O(N, "name", D.icon);
                  if (C) O(N, "size", "44rpx");
                },
                l
              );
            }
          },
          i = (C, T, E, B) => {
            j = D.icon == "slot" ? 1 : 0;
            B(j, k);
          },
          n,
          p = (C) => {},
          q = (C) => {},
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "name", "check");
                },
                p
              );
            } else if (n === 2) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "name", "close");
                },
                q
              );
            } else {
              C || K || !!U.index || undefined ? T(Y(D.index + 1)) : T();
            }
          },
          m = (C, T, E, B) => {
            n = D.curStatus == "finish" ? 1 : D.curStatus == "error" ? 2 : 0;
            B(n, o);
          },
          g = (C, T, E) => {
            if (f === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.curStatus])
                    ) ||
                    undefined
                  )
                    L(N, P(X(a).cls)(D.classPrefix + "__dot", [D.curStatus]));
                },
                h
              );
            } else if (f === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.curStatus])
                    ) ||
                    undefined
                  )
                    L(N, P(X(a).cls)(D.classPrefix + "__icon", [D.curStatus]));
                },
                i
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.curStatus])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__circle", [D.curStatus])
                    );
                },
                m
              );
            }
          },
          e = (C, T, E, B) => {
            f = D.isDot ? 1 : D.icon ? 2 : 0;
            B(f, g);
          },
          t,
          u = (C, T) => {
            if (t === 1) {
              C || K || U.title ? T(Y(D.title)) : T();
            }
          },
          v,
          w = (C, T, E, B, F, S) => {
            if (v === 1) {
              S("title-right");
            }
          },
          s = (C, T, E, B, F, S) => {
            t = D.title ? 1 : 0;
            B(t, u);
            S("title");
            v = D.layout === "vertical" ? 1 : 0;
            B(v, w);
          },
          y,
          z = (C, T) => {
            if (y === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          x = (C, T, E, B, F, S) => {
            y = D.content ? 1 : 0;
            B(y, z);
            S("content");
          },
          A0 = (C, T, E, B, F, S) => {
            S("extra");
          },
          r = (C, T, E, B, F, S) => {
            S("");
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.curStatus, U.layout]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__title", [
                        D.curStatus,
                        D.layout,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-title"
                  );
                A["prefix"][2] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__title", [
                        D.curStatus,
                        D.layout,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-title"
                  );
                };
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__description", [D.layout])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-description"
                  );
                A["prefix"][3] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__description", [D.layout])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-description"
                  );
                };
              },
              x
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__extra", [D.layout])) +
                      " " +
                      Y(D.prefix) +
                      "-class-extra"
                  );
                A["prefix"][4] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__extra", [D.layout])) +
                      " " +
                      Y(D.prefix) +
                      "-class-extra"
                  );
                };
              },
              A0
            );
          },
          B0,
          D0 = (C) => {},
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.curStatus, U.layout, U.theme, U.sequence])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__line", [
                        D.curStatus,
                        D.layout,
                        D.theme,
                        D.sequence,
                      ])
                    );
                  if (C) O(N, "aria-hidden", "true");
                },
                D0
              );
            }
          },
          d = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "cls") || U.classPrefix || Q.a([U.layout])) ||
                  undefined
                )
                  L(N, P(X(a).cls)(D.classPrefix + "__anchor", [D.layout]));
                if (C) O(N, "aria-hidden", "true");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout, Q.a([U.isLastChild])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__content", [
                        D.layout,
                        ["last", D.isLastChild],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                A["prefix"][1] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__content", [
                        D.layout,
                        ["last", D.isLastChild],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                };
                if (C) O(N, "aria-hidden", "true");
              },
              r
            );
            B0 = !D.isLastChild ? 1 : 0;
            B(B0, C0);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout, Q.a([U.readonly])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D.layout,
                        ["readonly", D.readonly],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["readonly"][1] = A["prefix"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D.layout,
                        ["readonly", D.readonly],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                var $A = D.ariaRole || D.readonly;
                if (
                  C ||
                  K ||
                  !!(U.ariaRole || U.readonly) ||
                  ($A ? undefined : undefined)
                )
                  O(N, "aria-role", $A ? "option" : "button");
                A["ariaRole"][0] = A["readonly"][0] = (D, E, T) => {
                  var $B = D.ariaRole || D.readonly;
                  O(N, "aria-role", $B ? "option" : "button");
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    Z(undefined, "getAriaLabel") ||
                    U.index ||
                    U.title ||
                    U.content
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      P(X(b).getAriaLabel)(D.index, D.title, D.content)
                  );
                A["ariaLabel"][0] = (D, E, T) => {
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      P(X(b).getAriaLabel)(D.index, D.title, D.content)
                  );
                  E(N);
                };
                var $B = D.curStatus == "process";
                if (C || K || !!U.curStatus || ($B ? undefined : undefined))
                  O(N, "aria-current", $B ? "step" : "");
                if (C) R.v(N, "tap", "onTap", !1, !1, !1, !1);
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/steps/steps": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            prefix: new Array(1),
            layout: new Array(1),
            sequence: new Array(1),
            customStyle: new Array(1),
            classPrefix: new Array(1),
            readonly: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout, Q.a([U.readonly]), U.sequence]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D.layout,
                        ["readonly", D.readonly],
                        D.sequence,
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["classPrefix"][0] =
                  A["layout"][0] =
                  A["readonly"][0] =
                  A["sequence"][0] =
                  A["prefix"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(
                          P(X(a).cls)(D.classPrefix, [
                            D.layout,
                            ["readonly", D.readonly],
                            D.sequence,
                          ])
                        ) +
                          " class " +
                          Y(D.prefix) +
                          "-class"
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/step-item/step-item.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-steps-item{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;position:relative;vertical-align:top}\n.",
      [1],
      "t-steps-item__circle--default{background-color:var(--td-step-item-default-circle-bg,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));color:var(--td-step-item-default-circle-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))))}\n.",
      [1],
      "t-steps-item__title--default{color:var(--td-step-item-default-title-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))))}\n.",
      [1],
      "t-steps-item__icon--default{color:var(--td-step-item-default-icon-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))))}\n.",
      [1],
      "t-steps-item__dot--default{background-color:var(--td-step-item-default-dot-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));border-color:var(--td-step-item-default-dot-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)))}\n.",
      [1],
      "t-steps-item__circle--process{background-color:var(--td-step-item-process-circle-bg,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));color:var(--td-step-item-process-circle-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
      [1],
      "t-steps-item__title--process{color:var(--td-step-item-process-title-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-steps-item__icon--process{color:var(--td-step-item-process-icon-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-steps-item__dot--process{background-color:var(--td-step-item-process-dot-border-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-color:var(--td-step-item-process-dot-border-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-steps-item__circle--finish{background-color:var(--td-step-item-finish-circle-bg,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));color:var(--td-step-item-finish-circle-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-steps-item__title--finish{color:var(--td-step-item-finish-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
      [1],
      "t-steps-item__icon--finish{color:var(--td-step-item-finish-icon-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-steps-item__dot--finish{background-color:var(--td-step-item-finish-dot-border-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-color:var(--td-step-item-finish-dot-border-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-steps-item__circle--error{background-color:var(--td-step-item-error-circle-bg,var(--td-error-color-1,#fff0ed));color:var(--td-step-item-error-circle-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
      [1],
      "t-steps-item__title--error{color:var(--td-step-item-error-title-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
      [1],
      "t-steps-item__icon--error{color:var(--td-step-item-error-icon-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
      [1],
      "t-steps-item__dot--error{background-color:var(--td-step-item-error-dot-border-color,var(--td-error-color,var(--td-error-color-6,#d54941)));border-color:var(--td-step-item-error-dot-border-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
      [1],
      "t-steps-item--horizontal{-webkit-flex-direction:column;flex-direction:column}\n.",
      [1],
      "t-steps-item--horizontal,.",
      [1],
      "t-steps-item__anchor{-webkit-align-items:center;align-items:center;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "t-steps-item__anchor{display:-webkit-flex;display:flex}\n.",
      [1],
      "t-steps-item__anchor--vertical,.",
      [1],
      "t-steps-item__circle{height:var(--td-step-item-circle-size,",
      [0, 44],
      ");width:var(--td-step-item-circle-size,",
      [0, 44],
      ")}\n.",
      [1],
      "t-steps-item__circle{-webkit-align-items:center;align-items:center;border-radius:50%;display:-webkit-flex;display:flex;font-size:var(--td-step-item-circle-font-size,",
      [0, 28],
      ");-webkit-justify-content:center;justify-content:center;text-align:center}\n.",
      [1],
      "t-steps-item__icon{font-size:var(--td-font-size-base,",
      [0, 28],
      ");position:relative;vertical-align:top;z-index:1}\n.",
      [1],
      "t-steps-item__dot{border-radius:50%;border-style:solid;border-width:1px;box-sizing:border-box;height:var(--td-step-item-dot-size,",
      [0, 16],
      ");width:var(--td-step-item-dot-size,",
      [0, 16],
      ")}\n.",
      [1],
      "t-steps-item__content{text-align:center}\n.",
      [1],
      "t-steps-item__content--horizontal{margin-top:",
      [0, 16],
      ";max-width:80px}\n.",
      [1],
      "t-steps-item__content--vertical{-webkit-flex:1;flex:1;margin-left:",
      [0, 16],
      ";padding-bottom:",
      [0, 32],
      "}\n.",
      [1],
      "t-steps-item__content--vertical.",
      [1],
      "t-steps-item__content--last{padding-bottom:0}\n.",
      [1],
      "t-steps-item__title{font-size:var(--td-font-size-base,",
      [0, 28],
      ");line-height:var(--td-step-item-circle-size,",
      [0, 44],
      ");position:relative}\n.",
      [1],
      "t-steps-item__title--process{font-weight:600}\n.",
      [1],
      "t-steps-item__title--vertical{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between;margin-bottom:",
      [0, 8],
      ";text-align:left}\n.",
      [1],
      "t-steps-item__description{color:var(--td-step-item-description-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-font-size-s,",
      [0, 24],
      ");line-height:",
      [0, 40],
      "}\n.",
      [1],
      "t-steps-item__description--vertical{text-align:left}\n.",
      [1],
      "t-steps-item__extra:not(:empty){margin-top:",
      [0, 16],
      "}\n.",
      [1],
      "t-steps-item__line{background-color:var(--td-step-item-line-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));content:\x22\x22;display:block;position:absolute}\n.",
      [1],
      "t-steps-item__line--horizontal{height:1px;left:calc(50% + var(--td-step-item-circle-size,",
      [0, 44],
      ")/ 2 + ",
      [0, 16],
      ");top:calc(var(--td-step-item-circle-size,",
      [0, 44],
      ")/ 2 + 1px);-webkit-transform:translateY(-50%);transform:translateY(-50%);width:calc(100% - ",
      [0, 32],
      " - var(--td-step-item-circle-size,",
      [0, 44],
      "))}\n.",
      [1],
      "t-steps-item__line--horizontal.",
      [1],
      "t-steps-item__line--dot{top:calc(var(--td-step-item-dot-size,",
      [0, 16],
      ")/ 2)}\n.",
      [1],
      "t-steps-item__line--finish,.",
      [1],
      "t-steps-item__line--reverse.",
      [1],
      "t-steps-item__line--process{background-color:var(--td-step-item-finish-line-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-steps-item__line--vertical{height:calc(100% - ",
      [0, 32],
      " - var(--td-step-item-circle-size,",
      [0, 44],
      "));left:calc(var(--td-step-item-circle-size,",
      [0, 44],
      ")/ 2);top:calc(var(--td-step-item-circle-size,",
      [0, 44],
      ") + ",
      [0, 16],
      ");-webkit-transform:translateX(-50%);transform:translateX(-50%);width:1px}\n.",
      [1],
      "t-steps-item__line--vertical.",
      [1],
      "t-steps-item__line--dot{height:calc(100% - var(--td-step-item-circle-size,",
      [0, 44],
      "));top:var(--td-step-item-circle-size,",
      [0, 44],
      ")}\n",
    ],
    undefined,
    { path: "./miniprogram_npm/tdesign-miniprogram/step-item/step-item.wxss" }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/steps/steps.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-step--vertical{padding-right:",
        [0, 32],
        "}\n.",
        [1],
        "t-steps{display:-webkit-flex;display:flex;width:100%}\n.",
        [1],
        "t-steps--vertical{-webkit-flex-direction:column;flex-direction:column}\n.",
        [1],
        "t-steps--reverse{-webkit-flex-direction:row-reverse;flex-direction:row-reverse}\n.",
        [1],
        "t-steps--vertical.",
        [1],
        "t-steps--reverse{-webkit-flex-direction:column-reverse;flex-direction:column-reverse}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/steps/steps.wxss" }
    );
}
