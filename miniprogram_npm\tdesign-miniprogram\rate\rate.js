Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/slicedToArray"),
  t = require("../../../@babel/runtime/helpers/createClass"),
  i = require("../../../@babel/runtime/helpers/classCallCheck"),
  r = require("../../../@babel/runtime/helpers/inherits"),
  a = require("../../../@babel/runtime/helpers/createSuper"),
  s = require("tslib"),
  n = require("../common/src/index"),
  o = l(require("../common/config")),
  c = l(require("./props")),
  u = require("../common/utils");
function l(e) {
  return e && e.__esModule ? e : { default: e };
}
var h = o.default.prefix,
  p = "".concat(h, "-rate"),
  d = (function (s) {
    r(o, s);
    var n = a(o);
    function o() {
      var t;
      return (
        i(this, o),
        ((t = n.apply(this, arguments)).externalClasses = [
          "".concat(h, "-class"),
          "".concat(h, "-class-icon"),
          "".concat(h, "-class-text"),
        ]),
        (t.properties = c.default),
        (t.controlledProps = [{ key: "value", event: "change" }]),
        (t.data = {
          prefix: h,
          classPrefix: p,
          defaultTexts: ["极差", "失望", "一般", "满意", "惊喜"],
          tipsVisible: !1,
          tipsLeft: 0,
          actionType: "",
          scaleIndex: -1,
          isVisibleToScreenReader: !1,
        }),
        (t.methods = {
          onTouch: function (t, i) {
            var r = this,
              a = this.properties,
              s = a.count,
              n = a.allowHalf,
              o = a.gap,
              c = a.value,
              l = a.size,
              h = e(t.changedTouches, 1)[0],
              d = (0, u.unitConvert)(o);
            (0, u.getRect)(this, ".".concat(p, "__wrapper")).then(function (e) {
              var t = e.width,
                a = e.left,
                p = (t - (s - 1) * d) / s,
                f = (h.pageX - a + d) / (p + d),
                v = f % 1,
                T = f - v,
                b = v <= 0.5 && n ? T + 0.5 : T + 1;
              if (
                (b > s ? (b = s) : b < 0 && (b = 0),
                "move" === i || ("tap" === i && n))
              ) {
                var m =
                  Math.ceil(b - 1) *
                    ((0, u.unitConvert)(o) + (0, u.unitConvert)(l)) +
                  0.5 * (0, u.unitConvert)(l);
                r.setData({
                  tipsVisible: !0,
                  actionType: i,
                  scaleIndex: Math.ceil(b),
                  tipsLeft: Math.max(m, 0),
                });
              }
              b !== c && r._trigger("change", { value: b }),
                r.touchEnd && r.hideTips();
            });
          },
          onTap: function (e) {
            this.properties.disabled || this.onTouch(e, "tap");
          },
          onTouchStart: function () {
            this.touchEnd = !1;
          },
          onTouchMove: function (e) {
            this.onTouch(e, "move"), this.showAlertText();
          },
          onTouchEnd: function () {
            (this.touchEnd = !0), this.hideTips();
          },
          hideTips: function () {
            "move" === this.data.actionType &&
              this.setData({ tipsVisible: !1, scaleIndex: -1 });
          },
          onSelect: function (e) {
            var t = this,
              i = e.currentTarget.dataset.value;
            "move" !== this.data.actionType &&
              (this._trigger("change", { value: i }),
              setTimeout(function () {
                return t.setData({ tipsVisible: !1, scaleIndex: -1 });
              }, 300));
          },
          showAlertText: function () {
            var e = this;
            !0 !== this.data.isVisibleToScreenReader &&
              (this.setData({ isVisibleToScreenReader: !0 }),
              setTimeout(function () {
                e.setData({ isVisibleToScreenReader: !1 });
              }, 2e3));
          },
        }),
        t
      );
    }
    return t(o);
  })(n.SuperComponent),
  f = (d = (0, s.__decorate)([(0, n.wxComponent)()], d));
exports.default = f;
