__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/scroll-view/scroll-view": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { scrollIntoView: new Array(1), prefix: new Array(1) },
          K = U === true,
          b = (C, T, E, B, F, S) => {
            S("");
          },
          a = (C, T, E) => {
            E(
              "scroll-view",
              {},
              (N, C) => {
                if (C || K || !!U.prefix || undefined)
                  L(N, "class " + Y(D.prefix) + "-class");
                A["prefix"][0] = (D, E, T) => {
                  L(N, "class " + Y(D.prefix) + "-class");
                };
                if (C) O(N, "type", "list");
                if (C) O(N, "scroll-y", true);
                if (C) O(N, "enhanced", true);
                if (C || K || undefined) O(N, "show-scrollbar", false);
                if (C || K || U.scrollIntoView)
                  O(N, "scroll-into-view", D.scrollIntoView);
                A["scrollIntoView"][0] = (D, E, T) => {
                  O(N, "scroll-into-view", D.scrollIntoView);
                  E(N);
                };
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/side-bar-item/side-bar-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/badge"] ||
              {}
            )._,
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            prefix: new Array(1),
            tId: new Array(1),
            customStyle: new Array(1),
            disabled: new Array(2),
          },
          K = U === true,
          d,
          f = (C) => {},
          g = (C) => {},
          h = (C) => {},
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__line");
                },
                f
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__prefix");
                },
                g
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__suffix");
                },
                h
              );
            }
          },
          i,
          k,
          l = (C, T, E, B, F, S, J) => {
            var $A = I(k);
            if (k && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.classPrefix + "__icon" },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.classPrefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          j = (C, T, E, B, F, S, J) => {
            if (i === 1) {
              k = "icon";
              B(k, l);
            }
          },
          m,
          o,
          p = (C, T, E, B, F, S, J) => {
            var $A = I(o);
            if (o && $A)
              $A(
                R,
                C,
                Object.assign({}, X(D.badgeProps), { content: D.label }),
                K ||
                  (U
                    ? U.badgeProps === true ||
                      Object.assign({}, X(U.badgeProps), { content: U.label })
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          n = (C, T, E, B, F, S, J) => {
            if (m === 1) {
              o = "badge";
              B(o, p);
            } else {
              C || K || U.label ? T(Y(D.label)) : T();
            }
          },
          c = (C, T, E, B) => {
            d = D.active ? 1 : 0;
            B(d, e);
            i = D._icon ? 1 : 0;
            B(i, j);
            m = D.badgeProps ? 1 : 0;
            B(m, n);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.active]), Q.a([U.disabled])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["active", D.active],
                        ["disabled", D.disabled],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["disabled"][1] = A["prefix"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["active", D.active],
                        ["disabled", D.disabled],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-role", "button");
                var $A = D.active;
                if (
                  C ||
                  K ||
                  !!U.active ||
                  ($A ? !!U.label || undefined : U.label)
                )
                  O(N, "aria-label", $A ? "已选中，" + D.label : D.label);
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                A["disabled"][0] = (D, E, T) => {
                  O(N, "aria-disabled", D.disabled);
                  E(N);
                };
                if (C) R.v(N, "tap", "handleClick", !1, !1, !1, !1);
                if (C || K || U.tId) R.i(N, D.tId);
                A["tId"][0] = (D, E, T) => {
                  R.i(N, D.tId);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/side-bar/side-bar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            style: new Array(1),
            classPrefix: new Array(2),
            prefix: new Array(1),
          },
          K = U === true,
          d = (C) => {},
          c = (C, T, E, B, F, S) => {
            S("");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__padding");
                A["classPrefix"][1] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + "__padding");
                };
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["classPrefix"][0] = A["prefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/tree-select/tree-select": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/tree-select/index"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            style: new Array(1),
            height: new Array(1),
          },
          K = U === true,
          e = (C, f, g, h, i, j, T, E) => {
            var l,
              o = (C, p, q, r, s, t, T, E) => {
                var u = (C) => {};
                E(
                  "t-side-bar-item",
                  {},
                  (N, C) => {
                    if (C) L(N, "scroll-into-view");
                    if (C || K || Z(r, "label")) O(N, "label", X(p).label);
                    if (C || K || Z(r, "value")) O(N, "value", X(p).value);
                    if (C || K || !!Z(r, "value") || undefined)
                      O(N, "tId", "scroll-to-" + Y(X(p).value));
                    if (C || K || !!U.prefix || undefined)
                      O(N, "t-class", Y(D.prefix) + "-class-left-item");
                  },
                  u
                );
              },
              n = (C, T, E, B, F) => {
                var $A = g;
                F(
                  X(D.treeOptions)[$A],
                  "index",
                  U ? !!i || Z(U.treeOptions, $A) : undefined,
                  [0, "treeOptions", $A],
                  o
                );
              },
              p = (C, q, r, s, t, u, T, E) => {
                var w = (C, T) => {
                    C || K || Z(s, "label") ? T(Y(X(q).label)) : T();
                  },
                  v = (C, T, E) => {
                    E(
                      "view",
                      {},
                      (N, C) => {
                        if (C || K || !!Z(s, "value") || undefined)
                          R.i(N, "scroll-to-" + Y(X(q).value));
                      },
                      w
                    );
                  };
                E(
                  "view",
                  {},
                  (N, C) => {
                    var $A = g;
                    if (
                      C ||
                      K ||
                      !!(
                        Z(undefined, "cls") ||
                        U.classPrefix ||
                        Q.a([
                          Q.a([
                            !!(Z(s, "value") || i || Z(U.innerValue, $A)) ||
                              undefined,
                          ]),
                        ]) ||
                        U.prefix
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        Y(
                          P(X(a).cls)(D.classPrefix + "__item", [
                            ["active", X(q).value === X(D.innerValue)[$A]],
                          ])
                        ) +
                          " " +
                          Y(D.prefix) +
                          "-class-middle-item scroll-into-view"
                      );
                    if (C || K || i) R.d(N, "level", g);
                    if (C || K || Z(s, "value")) R.d(N, "value", X(q).value);
                    if (C) R.v(N, "tap", "handleTreeClick", !1, !1, !1, !1);
                  },
                  v
                );
              },
              r = (C, s, t, u, v, w, T, E) => {
                var x = (C, T) => {
                  C || K || Z(u, "label") ? T(Y(X(s).label)) : T();
                };
                E(
                  "t-radio",
                  {},
                  (N, C) => {
                    if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                      L(
                        N,
                        "scroll-into-view " +
                          Y(D.classPrefix) +
                          "__radio-item " +
                          Y(D.prefix) +
                          "-class-right-item"
                      );
                    if (C || K || !!Z(u, "value") || undefined)
                      O(N, "tId", "scroll-to-" + Y(X(s).value));
                    if (C || K || !!U.prefix || undefined)
                      O(
                        N,
                        "t-class-label",
                        Y(D.prefix) + "-class-right-item-label"
                      );
                    if (C) O(N, "icon", "line");
                    if (C || K || Z(u, "value")) O(N, "value", X(s).value);
                    if (C || K || undefined) O(N, "maxLabelRow", 1);
                    if (C) O(N, "borderless", true);
                    if (C) O(N, "placement", "right");
                  },
                  x
                );
              },
              q = (C, T, E, B, F) => {
                var $A = g;
                F(
                  X(D.treeOptions)[$A],
                  "value",
                  U ? !!i || Z(U.treeOptions, $A) : undefined,
                  [0, "treeOptions", $A],
                  r
                );
              },
              t = (C, u, v, w, x, y, T, E) => {
                var z = (C, T) => {
                  C || K || Z(w, "label") ? T(Y(X(u).label)) : T();
                };
                E(
                  "t-checkbox",
                  {},
                  (N, C) => {
                    if (C || K || !!U.prefix || undefined)
                      L(
                        N,
                        "scroll-into-view " + Y(D.prefix) + "-class-right-item"
                      );
                    if (C) O(N, "placement", "right");
                    if (C) O(N, "icon", "line");
                    if (C || K || undefined) O(N, "maxLabelRow", 1);
                    if (C || K || !!Z(w, "value") || undefined)
                      O(N, "tId", "scroll-to-" + Y(X(u).value));
                    if (C || K || !!U.prefix || undefined)
                      O(
                        N,
                        "t-class-label",
                        Y(D.prefix) + "-class-right-item-label"
                      );
                    if (C) O(N, "borderless", true);
                    if (C || K || Z(w, "value")) O(N, "value", X(u).value);
                  },
                  z
                );
              },
              s = (C, T, E, B, F) => {
                var $A = g;
                F(
                  X(D.treeOptions)[$A],
                  "value",
                  U ? !!i || Z(U.treeOptions, $A) : undefined,
                  [0, "treeOptions", $A],
                  t
                );
              },
              m = (C, T, E, B, F) => {
                if (l === 1) {
                  E(
                    "t-side-bar",
                    {},
                    (N, C) => {
                      var $A = g;
                      if (C || K || !!i || Z(U.innerValue, $A))
                        O(N, "value", X(D.innerValue)[$A]);
                      if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                        O(
                          N,
                          "t-class",
                          Y(D.classPrefix) +
                            "-column " +
                            Y(D.prefix) +
                            "-class-left-column"
                        );
                      if (C) R.v(N, "change", "onRootChange", !1, !1, !1, !1);
                    },
                    n
                  );
                } else if (l === 2) {
                  var $A = g;
                  F(
                    X(D.treeOptions)[$A],
                    "value",
                    U ? !!i || Z(U.treeOptions, $A) : undefined,
                    [0, "treeOptions", $A],
                    p
                  );
                } else if (l === 3) {
                  E(
                    "t-radio-group",
                    {},
                    (N, C) => {
                      if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                        L(
                          N,
                          Y(D.classPrefix) +
                            "__radio " +
                            Y(D.prefix) +
                            "-class-right-column"
                        );
                      var $A = g;
                      if (C || K || !!i || Z(U.innerValue, $A))
                        O(N, "value", X(D.innerValue)[$A]);
                      if (C || K || i) R.d(N, "level", g);
                      if (C) R.d(N, "type", "single");
                      if (C) R.v(N, "change", "handleChange", !1, !1, !1, !1);
                    },
                    q
                  );
                } else {
                  E(
                    "t-checkbox-group",
                    {},
                    (N, C) => {
                      if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                        L(
                          N,
                          Y(D.classPrefix) +
                            "__checkbox " +
                            Y(D.prefix) +
                            "-class-right-column"
                        );
                      var $A = g;
                      if (
                        C ||
                        K ||
                        !!(i || Z(U.innerValue, $A) || Q.a([])) ||
                        undefined
                      )
                        O(N, "value", X(D.innerValue)[$A] || []);
                      if (C || K || i) R.d(N, "level", g);
                      if (C) R.d(N, "type", "multiple");
                      if (C) R.v(N, "change", "handleChange", !1, !1, !1, !1);
                    },
                    s
                  );
                }
              },
              k = (C, T, E, B) => {
                l = g == 0 ? 1 : g != D.leafLevel ? 2 : !D.multiple ? 3 : 0;
                B(l, m);
              };
            E(
              "t-scroll-view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      !!(
                        Z(undefined, "getTreeClass") ||
                        U.leafLevel ||
                        i ||
                        Z(U.treeOptions, "length")
                      ) || undefined,
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__column", [
                        P(X(b).getTreeClass)(
                          D.leafLevel - g,
                          X(D.treeOptions).length
                        ),
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class"
                  );
                var $B = g;
                var $A = D.scrollIntoView && X(D.scrollIntoView)[$B];
                var $C = g;
                if (
                  C ||
                  K ||
                  !!(U.scrollIntoView || i || Z(U.scrollIntoView, $B)) ||
                  ($A
                    ? !!(i || Z(U.scrollIntoView, $C)) || undefined
                    : undefined)
                )
                  O(
                    N,
                    "scrollIntoView",
                    $A
                      ? ".scroll-into-view >>> #scroll-to-" +
                          X(D.scrollIntoView)[$C]
                      : ""
                  );
              },
              k
            );
          },
          d = (C, T, E, B, F, S) => {
            F(
              D.treeOptions,
              "level",
              U ? U.treeOptions : undefined,
              [0, "treeOptions"],
              e
            );
            S("content");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + " class");
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([
                      !!(Z(undefined, "addUnit") || U.height) || undefined,
                      U.style,
                      U.customStyle,
                    ])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([
                      "height:" + P(X(a).addUnit)(D.height),
                      D.style,
                      D.customStyle,
                    ])
                  );
                A["height"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([
                          "height:" + P(X(a).addUnit)(D.height),
                          D.style,
                          D.customStyle,
                        ])
                      );
                    };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/scroll-view/scroll-view.wxss"
  ] = setCssToHead(
    [[2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"]],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/scroll-view/scroll-view.wxss",
    }
  );
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/side-bar-item/side-bar-item.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-side-bar-item{-webkit-align-items:center;align-items:center;background:var(--td-side-bar-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));box-sizing:border-box;color:var(--td-side-bar-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex;font-size:var(--td-side-bar-font-size,",
      [0, 32],
      ");line-height:var(--td-side-bar-item-line-height,",
      [0, 48],
      ");min-height:var(--td-side-bar-item-height,",
      [0, 112],
      ");padding:",
      [0, 32],
      ";position:relative;white-space:wrap}\n.",
      [1],
      "t-side-bar-item--active{--td-badge-content-text-color:var(--td-side-bar-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));background:var(--td-bg-color-container,var(--td-font-white-1,#fff));color:var(--td-side-bar-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));font-weight:600}\n.",
      [1],
      "t-side-bar-item__icon{font-size:var(--td-side-bar-icon-size,",
      [0, 40],
      ");margin-right:",
      [0, 4],
      "}\n.",
      [1],
      "t-side-bar-item__prefix,.",
      [1],
      "t-side-bar-item__suffix{background:var(--td-bg-color-container,var(--td-font-white-1,#fff));height:calc(var(--td-side-bar-border-radius,",
      [0, 18],
      ") * 2);pointer-events:none;position:absolute;right:0;width:100%;z-index:1}\n.",
      [1],
      "t-side-bar-item__prefix::after,.",
      [1],
      "t-side-bar-item__suffix::after{background-color:var(--td-side-bar-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));content:\x22\x22;display:block;height:100%;width:100%}\n.",
      [1],
      "t-side-bar-item__prefix{top:calc(var(--td-side-bar-border-radius,",
      [0, 18],
      ") * -2)}\n.",
      [1],
      "t-side-bar-item__prefix::after{border-bottom-right-radius:var(--td-side-bar-border-radius,",
      [0, 18],
      ")}\n.",
      [1],
      "t-side-bar-item__suffix{bottom:calc(var(--td-side-bar-border-radius,",
      [0, 18],
      ") * -2)}\n.",
      [1],
      "t-side-bar-item__suffix::after{border-top-right-radius:var(--td-side-bar-border-radius,",
      [0, 18],
      ")}\n.",
      [1],
      "t-side-bar-item--disabled{color:var(--td-side-bar-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
      [1],
      "t-side-bar-item__line{background:var(--td-side-bar-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:",
      [0, 8],
      ";height:",
      [0, 28],
      ";left:0;position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:",
      [0, 6],
      "}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/side-bar-item/side-bar-item.wxss",
    }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/side-bar/side-bar.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-side-bar{display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;height:var(--td-side-bar-height,100%);overflow-y:auto;width:var(--td-side-bar-width,",
        [0, 206],
        ")}\n.",
        [1],
        "t-side-bar::-webkit-scrollbar{display:none}\n.",
        [1],
        "t-side-bar__padding{background-color:var(--td-side-bar-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));-webkit-flex:1;flex:1}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/side-bar/side-bar.wxss" }
    );
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/tree-select/tree-select.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-tree-select{background-color:var(--td-tree-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));display:-webkit-flex;display:flex}\n.",
      [1],
      "t-tree-select__column{color:var(--td-tree-colum-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));width:var(--td-tree-colum-width,",
      [0, 206],
      ")}\n.",
      [1],
      "t-tree-select__column--left{background:var(--td-tree-root-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)))}\n.",
      [1],
      "t-tree-select__column--right{-webkit-flex:1;flex:1}\n.",
      [1],
      "t-tree-select__column::-webkit-scrollbar{color:transparent;display:none;height:0;width:0}\n.",
      [1],
      "t-tree-select__item{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;font-size:var(--td-tree-item-font-size,",
      [0, 32],
      ");height:var(--td-tree-item-height,",
      [0, 112],
      ");line-height:var(--td-tree-item-height,",
      [0, 112],
      ");overflow:hidden;padding-left:",
      [0, 32],
      ";text-overflow:ellipsis}\n.",
      [1],
      "t-tree-select__item--active{color:var(--td-tree-item-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));font-weight:600}\n.",
      [1],
      "t-tree-select-column{height:auto;width:100%}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/tree-select/tree-select.wxss",
    }
  );
}
