Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  r = require("../../../@babel/runtime/helpers/inherits"),
  i = require("../../../@babel/runtime/helpers/createSuper"),
  a = require("tslib"),
  s = require("../common/src/index"),
  n = l(require("../common/config")),
  u = l(require("./props"));
function l(e) {
  return e && e.__esModule ? e : { default: e };
}
var o = n.default.prefix,
  h = "".concat(o, "-stepper"),
  c = (function (a) {
    r(n, a);
    var s = i(n);
    function n() {
      var e;
      return (
        t(this, n),
        ((e = s.apply(this, arguments)).externalClasses = [
          "".concat(o, "-class"),
          "".concat(o, "-class-input"),
          "".concat(o, "-class-minus"),
          "".concat(o, "-class-plus"),
        ]),
        (e.properties = Object.assign({}, u.default)),
        (e.controlledProps = [{ key: "value", event: "change" }]),
        (e.observers = {
          value: function (e) {
            (this.preValue = Number(e)),
              this.setData({ currentValue: this.format(Number(e)) });
          },
        }),
        (e.data = { currentValue: 0, classPrefix: h, prefix: o }),
        (e.lifetimes = {
          attached: function () {
            var e = this.properties,
              t = e.value,
              r = e.min;
            this.setData({ currentValue: t ? Number(t) : r });
          },
        }),
        (e.methods = {
          isDisabled: function (e) {
            var t = this.properties,
              r = t.min,
              i = t.max,
              a = t.disabled,
              s = this.data.currentValue;
            return !!a || ("minus" === e && s <= r) || ("plus" === e && s >= i);
          },
          getLen: function (e) {
            var t = e.toString();
            return -1 === t.indexOf(".") ? 0 : t.split(".")[1].length;
          },
          add: function (e, t) {
            var r = Math.max(this.getLen(e), this.getLen(t)),
              i = Math.pow(10, r);
            return Math.round(e * i + t * i) / i;
          },
          format: function (e) {
            var t = this.properties,
              r = t.min,
              i = t.max,
              a = t.step,
              s = Math.max(this.getLen(a), this.getLen(e));
            return Math.max(
              Math.min(i, e, Number.MAX_SAFE_INTEGER),
              r,
              Number.MIN_SAFE_INTEGER
            ).toFixed(s);
          },
          setValue: function (e) {
            (e = this.format(e)),
              this.preValue !== e &&
                ((this.preValue = e),
                this._trigger("change", { value: Number(e) }));
          },
          minusValue: function () {
            if (this.isDisabled("minus"))
              return this.triggerEvent("overlimit", { type: "minus" }), !1;
            var e = this.data,
              t = e.currentValue,
              r = e.step;
            this.setValue(this.add(t, -r));
          },
          plusValue: function () {
            if (this.isDisabled("plus"))
              return this.triggerEvent("overlimit", { type: "plus" }), !1;
            var e = this.data,
              t = e.currentValue,
              r = e.step;
            this.setValue(this.add(t, r));
          },
          filterIllegalChar: function (e) {
            var t = String(e).replace(/[^0-9.]/g, ""),
              r = t.indexOf(".");
            return this.properties.integer && -1 !== r
              ? t.split(".")[0]
              : this.properties.integer || -1 === r || r === t.lastIndexOf(".")
              ? t
              : t.split(".", 2).join(".");
          },
          handleFocus: function (e) {
            var t = e.detail.value;
            this.triggerEvent("focus", { value: t });
          },
          handleInput: function (e) {
            var t = e.detail.value;
            if ("" !== t) {
              var r = this.filterIllegalChar(t);
              this.setData({ currentValue: r }),
                this.triggerEvent("input", { value: r });
            }
          },
          handleBlur: function (e) {
            var t = e.detail.value,
              r = this.format(t);
            this.setValue(r), this.triggerEvent("blur", { value: r });
          },
        }),
        e
      );
    }
    return e(n);
  })(s.SuperComponent),
  p = (c = (0, a.__decorate)([(0, s.wxComponent)()], c));
exports.default = p;
