Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/slicedToArray");
require("../../../@babel/runtime/helpers/Arrayincludes");
var t = require("../../../@babel/runtime/helpers/toConsumableArray"),
  i = require("../../../@babel/runtime/helpers/classCallCheck"),
  r = require("../../../@babel/runtime/helpers/createClass"),
  a = require("../../../@babel/runtime/helpers/inherits"),
  s = require("../../../@babel/runtime/helpers/createSuper"),
  n = require("tslib"),
  o = require("../common/src/index"),
  l = g(require("./props")),
  u = g(require("../common/config")),
  c = require("../common/utils"),
  d = require("../common/validator");
function g(e) {
  return e && e.__esModule ? e : { default: e };
}
var h = u.default.prefix,
  f = "".concat(h, "-upload"),
  m = (function (o) {
    a(g, o);
    var u = s(g);
    function g() {
      var e;
      return (
        i(this, g),
        ((e = u.apply(this, arguments)).externalClasses = [
          "".concat(h, "-class"),
        ]),
        (e.options = { multipleSlots: !0 }),
        (e.data = {
          classPrefix: f,
          prefix: h,
          current: !1,
          proofs: [],
          customFiles: [],
          customLimit: 0,
          column: 4,
          dragBaseData: {},
          rows: 0,
          dragWrapStyle: "",
          dragList: [],
          dragging: !0,
          dragLayout: !1,
        }),
        (e.properties = l.default),
        (e.controlledProps = [{ key: "files", event: "success" }]),
        (e.observers = {
          "files, max, draggable": function (e, t) {
            this.handleLimit(e, t);
          },
          gridConfig: function () {
            this.updateGrid();
          },
        }),
        (e.lifetimes = {
          ready: function () {
            this.handleLimit(this.data.customFiles, this.data.max),
              this.updateGrid();
          },
        }),
        (e.methods = {
          uploadFiles: function (e) {
            var t = this;
            return new Promise(function (i) {
              var r = t.data.requestMethod(e);
              if (r instanceof Promise) return r;
              i({});
            });
          },
          startUpload: function (e) {
            var t = this;
            return "function" == typeof this.data.requestMethod
              ? this.uploadFiles(e)
                  .then(function () {
                    e.forEach(function (e) {
                      e.percent = 100;
                    }),
                      t.triggerSuccessEvent(e);
                  })
                  .catch(function (e) {
                    t.triggerFailEvent(e);
                  })
              : (this.triggerSuccessEvent(e),
                this.handleLimit(this.data.customFiles, this.data.max),
                Promise.resolve());
          },
          onAddTap: function () {
            var e = this.properties,
              t = e.disabled,
              i = e.mediaType,
              r = e.source;
            t ||
              ("media" === r ? this.chooseMedia(i) : this.chooseMessageFile(i));
          },
          chooseMedia: function (e) {
            var t = this,
              i = this.data,
              r = i.config,
              a = i.sizeLimit,
              s = i.customLimit;
            wx.chooseMedia(
              Object.assign(Object.assign({ count: s, mediaType: e }, r), {
                success: function (i) {
                  var r = [];
                  i.tempFiles.forEach(function (i) {
                    var s = i.size,
                      o = i.fileType,
                      l = i.tempFilePath,
                      u = i.width,
                      d = i.height,
                      g = i.duration,
                      h = i.thumbTempFilePath,
                      f = (0, n.__rest)(i, [
                        "size",
                        "fileType",
                        "tempFilePath",
                        "width",
                        "height",
                        "duration",
                        "thumbTempFilePath",
                      ]);
                    if ((0, c.isOverSize)(s, a)) {
                      var m =
                        ("image" === o ? "图片" : "视频") + "大小超过限制";
                      return (
                        "number" != typeof a &&
                          (m = a.message.replace(
                            "{sizeLimit}",
                            null == a ? void 0 : a.size
                          )),
                        void wx.showToast({ icon: "none", title: m })
                      );
                    }
                    var p = t.getRandFileName(l);
                    r.push(
                      Object.assign(
                        {
                          name: p,
                          type: t.getFileType(e, l, o),
                          url: l,
                          size: s,
                          width: u,
                          height: d,
                          duration: g,
                          thumb: h,
                          percent: 0,
                        },
                        f
                      )
                    );
                  }),
                    t.afterSelect(r);
                },
                fail: function (e) {
                  t.triggerFailEvent(e);
                },
                complete: function (e) {
                  t.triggerEvent("complete", e);
                },
              })
            );
          },
          chooseMessageFile: function (e) {
            var t = this,
              i = this.properties,
              r = i.max,
              a = i.config,
              s = i.sizeLimit;
            wx.chooseMessageFile(
              Object.assign(
                Object.assign(
                  { count: r, type: Array.isArray(e) ? "all" : e },
                  a
                ),
                {
                  success: function (i) {
                    var r = [];
                    i.tempFiles.forEach(function (i) {
                      var a = i.size,
                        o = i.type,
                        l = i.path,
                        u = (0, n.__rest)(i, ["size", "type", "path"]);
                      if ((0, c.isOverSize)(a, s)) {
                        var d =
                          ("image" === o ? "图片" : "视频") + "大小超过限制";
                        return (
                          "number" != typeof s &&
                            (d = s.message.replace(
                              "{sizeLimit}",
                              null == s ? void 0 : s.size
                            )),
                          void wx.showToast({ icon: "none", title: d })
                        );
                      }
                      var g = t.getRandFileName(l);
                      r.push(
                        Object.assign(
                          {
                            name: g,
                            type: t.getFileType(e, l, o),
                            url: l,
                            size: a,
                            percent: 0,
                          },
                          u
                        )
                      );
                    }),
                      t.afterSelect(r);
                  },
                  fail: function (e) {
                    return t.triggerFailEvent(e);
                  },
                  complete: function (e) {
                    return t.triggerEvent("complete", e);
                  },
                }
              )
            );
          },
          afterSelect: function (e) {
            this._trigger("select-change", {
              files: t(this.data.customFiles),
              currentSelectedFiles: [e],
            }),
              this._trigger("add", { files: e }),
              this.startUpload(e);
          },
          dragVibrate: function (e) {
            var t,
              i = e.vibrateType,
              r = this.data.draggable,
              a =
                null === (t = null == r ? void 0 : r.vibrate) ||
                void 0 === t ||
                t,
              s = null == r ? void 0 : r.collisionVibrate;
            ((a && "longPress" === i) || (s && "touchMove" === i)) &&
              wx.vibrateShort({ type: "light" });
          },
          dragStatusChange: function (e) {
            var t = e.dragging;
            this.setData({ dragging: t });
          },
          dragEnd: function (e) {
            var t,
              i = e.dragCollisionList;
            (t =
              0 === i.length
                ? this.data.customFiles
                : i.reduce(function (e, t) {
                    var i = t.realKey,
                      r = t.data;
                    return t.fixed || (e[i] = Object.assign({}, r)), e;
                  }, [])),
              this.triggerDropEvent(t);
          },
          triggerDropEvent: function (e) {
            var t = this,
              i = this.properties.transition;
            if (i.backTransition)
              var r = setTimeout(function () {
                t.triggerEvent("drop", { files: e }), clearTimeout(r);
              }, i.duration);
            else this.triggerEvent("drop", { files: e });
          },
        }),
        e
      );
    }
    return (
      r(g, [
        {
          key: "onProofTap",
          value: function (e) {
            var t;
            this.onFileClick(e);
            var i = e.currentTarget.dataset.index;
            wx.previewImage({
              urls: this.data.customFiles
                .filter(function (e) {
                  return -1 !== e.percent;
                })
                .map(function (e) {
                  return e.url;
                }),
              current:
                null === (t = this.data.customFiles[i]) || void 0 === t
                  ? void 0
                  : t.url,
            });
          },
        },
        {
          key: "handleLimit",
          value: function (e, t) {
            0 === t && (t = 20),
              this.setData({
                customFiles: e.length > t ? e.slice(0, t) : e,
                customLimit: t - e.length,
                dragging: !0,
              }),
              this.initDragLayout();
          },
        },
        {
          key: "triggerSuccessEvent",
          value: function (e) {
            this._trigger("success", {
              files: [].concat(t(this.data.customFiles), t(e)),
            });
          },
        },
        {
          key: "triggerFailEvent",
          value: function (e) {
            this.triggerEvent("fail", e);
          },
        },
        {
          key: "onFileClick",
          value: function (e) {
            var t = e.currentTarget.dataset.file;
            this.triggerEvent("click", { file: t });
          },
        },
        {
          key: "getFileType",
          value: function (e, t, i) {
            if (i) return i;
            if (1 === e.length) return e[0];
            var r = t.split("."),
              a = r[r.length - 1];
            return [
              "avi",
              "wmv",
              "mkv",
              "mp4",
              "mov",
              "rm",
              "3gp",
              "flv",
              "mpg",
              "rmvb",
            ].includes(a.toLocaleLowerCase())
              ? "video"
              : "image";
          },
        },
        {
          key: "getRandFileName",
          value: function (e) {
            var t = e.lastIndexOf("."),
              i = -1 === t ? "" : e.substr(t);
            return (
              parseInt(
                ""
                  .concat(Date.now())
                  .concat(Math.floor(900 * Math.random() + 100)),
                10
              ).toString(36) + i
            );
          },
        },
        {
          key: "onDelete",
          value: function (e) {
            var t = e.currentTarget.dataset.index;
            this.deleteHandle(t);
          },
        },
        {
          key: "deleteHandle",
          value: function (e) {
            var t = this.data.customFiles[e];
            this.triggerEvent("remove", { index: e, file: t });
          },
        },
        {
          key: "updateGrid",
          value: function () {
            var e = this.properties.gridConfig,
              t = void 0 === e ? {} : e;
            (0, d.isObject)(t) || (t = {});
            var i = t,
              r = i.column,
              a = void 0 === r ? 4 : r,
              s = i.width,
              n = void 0 === s ? 160 : s,
              o = i.height,
              l = void 0 === o ? 160 : o;
            this.setData({
              gridItemStyle: "width:".concat(n, "rpx;height:").concat(l, "rpx"),
              column: a,
            });
          },
        },
        {
          key: "initDragLayout",
          value: function () {
            var e = this.properties,
              t = e.draggable,
              i = e.disabled;
            t && !i && (this.initDragList(), this.initDragBaseData());
          },
        },
        {
          key: "initDragList",
          value: function () {
            var e = 0,
              t = this.data,
              i = t.column,
              r = t.customFiles,
              a = t.customLimit,
              s = [];
            if (
              (r.forEach(function (t, r) {
                s.push({
                  realKey: e,
                  sortKey: r,
                  tranX: (r % i) * 100 + "%",
                  tranY: 100 * Math.floor(r / i) + "%",
                  data: Object.assign({}, t),
                }),
                  (e += 1);
              }),
              a > 0)
            ) {
              var n = s.length;
              s.push({
                realKey: n,
                sortKey: n,
                tranX: (n % i) * 100 + "%",
                tranY: 100 * Math.floor(n / i) + "%",
                fixed: !0,
              });
            }
            (this.data.rows = Math.ceil(s.length / i)),
              this.setData({ dragList: s });
          },
        },
        {
          key: "initDragBaseData",
          value: function () {
            var t = this,
              i = this.data,
              r = i.classPrefix,
              a = i.rows,
              s = i.column;
            if (0 !== i.customFiles.length) {
              var n = this.createSelectorQuery(),
                o = ".".concat(r, " >>> .t-grid-item"),
                l = ".".concat(r, " >>> .t-grid");
              n.select(o).boundingClientRect(),
                n.select(l).boundingClientRect(),
                n.selectViewport().scrollOffset(),
                n.exec(function (i) {
                  var n = e(i, 3),
                    o = n[0],
                    l = o.width,
                    u = o.height,
                    c = n[1],
                    d = c.left,
                    g = c.top,
                    h = n[2].scrollTop,
                    f = {
                      rows: a,
                      classPrefix: r,
                      itemWidth: l,
                      itemHeight: u,
                      wrapLeft: d,
                      wrapTop: g + h,
                      columns: s,
                    },
                    m = "height: ".concat(a * u, "px");
                  t.setData(
                    { dragBaseData: f, dragWrapStyle: m, dragLayout: !0 },
                    function () {
                      var e = setTimeout(function () {
                        t.setData({ dragging: !1 }), clearTimeout(e);
                      }, 0);
                    }
                  );
                });
            } else
              this.setData({
                dragBaseData: {},
                dragWrapStyle: "",
                dragLayout: !1,
              });
          },
        },
      ]),
      g
    );
  })(o.SuperComponent),
  p = (m = (0, n.__decorate)([(0, o.wxComponent)()], m));
exports.default = p;
