__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/stepper/stepper": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            currentValue: new Array(5),
            step: new Array(2),
            size: new Array(4),
            style: new Array(1),
            classPrefix: new Array(14),
            prefix: new Array(4),
            customStyle: new Array(1),
            min: new Array(2),
            disablePlus: new Array(2),
            disableInput: new Array(2),
            inputWidth: new Array(2),
            max: new Array(2),
            disabled: new Array(6),
            theme: new Array(6),
            disableMinus: new Array(2),
            integer: new Array(1),
          },
          K = U === true,
          e = (C) => {},
          d = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C) O(N, "name", "remove");
              },
              e
            );
          },
          g = (C) => {},
          f = (C, T, E) => {
            E(
              "input",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.size || U.prefix) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__input " +
                      Y(D.classPrefix) +
                      "__input--" +
                      Y(D.size) +
                      " " +
                      Y(D.prefix) +
                      "-class-input"
                  );
                A["classPrefix"][8] =
                  A["classPrefix"][9] =
                  A["size"][2] =
                  A["prefix"][2] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__input " +
                          Y(D.classPrefix) +
                          "__input--" +
                          Y(D.size) +
                          " " +
                          Y(D.prefix) +
                          "-class-input"
                      );
                    };
                var $A = D.inputWidth;
                if (
                  C ||
                  K ||
                  !!U.inputWidth ||
                  ($A ? !!U.inputWidth || undefined : undefined)
                )
                  R.y(N, $A ? "width:" + D.inputWidth + "px;" : "");
                A["inputWidth"][0] = A["inputWidth"][1] = (D, E, T) => {
                  var $B = D.inputWidth;
                  R.y(N, $B ? "width:" + D.inputWidth + "px;" : "");
                };
                if (C || K || !!(U.disabled || U.disableInput) || undefined)
                  O(N, "disabled", D.disabled || D.disableInput);
                A["disabled"][3] = A["disableInput"][1] = (D, E, T) => {
                  O(N, "disabled", D.disabled || D.disableInput);
                  E(N);
                };
                var $B = D.integer;
                if (C || K || !!U.integer || ($B ? undefined : undefined))
                  O(N, "type", $B ? "number" : "digit");
                A["integer"][0] = (D, E, T) => {
                  var $C = D.integer;
                  O(N, "type", $C ? "number" : "digit");
                  E(N);
                };
                if (C || K || U.currentValue) O(N, "value", D.currentValue);
                A["currentValue"][2] = (D, E, T) => {
                  O(N, "value", D.currentValue);
                  E(N);
                };
                if (C) O(N, "catchinput", "handleInput");
                if (C) O(N, "catchfocus", "handleFocus");
                if (C) O(N, "catchblur", "handleBlur");
              },
              g
            );
          },
          i = (C) => {},
          h = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C) O(N, "name", "add");
              },
              i
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A =
                  D.disabled || D.disableMinus || D.currentValue <= D.min;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.classPrefix ||
                    U.theme ||
                    U.classPrefix ||
                    U.size ||
                    U.disabled ||
                    U.disableMinus ||
                    U.currentValue ||
                    U.min ||
                    ($A
                      ? !!(U.classPrefix || U.theme) || undefined
                      : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__minus " +
                      Y(D.classPrefix) +
                      "__minus--" +
                      Y(D.theme) +
                      " " +
                      Y(D.classPrefix) +
                      "__icon--" +
                      Y(D.size) +
                      " " +
                      Y(
                        $A ? D.classPrefix + "--" + D.theme + "-disabled" : ""
                      ) +
                      " " +
                      Y(D.prefix) +
                      "-class-minus"
                  );
                A["classPrefix"][2] =
                  A["classPrefix"][3] =
                  A["theme"][0] =
                  A["classPrefix"][4] =
                  A["size"][1] =
                  A["disabled"][1] =
                  A["disableMinus"][1] =
                  A["currentValue"][1] =
                  A["min"][1] =
                  A["classPrefix"][5] =
                  A["theme"][1] =
                  A["prefix"][1] =
                    (D, E, T) => {
                      var $B =
                        D.disabled || D.disableMinus || D.currentValue <= D.min;
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__minus " +
                          Y(D.classPrefix) +
                          "__minus--" +
                          Y(D.theme) +
                          " " +
                          Y(D.classPrefix) +
                          "__icon--" +
                          Y(D.size) +
                          " " +
                          Y(
                            $B
                              ? D.classPrefix + "--" + D.theme + "-disabled"
                              : ""
                          ) +
                          " " +
                          Y(D.prefix) +
                          "-class-minus"
                      );
                    };
                if (C) O(N, "catchtap", "minusValue");
                if (C || K || !!U.step || undefined)
                  O(N, "aria-label", "减少" + D.step);
                A["step"][0] = (D, E, T) => {
                  O(N, "aria-label", "减少" + D.step);
                  E(N);
                };
                if (C) O(N, "aria-role", "button");
                if (
                  C ||
                  K ||
                  !!(U.disabled || U.disableMinus || U.currentValue || U.min) ||
                  undefined
                )
                  O(
                    N,
                    "aria-disabled",
                    D.disabled || D.disableMinus || D.currentValue <= D.min
                  );
                A["disabled"][0] =
                  A["disableMinus"][0] =
                  A["currentValue"][0] =
                  A["min"][0] =
                    (D, E, T) => {
                      O(
                        N,
                        "aria-disabled",
                        D.disabled || D.disableMinus || D.currentValue <= D.min
                      );
                      E(N);
                    };
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.disabled || D.disableInput;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.theme ||
                    U.disabled ||
                    U.disableInput ||
                    ($A ? !!(U.classPrefix || U.theme) || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__input--" +
                      Y(D.theme) +
                      " " +
                      Y($A ? D.classPrefix + "--" + D.theme + "-disabled" : "")
                  );
                A["classPrefix"][6] =
                  A["theme"][2] =
                  A["disabled"][2] =
                  A["disableInput"][0] =
                  A["classPrefix"][7] =
                  A["theme"][3] =
                    (D, E, T) => {
                      var $B = D.disabled || D.disableInput;
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__input--" +
                          Y(D.theme) +
                          " " +
                          Y(
                            $B
                              ? D.classPrefix + "--" + D.theme + "-disabled"
                              : ""
                          )
                      );
                    };
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.disabled || D.disablePlus || D.currentValue >= D.max;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.classPrefix ||
                    U.theme ||
                    U.classPrefix ||
                    U.size ||
                    U.disabled ||
                    U.disablePlus ||
                    U.currentValue ||
                    U.max ||
                    ($A
                      ? !!(U.classPrefix || U.theme) || undefined
                      : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__plus " +
                      Y(D.classPrefix) +
                      "__plus--" +
                      Y(D.theme) +
                      " " +
                      Y(D.classPrefix) +
                      "__icon--" +
                      Y(D.size) +
                      " " +
                      Y(
                        $A ? D.classPrefix + "--" + D.theme + "-disabled" : ""
                      ) +
                      " " +
                      Y(D.prefix) +
                      "-class-plus"
                  );
                A["classPrefix"][10] =
                  A["classPrefix"][11] =
                  A["theme"][4] =
                  A["classPrefix"][12] =
                  A["size"][3] =
                  A["disabled"][5] =
                  A["disablePlus"][1] =
                  A["currentValue"][4] =
                  A["max"][1] =
                  A["classPrefix"][13] =
                  A["theme"][5] =
                  A["prefix"][3] =
                    (D, E, T) => {
                      var $B =
                        D.disabled || D.disablePlus || D.currentValue >= D.max;
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__plus " +
                          Y(D.classPrefix) +
                          "__plus--" +
                          Y(D.theme) +
                          " " +
                          Y(D.classPrefix) +
                          "__icon--" +
                          Y(D.size) +
                          " " +
                          Y(
                            $B
                              ? D.classPrefix + "--" + D.theme + "-disabled"
                              : ""
                          ) +
                          " " +
                          Y(D.prefix) +
                          "-class-plus"
                      );
                    };
                if (C) O(N, "catchtap", "plusValue");
                if (C || K || !!U.step || undefined)
                  O(N, "aria-label", "增加" + D.step);
                A["step"][1] = (D, E, T) => {
                  O(N, "aria-label", "增加" + D.step);
                  E(N);
                };
                if (C) O(N, "aria-role", "button");
                if (
                  C ||
                  K ||
                  !!(U.disabled || U.disablePlus || U.currentValue || U.max) ||
                  undefined
                )
                  O(
                    N,
                    "aria-disabled",
                    D.disabled || D.disablePlus || D.currentValue >= D.max
                  );
                A["disabled"][4] =
                  A["disablePlus"][0] =
                  A["currentValue"][3] =
                  A["max"][0] =
                    (D, E, T) => {
                      O(
                        N,
                        "aria-disabled",
                        D.disabled || D.disablePlus || D.currentValue >= D.max
                      );
                      E(N);
                    };
              },
              h
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.size || U.prefix) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.size) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["classPrefix"][0] =
                  A["classPrefix"][1] =
                  A["size"][0] =
                  A["prefix"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(D.classPrefix) +
                          " " +
                          Y(D.classPrefix) +
                          "--" +
                          Y(D.size) +
                          " class " +
                          Y(D.prefix) +
                          "-class"
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/stepper/stepper";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/stepper/stepper.js";
define(
  "miniprogram_npm/tdesign-miniprogram/stepper/stepper.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      s = require("../common/src/index"),
      n = l(require("../common/config")),
      u = l(require("./props"));
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = n.default.prefix,
      h = "".concat(o, "-stepper"),
      c = (function (a) {
        r(n, a);
        var s = i(n);
        function n() {
          var e;
          return (
            t(this, n),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(o, "-class"),
              "".concat(o, "-class-input"),
              "".concat(o, "-class-minus"),
              "".concat(o, "-class-plus"),
            ]),
            (e.properties = Object.assign({}, u.default)),
            (e.controlledProps = [{ key: "value", event: "change" }]),
            (e.observers = {
              value: function (e) {
                (this.preValue = Number(e)),
                  this.setData({ currentValue: this.format(Number(e)) });
              },
            }),
            (e.data = { currentValue: 0, classPrefix: h, prefix: o }),
            (e.lifetimes = {
              attached: function () {
                var e = this.properties,
                  t = e.value,
                  r = e.min;
                this.setData({ currentValue: t ? Number(t) : r });
              },
            }),
            (e.methods = {
              isDisabled: function (e) {
                var t = this.properties,
                  r = t.min,
                  i = t.max,
                  a = t.disabled,
                  s = this.data.currentValue;
                return (
                  !!a || ("minus" === e && s <= r) || ("plus" === e && s >= i)
                );
              },
              getLen: function (e) {
                var t = e.toString();
                return -1 === t.indexOf(".") ? 0 : t.split(".")[1].length;
              },
              add: function (e, t) {
                var r = Math.max(this.getLen(e), this.getLen(t)),
                  i = Math.pow(10, r);
                return Math.round(e * i + t * i) / i;
              },
              format: function (e) {
                var t = this.properties,
                  r = t.min,
                  i = t.max,
                  a = t.step,
                  s = Math.max(this.getLen(a), this.getLen(e));
                return Math.max(
                  Math.min(i, e, Number.MAX_SAFE_INTEGER),
                  r,
                  Number.MIN_SAFE_INTEGER
                ).toFixed(s);
              },
              setValue: function (e) {
                (e = this.format(e)),
                  this.preValue !== e &&
                    ((this.preValue = e),
                    this._trigger("change", { value: Number(e) }));
              },
              minusValue: function () {
                if (this.isDisabled("minus"))
                  return this.triggerEvent("overlimit", { type: "minus" }), !1;
                var e = this.data,
                  t = e.currentValue,
                  r = e.step;
                this.setValue(this.add(t, -r));
              },
              plusValue: function () {
                if (this.isDisabled("plus"))
                  return this.triggerEvent("overlimit", { type: "plus" }), !1;
                var e = this.data,
                  t = e.currentValue,
                  r = e.step;
                this.setValue(this.add(t, r));
              },
              filterIllegalChar: function (e) {
                var t = String(e).replace(/[^0-9.]/g, ""),
                  r = t.indexOf(".");
                return this.properties.integer && -1 !== r
                  ? t.split(".")[0]
                  : this.properties.integer ||
                    -1 === r ||
                    r === t.lastIndexOf(".")
                  ? t
                  : t.split(".", 2).join(".");
              },
              handleFocus: function (e) {
                var t = e.detail.value;
                this.triggerEvent("focus", { value: t });
              },
              handleInput: function (e) {
                var t = e.detail.value;
                if ("" !== t) {
                  var r = this.filterIllegalChar(t);
                  this.setData({ currentValue: r }),
                    this.triggerEvent("input", { value: r });
                }
              },
              handleBlur: function (e) {
                var t = e.detail.value,
                  r = this.format(t);
                this.setValue(r), this.triggerEvent("blur", { value: r });
              },
            }),
            e
          );
        }
        return e(n);
      })(s.SuperComponent),
      p = (c = (0, a.__decorate)([(0, s.wxComponent)()], c));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/stepper/stepper.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/stepper/stepper.js");
