@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-side-bar {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  height: var(--td-side-bar-height, 100%);
  overflow-y: auto;
  width: var(--td-side-bar-width, 206rpx);
}
.t-side-bar::-webkit-scrollbar {
  display: none;
}
.t-side-bar__padding {
  background-color: var(
    --td-side-bar-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  flex: 1;
}
