@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-color-picker__panel {
  background: var(--td-color-picker-background, #fff);
  border-top-left-radius: var(--td-color-picker-panel-radius, 24rpx);
  border-top-right-radius: var(--td-color-picker-panel-radius, 24rpx);
  padding: 0;
  user-select: none;
  width: var(--td-color-picker-panel-width, 750rpx);
}
.t-color-picker__body {
  padding: var(--td-color-picker-panel-padding, 32rpx);
  padding-bottom: 56rpx;
}
.t-color-picker__thumb {
  box-shadow: var(
    --td-shadow-1,
    0 1px 10px rgba(0, 0, 0, 0.05),
    0 4px 5px rgba(0, 0, 0, 0.08),
    0 2px 4px -1px rgba(0, 0, 0, 0.12)
  );
  color: var(
    --td-text-color-brand,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  height: var(--td-color-picker-slider-thumb-size, 48rpx);
  outline: 0;
  width: var(--td-color-picker-slider-thumb-size, 48rpx);
  z-index: 1;
}
.t-color-picker__thumb,
.t-color-picker__thumb::after,
.t-color-picker__thumb::before {
  border-radius: var(--td-color-picker-border-radius-circle, 50%);
  box-sizing: border-box;
  position: absolute;
}
.t-color-picker__thumb::after,
.t-color-picker__thumb::before {
  border: 1px solid #dcdcdc;
  content: "";
  display: block;
}
.t-color-picker__thumb::before {
  background-color: #fff;
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
}
.t-color-picker__thumb::after {
  background: currentcolor;
  height: calc(100% - 6px);
  left: 3px;
  padding: var(--td-color-picker-slider-thumb-padding, 6rpx);
  top: 3px;
  width: calc(100% - 6px);
}
.t-color-picker__saturation {
  background: 0 0;
  border-radius: var(--td-color-picker-saturation-radius, 12rpx);
  height: var(--td-color-picker-saturation-height, 288rpx);
  overflow: hidden;
  position: relative;
}
.t-color-picker__saturation::after,
.t-color-picker__saturation::before {
  content: "";
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}
.t-color-picker__saturation::before {
  background: linear-gradient(90deg, #fff, transparent);
}
.t-color-picker__saturation::after {
  background: linear-gradient(0deg, #000, transparent);
}
.t-color-picker__saturation .t-color-picker__thumb {
  border-radius: var(--td-color-picker-border-radius-circle, 50%);
  height: var(--td-color-picker-saturation-thumb-size, 48rpx);
  transform: translate(-50%, -50%);
  width: var(--td-color-picker-saturation-thumb-size, 48rpx);
}
.t-color-picker__slider-wrapper {
  border-radius: calc(var(--td-color-picker-slider-height, 16rpx) / 2);
  padding: var(--td-color-picker-slider-wrapper-padding, 0 18rpx);
  position: relative;
}
.t-color-picker__slider-wrapper--hue-type {
  background: linear-gradient(
    90deg,
    red,
    #ff0 17%,
    #0f0 33%,
    #0ff 50%,
    #00f 67%,
    #f0f 83%,
    red
  );
  margin: 16rpx 0;
}
.t-color-picker__slider-wrapper--alpha-type {
  background: var(--td-text-color-anti, var(--td-font-white-1, #fff));
  background-image: linear-gradient(
      45deg,
      #c5c5c5 25%,
      transparent 0,
      transparent 75%,
      #c5c5c5 0,
      #c5c5c5
    ),
    linear-gradient(
      45deg,
      #c5c5c5 25%,
      transparent 0,
      transparent 75%,
      #c5c5c5 0,
      #c5c5c5
    );
  background-position: 0 0, 3px 3px;
  background-size: 6px 6px;
  margin: 40rpx 0 16rpx;
}
.t-color-picker__slider-wrapper--alpha-type .t-color-picker__rail {
  background: linear-gradient(90deg, transparent, currentcolor);
}
.t-color-picker__slider-padding {
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}
.t-color-picker__slider,
.t-color-picker__slider-padding {
  border-radius: calc(var(--td-color-picker-slider-height, 16rpx) / 2);
  height: var(--td-color-picker-slider-height, 16rpx);
}
.t-color-picker__slider {
  color: transparent;
  outline: 0;
  position: relative;
  z-index: 1;
}
.t-color-picker__slider .t-color-picker__thumb {
  top: 50%;
  transform: translate(
    var(--td-color-picker-slider-thumb-transform-x, -18rpx),
    -50%
  );
}
.t-color-picker__slider .t-color-picker__rail {
  border-radius: inherit;
  height: 100%;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 100%;
}
.t-color-picker__sliders-wrapper {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  margin: 32rpx 0 40rpx;
}
.t-color-picker__sliders {
  width: 100%;
}
.t-color-picker__sliders-preview {
  background: var(--td-text-color-anti, var(--td-font-white-1, #fff));
  background-image: linear-gradient(
      45deg,
      #c5c5c5 25%,
      transparent 0,
      transparent 75%,
      #c5c5c5 0,
      #c5c5c5
    ),
    linear-gradient(
      45deg,
      #c5c5c5 25%,
      transparent 0,
      transparent 75%,
      #c5c5c5 0,
      #c5c5c5
    );
  background-position: 0 0, 3px 3px;
  background-size: 6px 6px;
  border-radius: var(--td-color-picker-gradient-preview-radius, 6rpx);
  flex-shrink: 0;
  height: var(--td-color-picker-gradient-preview-height, 56rpx);
  margin-left: var(--td-color-picker-margin, 24rpx);
  overflow: hidden;
  width: var(--td-color-picker-gradient-preview-width, 56rpx);
}
.t-color-picker__sliders-preview-inner {
  display: block;
  height: 100%;
  width: 100%;
}
.t-color-picker__format {
  align-items: center;
  color: rgba(0, 0, 0, 0.4);
  display: -webkit-flex;
  display: flex;
  font-size: 28rpx;
  height: 56rpx;
  justify-content: space-between;
  line-height: 56rpx;
  margin-top: 40rpx;
  text-align: center;
}
.t-color-picker__format-item {
  background: var(
    --td-color-picker-format-background-color,
    var(--td-gray-color-1, #f3f3f3)
  );
}
.t-color-picker__format-item--first {
  border: 1px solid #dcdcdc;
  border-radius: 12rpx;
  flex-shrink: 0;
  margin-right: 24rpx;
  width: 136rpx;
}
.t-color-picker__format-item--second {
  flex: 1;
}
.t-color-picker__format-inputs {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: space-around;
}
.t-color-picker__format-input {
  border: 1px solid #dcdcdc;
  border-radius: 12rpx;
  flex: 1;
  margin-left: -1px;
  width: 0;
}
.t-color-picker__format-input:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.t-color-picker__format-input:first-child:not(:last-child) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.t-color-picker__format-input:last-child:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.t-color-picker__format-input--fixed {
  flex-basis: 133rpx;
  flex-grow: 0;
  flex-shrink: 0;
}
.t-color-picker__swatches-wrap {
  margin-top: 56rpx;
  position: relative;
}
.t-color-picker__swatches + .t-color-picker__swatches {
  margin-top: var(--td-color-picker-margin, 24rpx);
}
.t-color-picker__swatches-title {
  align-items: center;
  color: rgba(0, 0, 0, 0.9);
  display: -webkit-flex;
  display: flex;
  font: var(--td-color-picker-swatches-title-font, 32rpx);
  height: 48rpx;
  justify-content: space-between;
  line-height: 48rpx;
  padding: 0;
}
.t-color-picker__swatches-items {
  display: -webkit-flex;
  display: flex;
  list-style: none;
  margin-top: 24rpx;
  overflow-x: auto;
  overflow-y: auto;
  width: 100%;
}
.t-color-picker__swatches-items::-webkit-scrollbar {
  color: transparent;
  display: none;
  height: 0;
  width: 0;
}
.t-color-picker__swatches-item {
  align-items: center;
  border-radius: 6rpx;
  border-radius: var(--td-color-picker-swatch-border-radius, 6rpx);
  box-sizing: border-box;
  display: -webkit-flex;
  display: flex;
  flex-shrink: 0;
  height: var(--td-color-picker-swatch-height, 48rpx);
  justify-content: center;
  margin-right: 24rpx;
  overflow: hidden;
  padding: var(--td-color-picker-swatch-padding, 0);
  position: relative;
  transform-origin: center;
  transition: all var(--td-anim-duration-base, 0.2s)
    var(--td-anim-time-fn-easing, cubic-bezier(0.38, 0, 0.24, 1));
  width: var(--td-color-picker-swatch-width, 48rpx);
}
.t-color-picker__swatches-item::after {
  background: rgba(0, 0, 0, 0.2);
  content: "";
  height: 100%;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  width: 100%;
}
.t-color-picker__swatches-item:active::after {
  opacity: 1;
}
.t-color-picker__swatches-inner {
  border-radius: var(--td-color-picker-swatch-border-radius, 6rpx);
  display: block;
  height: 100%;
  position: relative;
  width: 100%;
}
