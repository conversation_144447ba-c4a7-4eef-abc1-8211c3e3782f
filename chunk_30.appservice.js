__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/icon/icon": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            ariaRole: new Array(1),
            componentPrefix: new Array(1),
            customStyle: new Array(1),
            ariaLabel: new Array(1),
            iconStyle: new Array(1),
            ariaHidden: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          d,
          g = (C) => {},
          f = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__image");
                if (C || K || U.name) O(N, "src", D.name);
                if (C) O(N, "mode", "aspectFit");
              },
              g
            );
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "--image");
                },
                f
              );
            }
          },
          h,
          j = (C) => {},
          i = (C, T, E) => {
            if (h === 1) {
              E(
                "label",
                {},
                (N, C) => {
                  var $A = D.prefix;
                  if (
                    C ||
                    K ||
                    !!(
                      U.prefix ||
                      ($A ? U.prefix : U.classPrefix) ||
                      U.name ||
                      U.classPrefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y($A ? D.prefix : D.classPrefix) +
                        "-" +
                        Y(D.name) +
                        " " +
                        Y(D.classPrefix) +
                        "-base"
                    );
                },
                j
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.isImage ? 1 : 0;
            B(d, e);
            h = P(X(a).isValidIconName)(D.name) && !D.isImage ? 1 : 0;
            B(h, i);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.prefix;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    ($A ? U.prefix : U.classPrefix) ||
                    U.componentPrefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y($A ? D.prefix : D.classPrefix) +
                      " class " +
                      Y(D.componentPrefix) +
                      "-class"
                  );
                A["componentPrefix"][0] = (D, E, T) => {
                  var $B = D.prefix;
                  L(
                    N,
                    Y($B ? D.prefix : D.classPrefix) +
                      " class " +
                      Y(D.componentPrefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([U.iconStyle, U.style, U.customStyle])
                  ) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.iconStyle, D.style, D.customStyle]));
                A["iconStyle"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([D.iconStyle, D.style, D.customStyle])
                      );
                    };
                if (C || K || U.ariaHidden) O(N, "aria-hidden", D.ariaHidden);
                A["ariaHidden"][0] = (D, E, T) => {
                  O(N, "aria-hidden", D.ariaHidden);
                  E(N);
                };
                if (C || K || U.ariaLabel) O(N, "aria-label", D.ariaLabel);
                A["ariaLabel"][0] = (D, E, T) => {
                  O(N, "aria-label", D.ariaLabel);
                  E(N);
                };
                if (C || K || U.ariaRole) O(N, "aria-role", D.ariaRole);
                A["ariaRole"][0] = (D, E, T) => {
                  O(N, "aria-role", D.ariaRole);
                  E(N);
                };
                if (C) R.v(N, "tap", "onTap", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/icon/icon";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/icon/icon.js";
define(
  "miniprogram_npm/tdesign-miniprogram/icon/icon.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
      t = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      a = require("../common/src/index"),
      o = l(require("../common/config")),
      c = l(require("./props")),
      u = require("../common/utils");
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var f = o.default.prefix,
      d = "".concat(f, "-icon"),
      h = (function (a) {
        i(l, a);
        var o = n(l);
        function l() {
          var t;
          return (
            r(this, l),
            ((t = o.apply(this, arguments)).externalClasses = [
              "".concat(f, "-class"),
            ]),
            (t.properties = c.default),
            (t.data = {
              componentPrefix: f,
              classPrefix: d,
              isImage: !1,
              iconStyle: void 0,
            }),
            (t.observers = {
              "name, color, size, style": function () {
                this.setIconStyle();
              },
            }),
            (t.methods = {
              onTap: function (e) {
                this.triggerEvent("click", e.detail);
              },
              setIconStyle: function () {
                var t = this,
                  r = this.data,
                  i = r.name,
                  n = r.color,
                  a = r.size,
                  o = r.classPrefix,
                  c = -1 !== i.indexOf("/"),
                  l = (0, u.addUnit)(a),
                  f = n ? { color: n } : {},
                  d = a ? { "font-size": l } : {},
                  h = Object.assign(Object.assign({}, f), d);
                this.setData({ isImage: c }, function () {
                  return (0, s.__awaiter)(
                    t,
                    void 0,
                    void 0,
                    e().mark(function t() {
                      var r;
                      return e().wrap(
                        function (e) {
                          for (;;)
                            switch ((e.prev = e.next)) {
                              case 0:
                                if (!c) {
                                  e.next = 8;
                                  break;
                                }
                                if (((r = l), (e.t0 = r), e.t0)) {
                                  e.next = 6;
                                  break;
                                }
                                return (
                                  (e.next = 6),
                                  (0, u.getRect)(this, ".".concat(o))
                                    .then(function (e) {
                                      r = (0, u.addUnit)(
                                        null == e ? void 0 : e.height
                                      );
                                    })
                                    .catch(function () {})
                                );
                              case 6:
                                (h.width = r), (h.height = r);
                              case 8:
                                this.setData({
                                  iconStyle: "".concat((0, u.styles)(h)),
                                });
                              case 9:
                              case "end":
                                return e.stop();
                            }
                        },
                        t,
                        this
                      );
                    })
                  );
                });
              },
            }),
            t
          );
        }
        return t(l);
      })(a.SuperComponent),
      p = (h = (0, s.__decorate)([(0, a.wxComponent)()], h));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/icon/icon.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/icon/icon.js");
