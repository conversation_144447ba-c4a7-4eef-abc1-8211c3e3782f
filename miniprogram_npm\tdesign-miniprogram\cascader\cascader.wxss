@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-cascader {
  --td-radio-icon-checked-color: var(
    --td-cascader-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  --td-tab-item-active-color: var(
    --td-cascader-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  --td-tab-track-color: var(
    --td-cascader-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  background-color: var(
    --td-cascader-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  border-radius: var(--td-radius-extra-large, 24rpx)
    var(--td-radius-extra-large, 24rpx) 0 0;
  color: var(
    --td-cascader-title-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
}
.t-cascader__close-btn {
  position: absolute;
  right: 32rpx;
  top: 24rpx;
}
.t-cascader__title {
  font-size: var(--td-cascder-title-font-size, 36rpx);
  font-weight: 700;
  line-height: var(--td-cascader-title-height, 26rpx);
  padding: var(--td-cascader-title-padding, var(--td-spacer-2, 32rpx));
  position: relative;
  text-align: center;
}
.t-cascader__content {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  height: var(--td-cascader-content-height, 78vh);
  width: 100%;
}
.t-cascader__options {
  width: 100vw;
}
.t-cascader__options-title {
  box-sizing: border-box;
  color: var(
    --td-cascader-options-title-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  font-size: var(--td-font-size-base, 28rpx);
  line-height: 44rpx;
  padding-left: var(--td-spacer-2, 32rpx);
  padding-top: 40rpx;
}
.t-cascader__options-container {
  display: -webkit-flex;
  display: flex;
  flex: 1;
  transition: all 0.3s ease;
}
.t-cascader__step {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  height: var(--td-cascader-step-height, 88rpx);
}
.t-cascader__steps {
  padding: 0 32rpx 10rpx;
  position: relative;
}
.t-cascader__steps::after {
  background-color: var(
    --td-cascader-border-color,
    var(--td-component-stroke, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: unset;
  transform: scaleY(0.5);
}
.t-cascader__step-dot {
  border: 2rpx solid
    var(
      --td-cascader-active-color,
      var(--td-brand-color, var(--td-primary-color-7, #0052d9))
    );
  border-radius: 50%;
  box-sizing: border-box;
  height: var(--td-cascader-step-dot-size, 16rpx);
  position: relative;
  width: var(--td-cascader-step-dot-size, 16rpx);
}
.t-cascader__step-dot:not(.t-cascader__step-dot--last)::after {
  content: "";
  display: block;
  height: 36rpx;
  left: 50%;
  position: absolute;
  top: calc(var(--td-cascader-step-dot-size, 16rpx) + 14rpx);
  transform: translateX(-50%);
  width: 2rpx;
}
.t-cascader__step-dot--active,
.t-cascader__step-dot:not(.t-cascader__step-dot--last)::after {
  background: var(
    --td-cascader-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-cascader__step-dot--active {
  border-color: var(
    --td-cascader-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-cascader__step-label {
  font-size: var(--td-font-size-m, 32rpx);
  padding-left: var(--td-spacer-2, 32rpx);
}
.t-cascader__step-label--active {
  color: var(
    --td-cascader-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  font-weight: 600;
}
.t-cascader__step-arrow {
  color: var(
    --td-cascader-step-arrow-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  margin-left: auto;
}
