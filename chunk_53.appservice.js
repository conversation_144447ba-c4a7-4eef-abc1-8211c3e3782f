__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/swipe-cell/swipe-cell": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/swipe-cell/swipe-cell"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            leftWidth: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            skipMove: new Array(2),
            rightWidth: new Array(1),
            prefix: new Array(1),
            disabled: new Array(4),
            opened: new Array(1),
          },
          K = U === true,
          g = (C, h, i, j, k, l, T, E) => {
            var n,
              p,
              q = (C, T, E, B, F, S, J) => {
                var $A = I(p);
                if (p && $A)
                  $A(
                    R,
                    C,
                    Object.assign(
                      { tClass: D.classPrefix + "__icon", name: X(h).icon },
                      X(X(h).icon),
                      {}
                    ),
                    K ||
                      (U
                        ? Z(j, "icon") === true ||
                          Object.assign(
                            {
                              tClass: !!U.classPrefix || undefined,
                              name: Z(j, "icon"),
                            },
                            X(Z(j, "icon")),
                            {}
                          )
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              },
              o = (C, T, E, B, F, S, J) => {
                if (n === 1) {
                  p = "icon";
                  B(p, q);
                }
              },
              r,
              t = (C, T) => {
                C || K || Z(j, "text") ? T(Y(X(h).text)) : T();
              },
              s = (C, T, E) => {
                if (r === 1) {
                  E(
                    "text",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__text");
                    },
                    t
                  );
                }
              },
              m = (C, T, E, B) => {
                n = X(h).icon ? 1 : 0;
                B(n, o);
                r = X(h).text ? 1 : 0;
                B(r, s);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || Z(j, "className")) ||
                  undefined
                )
                  L(N, Y(D.classPrefix) + "__content " + Y(X(h).className));
                if (C || K || Z(j, "style")) R.y(N, X(h).style);
                if (C || K || j) R.d(N, "action", h);
                if (C) R.v(N, "tap", "onActionTap", !1, !1, !1, !1);
              },
              m
            );
          },
          f = (C, T, E, B, F, S) => {
            S("left");
            F(D.left, "index", U ? U.left : undefined, [0, "left"], g);
          },
          i = (C, j, k, l, m, n, T, E) => {
            var p,
              r,
              s = (C, T, E, B, F, S, J) => {
                var $A = I(r);
                if (r && $A)
                  $A(
                    R,
                    C,
                    Object.assign(
                      { tClass: D.classPrefix + "__icon", name: X(j).icon },
                      X(X(j).icon),
                      {}
                    ),
                    K ||
                      (U
                        ? Z(l, "icon") === true ||
                          Object.assign(
                            {
                              tClass: !!U.classPrefix || undefined,
                              name: Z(l, "icon"),
                            },
                            X(Z(l, "icon")),
                            {}
                          )
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              },
              q = (C, T, E, B, F, S, J) => {
                if (p === 1) {
                  r = "icon";
                  B(r, s);
                }
              },
              t,
              v = (C, T) => {
                C || K || Z(l, "text") ? T(Y(X(j).text)) : T();
              },
              u = (C, T, E) => {
                if (t === 1) {
                  E(
                    "text",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__text");
                    },
                    v
                  );
                }
              },
              o = (C, T, E, B) => {
                p = X(j).icon ? 1 : 0;
                B(p, q);
                t = X(j).text ? 1 : 0;
                B(t, u);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || Z(l, "className")) ||
                  undefined
                )
                  L(N, Y(D.classPrefix) + "__content " + Y(X(j).className));
                if (C || K || Z(l, "style")) R.y(N, X(j).style);
                if (C || K || l) R.d(N, "action", j);
                if (C) R.v(N, "tap", "onActionTap", !1, !1, !1, !1);
              },
              o
            );
          },
          h = (C, T, E, B, F, S) => {
            S("right");
            F(D.right, "index", U ? U.right : undefined, [0, "right"], i);
          },
          e = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__left");
                if (C) R.d(N, "key", "left");
              },
              f
            );
            S("");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__right");
                if (C) R.d(N, "key", "right");
              },
              h
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) R.i(N, "wrapper");
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                A["prefix"][0] = (D, E, T) => {
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
                if (C || K || Z(undefined, "onOpenedChange"))
                  R.p(N, "opened", X(a).onOpenedChange, [
                    1,
                    "miniprogram_npm/tdesign-miniprogram/swipe-cell/swipe-cell",
                    "onOpenedChange",
                  ]);
                if (C || K || Z(undefined, "initLeftWidth"))
                  R.p(N, "leftWidth", X(a).initLeftWidth, [
                    1,
                    "miniprogram_npm/tdesign-miniprogram/swipe-cell/swipe-cell",
                    "initLeftWidth",
                  ]);
                if (C || K || Z(undefined, "initRightWidth"))
                  R.p(N, "rightWidth", X(a).initRightWidth, [
                    1,
                    "miniprogram_npm/tdesign-miniprogram/swipe-cell/swipe-cell",
                    "initRightWidth",
                  ]);
                if (C || K || U.opened) O(N, "opened", D.opened);
                A["opened"][0] = (D, E, T) => {
                  O(N, "opened", D.opened);
                  E(N);
                };
                if (C || K || U.leftWidth) O(N, "leftWidth", D.leftWidth);
                A["leftWidth"][0] = (D, E, T) => {
                  O(N, "leftWidth", D.leftWidth);
                  E(N);
                };
                if (C || K || U.rightWidth) O(N, "rightWidth", D.rightWidth);
                A["rightWidth"][0] = (D, E, T) => {
                  O(N, "rightWidth", D.rightWidth);
                  E(N);
                };
                if (C) R.d(N, "key", "cell");
                if (C) R.v(N, "tap", "onTap", !1, !1, !1, !1);
                if (
                  C ||
                  K ||
                  !!(U.disabled || Z(undefined, "startDrag")) ||
                  undefined
                )
                  R.v(
                    N,
                    "touchstart",
                    D.disabled || X(a).startDrag,
                    !1,
                    !1,
                    !1,
                    !0
                  );
                A["disabled"][0] = (D, E, T) => {
                  R.v(
                    N,
                    "touchstart",
                    D.disabled || X(a).startDrag,
                    !1,
                    !1,
                    !1,
                    !0
                  );
                };
                var $A = D.skipMove;
                if (
                  C ||
                  K ||
                  !!U.skipMove ||
                  ($A
                    ? undefined
                    : !!(U.disabled || Z(undefined, "onDrag")) || undefined)
                )
                  R.v(
                    N,
                    "touchmove",
                    $A ? "" : D.disabled || X(a).onDrag,
                    !1,
                    !1,
                    !1,
                    !0
                  );
                A["skipMove"][0] = A["disabled"][1] = (D, E, T) => {
                  var $B = D.skipMove;
                  R.v(
                    N,
                    "touchmove",
                    $B ? "" : D.disabled || X(a).onDrag,
                    !1,
                    !1,
                    !1,
                    !0
                  );
                };
                var $B = D.skipMove;
                if (
                  C ||
                  K ||
                  !!U.skipMove ||
                  ($B
                    ? undefined
                    : !!(U.disabled || Z(undefined, "endDrag")) || undefined)
                )
                  R.v(
                    N,
                    "touchend",
                    $B ? "" : D.disabled || X(a).endDrag,
                    !1,
                    !1,
                    !1,
                    !0
                  );
                A["skipMove"][1] = A["disabled"][2] = (D, E, T) => {
                  var $C = D.skipMove;
                  R.v(
                    N,
                    "touchend",
                    $C ? "" : D.disabled || X(a).endDrag,
                    !1,
                    !1,
                    !1,
                    !0
                  );
                };
                if (
                  C ||
                  K ||
                  !!(U.disabled || Z(undefined, "endDrag")) ||
                  undefined
                )
                  R.v(
                    N,
                    "touchcancel",
                    D.disabled || X(a).endDrag,
                    !1,
                    !1,
                    !1,
                    !0
                  );
                A["disabled"][3] = (D, E, T) => {
                  R.v(
                    N,
                    "touchcancel",
                    D.disabled || X(a).endDrag,
                    !1,
                    !1,
                    !1,
                    !0
                  );
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/swipe-cell/swipe-cell";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/swipe-cell/swipe-cell.js";
define(
  "miniprogram_npm/tdesign-miniprogram/swipe-cell/swipe-cell.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/slicedToArray"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      a = require("../common/src/index"),
      o = h(require("../common/config")),
      c = h(require("./props")),
      u = require("../common/utils"),
      l = require("../common/wechat");
    function h(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var p = [],
      f = o.default.prefix,
      d = "".concat(f, "-swipe-cell"),
      v = ".".concat(d),
      m = (function (s) {
        r(o, s);
        var a = n(o);
        function o() {
          var e;
          return (
            t(this, o),
            ((e = a.apply(this, arguments)).externalClasses = [
              "".concat(f, "-class"),
            ]),
            (e.options = { multipleSlots: !0 }),
            (e.properties = c.default),
            (e.data = {
              prefix: f,
              wrapperStyle: "",
              closed: !0,
              classPrefix: d,
              skipMove: !1,
            }),
            (e.observers = {
              "left, right": function () {
                this.setSwipeWidth();
              },
            }),
            (e.lifetimes = {
              attached: function () {
                p.push(this);
              },
              ready: function () {
                this.setSwipeWidth();
              },
              detached: function () {
                var e = this;
                p = p.filter(function (t) {
                  return t !== e;
                });
              },
            }),
            e
          );
        }
        return (
          i(o, [
            {
              key: "setSwipeWidth",
              value: function () {
                var t = this;
                Promise.all([
                  (0, u.getRect)(this, "".concat(v, "__left")),
                  (0, u.getRect)(this, "".concat(v, "__right")),
                ]).then(function (i) {
                  var r = e(i, 2),
                    n = r[0],
                    s = r[1];
                  0 !== n.width ||
                    0 !== s.width ||
                    t._hasObserved ||
                    ((t._hasObserved = !0),
                    (0, l.getObserver)(t, ".".concat(d)).then(function () {
                      t.setSwipeWidth();
                    })),
                    t.setData({ leftWidth: n.width, rightWidth: s.width });
                });
              },
            },
            {
              key: "skipMove",
              value: function () {
                this.data.skipMove || this.setData({ skipMove: !0 });
              },
            },
            {
              key: "catchMove",
              value: function () {
                this.data.skipMove && this.setData({ skipMove: !1 });
              },
            },
            {
              key: "open",
              value: function () {
                this.setData({ opened: !0 });
              },
            },
            {
              key: "close",
              value: function () {
                this.setData({ opened: !1 });
              },
            },
            {
              key: "closeOther",
              value: function () {
                var e = this;
                p.filter(function (t) {
                  return t !== e;
                }).forEach(function (e) {
                  return e.close();
                });
              },
            },
            {
              key: "onTap",
              value: function () {
                this.close();
              },
            },
            {
              key: "onActionTap",
              value: function (e) {
                var t = e.currentTarget.dataset.action;
                this.triggerEvent("click", t);
              },
            },
          ]),
          o
        );
      })(a.SuperComponent),
      b = (m = (0, s.__decorate)([(0, a.wxComponent)()], m));
    exports.default = b;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/swipe-cell/swipe-cell.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/swipe-cell/swipe-cell.js");
