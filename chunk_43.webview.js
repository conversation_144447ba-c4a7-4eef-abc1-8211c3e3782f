__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/rate/rate": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/rate/rate"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { style: new Array(1), customStyle: new Array(1) },
          K = U === true,
          f = (C, g, h, i, j, k, T, E) => {
            var l = (C) => {};
            E(
              "t-icon",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    Z(undefined, "getIconClass") ||
                    U.classPrefix ||
                    U.defaultValue ||
                    U.value ||
                    j ||
                    U.allowHalf ||
                    U.disabled ||
                    U.scaleIndex
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__icon " +
                      Y(
                        P(X(b).getIconClass)(
                          D.classPrefix + "__icon",
                          D.defaultValue,
                          D.value,
                          h,
                          D.allowHalf,
                          D.disabled,
                          D.scaleIndex
                        )
                      )
                  );
                var $A = D.count - h > 1;
                if (
                  C ||
                  K ||
                  !!(
                    U.count ||
                    j ||
                    ($A
                      ? !!(Z(undefined, "addUnit") || U.gap) || undefined
                      : undefined) ||
                    Z(undefined, "getColor") ||
                    U.color
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    "margin-right:" +
                      Y($A ? P(X(a).addUnit)(D.gap) : 0) +
                      ";" +
                      Y(P(X(b).getColor)(D.color))
                  );
                if (C || K || !!U.prefix || undefined)
                  O(N, "t-class", Y(D.prefix) + "-class-icon");
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getIconName") ||
                    U.defaultValue ||
                    U.value ||
                    j ||
                    U.icon
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "name",
                    P(X(b).getIconName)(D.defaultValue, D.value, h, D.icon)
                  );
                if (C || K || U.size) O(N, "size", D.size);
                if (C || K || U.iconPrefix) O(N, "prefix", D.iconPrefix);
              },
              l
            );
          },
          e = (C, T, E, B, F) => {
            F(D.count, "*this", U ? U.count : undefined, [0, "count"], f);
          },
          g,
          i = (C, T) => {
            C ||
            K ||
            !!(
              Z(undefined, "getText") ||
              U.texts ||
              U.value ||
              U.defaultTexts
            ) ||
            undefined
              ? T(Y(P(X(b).getText)(D.texts, D.value, D.defaultTexts)))
              : T();
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([!!U.value || undefined])]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__text", [
                          ["active", D.value > 0],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-text"
                    );
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                i
              );
            }
          },
          j,
          l = (C, T) => {
            C ||
            K ||
            !!(
              U.value ||
              Z(undefined, "getText") ||
              U.texts ||
              U.value ||
              U.defaultTexts
            ) ||
            undefined
              ? T(
                  Y(
                    Y(D.value + "星 ") +
                      Y(P(X(b).getText)(D.texts, D.value, D.defaultTexts))
                  )
                )
              : T();
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([!!U.value || undefined]),
                        Q.a([U.isVisibleToScreenReader]),
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__text", [
                          ["active", D.value > 0],
                          ["sr-only", D.isVisibleToScreenReader],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-text"
                    );
                  if (C) O(N, "aria-role", "alert");
                  if (C) O(N, "aria-live", "assertive");
                },
                l
              );
            }
          },
          m,
          p,
          r,
          u = (C) => {},
          v = (C, T) => {
            C || K || !!(Z(undefined, "ceil") || U.value) || undefined
              ? T(Y(P(X(b).ceil)(D.value) - 0.5))
              : T();
          },
          t = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__icon " +
                      Y(D.classPrefix) +
                      "__icon--selected-half"
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getColor") || U.color) ||
                  undefined
                )
                  R.y(N, P(X(b).getColor)(D.color));
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getIconName") ||
                    U.defaultValue ||
                    U.value ||
                    U.index ||
                    U.icon
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "name",
                    P(X(b).getIconName)(
                      D.defaultValue,
                      D.value,
                      D.index,
                      D.icon
                    )
                  );
                if (C || K || U.size) O(N, "size", D.size);
              },
              u
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__tips-text");
              },
              v
            );
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([
                          !!(Z(undefined, "ceil") || U.value || U.value) ||
                            undefined,
                        ]),
                      ])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__tips-item", [
                        ["active", P(X(b).ceil)(D.value) - 0.5 == D.value],
                      ])
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "ceil") || U.value) ||
                    undefined
                  )
                    R.d(N, "value", P(X(b).ceil)(D.value) - 0.5);
                  if (C) R.v(N, "tap", "onSelect", !1, !1, !1, !1);
                },
                t
              );
            }
          },
          x = (C) => {},
          y = (C, T) => {
            C || K || !!(Z(undefined, "ceil") || U.value) || undefined
              ? T(Y(P(X(b).ceil)(D.value)))
              : T();
          },
          w = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "cls") || U.classPrefix || Q.a([])) ||
                  undefined
                )
                  L(N, P(X(a).cls)(D.classPrefix + "__icon", ["selected"]));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getColor") || U.color) ||
                  undefined
                )
                  R.y(N, P(X(b).getColor)(D.color));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getIconName") || U.defaultValue || U.icon) ||
                  undefined
                )
                  O(
                    N,
                    "name",
                    P(X(b).getIconName)(D.defaultValue, 0, 0, D.icon)
                  );
                if (C || K || U.size) O(N, "size", D.size);
              },
              x
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__tips-text");
              },
              y
            );
          },
          A0 = (C) => {},
          B0 = (C, T) => {
            C || K || U.value ? T(Y(D.value)) : T();
          },
          z = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([
                        !!(Z(undefined, "ceil") || U.value || U.value) ||
                          undefined,
                      ]),
                      Q.a([
                        !!(Z(undefined, "ceil") || U.value || U.value) ||
                          undefined,
                      ]),
                    ])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__icon", [
                      ["selected", P(X(b).ceil)(D.value) == D.value],
                      ["selected-half", P(X(b).ceil)(D.value) != D.value],
                    ])
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getColor") || U.color) ||
                  undefined
                )
                  R.y(N, P(X(b).getColor)(D.color));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getIconName") || U.defaultValue || U.icon) ||
                  undefined
                )
                  O(
                    N,
                    "name",
                    P(X(b).getIconName)(D.defaultValue, 0, 0, D.icon)
                  );
                if (C || K || U.size) O(N, "size", D.size);
              },
              A0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__tips-text");
              },
              B0
            );
          },
          q = (C, T, E, B) => {
            if (p === 1) {
              r = D.allowHalf ? 1 : 0;
              B(r, s);
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([
                          !!(Z(undefined, "ceil") || U.value || U.value) ||
                            undefined,
                        ]),
                      ])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__tips-item", [
                        ["active", P(X(b).ceil)(D.value) == D.value],
                      ])
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "ceil") || U.value) ||
                    undefined
                  )
                    R.d(N, "value", P(X(b).ceil)(D.value));
                  if (C) R.v(N, "tap", "onSelect", !1, !1, !1, !1);
                },
                w
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([
                          !!(
                            Z(undefined, "ceil") ||
                            U.value ||
                            U.value ||
                            U.actionType
                          ) || undefined,
                        ]),
                      ])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__tips-item", [
                        [
                          "active",
                          P(X(b).ceil)(D.value) == D.value &&
                            D.actionType == "tap",
                        ],
                      ])
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "ceil") || U.value) ||
                    undefined
                  )
                    R.d(N, "value", P(X(b).ceil)(D.value));
                  if (C) R.v(N, "tap", "onSelect", !1, !1, !1, !1);
                },
                z
              );
            }
          },
          o = (C, T, E, B) => {
            p = D.actionType == "tap" ? 1 : 0;
            B(p, q);
          },
          n = (C, T, E) => {
            if (m === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.placement])
                    ) ||
                    undefined
                  )
                    L(N, P(X(a).cls)(D.classPrefix + "__tips", [D.placement]));
                  if (C || K || !!U.tipsLeft || undefined)
                    R.y(N, "left:" + Y(D.tipsLeft) + "px");
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                o
              );
            }
          },
          d = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__wrapper " + Y(D.prefix) + "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "regSize") || U.size) ||
                  undefined
                )
                  R.y(N, "font-size:" + Y(P(X(b).regSize)(D.size)));
                if (C) O(N, "aria-role", "slider");
                if (C || K || U.count) O(N, "aria-valuemax", D.count);
                if (C || K || undefined) O(N, "aria-valuemin", 0);
                if (C || K || U.value) O(N, "aria-valuenow", D.value);
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getText") ||
                    U.texts ||
                    U.value ||
                    U.defaultTexts
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-valuetext",
                    P(X(b).getText)(D.texts, D.value, D.defaultTexts)
                  );
                var $A = !D.disabled;
                if (C || K || !!U.disabled || ($A ? undefined : undefined))
                  R.v(
                    N,
                    "touchstart",
                    $A ? "onTouchStart" : "",
                    !1,
                    !1,
                    !1,
                    !0
                  );
                var $B = !D.disabled;
                if (C || K || !!U.disabled || ($B ? undefined : undefined))
                  R.v(N, "touchmove", $B ? "onTouchMove" : "", !1, !1, !1, !0);
                if (C) R.v(N, "tap", "onTap", !1, !1, !1, !1);
                var $C = !D.disabled;
                if (C || K || !!U.disabled || ($C ? undefined : undefined))
                  R.v(N, "touchend", $C ? "onTouchEnd" : "", !1, !1, !1, !0);
                var $D = !D.disabled;
                if (C || K || !!U.disabled || ($D ? undefined : undefined))
                  R.v(N, "touchcancel", $D ? "onTouchEnd" : "", !1, !1, !1, !0);
              },
              e
            );
            g = D.showText ? 1 : 0;
            B(g, h);
            j = D.isVisibleToScreenReader ? 1 : 0;
            B(j, k);
            m = D.tipsVisible && D.placement ? 1 : 0;
            B(m, n);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + " class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/rate/rate.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-rate{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:flex-start;justify-content:flex-start;position:relative}\n.",
        [1],
        "t-rate__wrapper{display:-webkit-inline-flex;display:inline-flex;line-height:1em}\n.",
        [1],
        "t-rate__icon{display:block;line-height:1em;transition:-webkit-transform .3s ease;transition:transform .3s ease;transition:transform .3s ease,-webkit-transform .3s ease;width:1em}\n.",
        [1],
        "t-rate__icon--current{-webkit-transform:scale(var(--td-rate-icon-scale,1.33));transform:scale(var(--td-rate-icon-scale,1.33))}\n.",
        [1],
        "t-rate__icon--selected{color:var(--td-rate-selected-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
        [1],
        "t-rate__icon--selected-half{background:linear-gradient(to right,var(--td-rate-selected-color,var(--td-warning-color,var(--td-warning-color-5,#e37318))) 0,var(--td-rate-selected-color,var(--td-warning-color,var(--td-warning-color-5,#e37318))) 50%,var(--td-rate-unselected-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc))) 51%,var(--td-rate-unselected-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc))) 100%);-webkit-background-clip:text;background-clip:text;color:transparent}\n.",
        [1],
        "t-rate__icon--unselected{color:var(--td-rate-unselected-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)))}\n.",
        [1],
        "t-rate__text{color:var(--td-rate-text-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))));font-size:var(--td-rate-text-font-size,var(--td-font-size-m,",
        [0, 32],
        "));margin-left:",
        [0, 32],
        ";vertical-align:middle}\n.",
        [1],
        "t-rate__text--active{color:var(--td-rate-text-active-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-weight:var(--td-rate-text-active-font-weight,600)}\n.",
        [1],
        "t-rate__text--sr-only{clip:rect(0,0,0,0);border:0;-webkit-clip-path:inset(50%);clip-path:inset(50%);height:1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}\n.",
        [1],
        "t-rate__tips{-webkit-align-items:center;align-items:center;background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff));border-radius:",
        [0, 12],
        ";bottom:calc(100% + ",
        [0, 16],
        ");box-shadow:var(--td-shadow-1,0 1px 10px rgba(0,0,0,.05),0 4px 5px rgba(0,0,0,.08),0 2px 4px -1px rgba(0,0,0,.12));display:-webkit-flex;display:flex;padding:",
        [0, 8],
        ";position:absolute;-webkit-transform:translateX(-50%);transform:translateX(-50%)}\n.",
        [1],
        "t-rate__tips--bottom{bottom:auto;top:calc(100% + ",
        [0, 16],
        ")}\n.",
        [1],
        "t-rate__tips-item{-webkit-align-items:center;align-items:center;border-radius:",
        [0, 6],
        ";display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;width:",
        [0, 64],
        "}\n.",
        [1],
        "t-rate__tips-item--active{background-color:var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7))}\n.",
        [1],
        "t-rate__tips-text{font-size:",
        [0, 24],
        ";line-height:",
        [0, 40],
        ";text-align:center}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/rate/rate.wxss" }
    );
}
