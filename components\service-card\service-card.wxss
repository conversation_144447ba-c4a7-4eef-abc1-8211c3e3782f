.fwdd_list {
  background: #fff;
  border-radius: 24rpx;
  margin: 20rpx auto;
  padding: 24rpx;
  width: 95%;
}
.fwdd_list_top {
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.fwdd_list_top,
.fwdd_list_top_left {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.fwdd_list_top_left {
  gap: 12rpx;
}
.avatar {
  border-radius: 50%;
  height: 36rpx;
  width: 36rpx;
}
.name {
  color: #333;
  font-size: 28rpx;
}
.id {
  color: #999;
  font-size: 24rpx;
}
.fwdd_list_top_right {
  display: -webkit-flex;
  display: flex;
  gap: 12rpx;
}
.btn-delivery,
.btn-service {
  border-radius: 8rpx;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
}
.btn-service {
  border: 2rpx solid #ff4d4f;
  color: #ff4d4f;
}
.btn-delivery {
  border: 2rpx solid #ddd;
  color: #666;
}
.fwdd_list_info {
  border-bottom: 2rpx solid #f5f5f5;
  border-top: 2rpx solid #f5f5f5;
  padding: 20rpx 0;
}
.fwdd_list_info,
.fwdd_list_info_left {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.fwdd_list_info_left {
  background-color: #eff2f5;
  border-radius: 15rpx;
  padding: 5rpx;
}
.service-icon {
  border-radius: 16rpx;
  height: 160rpx;
  width: 160rpx;
}
.fwdd_list_info_midle {
  display: -webkit-flex;
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  margin-left: 35rpx;
}
.service-title {
  color: #1e1e1e;
  font-size: 30rpx;
  font-weight: 700;
}
.attachment-box {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  margin: 8rpx 0;
}
.attachment-label {
  color: #2b5ee3;
  font-size: 26rpx;
}
.attachment-count {
  color: #666;
  font-size: 26rpx;
}
.download-hint {
  color: #999;
  font-size: 24rpx;
}
.fwdd_list_info_right {
  align-items: flex-end;
  flex-direction: column;
  justify-content: center;
  min-width: 120rpx;
  text-align: right;
}
.fwdd_list_info_right,
.price-box {
  display: -webkit-flex;
  display: flex;
}
.price-box {
  align-items: baseline;
}
.price-symbol {
  color: #ff4d4f;
  font-size: 24rpx;
}
.price-value {
  color: #ff4d4f;
  font-size: 32rpx;
  font-weight: 500;
}
.quantity {
  color: #999;
  font-size: 24rpx;
  margin: 8rpx 0;
}
.total-price {
  color: #666;
  font-size: 24rpx;
}
.amount {
  color: #ff4d4f;
}
.fwdd_list_bottom {
  margin-top: 20rpx;
}
.contact-btn {
  color: #2b5ee3;
  font-size: 28rpx;
  margin-left: 20rpx;
}
.confirm-btn {
  border: 5rpx solid #ff4d4f;
  border-radius: 20rpx;
  color: #ff4d4f;
  font-size: 24rpx;
  padding: 8rpx 32rpx;
}
.service-description {
  color: #666;
  font-size: 22rpx;
  line-height: 1.8;
  margin: 8rpx 0;
}
.service-stats {
  display: block;
  margin: 4rpx 0;
}
.category {
  margin-top: 8rpx;
}
.service-time {
  color: #999;
  font-size: 24rpx;
}
.action-buttons {
  display: -webkit-flex;
  display: flex;
  gap: 20rpx;
}
.fwdd_list_bottom {
  gap: 80rpx;
  justify-content: space-between;
}
.fwdd_list_bottom,
.service-stats {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.service-stats {
  height: 40rpx;
  justify-content: flex-start;
}
.service-stats:last-child {
  justify-content: flex-end;
}
.order-count,
.positive-rate {
  color: #999;
  font-size: 24rpx;
  line-height: 1;
}
.order-count {
  margin-left: 10rpx;
}
.category {
  color: #666;
  font-size: 24rpx;
}
