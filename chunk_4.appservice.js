__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G[
                "miniprogram_npm/tdesign-miniprogram/action-sheet/template/list"
              ] || {}
            )._,
            (
              G[
                "miniprogram_npm/tdesign-miniprogram/action-sheet/template/grid"
              ] || {}
            )._,
            H
          );
        return S[P];
      };
      var a =
        R["miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            usingCustomNavbar: new Array(1),
            customStyle: new Array(1),
            showOverlay: new Array(1),
            defaultPopUpzIndex: new Array(1),
            defaultPopUpProps: new Array(1),
            visible: new Array(1),
            popupProps: new Array(2),
            style: new Array(1),
          },
          K = U === true,
          g,
          i = (C, T) => {
            C || K || U.description ? T(Y(D.description)) : T();
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.align])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(b).cls)(D.classPrefix + "__description", [D.align])
                    );
                  if (C) O(N, "tabindex", "0");
                },
                i
              );
            }
          },
          j,
          l,
          m = (C, T, E, B, F, S, J) => {
            var $A = I(l);
            if (l && $A)
              $A(
                R,
                C,
                {
                  classPrefix: D.classPrefix,
                  prefix: D.prefix,
                  gridThemeItems: D.gridThemeItems,
                  count: D.count,
                  currentSwiperIndex: D.currentSwiperIndex,
                },
                K ||
                  (U
                    ? {
                        classPrefix: U.classPrefix,
                        prefix: U.prefix,
                        gridThemeItems: U.gridThemeItems,
                        count: U.count,
                        currentSwiperIndex: U.currentSwiperIndex,
                      }
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          o = (C, p, q, r, s, t, T, E, B, F, S, J) => {
            var u,
              v = (C, T, E, B, F, S, J) => {
                var $A = I(u);
                if (u && $A)
                  $A(
                    R,
                    C,
                    {
                      index: q,
                      classPrefix: D.classPrefix,
                      listThemeItemClass: P(X(b).cls)(
                        D.classPrefix + "__list-item",
                        [D.align, [D.disabled, X(p).disabled]]
                      ),
                      item: p,
                    },
                    K ||
                      (U
                        ? {
                            index: s,
                            classPrefix: U.classPrefix,
                            listThemeItemClass:
                              !!(
                                Z(undefined, "cls") ||
                                U.classPrefix ||
                                Q.a([
                                  U.align,
                                  Q.a([U.disabled, Z(r, "disabled")]),
                                ])
                              ) || undefined,
                            item: r,
                          }
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              };
            u = "list";
            B(u, v);
          },
          n = (C, T, E, B, F) => {
            F(D.items, "index", U ? U.items : undefined, [0, "items"], o);
          },
          k = (C, T, E, B, F, S, J) => {
            if (j === 1) {
              l = "grid";
              B(l, m);
            } else if (j === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__list");
                },
                n
              );
            }
          },
          f = (C, T, E, B) => {
            g = D.description ? 1 : 0;
            B(g, h);
            j = X(D.gridThemeItems).length
              ? 1
              : D.items && X(D.items).length
              ? 2
              : 0;
            B(j, k);
          },
          p,
          s = (C) => {},
          t = (C, T) => {
            C || K || !!U.cancelText || undefined
              ? T(Y(D.cancelText || "取消"))
              : T();
          },
          r = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.theme) || undefined)
                  L(N, Y(D.classPrefix) + "__gap-" + Y(D.theme));
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__cancel " +
                      Y(D.prefix) +
                      "-class-cancel"
                  );
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "hover-class", Y(D.classPrefix) + "__cancel--hover");
                if (C) O(N, "hover-stay-time", "70");
                if (C) O(N, "aria-role", "button");
                if (C) R.v(N, "tap", "onCancel", !1, !1, !1, !1);
              },
              t
            );
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__footer");
                },
                r
              );
            }
          },
          e = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([Z(U.gridThemeItems, "length")])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__content", [
                        ["grid", X(D.gridThemeItems).length],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C) O(N, "tabindex", "0");
              },
              f
            );
            S("");
            p = D.showCancel ? 1 : 0;
            B(p, q);
          },
          d = (C, T, E) => {
            E(
              "t-popup",
              {},
              (N, C) => {
                if (C || K || U.visible) O(N, "visible", D.visible);
                A["visible"][0] = (D, E, T) => {
                  O(N, "visible", D.visible);
                  E(N);
                };
                if (C) O(N, "placement", "bottom");
                if (C || K || U.usingCustomNavbar)
                  O(N, "usingCustomNavbar", D.usingCustomNavbar);
                A["usingCustomNavbar"][0] = (D, E, T) => {
                  O(N, "usingCustomNavbar", D.usingCustomNavbar);
                  E(N);
                };
                if (C || K || U.showOverlay)
                  O(N, "show-overlay", D.showOverlay);
                A["showOverlay"][0] = (D, E, T) => {
                  O(N, "show-overlay", D.showOverlay);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(Z(U.popupProps, "zIndex") || U.defaultPopUpzIndex) ||
                  undefined
                )
                  O(
                    N,
                    "z-index",
                    X(D.popupProps).zIndex || D.defaultPopUpzIndex
                  );
                A["popupProps"][0] = A["defaultPopUpzIndex"][0] = (D, E, T) => {
                  O(
                    N,
                    "z-index",
                    X(D.popupProps).zIndex || D.defaultPopUpzIndex
                  );
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(Z(U.popupProps, "overlayProps") || U.defaultPopUpProps) ||
                  undefined
                )
                  O(
                    N,
                    "overlay-props",
                    X(D.popupProps).overlayProps || D.defaultPopUpProps
                  );
                A["popupProps"][1] = A["defaultPopUpProps"][0] = (D, E, T) => {
                  O(
                    N,
                    "overlay-props",
                    X(D.popupProps).overlayProps || D.defaultPopUpProps
                  );
                  E(N);
                };
                if (C)
                  R.v(
                    N,
                    "visible-change",
                    "onPopupVisibleChange",
                    !1,
                    !1,
                    !1,
                    !1
                  );
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
                if (C || K || U.classPrefix) R.i(N, D.classPrefix);
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet.js";
define(
  "miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      s = require("../../../@babel/runtime/helpers/createSuper"),
      r = require("tslib"),
      n = require("../common/utils"),
      a = require("../common/src/index"),
      o = u(require("../common/config")),
      c = require("./show"),
      l = u(require("./props")),
      h = u(require("../mixins/using-custom-navbar"));
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var d = o.default.prefix,
      g = "".concat(d, "-action-sheet"),
      m = (function (r) {
        i(o, r);
        var a = s(o);
        function o() {
          var e;
          return (
            t(this, o),
            ((e = a.apply(this, arguments)).behaviors = [h.default]),
            (e.externalClasses = [
              "".concat(d, "-class"),
              "".concat(d, "-class-content"),
              "".concat(d, "-class-cancel"),
            ]),
            (e.properties = Object.assign({}, l.default)),
            (e.data = {
              prefix: d,
              classPrefix: g,
              gridThemeItems: [],
              currentSwiperIndex: 0,
              defaultPopUpProps: {},
              defaultPopUpzIndex: 11500,
            }),
            (e.controlledProps = [{ key: "visible", event: "visible-change" }]),
            (e.observers = {
              "visible, items": function (e) {
                e && this.init();
              },
            }),
            (e.methods = {
              init: function () {
                this.memoInitialData(), this.splitGridThemeActions();
              },
              memoInitialData: function () {
                this.initialData = Object.assign(
                  Object.assign({}, this.properties),
                  this.data
                );
              },
              splitGridThemeActions: function () {
                this.data.theme === c.ActionSheetTheme.Grid &&
                  this.setData({
                    gridThemeItems: (0, n.chunk)(
                      this.data.items,
                      this.data.count
                    ),
                  });
              },
              show: function (e) {
                this.setData(
                  Object.assign(
                    Object.assign(Object.assign({}, this.initialData), e),
                    { visible: !0 }
                  )
                ),
                  this.splitGridThemeActions(),
                  (this.autoClose = !0),
                  this._trigger("visible-change", { visible: !0 });
              },
              close: function () {
                this.triggerEvent("close", { trigger: "command" }),
                  this._trigger("visible-change", { visible: !1 });
              },
              onPopupVisibleChange: function (e) {
                e.detail.visible ||
                  (this.triggerEvent("close", { trigger: "overlay" }),
                  this._trigger("visible-change", { visible: !1 })),
                  this.autoClose &&
                    (this.setData({ visible: !1 }), (this.autoClose = !1));
              },
              onSwiperChange: function (e) {
                var t = e.detail.current;
                this.setData({ currentSwiperIndex: t });
              },
              onSelect: function (e) {
                var t = this.data,
                  i = t.currentSwiperIndex,
                  s = t.items,
                  r = t.gridThemeItems,
                  n = t.count,
                  a = t.theme,
                  o = e.currentTarget.dataset.index,
                  l = a === c.ActionSheetTheme.Grid,
                  h = l ? r[i][o] : s[o],
                  u = l ? o + i * n : o;
                h &&
                  (this.triggerEvent("selected", { selected: h, index: u }),
                  h.disabled ||
                    (this.triggerEvent("close", { trigger: "select" }),
                    this._trigger("visible-change", { visible: !1 })));
              },
              onCancel: function () {
                this.triggerEvent("cancel"),
                  this.autoClose &&
                    (this.setData({ visible: !1 }), (this.autoClose = !1));
              },
            }),
            e
          );
        }
        return e(o);
      })(a.SuperComponent);
    m.show = c.show;
    var p = (m = (0, r.__decorate)([(0, a.wxComponent)()], m));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/action-sheet/action-sheet.js");
