__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/guide/guide": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (G["miniprogram_npm/tdesign-miniprogram/guide/content"] || {})._,
            H
          );
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b,
          f = (C) => {},
          h,
          i = (C, T, E, B, F, S, J) => {
            var $A = I(h);
            if (h && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    prefix: D.prefix,
                    classPrefix: D.classPrefix,
                    title: D.title,
                    body: D.body,
                    steps: D.steps,
                    current: D.current,
                    modeType: D.modeType,
                    hideSkip: D.hideSkip,
                    hideBack: D.hideBack,
                  },
                  X(D.buttonProps),
                  {}
                ),
                K ||
                  (U
                    ? U.buttonProps === true ||
                      Object.assign(
                        {
                          prefix: U.prefix,
                          classPrefix: U.classPrefix,
                          title: U.title,
                          body: U.body,
                          steps: U.steps,
                          current: U.current,
                          modeType: U.modeType,
                          hideSkip: U.hideSkip,
                          hideBack: U.hideBack,
                        },
                        X(U.buttonProps),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          g = (C, T, E, B, F, S, J) => {
            h = "content";
            B(h, i);
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.nonOverlay;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    U.classPrefix ||
                    U.nonOverlay ||
                    ($A ? !!U.classPrefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.prefix) +
                      "-class-reference " +
                      Y(D.classPrefix) +
                      "__reference " +
                      Y($A ? D.classPrefix + "__reference--nonoverlay" : "")
                  );
                if (C || K || U.referenceStyle) R.y(N, D.referenceStyle);
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.title || D.body;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    U.classPrefix ||
                    U.title ||
                    U.body ||
                    ($A
                      ? !!(U.classPrefix || U.modeType) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.prefix) +
                      "-class-popover " +
                      Y(D.classPrefix) +
                      "__container " +
                      Y($A ? D.classPrefix + "__container--" + D.modeType : "")
                  );
                if (C || K || U.popoverStyle) R.y(N, D.popoverStyle);
              },
              g
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
              },
              e
            );
          },
          m,
          n = (C, T, E, B, F, S, J) => {
            var $A = I(m);
            if (m && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    prefix: D.prefix,
                    classPrefix: D.classPrefix,
                    title: D.title,
                    body: D.body,
                    steps: D.steps,
                    current: D.current,
                    modeType: D.modeType,
                    hideSkip: D.hideSkip,
                  },
                  X(D.buttonProps),
                  {}
                ),
                K ||
                  (U
                    ? U.buttonProps === true ||
                      Object.assign(
                        {
                          prefix: U.prefix,
                          classPrefix: U.classPrefix,
                          title: U.title,
                          body: U.body,
                          steps: U.steps,
                          current: U.current,
                          modeType: U.modeType,
                          hideSkip: U.hideSkip,
                        },
                        X(U.buttonProps),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          l = (C, T, E, B, F, S, J) => {
            m = "content";
            B(m, n);
          },
          k = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.title || D.body;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    U.classPrefix ||
                    U.title ||
                    U.body ||
                    ($A
                      ? !!(U.classPrefix || U.modeType) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.prefix) +
                      "-class-popover " +
                      Y(D.classPrefix) +
                      "__container " +
                      Y($A ? D.classPrefix + "__container--" + D.modeType : "")
                  );
              },
              l
            );
          },
          j = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
              },
              k
            );
          },
          c = (C, T, E) => {
            if (b === 1) {
              E(
                "t-overlay",
                {},
                (N, C) => {
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C || K || U.usingCustomNavbar)
                    O(N, "using-custom-navbar", D.usingCustomNavbar);
                  if (C) O(N, "background-color", "transparent");
                  if (C || K || U.zIndex) O(N, "z-index", D.zIndex);
                },
                d
              );
            } else if (b === 2) {
              E(
                "t-popup",
                {},
                (N, C) => {
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C || K || !!U.nonOverlay || undefined)
                    O(N, "show-overlay", !D.nonOverlay);
                  if (C || K || U.usingCustomNavbar)
                    O(N, "using-custom-navbar", D.usingCustomNavbar);
                  if (C || K || U.zIndex) O(N, "z-index", D.zIndex);
                  if (C) O(N, "placement", "center");
                },
                j
              );
            }
          },
          a = (C, T, E, B) => {
            b = D.modeType === "popover" ? 1 : D.modeType === "dialog" ? 2 : 0;
            B(b, c);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/guide/guide";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/guide/guide.js";
define(
  "miniprogram_npm/tdesign-miniprogram/guide/guide.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var t = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
      e = require("../../../@babel/runtime/helpers/createClass"),
      n = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      o = require("tslib"),
      i = require("../common/src/index"),
      c = p(require("./props")),
      s = p(require("../common/config")),
      u = require("../common/validator"),
      l = require("../common/utils");
    function p(t) {
      return t && t.__esModule ? t : { default: t };
    }
    var h = s.default.prefix,
      d = "".concat(h, "-guide"),
      x = (function (i) {
        a(p, i);
        var s = r(p);
        function p() {
          var e;
          return (
            n(this, p),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
              "".concat(h, "-class-reference"),
              "".concat(h, "-class-popover"),
              "".concat(h, "-class-tooltip"),
              "".concat(h, "-class-title"),
              "".concat(h, "-class-body"),
              "".concat(h, "-class-footer"),
              "".concat(h, "-class-skip"),
              "".concat(h, "-class-next"),
              "".concat(h, "-class-back"),
              "".concat(h, "-class-finish"),
            ]),
            (e.properties = c.default),
            (e.options = { pureDataPattern: /^_/, multipleSlots: !0 }),
            (e.data = {
              prefix: h,
              classPrefix: d,
              visible: !1,
              _current: -1,
              _steps: [],
              buttonProps: {},
              referenceStyle: "",
              popoverStyle: "",
              title: "",
              body: "",
              nonOverlay: !1,
              modeType: "",
            }),
            (e.controlledProps = [{ key: "current", event: "change" }]),
            (e.observers = {
              "steps, current, showOverlay": function () {
                return (0, o.__awaiter)(
                  this,
                  void 0,
                  void 0,
                  t().mark(function e() {
                    return t().wrap(
                      function (t) {
                        for (;;)
                          switch ((t.prev = t.next)) {
                            case 0:
                              this._init();
                            case 1:
                            case "end":
                              return t.stop();
                          }
                      },
                      e,
                      this
                    );
                  })
                );
              },
            }),
            (e.lifetimes = {
              created: function () {
                var t = this;
                (this._init = (0, l.debounce)(function () {
                  return t.init();
                }, 20)),
                  (this._getPlacement = this.getPlacement());
              },
              attached: function () {
                this._init();
              },
            }),
            (e.methods = {
              init: function () {
                var e, n, a, r, i, c, s;
                return (0, o.__awaiter)(
                  this,
                  void 0,
                  void 0,
                  t().mark(function o() {
                    var u, p, h, d, x, f, b, m, v, g, _, k, y;
                    return t().wrap(
                      function (t) {
                        for (;;)
                          switch ((t.prev = t.next)) {
                            case 0:
                              if (
                                ((u = this.properties),
                                (p = u.steps),
                                (h = u.current),
                                (d = this.data),
                                (x = d._steps),
                                (f = d._current),
                                (b = p[h]))
                              ) {
                                t.next = 3;
                                break;
                              }
                              return t.abrupt(
                                "return",
                                this.setData({ visible: !1 })
                              );
                            case 3:
                              if (
                                ((m =
                                  "dialog" ===
                                  (null !== (e = b.mode) && void 0 !== e
                                    ? e
                                    : this.data.mode)
                                    ? "dialog"
                                    : "popover"),
                                (v =
                                  null !== (n = b.showOverlay) && void 0 !== n
                                    ? n
                                    : this.data.showOverlay),
                                this.setData({ nonOverlay: !v, modeType: m }),
                                p === x && h === f)
                              ) {
                                t.next = 20;
                                break;
                              }
                              if ("popover" !== m) {
                                t.next = 19;
                                break;
                              }
                              return (t.next = 8), b.element();
                            case 8:
                              if ((g = t.sent)) {
                                t.next = 11;
                                break;
                              }
                              return t.abrupt("return");
                            case 11:
                              return (
                                (_ = (0, l.rpx2px)(
                                  null !== (a = b.highlightPadding) &&
                                    void 0 !== a
                                    ? a
                                    : this.data.highlightPadding
                                )),
                                (k = {
                                  top: "".concat(g.top - _, "px"),
                                  right: "".concat(
                                    l.systemInfo.windowWidth - g.right - _,
                                    "px"
                                  ),
                                  left: "".concat(g.left - _, "px"),
                                  width: "".concat(g.width + 2 * _, "px"),
                                  height: "".concat(g.height + 2 * _, "px"),
                                }),
                                this.setData({
                                  _steps: this.data.steps,
                                  _current: this.data.current,
                                  visible: !0,
                                  referenceStyle: (0, l.styles)(k),
                                  title:
                                    null !== (r = b.title) && void 0 !== r
                                      ? r
                                      : "",
                                  body:
                                    null !== (i = b.body) && void 0 !== i
                                      ? i
                                      : "",
                                  buttonProps: this.buttonProps(b, "popover"),
                                }),
                                (t.next = 15),
                                this.placementOffset(b, k)
                              );
                            case 15:
                              (y = t.sent),
                                this.setData({ popoverStyle: y }),
                                (t.next = 20);
                              break;
                            case 19:
                              this.setData({
                                _steps: this.data.steps,
                                _current: this.data.current,
                                visible: !0,
                                title:
                                  null !== (c = b.title) && void 0 !== c
                                    ? c
                                    : "",
                                body:
                                  null !== (s = b.body) && void 0 !== s
                                    ? s
                                    : "",
                                buttonProps: this.buttonProps(b, "dialog"),
                              });
                            case 20:
                            case "end":
                              return t.stop();
                          }
                      },
                      o,
                      this
                    );
                  })
                );
              },
              placementOffset: function (e, n) {
                var a,
                  r,
                  i = e.placement,
                  c = e.offset;
                return (0, o.__awaiter)(
                  this,
                  void 0,
                  void 0,
                  t().mark(function e() {
                    var o, s;
                    return t().wrap(
                      function (t) {
                        for (;;)
                          switch ((t.prev = t.next)) {
                            case 0:
                              return (t.next = 2), (0, l.nextTick)();
                            case 2:
                              return (
                                (t.next = 4),
                                (0, l.getRect)(
                                  this,
                                  ".".concat(d, "__container")
                                )
                              );
                            case 4:
                              return (
                                (o = t.sent),
                                (s =
                                  null === (r = (a = this._getPlacement)[i]) ||
                                  void 0 === r
                                    ? void 0
                                    : r.call(a, o, n, c)),
                                t.abrupt(
                                  "return",
                                  (0, l.styles)(
                                    Object.assign({ position: "absolute" }, s)
                                  )
                                )
                              );
                            case 7:
                            case "end":
                              return t.stop();
                          }
                      },
                      e,
                      this
                    );
                  })
                );
              },
              buttonProps: function (t, e) {
                var n,
                  a,
                  r,
                  o,
                  i =
                    null !== (n = t.skipButtonProps) && void 0 !== n
                      ? n
                      : this.data.skipButtonProps,
                  c = "popover" === e ? "extra-small" : "medium";
                i = Object.assign(
                  Object.assign(
                    { theme: "light", content: "跳过", size: c },
                    i
                  ),
                  {
                    tClass: ""
                      .concat(h, "-class-skip ")
                      .concat(d, "__button ")
                      .concat((null == i ? void 0 : i.class) || ""),
                    type: "skip",
                  }
                );
                var s =
                  null !== (a = t.nextButtonProps) && void 0 !== a
                    ? a
                    : this.data.nextButtonProps;
                (s = Object.assign(
                  Object.assign(
                    { theme: "primary", content: "下一步", size: c },
                    s
                  ),
                  {
                    tClass: ""
                      .concat(h, "-class-next ")
                      .concat(d, "__button ")
                      .concat((null == s ? void 0 : s.class) || ""),
                    type: "next",
                  }
                )),
                  (s = Object.assign(Object.assign({}, s), {
                    content: this.buttonContent(s),
                  }));
                var u =
                  null !== (r = t.backButtonProps) && void 0 !== r
                    ? r
                    : this.data.backButtonProps;
                u = Object.assign(
                  Object.assign(
                    { theme: "light", content: "返回", size: c },
                    u
                  ),
                  {
                    tClass: ""
                      .concat(h, "-class-back ")
                      .concat(d, "__button ")
                      .concat((null == u ? void 0 : u.class) || ""),
                    type: "back",
                  }
                );
                var l =
                  null !== (o = t.finishButtonProps) && void 0 !== o
                    ? o
                    : this.data.finishButtonProps;
                return (
                  (l = Object.assign(
                    Object.assign(
                      { theme: "primary", content: "完成", size: c },
                      l
                    ),
                    {
                      tClass: ""
                        .concat(h, "-class-finish ")
                        .concat(d, "__button ")
                        .concat((null == l ? void 0 : l.class) || ""),
                      type: "finish",
                    }
                  )),
                  {
                    skipButton: i,
                    nextButton: s,
                    backButton: u,
                    finishButton: (l = Object.assign(Object.assign({}, l), {
                      content: this.buttonContent(l),
                    })),
                  }
                );
              },
              renderCounter: function () {
                var t = this.data,
                  e = t.steps,
                  n = t.current,
                  a = t.counter,
                  r = e.length,
                  o = n + 1,
                  i = (0, u.isFunction)(a) ? a({ total: r, current: o }) : a;
                return a ? i : "(".concat(o, "/").concat(r, ")");
              },
              buttonContent: function (t) {
                var e = this.data.hideCounter;
                return ""
                  .concat(t.content.replace(/ \(.*?\)/, ""), " ")
                  .concat(e ? "" : this.renderCounter());
              },
              onTplButtonTap: function (t) {
                var e = t.target.dataset.type,
                  n = {
                    e: t,
                    current: this.data.current,
                    total: this.data.steps.length,
                  };
                switch (e) {
                  case "next":
                    this.triggerEvent(
                      "next-step-click",
                      Object.assign({ next: this.data.current + 1 }, n)
                    ),
                      this.setData({ current: this.data.current + 1 });
                    break;
                  case "skip":
                    this.triggerEvent("skip", n), this.setData({ current: -1 });
                    break;
                  case "back":
                    this.triggerEvent("back", n), this.setData({ current: 0 });
                    break;
                  case "finish":
                    this.triggerEvent("finish", n),
                      this.setData({ current: -1 });
                }
                this.triggerEvent("change", { current: this.data.current });
              },
              getPlacement: function () {
                var t = (0, l.rpx2px)(32),
                  e = function (t) {
                    return (0, l.unitConvert)(
                      (0, u.isNumber)(null == t ? void 0 : t[0])
                        ? "".concat(null == t ? void 0 : t[0], "rpx")
                        : (null == t ? void 0 : t[0]) || 0
                    );
                  },
                  n = function (t) {
                    return (0, l.unitConvert)(
                      (0, u.isNumber)(null == t ? void 0 : t[1])
                        ? "".concat(null == t ? void 0 : t[1], "rpx")
                        : (null == t ? void 0 : t[1]) || 0
                    );
                  },
                  a = function (t) {
                    return parseFloat(t.left);
                  },
                  r = function (t) {
                    return parseFloat(t.right);
                  },
                  o = function (t) {
                    return parseFloat(t.top);
                  },
                  i = function (t) {
                    return parseFloat(t.height);
                  },
                  c = function (t) {
                    return parseFloat(t.width);
                  };
                return {
                  center: function (r, s, u) {
                    return {
                      top: "".concat(Math.max(i(s) + o(s) + t + n(u), 1), "px"),
                      left: "".concat(
                        Math.max(c(s) / 2 + a(s) - r.width / 2 + e(u), 1),
                        "px"
                      ),
                    };
                  },
                  bottom: function (r, s, u) {
                    return {
                      top: "".concat(Math.max(i(s) + o(s) + t + n(u), 1), "px"),
                      left: "".concat(
                        Math.max(c(s) / 2 + a(s) - r.width / 2 + e(u), 1),
                        "px"
                      ),
                    };
                  },
                  "bottom-left": function (r, c, s) {
                    return {
                      top: "".concat(Math.max(i(c) + o(c) + t + n(s), 1), "px"),
                      left: "".concat(Math.max(a(c) + e(s), 1), "px"),
                    };
                  },
                  "bottom-right": function (a, c, s) {
                    return {
                      top: "".concat(Math.max(i(c) + o(c) + t + n(s), 1), "px"),
                      right: "".concat(Math.max(r(c) - e(s), 1), "px"),
                    };
                  },
                  left: function (a, s, u) {
                    return {
                      top: "".concat(
                        Math.max(i(s) / 2 + o(s) - a.height / 2 + n(u), 1),
                        "px"
                      ),
                      right: "".concat(
                        Math.max(c(s) + r(s) + t - e(u), 1),
                        "px"
                      ),
                    };
                  },
                  "left-top": function (a, i, s) {
                    return {
                      top: "".concat(Math.max(o(i) + n(s), 1), "px"),
                      right: "".concat(
                        Math.max(c(i) + r(i) + t - e(s), 1),
                        "px"
                      ),
                    };
                  },
                  "left-bottom": function (a, s, u) {
                    return {
                      top: "".concat(
                        Math.max(o(s) + i(s) - a.height - n(u), 1),
                        "px"
                      ),
                      right: "".concat(
                        Math.max(c(s) + r(s) + t - e(u), 1),
                        "px"
                      ),
                    };
                  },
                  right: function (r, s, u) {
                    return {
                      top: "".concat(
                        Math.max(i(s) / 2 + o(s) - r.height / 2 + n(u), 1),
                        "px"
                      ),
                      left: "".concat(
                        Math.max(a(s) + c(s) + t + e(u), 1),
                        "px"
                      ),
                    };
                  },
                  "right-top": function (r, i, s) {
                    return {
                      top: "".concat(Math.max(o(i) + n(s), 1), "px"),
                      left: "".concat(
                        Math.max(a(i) + c(i) + t + e(s), 1),
                        "px"
                      ),
                    };
                  },
                  "right-bottom": function (r, s, u) {
                    return {
                      top: "".concat(
                        Math.max(o(s) + i(s) - r.height - n(u), 1),
                        "px"
                      ),
                      left: "".concat(
                        Math.max(a(s) + c(s) + t + e(u), 1),
                        "px"
                      ),
                    };
                  },
                  top: function (r, i, s) {
                    return {
                      top: "".concat(
                        Math.max(o(i) - r.height - t + n(s), 1),
                        "px"
                      ),
                      left: "".concat(
                        Math.max(c(i) / 2 + a(i) - r.width / 2 + e(s), 1),
                        "px"
                      ),
                    };
                  },
                  "top-left": function (r, i, c) {
                    return {
                      top: "".concat(
                        Math.max(o(i) - r.height - t + n(c), 1),
                        "px"
                      ),
                      left: "".concat(Math.max(a(i) + e(c), 1), "px"),
                    };
                  },
                  "top-right": function (a, i, c) {
                    return {
                      top: "".concat(
                        Math.max(o(i) - a.height - t + n(c), 1),
                        "px"
                      ),
                      right: "".concat(Math.max(r(i) - e(c), 1), "px"),
                    };
                  },
                };
              },
            }),
            e
          );
        }
        return e(p);
      })(i.SuperComponent),
      f = (x = (0, o.__decorate)([(0, i.wxComponent)()], x));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/guide/guide.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/guide/guide.js");
