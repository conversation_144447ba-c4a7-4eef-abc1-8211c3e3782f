__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/badge/badge": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/badge/badge"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            labelID: new Array(2),
            customStyle: new Array(1),
            ariaRole: new Array(1),
          },
          K = U === true,
          f,
          h = (C, T) => {
            C || K || U.content ? T(Y(D.content)) : T();
          },
          g = (C, T, E, B, F, S) => {
            if (f === 1) {
              S("", (N) => {
                if (C || K || !!U.classPrefix || undefined)
                  R.l(N, "class", Y(D.classPrefix) + "__content-slot");
              });
            } else {
              E(
                "text",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__content-text");
                },
                h
              );
            }
          },
          e = (C, T, E, B) => {
            f = !D.content ? 1 : 0;
            B(f, g);
          },
          i,
          k = (C, T) => {
            C ||
            K ||
            !!(
              Z(undefined, "getBadgeValue") ||
              Q.b({ dot: U.dot, count: U.count, maxCount: U.maxCount })
            ) ||
            undefined
              ? T(
                  Y(
                    P(X(a).getBadgeValue)({
                      dot: D.dot,
                      count: D.count,
                      maxCount: D.maxCount,
                    })
                  )
                )
              : T();
          },
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "getBadgeInnerClass") ||
                      Q.b({
                        dot: U.dot,
                        size: U.size,
                        shape: U.shape,
                        count: U.count,
                      }) ||
                      U.prefix ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).getBadgeInnerClass)({
                          dot: D.dot,
                          size: D.size,
                          shape: D.shape,
                          count: D.count,
                        })
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-has-count " +
                        Y(D.prefix) +
                        "-class-count"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!(
                          Z(undefined, "getBadgeStyles") ||
                          Q.b({ color: U.color, offset: U.offset })
                        ) || undefined,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(b)._style)([
                        P(X(a).getBadgeStyles)({
                          color: D.color,
                          offset: D.offset,
                        }),
                      ])
                    );
                  if (C) O(N, "aria-hidden", "true");
                  if (
                    C ||
                    K ||
                    !!(
                      U.ariaLabel ||
                      Z(undefined, "getBadgeAriaLabel") ||
                      Q.b({ dot: U.dot, count: U.count, maxCount: U.maxCount })
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "aria-label",
                      D.ariaLabel ||
                        P(X(b).getBadgeAriaLabel)({
                          dot: D.dot,
                          count: D.count,
                          maxCount: D.maxCount,
                        })
                    );
                  if (C || K || U.descriptionID) R.i(N, D.descriptionID);
                },
                k
              );
            }
          },
          d = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C) O(N, "aria-hidden", "true");
                if (C || K || U.labelID) R.i(N, D.labelID);
                A["labelID"][1] = (D, E, T) => {
                  R.i(N, D.labelID);
                };
              },
              e
            );
            i = P(X(a).isShowBadge)({
              dot: D.dot,
              count: D.count,
              showZero: D.showZero,
            })
              ? 1
              : 0;
            B(i, j);
            S("count");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getBadgeOuterClass") ||
                    Q.b({ shape: U.shape }) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).getBadgeOuterClass)({ shape: D.shape })) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
                if (C || K || U.labelID) O(N, "aria-labelledby", D.labelID);
                A["labelID"][0] = (D, E, T) => {
                  O(N, "aria-labelledby", D.labelID);
                  E(N);
                };
                if (C || K || U.descriptionID)
                  O(N, "aria-describedby", D.descriptionID);
                if (C || K || !!U.ariaRole || undefined)
                  O(N, "aria-role", D.ariaRole || "option");
                A["ariaRole"][0] = (D, E, T) => {
                  O(N, "aria-role", D.ariaRole || "option");
                  E(N);
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/badge/badge.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-badge{-webkit-align-items:start;align-items:start;display:-webkit-inline-flex;display:inline-flex;position:relative}\n.",
        [1],
        "t-badge--basic{background-color:var(--td-badge-bg-color,var(--td-error-color,var(--td-error-color-6,#d54941)));border-radius:var(--td-badge-border-radius,",
        [0, 4],
        ");color:var(--td-badge-text-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)));font-size:var(--td-badge-font-size,var(--td-font-size-xs,var(--td-font-size,",
        [0, 20],
        ")));font-weight:var(--td-badge-font-weight,600);height:var(--td-badge-basic-height,",
        [0, 32],
        ");line-height:var(--td-badge-basic-height,",
        [0, 32],
        ");padding:0 var(--td-badge-basic-padding,",
        [0, 8],
        ");text-align:center;z-index:100}\n.",
        [1],
        "t-badge--dot{border-radius:50%;height:var(--td-badge-dot-size,",
        [0, 16],
        ");min-width:var(--td-badge-dot-size,",
        [0, 16],
        ");padding:0}\n.",
        [1],
        "t-badge--count{box-sizing:border-box;min-width:var(--td-badge-basic-width,",
        [0, 32],
        ");white-space:nowrap}\n.",
        [1],
        "t-badge--circle{border-radius:calc(var(--td-badge-basic-height,",
        [0, 32],
        ")/ 2)}\n.",
        [1],
        "t-badge__ribbon-outer{position:absolute;right:0;top:0}\n.",
        [1],
        "t-badge--ribbon{border-radius:0;display:inline-block;position:relative;-webkit-transform:translate(calc(50% - var(--td-badge-basic-height,",
        [0, 32],
        ") + ",
        [0, 1],
        "),calc(-50% + var(--td-badge-basic-height,",
        [0, 32],
        ") - ",
        [0, 1],
        ")) rotate(45deg);transform:translate(calc(50% - var(--td-badge-basic-height,",
        [0, 32],
        ") + ",
        [0, 1],
        "),calc(-50% + var(--td-badge-basic-height,",
        [0, 32],
        ") - ",
        [0, 1],
        ")) rotate(45deg);-webkit-transform-origin:center center;transform-origin:center center}\n.",
        [1],
        "t-badge--ribbon::after,.",
        [1],
        "t-badge--ribbon::before{border-bottom:var(--td-badge-basic-height,",
        [0, 32],
        ") solid var(--td-badge-bg-color,var(--td-error-color,var(--td-error-color-6,#d54941)));bottom:0;content:\x22\x22;font-size:0;height:0;position:absolute;width:0}\n.",
        [1],
        "t-badge--ribbon::before{border-left:var(--td-badge-basic-height,",
        [0, 32],
        ") solid transparent;left:calc(-1 * var(--td-badge-basic-height,",
        [0, 32],
        ") + ",
        [0, 1],
        ")}\n.",
        [1],
        "t-badge--ribbon::after{border-right:var(--td-badge-basic-height,",
        [0, 32],
        ") solid transparent;right:calc(-1 * var(--td-badge-basic-height,",
        [0, 32],
        ") + ",
        [0, 1],
        ")}\n.",
        [1],
        "t-badge--bubble{border-radius:var(--td-badge-bubble-border-radius,",
        [0, 20],
        " ",
        [0, 20],
        " ",
        [0, 20],
        " 1px)}\n.",
        [1],
        "t-badge--large{font-size:var(--td-badge-large-font-size,var(--td-font-size-s,",
        [0, 24],
        "));height:var(--td-badge-large-height,",
        [0, 40],
        ");line-height:var(--td-badge-large-height,",
        [0, 40],
        ");min-width:var(--td-badge-large-height,",
        [0, 40],
        ");padding:0 var(--td-badge-large-padding,",
        [0, 10],
        ")}\n.",
        [1],
        "t-badge--large.",
        [1],
        "t-badge--circle{border-radius:calc(var(--td-badge-large-height,",
        [0, 40],
        ")/ 2)}\n.",
        [1],
        "t-badge__content:not(:empty)+.",
        [1],
        "t-has-count{left:100%;position:absolute;top:0;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);-webkit-transform-origin:center center;transform-origin:center center}\n.",
        [1],
        "t-badge__content-text{color:var(--td-badge-content-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:block;line-height:",
        [0, 48],
        "}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/badge/badge.wxss" }
    );
}
