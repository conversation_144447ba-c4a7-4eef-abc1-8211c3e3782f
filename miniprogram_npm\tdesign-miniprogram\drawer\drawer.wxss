@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-drawer {
  background: var(
    --td-drawer-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: var(--td-drawer-width, 560rpx);
}
.t-drawer--hover {
  background-color: var(
    --td-drawer-hover-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
}
.t-drawer__title {
  color: var(
    --td-drawer-title-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  font-size: var(--td-drawer-title-font-size, 36rpx);
  font-weight: 600;
  padding: 48rpx 32rpx 16rpx;
}
.t-drawer__sidebar {
  height: var(--td-drawer-sidebar-height, 70vh);
}
.t-drawer__sidebar-item {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  line-height: var(--td-drawer-item-height, 48rpx);
  padding-bottom: var(--td-drawer-item-padding, 32rpx);
  padding-left: var(--td-drawer-item-padding, 32rpx);
  padding-right: 0;
  padding-top: var(--td-drawer-item-padding, 32rpx);
  position: relative;
}
.t-drawer__sidebar-item::after {
  background-color: var(
    --td-drawer-border-color,
    var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  left: var(--td-drawer-item-padding, 32rpx);
  position: absolute;
  right: unset;
  right: 0;
  top: unset;
  transform: scaleY(0.5);
}
.t-drawer__sidebar-item-title {
  color: var(
    --td-drawer-title-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  flex: 1;
}
.t-drawer__sidebar-item-icon {
  color: var(
    --td-drawer-item-icon-color,
    var(
      --td-drawer-title-color,
      var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
    )
  );
  font-size: var(--td-drawer-item-icon-size, 48rpx);
  padding-right: 16rpx;
}
.t-drawer__footer {
  display: -webkit-flex;
  display: flex;
  flex: 1;
  flex-direction: column;
  padding-bottom: var(--td-drawer-footer-padding-bottom, 40rpx);
}
