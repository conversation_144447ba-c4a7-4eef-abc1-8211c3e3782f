__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/row/row": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/row/row"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            prefix: new Array(1),
            gutter: new Array(1),
            customStyle: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.prefix || undefined)
                  L(N, "class " + Y(D.prefix) + "-row");
                A["prefix"][0] = (D, E, T) => {
                  L(N, "class " + Y(D.prefix) + "-row");
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getRowStyles") ||
                    U.gutter ||
                    U.style ||
                    U.customStyle
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a).getRowStyles)(D.gutter, D.style, D.customStyle)
                  );
                A["gutter"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a).getRowStyles)(D.gutter, D.style, D.customStyle)
                      );
                    };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/row/row";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/row/row.js";
define(
  "miniprogram_npm/tdesign-miniprogram/row/row.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      u = require("tslib"),
      a = require("../common/src/index"),
      s = o(require("../common/config")),
      n = o(require("./props"));
    function o(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var l = s.default.prefix,
      c = (function (u) {
        t(s, u);
        var a = i(s);
        function s() {
          var e;
          return (
            r(this, s),
            ((e = a.apply(this, arguments)).externalClasses = []),
            (e.properties = n.default),
            (e.data = { prefix: l }),
            (e.relations = {
              "../col/col": {
                type: "child",
                linked: function (e) {
                  var r = this.data.gutter;
                  r && e.setData({ gutter: r });
                },
              },
            }),
            (e.observers = {
              gutter: function () {
                this.setGutter();
              },
            }),
            (e.methods = {
              setGutter: function () {
                var e = this.data.gutter;
                this.$children.forEach(function (r) {
                  r.setData({ gutter: e });
                });
              },
            }),
            e
          );
        }
        return e(s);
      })(a.SuperComponent),
      p = (c = (0, u.__decorate)([(0, a.wxComponent)()], c));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/row/row.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/row/row.js");
