__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/edit-profile/edit-profile": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { genderOptions: new Array(2), bioLength: new Array(1) },
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "编辑个人信息");
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          g = (C, T) => {
            C ? T("头像") : T();
          },
          j,
          l = (C) => {},
          n = (C, T) => {
            C ? T("请选择") : T();
          },
          m = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder-text");
              },
              n
            );
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "image",
                {},
                (N, C) => {
                  if (C) L(N, "selected-avatar");
                  if (C || K || Z(U.formData, "avatar"))
                    O(N, "src", X(D.formData).avatar);
                  if (C) O(N, "mode", "aspectFill");
                },
                l
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "avatar-placeholder");
                },
                m
              );
            }
          },
          i = (C, T, E, B) => {
            j = X(D.formData).avatar ? 1 : 0;
            B(j, k);
          },
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "avatar-preview");
                if (C) O(N, "bindtap", "showAvatarSelector");
              },
              i
            );
          },
          f = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              g
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "avatar-section");
              },
              h
            );
          },
          p = (C, T) => {
            C ? T("性别") : T();
          },
          r = (C, T) => {
            var $A = X(D.formData).genderIndex;
            C ||
            K ||
            !!(Z(U.formData, "genderIndex") || Z(U.genderOptions, $A)) ||
            undefined
              ? T(Y(X(D.genderOptions)[$A] || "请选择"), (N) => {
                  A["genderOptions"][1] = (D, E, T) => {
                    var $B = X(D.formData).genderIndex;
                    T(N, Y(X(D.genderOptions)[$B] || "请选择"));
                  };
                })
              : T();
          },
          q = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-input picker-input");
              },
              r
            );
          },
          o = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              p
            );
            E(
              "picker",
              {},
              (N, C) => {
                if (C) O(N, "bindchange", "onGenderChange");
                if (C || K || Z(U.formData, "genderIndex"))
                  O(N, "value", X(D.formData).genderIndex);
                if (C || K || U.genderOptions) O(N, "range", D.genderOptions);
                A["genderOptions"][0] = (D, E, T) => {
                  O(N, "range", D.genderOptions);
                  E(N);
                };
              },
              q
            );
          },
          t = (C, T) => {
            C ? T("昵称") : T();
          },
          u = (C) => {},
          s = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              t
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "type", "nickname");
                if (C) O(N, "placeholder", "请输入昵称");
                if (C || K || Z(U.formData, "nickname"))
                  O(N, "value", X(D.formData).nickname);
                if (C) O(N, "bindinput", "onNicknameInput");
                if (C) O(N, "bindblur", "onNicknameBlur");
              },
              u
            );
          },
          w = (C, T) => {
            C ? T("个人简介") : T();
          },
          y = (C) => {},
          z = (C, T) => {
            C || K || !!U.bioLength || undefined
              ? T(Y(Y(D.bioLength) + "/200"), (N) => {
                  A["bioLength"][0] = (D, E, T) => {
                    T(N, Y(Y(D.bioLength) + "/200"));
                  };
                })
              : T();
          },
          x = (C, T, E) => {
            E(
              "textarea",
              {},
              (N, C) => {
                if (C) L(N, "form-textarea");
                if (C) O(N, "placeholder", "请输入");
                if (C || K || Z(U.formData, "bio"))
                  O(N, "value", X(D.formData).bio);
                if (C) O(N, "bindinput", "onBioInput");
                if (C) O(N, "maxlength", "200");
              },
              y
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "char-count-inner textarea-count");
              },
              z
            );
          },
          v = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              w
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "textarea-wrapper");
              },
              x
            );
          },
          B0 = (C, T) => {
            C ? T("取消") : T();
          },
          C0 = (C, T) => {
            C ? T("提交") : T();
          },
          A0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "onCancel");
              },
              B0
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-submit");
                if (C) O(N, "bindtap", "onSubmit");
              },
              C0
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              o
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "button-group");
              },
              A0
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-container");
              },
              e
            );
          },
          D0,
          G0 = (C) => {},
          J0 = (C, T) => {
            C ? T("选择头像") : T();
          },
          I0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "modal-title");
              },
              J0
            );
          },
          L0 = (C, M0, N0, O0, P0, Q0, T, E) => {
            var S0 = (C) => {},
              T0,
              V0 = (C, T) => {
                C ? T("✓") : T();
              },
              U0 = (C, T, E) => {
                if (T0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "selected-mark");
                    },
                    V0
                  );
                }
              },
              R0 = (C, T, E, B) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "avatar-img");
                    if (C || K || Z(O0, "src")) O(N, "src", X(M0).src);
                    if (C) O(N, "mode", "aspectFill");
                  },
                  S0
                );
                T0 = X(D.formData).avatar === X(M0).src ? 1 : 0;
                B(T0, U0);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "avatar-item");
                if (C) O(N, "bindtap", "selectAvatar");
                if (C || K || Z(O0, "src")) R.d(N, "src", X(M0).src);
                if (C || K || Z(O0, "fileID")) R.d(N, "fileid", X(M0).fileID);
              },
              R0
            );
          },
          K0 = (C, T, E, B, F) => {
            F(
              D.avatarList,
              "index",
              U ? U.avatarList : undefined,
              [0, "avatarList"],
              L0
            );
          },
          N0 = (C, T) => {
            C ? T("取消") : T();
          },
          M0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "hideAvatarSelector");
              },
              N0
            );
          },
          H0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-header");
              },
              I0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "avatar-grid");
              },
              K0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-footer");
              },
              M0
            );
          },
          F0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-mask");
                if (C) O(N, "bindtap", "hideAvatarSelector");
              },
              G0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-content");
              },
              H0
            );
          },
          E0 = (C, T, E) => {
            if (D0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "avatar-modal");
                },
                F0
              );
            }
          },
          a = (C, T, E, B) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
            D0 = D.showAvatarModal ? 1 : 0;
            B(D0, E0);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/edit-profile/edit-profile.wxss"] = setCssToHead(
    [
      ".",
      [1],
      "container{background-color:#fff;min-height:100vh;padding-top:",
      [0, 160],
      "}\n.",
      [1],
      "form-container{background-color:#fff;border-radius:",
      [0, 20],
      ";margin:",
      [0, 20],
      " auto;padding:",
      [0, 40],
      " ",
      [0, 30],
      "}\n.",
      [1],
      "form-item{margin-bottom:",
      [0, 30],
      ";position:relative}\n.",
      [1],
      "form-label{-webkit-align-items:center;align-items:center;color:#333;display:-webkit-flex;display:flex;font-size:",
      [0, 28],
      ";margin-bottom:",
      [0, 15],
      "}\n.",
      [1],
      "form-label.",
      [1],
      "required::before{color:#ff4d4f;content:\x22*\x22;font-size:",
      [0, 28],
      ";margin-right:",
      [0, 8],
      "}\n.",
      [1],
      "form-input{background-color:#fff;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 10],
      ";box-sizing:border-box;color:#333;font-size:",
      [0, 26],
      ";height:",
      [0, 80],
      ";padding:0 ",
      [0, 20],
      ";width:100%}\n.",
      [1],
      "form-input:focus{border-color:#195abf}\n.",
      [1],
      "form-input::-webkit-input-placeholder{color:#999}\n.",
      [1],
      "form-input::placeholder{color:#999}\n.",
      [1],
      "picker-input{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;height:",
      [0, 80],
      ";line-height:",
      [0, 80],
      "}\n.",
      [1],
      "textarea-wrapper{position:relative}\n.",
      [1],
      "form-textarea{background-color:#fff;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 10],
      ";box-sizing:border-box;color:#333;font-size:",
      [0, 26],
      ";min-height:",
      [0, 160],
      ";padding:",
      [0, 20],
      ";resize:none;width:100%}\n.",
      [1],
      "form-textarea:focus{border-color:#195abf}\n.",
      [1],
      "form-textarea::-webkit-input-placeholder{color:#999}\n.",
      [1],
      "form-textarea::placeholder{color:#999}\n.",
      [1],
      "char-count-inner{background-color:#fff;bottom:",
      [0, 20],
      ";color:#999;font-size:",
      [0, 22],
      ";padding:0 ",
      [0, 5],
      ";position:absolute;right:",
      [0, 20],
      "}\n.",
      [1],
      "textarea-count{bottom:",
      [0, 20],
      ";top:auto;-webkit-transform:none;transform:none}\n.",
      [1],
      "button-group{gap:",
      [0, 20],
      ";-webkit-justify-content:space-between;justify-content:space-between;margin-top:",
      [0, 60],
      "}\n.",
      [1],
      "btn,.",
      [1],
      "button-group{display:-webkit-flex;display:flex}\n.",
      [1],
      "btn{-webkit-align-items:center;align-items:center;border:none;border-radius:",
      [0, 20],
      ";-webkit-flex:1;flex:1;font-size:",
      [0, 28],
      ";height:",
      [0, 80],
      ";-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "btn-cancel{background-color:#fff;border:",
      [0, 3],
      " solid #1e1e1e;color:#666}\n.",
      [1],
      "btn-submit{background-color:#a5d8ff;border:",
      [0, 3],
      " solid #1971c2;color:#1971c2}\n.",
      [1],
      "btn:active{opacity:.8}\n.",
      [1],
      "avatar-section{-webkit-justify-content:flex-start;justify-content:flex-start;margin-top:",
      [0, 15],
      "}\n.",
      [1],
      "avatar-preview,.",
      [1],
      "avatar-section{display:-webkit-flex;display:flex}\n.",
      [1],
      "avatar-preview{-webkit-align-items:center;align-items:center;background-color:#f8f9fa;border:",
      [0, 2],
      " dashed #e0e0e0;border-radius:50%;height:",
      [0, 150],
      ";-webkit-justify-content:center;justify-content:center;overflow:hidden;position:relative;width:",
      [0, 150],
      "}\n.",
      [1],
      "selected-avatar{border-radius:50%;height:100%;width:100%}\n.",
      [1],
      "avatar-placeholder{-webkit-align-items:center;align-items:center;color:#1971c2;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "placeholder-text{color:#1971c2;font-size:",
      [0, 28],
      "}\n.",
      [1],
      "avatar-modal{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;position:fixed;z-index:9999}\n.",
      [1],
      "avatar-modal,.",
      [1],
      "modal-mask{bottom:0;left:0;right:0;top:0}\n.",
      [1],
      "modal-mask{background-color:rgba(0,0,0,.5);position:absolute}\n.",
      [1],
      "modal-content{background-color:#fff;border-radius:",
      [0, 20],
      ";max-width:",
      [0, 600],
      ";padding:",
      [0, 40],
      ";position:relative;width:90%}\n.",
      [1],
      "modal-header{margin-bottom:",
      [0, 30],
      ";text-align:center}\n.",
      [1],
      "modal-title{color:#333;font-size:",
      [0, 32],
      ";font-weight:500}\n.",
      [1],
      "avatar-grid{display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;gap:",
      [0, 20],
      ";-webkit-justify-content:space-around;justify-content:space-around;margin-bottom:",
      [0, 30],
      "}\n.",
      [1],
      "avatar-item{border-radius:50%;height:",
      [0, 120],
      ";padding:",
      [0, 10],
      ";position:relative;transition:all .3s ease;width:",
      [0, 120],
      "}\n.",
      [1],
      "avatar-item:active{-webkit-transform:scale(.95);transform:scale(.95)}\n.",
      [1],
      "avatar-img{border:",
      [0, 2],
      " solid #e0e0e0;border-radius:50%;height:100%;width:100%}\n.",
      [1],
      "selected-mark{-webkit-align-items:center;align-items:center;background-color:#195abf;border-radius:50%;color:#fff;font-size:",
      [0, 24],
      ";font-weight:700;height:",
      [0, 40],
      ";position:absolute;right:",
      [0, 5],
      ";top:",
      [0, 5],
      ";width:",
      [0, 40],
      "}\n.",
      [1],
      "modal-footer,.",
      [1],
      "selected-mark{display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "modal-footer{margin:0 auto;width:50%}\n",
    ],
    undefined,
    { path: "./pages/edit-profile/edit-profile.wxss" }
  );
}
