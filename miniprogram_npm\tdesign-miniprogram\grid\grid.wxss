@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-grid {
  background-color: var(
    --td-grid-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  overflow: hidden;
  position: relative;
}
.t-grid__content {
  width: auto;
}
.t-grid--card {
  border-radius: var(--td-grid-card-radius, var(--td-radius-large, 18rpx));
  margin: 0 32rpx;
  overflow: hidden;
}
