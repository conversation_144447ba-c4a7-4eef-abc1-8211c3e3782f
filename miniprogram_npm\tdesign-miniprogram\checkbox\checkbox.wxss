@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-checkbox {
  background: var(
    --td-checkbox-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  display: -webkit-inline-flex;
  display: inline-flex;
  font-size: var(--td-checkbox-font-size, 32rpx);
  position: relative;
  vertical-align: middle;
}
.t-checkbox:focus {
  outline: 0;
}
.t-checkbox--block {
  display: -webkit-flex;
  display: flex;
  padding: var(--td-checkbox-vertical-padding, 32rpx);
}
.t-checkbox--right {
  flex-direction: row-reverse;
}
.t-checkbox .limit-title-row {
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}
.t-checkbox .image-center {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.t-checkbox__icon-left {
  margin-right: 20rpx;
  width: 40rpx;
}
.t-checkbox__icon-right {
  display: contents;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}
.t-checkbox__icon-image {
  vertical-align: top;
}
.t-checkbox__icon,
.t-checkbox__icon-image {
  height: var(--td-checkbox-icon-size, 48rpx);
  width: var(--td-checkbox-icon-size, 48rpx);
}
.t-checkbox__icon {
  color: var(
    --td-checkbox-icon-color,
    var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
  );
  display: block;
  font-size: var(--td-checkbox-icon-size, 48rpx);
  margin-top: calc(
    (
        var(--td-checkbox-title-line-height, 48rpx) -
          var(--td-checkbox-icon-size, 48rpx)
      ) / 2
  );
  position: relative;
}
.t-checkbox__icon:empty {
  display: none;
}
.t-checkbox__icon--checked {
  color: var(
    --td-checkbox-icon-checked-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-checkbox__icon--disabled {
  color: var(
    --td-checkbox-icon-disabled-color,
    var(--td-brand-color-disabled, var(--td-primary-color-3, #b5c7ff))
  );
  cursor: not-allowed;
}
.t-checkbox__icon--left {
  margin-right: 16rpx;
}
.t-checkbox__icon-circle {
  border: calc(4rpx * 2) solid
    var(
      --td-checkbox-icon-color,
      var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
    );
  border-radius: 50%;
  box-sizing: border-box;
  height: calc((var(--td-checkbox-icon-size, 48rpx) - 4rpx) * 2);
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.5);
  width: calc((var(--td-checkbox-icon-size, 48rpx) - 4rpx) * 2);
}
.t-checkbox__icon-circle--disabled {
  background: var(
    --td-checkbox-icon-disabled-bg-color,
    var(--td-bg-color-component-disabled, var(--td-gray-color-2, #eee))
  );
}
.t-checkbox__icon-rectangle {
  border: calc(4rpx * 2) solid
    var(
      --td-checkbox-icon-color,
      var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
    );
  box-sizing: border-box;
  height: calc((var(--td-checkbox-icon-size, 48rpx) - 4rpx * 2) * 2);
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.5);
  width: calc((var(--td-checkbox-icon-size, 48rpx) - 4rpx * 2) * 2);
}
.t-checkbox__icon-rectangle--disabled {
  background: var(
    --td-checkbox-icon-disabled-bg-color,
    var(--td-bg-color-component-disabled, var(--td-gray-color-2, #eee))
  );
}
.t-checkbox__icon-line::after,
.t-checkbox__icon-line::before {
  background: var(
    --td-checkbox-icon-checked-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-radius: 2rpx;
  content: "";
  display: block;
  position: absolute;
  transform-origin: top center;
  width: 5rpx;
}
.t-checkbox__icon-line::before {
  height: 16rpx;
  left: 8rpx;
  top: 22rpx;
  transform: rotate(-45deg);
}
.t-checkbox__icon-line::after {
  height: 26rpx;
  right: 8rpx;
  top: 14rpx;
  transform: rotate(45deg);
}
.t-checkbox__icon-line--disabled::after,
.t-checkbox__icon-line--disabled::before {
  background: var(
    --td-checkbox-icon-disabled-color,
    var(--td-brand-color-disabled, var(--td-primary-color-3, #b5c7ff))
  );
}
.t-checkbox__content {
  flex: 1;
}
.t-checkbox__title {
  -webkit-box-orient: vertical;
  color: var(
    --td-checkbox-title-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-box;
  line-height: var(--td-checkbox-title-line-height, 48rpx);
  overflow: hidden;
}
.t-checkbox__title--disabled {
  color: var(
    --td-checkbox-title-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-checkbox__description {
  -webkit-box-orient: vertical;
  color: var(
    --td-checkbox-description-color,
    var(--td-text-color-secondary, var(--td-font-gray-2, rgba(0, 0, 0, 0.6)))
  );
  display: -webkit-box;
  font-size: 28rpx;
  line-height: var(--td-checkbox-description-line-height, 44rpx);
  overflow: hidden;
}
.t-checkbox__description--disabled {
  color: var(
    --td-checkbox-description-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-checkbox__title + .t-checkbox__description:not(:empty) {
  margin-top: 8rpx;
}
.t-checkbox__border {
  background: var(
    --td-checkbox-border-color,
    var(--td-component-stroke, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  height: 1px;
  left: 96rpx;
  position: absolute;
  right: 0;
  transform: scaleY(0.5);
}
.t-checkbox__border--right {
  left: 32rpx;
}
.t-checkbox--tag {
  background-color: var(
    --td-bg-color-secondarycontainer,
    var(--td-gray-color-1, #f3f3f3)
  );
  border-radius: 12rpx;
  font-size: 28rpx;
  padding-bottom: 16rpx;
  padding-top: 16rpx;
  text-align: center;
}
.t-checkbox--tag.t-checkbox--checked {
  background-color: var(
    --td-checkbox-tag-active-bg-color,
    var(--td-brand-color-light, var(--td-primary-color-1, #f2f3ff))
  );
}
.t-checkbox--tag .t-checkbox__title--checked,
.t-checkbox--tag.t-checkbox--checked {
  color: var(
    --td-checkbox-tag-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-checkbox--tag .t-checkbox__content {
  margin-right: 0;
}
