@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-count-down--small.t-count-down--default {
  font-size: var(--td-font-size-base, 28rpx);
}
.t-count-down--small.t-count-down--round > .t-count-down__item,
.t-count-down--small.t-count-down--square > .t-count-down__item {
  font-size: var(--td-font-size-s, 24rpx);
  height: 40rpx;
  width: 40rpx;
}
.t-count-down--small.t-count-down--round > .t-count-down__split--dot,
.t-count-down--small.t-count-down--square > .t-count-down__split--dot {
  font-size: var(--td-font-size-base, 28rpx);
  font-weight: 700;
  margin: 0 4rpx;
}
.t-count-down--small.t-count-down--round > .t-count-down__split--text,
.t-count-down--small.t-count-down--square > .t-count-down__split--text {
  font-size: var(--td-font-size, 20rpx);
  margin: 0 8rpx;
}
.t-count-down--medium.t-count-down--default {
  font-size: var(--td-font-size-m, 32rpx);
}
.t-count-down--medium.t-count-down--round > .t-count-down__item,
.t-count-down--medium.t-count-down--square > .t-count-down__item {
  font-size: var(--td-font-size-base, 28rpx);
  height: 48rpx;
  width: 48rpx;
}
.t-count-down--medium.t-count-down--round > .t-count-down__split--dot,
.t-count-down--medium.t-count-down--square > .t-count-down__split--dot {
  font-size: var(--td-font-size-m, 32rpx);
  font-weight: 700;
  margin: 0 6rpx;
}
.t-count-down--medium.t-count-down--round > .t-count-down__split--text,
.t-count-down--medium.t-count-down--square > .t-count-down__split--text {
  font-size: var(--td-font-size-s, 24rpx);
  margin: 0 10rpx;
}
.t-count-down--large.t-count-down--default {
  font-size: 36rpx;
}
.t-count-down--large.t-count-down--round > .t-count-down__item,
.t-count-down--large.t-count-down--square > .t-count-down__item {
  font-size: var(--td-font-size-m, 32rpx);
  height: 56rpx;
  width: 56rpx;
}
.t-count-down--large.t-count-down--round > .t-count-down__split--dot,
.t-count-down--large.t-count-down--square > .t-count-down__split--dot {
  font-size: 36rpx;
  font-weight: 700;
  margin: 0 12rpx;
}
.t-count-down--large.t-count-down--round > .t-count-down__split--text,
.t-count-down--large.t-count-down--square > .t-count-down__split--text {
  font-size: var(--td-font-size-base, 28rpx);
  margin: 0 12rpx;
}
.t-count-down {
  display: -webkit-flex;
  display: flex;
  font-family: TCloudNumber, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto,
    Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Source Han Sans CN,
    sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
}
.t-count-down .t-count-down__item,
.t-count-down .t-count-down__split {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.t-count-down--round > .t-count-down__split--dot,
.t-count-down--square > .t-count-down__split--dot {
  color: var(--td-error-color, var(--td-error-color-6, #d54941));
}
.t-count-down--round > .t-count-down__split--text,
.t-count-down--square > .t-count-down__split--text {
  color: var(
    --td-text-color-primary,
    var(--td-font-gray-1, rgba(0, 0, 0, 0.9))
  );
}
.t-count-down--default {
  color: var(
    --td-countdown-default-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
}
.t-count-down--square {
  color: var(
    --td-countdown-round-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
}
.t-count-down--square > .t-count-down__item {
  background: var(
    --td-countdown-bg-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
  border-radius: var(
    --td-countdown-square-border-radius,
    var(--td-radius-small, 6rpx)
  );
}
.t-count-down--round {
  color: var(
    --td-countdown-round-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
}
.t-count-down--round > .t-count-down__item {
  background: var(
    --td-countdown-bg-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
  border-radius: var(
    --td-countdown-round-border-radius,
    var(--td-radius-circle, 50%)
  );
}
