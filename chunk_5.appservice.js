__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/avatar-group/avatar-group": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            className: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          d = (C, T, E, B, F, S) => {
            S("collapse-avatar");
          },
          e,
          h = (C, T) => {
            C || K || U.collapseAvatar ? T(Y(D.collapseAvatar)) : T();
          },
          g = (C, T, E) => {
            E(
              "t-avatar",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.prefix || U.prefix || U.size || U.prefix) ||
                  undefined
                )
                  O(
                    N,
                    "t-class-image",
                    Y(D.prefix) +
                      "-avatar--border " +
                      Y(D.prefix) +
                      "-avatar--border-" +
                      Y(D.size) +
                      " " +
                      Y(D.prefix) +
                      "-class-image"
                  );
                if (C || K || !!U.prefix || undefined)
                  O(N, "t-class-content", Y(D.prefix) + "-class-content");
                if (C || K || U.size) O(N, "size", D.size);
                if (C || K || U.shape) O(N, "shape", D.shape);
                var $A = D.collapseAvatar;
                if (
                  C ||
                  K ||
                  !!U.collapseAvatar ||
                  ($A ? undefined : undefined)
                )
                  O(N, "icon", $A ? "" : "user-add");
                if (C) O(N, "aria-role", "none");
              },
              h
            );
          },
          f = (C, T, E) => {
            if (e === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__collapse--default");
                  if (C) O(N, "bindtap", "onCollapsedItemClick");
                },
                g
              );
            }
          },
          c = (C, T, E, B, F, S) => {
            S("");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__collapse--slot");
              },
              d
            );
            e = D.max && D.max < D.length ? 1 : 0;
            B(e, f);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.className || undefined)
                  L(N, Y(D.className) + " class");
                A["className"][0] = (D, E, T) => {
                  L(N, Y(D.className) + " class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/avatar/avatar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/avatar/avatar"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            ariaRole: new Array(1),
            size: new Array(2),
            systemInfo: new Array(1),
            bordered: new Array(1),
            ariaLabel: new Array(1),
            isShow: new Array(1),
            customStyle: new Array(1),
            badgeProps: new Array(12),
            ariaHidden: new Array(1),
            shape: new Array(1),
          },
          K = U === true,
          g,
          i = (C) => {},
          j,
          k = (C, T, E, B, F, S, J) => {
            var $A = I(j);
            if (j && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix + "__icon " + D.prefix + "-class-icon",
                    name: D.iconName,
                  },
                  X(D.iconData),
                  {}
                ),
                K ||
                  (U
                    ? U.iconData === true ||
                      Object.assign(
                        {
                          tClass: !!(U.classPrefix || U.prefix) || undefined,
                          name: U.iconName,
                        },
                        X(U.iconData),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          l = (C, T, E, B, F, S) => {
            S("");
          },
          h = (C, T, E, B, F, S, J) => {
            if (g === 1) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "style")) ||
                    undefined
                  )
                    R.y(N, (D.imageProps && X(D.imageProps).style) || "");
                  if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                    O(
                      N,
                      "t-class",
                      Y(D.prefix) + "-image " + Y(D.classPrefix) + "__image"
                    );
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class-load", Y(D.prefix) + "-class-alt");
                  if (C || K || U.image) O(N, "src", D.image);
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "mode")) ||
                    undefined
                  )
                    O(
                      N,
                      "mode",
                      (D.imageProps && X(D.imageProps).mode) || "aspectFill"
                    );
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "lazy")) ||
                    undefined
                  )
                    O(
                      N,
                      "lazy",
                      (D.imageProps && X(D.imageProps).lazy) || false
                    );
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "loading")) ||
                    undefined
                  )
                    O(
                      N,
                      "loading",
                      (D.imageProps && X(D.imageProps).loading) || "default"
                    );
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "shape")) ||
                    undefined
                  )
                    O(
                      N,
                      "shape",
                      (D.imageProps && X(D.imageProps).shape) || "round"
                    );
                  if (
                    C ||
                    K ||
                    !!(U.imageProps || Z(U.imageProps, "webp")) ||
                    undefined
                  )
                    O(
                      N,
                      "webp",
                      (D.imageProps && X(D.imageProps).webp) || false
                    );
                  if (C || K || !!U.alt || undefined)
                    O(N, "error", D.alt || "default");
                  if (C) R.v(N, "error", "onLoadError", !1, !1, !1, !1);
                },
                i
              );
            } else if (g === 2) {
              j = "icon";
              B(j, k);
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__text " +
                        Y(D.prefix) +
                        "-class-content"
                    );
                },
                l
              );
            }
          },
          f = (C, T, E, B) => {
            g = D.image
              ? 1
              : D.iconName || P(X(a).isNoEmptyObj)(D.iconData)
              ? 2
              : 0;
            B(g, h);
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getClass") ||
                    U.classPrefix ||
                    U.size ||
                    U.shape ||
                    U.bordered ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).getClass)(
                        D.classPrefix,
                        D.size || "medium",
                        D.shape,
                        D.bordered
                      )
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-image"
                  );
                A["size"][0] =
                  A["shape"][0] =
                  A["bordered"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(
                          P(X(b).getClass)(
                            D.classPrefix,
                            D.size || "medium",
                            D.shape,
                            D.bordered
                          )
                        ) +
                          " " +
                          Y(D.prefix) +
                          "-class-image"
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getSize") || U.size || U.systemInfo) ||
                  undefined
                )
                  R.y(N, P(X(b).getSize)(D.size, D.systemInfo));
                A["size"][1] = A["systemInfo"][0] = (D, E, T) => {
                  R.y(N, P(X(b).getSize)(D.size, D.systemInfo));
                };
                if (C || K || !!(U.ariaLabel || U.alt) || undefined)
                  O(N, "aria-label", D.ariaLabel || D.alt || "头像");
                A["ariaLabel"][0] = (D, E, T) => {
                  O(N, "aria-label", D.ariaLabel || D.alt || "头像");
                  E(N);
                };
                if (C || K || !!U.ariaRole || undefined)
                  O(N, "aria-role", D.ariaRole || "img");
                A["ariaRole"][0] = (D, E, T) => {
                  O(N, "aria-role", D.ariaRole || "img");
                  E(N);
                };
                if (C || K || U.ariaHidden) O(N, "aria-hidden", D.ariaHidden);
                A["ariaHidden"][0] = (D, E, T) => {
                  O(N, "aria-hidden", D.ariaHidden);
                  E(N);
                };
              },
              f
            );
          },
          d = (C, T, E) => {
            E(
              "t-badge",
              {},
              (N, C) => {
                if (C || K || !!Z(U.badgeProps, "color") || undefined)
                  O(N, "color", X(D.badgeProps).color || "");
                A["badgeProps"][0] = (D, E, T) => {
                  O(N, "color", X(D.badgeProps).color || "");
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "content") || undefined)
                  O(N, "content", X(D.badgeProps).content || "");
                A["badgeProps"][1] = (D, E, T) => {
                  O(N, "content", X(D.badgeProps).content || "");
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "count") || undefined)
                  O(N, "count", X(D.badgeProps).count || 0);
                A["badgeProps"][2] = (D, E, T) => {
                  O(N, "count", X(D.badgeProps).count || 0);
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "dot") || undefined)
                  O(N, "dot", X(D.badgeProps).dot || false);
                A["badgeProps"][3] = (D, E, T) => {
                  O(N, "dot", X(D.badgeProps).dot || false);
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "maxCount") || undefined)
                  O(N, "max-count", X(D.badgeProps).maxCount || 99);
                A["badgeProps"][4] = (D, E, T) => {
                  O(N, "max-count", X(D.badgeProps).maxCount || 99);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(Z(U.badgeProps, "offset") || Q.a([])) ||
                  undefined
                )
                  O(N, "offset", X(D.badgeProps).offset || []);
                A["badgeProps"][5] = (D, E, T) => {
                  O(N, "offset", X(D.badgeProps).offset || []);
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "shape") || undefined)
                  O(N, "shape", X(D.badgeProps).shape || "circle");
                A["badgeProps"][6] = (D, E, T) => {
                  O(N, "shape", X(D.badgeProps).shape || "circle");
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "showZero") || undefined)
                  O(N, "show-zero", X(D.badgeProps).showZero || false);
                A["badgeProps"][7] = (D, E, T) => {
                  O(N, "show-zero", X(D.badgeProps).showZero || false);
                  E(N);
                };
                if (C || K || !!Z(U.badgeProps, "size") || undefined)
                  O(N, "size", X(D.badgeProps).size || "medium");
                A["badgeProps"][8] = (D, E, T) => {
                  O(N, "size", X(D.badgeProps).size || "medium");
                  E(N);
                };
                if (C || K || Z(U.badgeProps, "tClass"))
                  O(N, "t-class", X(D.badgeProps).tClass);
                A["badgeProps"][9] = (D, E, T) => {
                  O(N, "t-class", X(D.badgeProps).tClass);
                  E(N);
                };
                if (C || K || Z(U.badgeProps, "tClassContent"))
                  O(N, "t-class-content", X(D.badgeProps).tClassContent);
                A["badgeProps"][10] = (D, E, T) => {
                  O(N, "t-class-content", X(D.badgeProps).tClassContent);
                  E(N);
                };
                if (C || K || Z(U.badgeProps, "tClassCount"))
                  O(N, "t-class-count", X(D.badgeProps).tClassCount);
                A["badgeProps"][11] = (D, E, T) => {
                  O(N, "t-class-count", X(D.badgeProps).tClassCount);
                  E(N);
                };
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__wrapper class " +
                      Y(D.prefix) +
                      "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([
                      !!(Z(undefined, "getStyles") || U.isShow) || undefined,
                      U.style,
                      U.customStyle,
                    ])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([
                      P(X(b).getStyles)(D.isShow),
                      D.style,
                      D.customStyle,
                    ])
                  );
                A["isShow"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([
                          P(X(b).getStyles)(D.isShow),
                          D.style,
                          D.customStyle,
                        ])
                      );
                    };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/avatar-group/avatar-group";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/avatar-group/avatar-group.js";
define(
  "miniprogram_npm/tdesign-miniprogram/avatar-group/avatar-group.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      s = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      r = require("../common/src/index"),
      n = l(require("../common/config")),
      c = l(require("./props"));
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = n.default.prefix,
      u = "".concat(o, "-avatar-group"),
      h = (function (i) {
        a(n, i);
        var r = s(n);
        function n() {
          var e;
          return (
            t(this, n),
            ((e = r.apply(this, arguments)).externalClasses = [
              "".concat(o, "-class"),
              "".concat(o, "-class-content"),
              "".concat(o, "-class-image"),
            ]),
            (e.properties = c.default),
            (e.data = {
              prefix: o,
              classPrefix: u,
              hasChild: !0,
              length: 0,
              className: "",
            }),
            (e.options = { multipleSlots: !0 }),
            (e.relations = { "../avatar/avatar": { type: "descendant" } }),
            (e.lifetimes = {
              attached: function () {
                this.setClass();
              },
              ready: function () {
                this.setData({ length: this.$children.length }),
                  this.handleMax();
              },
            }),
            (e.observers = {
              "cascading, size": function () {
                this.setClass();
              },
            }),
            (e.methods = {
              setClass: function () {
                var e = this.properties,
                  t = e.cascading,
                  a = e.size,
                  s = t.split("-")[0],
                  i = [
                    u,
                    "".concat(o, "-class"),
                    "".concat(u, "-offset-").concat(s),
                    ""
                      .concat(u, "-offset-")
                      .concat(s, "-")
                      .concat(a.indexOf("px") > -1 ? "medium" : a || "medium"),
                  ];
                this.setData({ className: i.join(" ") });
              },
              handleMax: function () {
                var e = this.data.max,
                  t = this.$children.length;
                !e ||
                  e > t ||
                  this.$children.splice(e, t - e).forEach(function (e) {
                    e.hide();
                  });
              },
              onCollapsedItemClick: function (e) {
                this.triggerEvent("collapsed-item-click", e.detail);
              },
            }),
            e
          );
        }
        return e(n);
      })(r.SuperComponent),
      d = (h = (0, i.__decorate)([(0, r.wxComponent)()], h));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/avatar-group/avatar-group.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/avatar-group/avatar-group.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/avatar/avatar";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/avatar/avatar.js";
define(
  "miniprogram_npm/tdesign-miniprogram/avatar/avatar.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      i = require("../common/src/index"),
      o = l(require("../common/config")),
      n = l(require("./props")),
      c = require("../common/utils");
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = o.default.prefix,
      p = "".concat(u, "-avatar"),
      d = (function (s) {
        r(o, s);
        var i = a(o);
        function o() {
          var e;
          return (
            t(this, o),
            ((e = i.apply(this, arguments)).options = { multipleSlots: !0 }),
            (e.externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-image"),
              "".concat(u, "-class-icon"),
              "".concat(u, "-class-alt"),
              "".concat(u, "-class-content"),
            ]),
            (e.properties = n.default),
            (e.data = {
              prefix: u,
              classPrefix: p,
              isShow: !0,
              zIndex: 0,
              systemInfo: c.systemInfo,
            }),
            (e.relations = {
              "../avatar-group/avatar-group": {
                type: "ancestor",
                linked: function (e) {
                  (this.parent = e),
                    this.setData({
                      shape: this.data.shape || e.data.shape || "circle",
                      size: this.data.size || e.data.size,
                      bordered: !0,
                    });
                },
              },
            }),
            (e.observers = {
              icon: function (e) {
                var t = (0, c.setIcon)("icon", e, "");
                this.setData(Object.assign({}, t));
              },
            }),
            (e.methods = {
              hide: function () {
                this.setData({ isShow: !1 });
              },
              onLoadError: function (e) {
                this.properties.hideOnLoadFailed &&
                  this.setData({ isShow: !1 }),
                  this.triggerEvent("error", e.detail);
              },
            }),
            e
          );
        }
        return e(o);
      })(i.SuperComponent),
      h = (d = (0, s.__decorate)([(0, i.wxComponent)()], d));
    exports.default = h;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/avatar/avatar.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/avatar/avatar.js");
