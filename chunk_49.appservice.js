__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/slider/slider": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/slider/slider"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { customStyle: new Array(1), style: new Array(1) },
          K = U === true,
          e,
          g,
          i = (C, T) => {
            var $A = D.label;
            C ||
            K ||
            !!U.label ||
            ($A
              ? !!(Z(undefined, "getValue") || U.label || U.min) || undefined
              : U.min)
              ? T(Y($A ? P(X(a).getValue)(D.label, D.min) : D.min))
              : T();
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__value " +
                        Y(D.classPrefix) +
                        "__value--min"
                    );
                },
                i
              );
            }
          },
          k,
          m = (C, n, o, p, q, r, T, E) => {
            var t,
              v = (C, T) => {
                var $A = o;
                C || K || !!q || Z(U.scaleTextArray, $A)
                  ? T(Y(X(D.scaleTextArray)[$A]))
                  : T();
              },
              u = (C, T, E) => {
                if (t === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (
                        C ||
                        K ||
                        !!(
                          Z(undefined, "cls") ||
                          U.classPrefix ||
                          Q.a([U.theme])
                        ) ||
                        undefined
                      )
                        L(
                          N,
                          P(X(b).cls)(D.classPrefix + "__scale-desc", [D.theme])
                        );
                    },
                    v
                  );
                }
              },
              s = (C, T, E, B) => {
                t = X(D.scaleTextArray).length ? 1 : 0;
                B(t, u);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([!!(U._value || Z(p, "val")) || undefined]),
                      Q.a([U.disabled]),
                      U.theme,
                      Q.a([
                        !!(
                          q ||
                          q ||
                          Z(U.scaleArray, "length") ||
                          U.theme ||
                          U.value ||
                          Z(p, "val")
                        ) || undefined,
                      ]),
                    ])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__scale-item", [
                      ["active", D._value >= X(n).val],
                      ["disabled", D.disabled],
                      D.theme,
                      [
                        "hidden",
                        ((o == 0 || o == X(D.scaleArray).length - 1) &&
                          D.theme == "capsule") ||
                          D.value == X(n).val,
                      ],
                    ])
                  );
                var $A = D.vertical;
                var $B = D.vertical;
                if (
                  C ||
                  K ||
                  !!(
                    U.vertical ||
                    ($A ? undefined : undefined) ||
                    Z(p, "left") ||
                    U.vertical ||
                    ($B ? undefined : undefined)
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    Y($A ? "top" : "left") +
                      ":" +
                      Y(X(n).left) +
                      "px; " +
                      Y(
                        $B
                          ? "transform: translate(-50%, -50%);"
                          : "transform: translateX(-50%);"
                      )
                  );
                if (C || K || undefined) O(N, "aria-hidden", true);
              },
              s
            );
          },
          l = (C, T, E, B, F) => {
            if (k === 1) {
              F(
                D.scaleArray,
                "index",
                U ? U.scaleArray : undefined,
                [0, "scaleArray"],
                m
              );
            }
          },
          p,
          r = (C, T) => {
            C ||
            K ||
            !!(Z(undefined, "getValue") || U.label || U._value || U._value) ||
            undefined
              ? T(Y(P(X(a).getValue)(D.label, D._value) || D._value))
              : T();
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([!!U.label || undefined])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(b).cls)(D.classPrefix + "__dot-value", [
                        ["sr-only", !D.label],
                      ])
                    );
                  if (C) O(N, "aria-role", "alert");
                  if (C) O(N, "aria-live", "assertive");
                  if (C || K || !!U.isVisibleToScreenReader || undefined)
                    O(N, "aria-hidden", !D.isVisibleToScreenReader);
                },
                r
              );
            }
          },
          s = (C) => {},
          o = (C, T, E, B) => {
            p = D.label || D.isVisibleToScreenReader ? 1 : 0;
            B(p, q);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__dot-slider");
                if (C) O(N, "aria-role", "slider");
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                if (C || K || U.max) O(N, "aria-valuemax", D.max);
                if (C || K || U.min) O(N, "aria-valuemin", D.min);
                if (C || K || U._value) O(N, "aria-valuenow", D._value);
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getValue") ||
                    U.label ||
                    U._value ||
                    U._value
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-valuetext",
                    P(X(a).getValue)(D.label, D._value) || D._value
                  );
              },
              s
            );
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__dot " + Y(D.prefix) + "-class-cursor"
                  );
                if (C) R.v(N, "touchstart", "onTouchStart", !0, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onSingleLineTap", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.i(N, "singleDot");
              },
              o
            );
          },
          j = (C, T, E, B) => {
            k = D.isScale ? 1 : 0;
            B(k, l);
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.disabled]), U.theme]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__line", [
                        ["disabled", D.disabled],
                        D.theme,
                        "single",
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-bar-active"
                  );
                var $A = D.vertical;
                if (
                  C ||
                  K ||
                  !!(
                    U.vertical ||
                    ($A ? undefined : undefined) ||
                    U.lineBarWidth
                  ) ||
                  undefined
                )
                  R.y(N, Y($A ? "height" : "width") + ": " + Y(D.lineBarWidth));
              },
              n
            );
          },
          t,
          v = (C, T) => {
            var $A = D.label;
            C ||
            K ||
            !!U.label ||
            ($A
              ? !!(Z(undefined, "getValue") || U.label || U.max) || undefined
              : U.max)
              ? T(Y($A ? P(X(a).getValue)(D.label, D.max) : D.max))
              : T();
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__value " +
                        Y(D.classPrefix) +
                        "__value--max"
                    );
                },
                v
              );
            }
          },
          f = (C, T, E, B) => {
            if (e === 1) {
              g = D.showExtremeValue ? 1 : 0;
              B(g, h);
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([U.disabled]),
                        U.theme,
                        Q.a([!!(U.isScale || U.theme) || undefined]),
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(b).cls)(D.classPrefix + "__bar", [
                          ["disabled", D.disabled],
                          D.theme,
                          ["marks", D.isScale && D.theme == "capsule"],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-bar"
                    );
                  if (C) R.v(N, "tap", "onSingleLineTap", !1, !1, !1, !1);
                  if (C) R.i(N, "sliderLine");
                },
                j
              );
              t = D.showExtremeValue ? 1 : 0;
              B(t, u);
            }
          },
          w,
          y,
          A0 = (C, T) => {
            C || K || U.min ? T(Y(D.min)) : T();
          },
          z = (C, T, E) => {
            if (y === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__range-extreme " +
                        Y(D.classPrefix) +
                        "__range-extreme--min"
                    );
                },
                A0
              );
            }
          },
          C0,
          E0 = (C, F0, G0, H0, I0, J0, T, E) => {
            var L0,
              N0 = (C, T) => {
                var $A = G0;
                C || K || !!I0 || Z(U.scaleTextArray, $A)
                  ? T(Y(X(D.scaleTextArray)[$A]))
                  : T();
              },
              M0 = (C, T, E) => {
                if (L0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (
                        C ||
                        K ||
                        !!(
                          Z(undefined, "cls") ||
                          U.classPrefix ||
                          Q.a([U.theme])
                        ) ||
                        undefined
                      )
                        L(
                          N,
                          P(X(b).cls)(D.classPrefix + "__scale-desc", [D.theme])
                        );
                    },
                    N0
                  );
                }
              },
              K0 = (C, T, E, B) => {
                L0 = X(D.scaleTextArray).length ? 1 : 0;
                B(L0, M0);
              };
            E(
              "view",
              {},
              (N, C) => {
                var $A = 1;
                var $B = 0;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([
                        !!(
                          Z(U.dotTopValue, $A) ||
                          Z(H0, "val") ||
                          Z(H0, "val") ||
                          Z(U.dotTopValue, $B)
                        ) || undefined,
                      ]),
                      Q.a([U.disabled]),
                      U.theme,
                      Q.a([
                        !!(
                          I0 ||
                          I0 ||
                          Z(U.scaleArray, "length") ||
                          U.theme ||
                          U.value ||
                          Z(H0, "val")
                        ) || undefined,
                      ]),
                    ])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__scale-item", [
                      [
                        "active",
                        X(D.dotTopValue)[$A] >= X(F0).val &&
                          X(F0).val >= X(D.dotTopValue)[$B],
                      ],
                      ["disabled", D.disabled],
                      D.theme,
                      [
                        "hidden",
                        ((G0 == 0 || G0 == X(D.scaleArray).length - 1) &&
                          D.theme == "capsule") ||
                          D.value == X(F0).val,
                      ],
                    ])
                  );
                var $C = D.vertical;
                var $D = D.vertical;
                if (
                  C ||
                  K ||
                  !!(
                    U.vertical ||
                    ($C ? undefined : undefined) ||
                    Z(H0, "left") ||
                    U.vertical ||
                    ($D ? undefined : undefined)
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    Y($C ? "top" : "left") +
                      ":" +
                      Y(X(F0).left) +
                      "px; " +
                      Y(
                        $D
                          ? "transform: translate(-50%, -50%);"
                          : "transform: translateX(-50%);"
                      )
                  );
                if (C || K || undefined) O(N, "aria-hidden", true);
              },
              K0
            );
          },
          D0 = (C, T, E, B, F) => {
            if (C0 === 1) {
              F(
                D.scaleArray,
                "index",
                U ? U.scaleArray : undefined,
                [0, "scaleArray"],
                E0
              );
            }
          },
          H0,
          J0 = (C, T) => {
            var $A = 0;
            var $B = 0;
            C ||
            K ||
            !!(
              Z(undefined, "getValue") ||
              U.label ||
              Z(U.dotTopValue, $A) ||
              Z(U.dotTopValue, $B)
            ) ||
            undefined
              ? T(
                  Y(
                    P(X(a).getValue)(D.label, X(D.dotTopValue)[$A]) ||
                      X(D.dotTopValue)[$B]
                  )
                )
              : T();
          },
          I0 = (C, T, E) => {
            if (H0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([!!U.label || undefined])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(b).cls)(D.classPrefix + "__dot-value", [
                        ["sr-only", !D.label],
                      ])
                    );
                  if (C) O(N, "aria-role", "alert");
                  if (C) O(N, "aria-live", "assertive");
                  if (C || K || !!U.isVisibleToScreenReader || undefined)
                    O(N, "aria-hidden", !D.isVisibleToScreenReader);
                },
                J0
              );
            }
          },
          K0 = (C) => {},
          G0 = (C, T, E, B) => {
            H0 = D.label || D.isVisibleToScreenReader ? 1 : 0;
            B(H0, I0);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__dot-slider");
                if (C) O(N, "aria-role", "slider");
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                if (C || K || U.max) O(N, "aria-valuemax", D.max);
                if (C || K || U.min) O(N, "aria-valuemin", D.min);
                var $A = 0;
                if (C || K || Z(U.dotTopValue, $A))
                  O(N, "aria-valuenow", X(D.dotTopValue)[$A]);
                var $B = 0;
                var $C = 0;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getValue") ||
                    U.label ||
                    Z(U.dotTopValue, $B) ||
                    Z(U.dotTopValue, $C)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-valuetext",
                    P(X(a).getValue)(D.label, X(D.dotTopValue)[$B]) ||
                      X(D.dotTopValue)[$C]
                  );
              },
              K0
            );
          },
          M0,
          O0 = (C, T) => {
            var $A = 1;
            var $B = 1;
            C ||
            K ||
            !!(
              Z(undefined, "getValue") ||
              U.label ||
              Z(U.dotTopValue, $A) ||
              Z(U.dotTopValue, $B)
            ) ||
            undefined
              ? T(
                  Y(
                    P(X(a).getValue)(D.label, X(D.dotTopValue)[$A]) ||
                      X(D.dotTopValue)[$B]
                  )
                )
              : T();
          },
          N0 = (C, T, E) => {
            if (M0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([!!U.label || undefined])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(b).cls)(D.classPrefix + "__dot-value", [
                        ["sr-only", !D.label],
                      ])
                    );
                  if (C) O(N, "aria-role", "alert");
                  if (C) O(N, "aria-live", "assertive");
                  if (C || K || !!U.isVisibleToScreenReader || undefined)
                    O(N, "aria-hidden", !D.isVisibleToScreenReader);
                },
                O0
              );
            }
          },
          P0 = (C) => {},
          L0 = (C, T, E, B) => {
            M0 = D.label || D.isVisibleToScreenReader ? 1 : 0;
            B(M0, N0);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__dot-slider");
                if (C) O(N, "aria-role", "slider");
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                if (C || K || U.max) O(N, "aria-valuemax", D.max);
                if (C || K || U.min) O(N, "aria-valuemin", D.min);
                var $A = 1;
                if (C || K || Z(U.dotTopValue, $A))
                  O(N, "aria-valuenow", X(D.dotTopValue)[$A]);
                var $B = 1;
                var $C = 1;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getValue") ||
                    U.label ||
                    Z(U.dotTopValue, $B) ||
                    Z(U.dotTopValue, $C)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-valuetext",
                    P(X(a).getValue)(D.label, X(D.dotTopValue)[$B]) ||
                      X(D.dotTopValue)[$C]
                  );
              },
              P0
            );
          },
          F0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.prefix) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__dot " +
                      Y(D.classPrefix) +
                      "__dot--left " +
                      Y(D.prefix) +
                      "-class-cursor"
                  );
                if (C) R.v(N, "touchstart", "onTouchStart", !0, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onTouchMoveLeft", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.i(N, "leftDot");
              },
              G0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.prefix) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__dot " +
                      Y(D.classPrefix) +
                      "__dot--right " +
                      Y(D.prefix) +
                      "-class-cursor"
                  );
                if (C) R.v(N, "touchstart", "onTouchStart", !0, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onTouchMoveRight", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchEnd", !0, !1, !1, !1);
                if (C) R.i(N, "rightDot");
              },
              L0
            );
          },
          B0 = (C, T, E, B) => {
            C0 = D.isScale ? 1 : 0;
            B(C0, D0);
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.disabled]), U.theme]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix + "__line", [
                        ["disabled", D.disabled],
                        D.theme,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-bar-active"
                  );
                var $A = D.vertical;
                var $B = D.vertical;
                if (
                  C ||
                  K ||
                  !!(
                    U.vertical ||
                    ($A ? undefined : undefined) ||
                    U.lineLeft ||
                    U.vertical ||
                    ($B ? undefined : undefined) ||
                    U.lineRight
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    Y($A ? "top" : "left") +
                      ": " +
                      Y(D.lineLeft) +
                      "px; " +
                      Y($B ? "bottom" : "right") +
                      ": " +
                      Y(D.lineRight) +
                      "px"
                  );
              },
              F0
            );
          },
          Q0,
          S0 = (C, T) => {
            C || K || U.max ? T(Y(D.max)) : T();
          },
          R0 = (C, T, E) => {
            if (Q0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__range-extreme " +
                        Y(D.classPrefix) +
                        "__range-extreme--max"
                    );
                },
                S0
              );
            }
          },
          x = (C, T, E, B) => {
            if (w === 1) {
              y = D.showExtremeValue ? 1 : 0;
              B(y, z);
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([U.disabled]),
                        U.theme,
                        Q.a([!!(U.isScale || U.theme) || undefined]),
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(b).cls)(D.classPrefix + "__bar", [
                          ["disabled", D.disabled],
                          D.theme,
                          ["marks", D.isScale && D.theme == "capsule"],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-bar"
                    );
                  if (C) R.v(N, "tap", "onLineTap", !1, !1, !1, !1);
                  if (C) R.i(N, "sliderLine");
                },
                B0
              );
              Q0 = D.showExtremeValue ? 1 : 0;
              B(Q0, R0);
            }
          },
          d = (C, T, E, B) => {
            e = !D.range ? 1 : 0;
            B(e, f);
            w = D.range ? 1 : 0;
            B(w, x);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.vertical;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([
                        !!(U.label || Z(U.scaleTextArray, "length")) ||
                          undefined,
                      ]),
                      Q.a([U.disabled]),
                      Q.a([U.range]),
                    ]) ||
                    U.prefix ||
                    U.vertical ||
                    ($A ? !!U.classPrefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).cls)(D.classPrefix, [
                        ["top", D.label || X(D.scaleTextArray).length],
                        ["disabled", D.disabled],
                        ["range", D.range],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class " +
                      Y($A ? D.classPrefix + "--vertical" : "")
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/slider/slider";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/slider/slider.js";
define(
  "miniprogram_npm/tdesign-miniprogram/slider/slider.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/toConsumableArray"),
      t = require("../../../@babel/runtime/helpers/slicedToArray"),
      i = require("../../../@babel/runtime/helpers/regeneratorRuntime"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/createClass"),
      n = require("../../../@babel/runtime/helpers/inherits"),
      s = require("../../../@babel/runtime/helpers/createSuper"),
      u = require("tslib"),
      l = require("../common/src/index"),
      o = f(require("../common/config")),
      c = require("./tool"),
      h = f(require("./props")),
      d = require("../common/utils"),
      v = f(require("../common/bus"));
    function f(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var p = o.default.prefix,
      g = "".concat(p, "-slider"),
      m = (function (l) {
        n(f, l);
        var o = s(f);
        function f() {
          var e;
          return (
            r(this, f),
            ((e = o.apply(this, arguments)).externalClasses = [
              "".concat(p, "-class"),
              "".concat(p, "-class-bar"),
              "".concat(p, "-class-bar-active"),
              "".concat(p, "-class-bar-disabled"),
              "".concat(p, "-class-cursor"),
            ]),
            (e.options = { pureDataPattern: /^__/ }),
            (e.properties = h.default),
            (e.controlledProps = [{ key: "value", event: "change" }]),
            (e.data = {
              sliderStyles: "",
              classPrefix: g,
              initialLeft: null,
              initialRight: null,
              activeLeft: 0,
              activeRight: 0,
              maxRange: 0,
              lineLeft: 0,
              lineRight: 0,
              dotTopValue: [0, 0],
              _value: 0,
              blockSize: 20,
              isScale: !1,
              scaleArray: [],
              scaleTextArray: [],
              prefix: p,
              isVisibleToScreenReader: !1,
              identifier: [-1, -1],
              __inited: !1,
            }),
            (e.observers = {
              value: function (e) {
                this.handlePropsChange(e);
              },
              _value: function (e) {
                var t = this;
                this.bus.on("initial", function () {
                  return t.renderLine(e);
                }),
                  this.toggleA11yTips();
              },
              marks: function (e) {
                var t = this;
                this.bus.on("initial", function () {
                  return t.handleMark(e);
                });
              },
            }),
            (e.lifetimes = {
              created: function () {
                this.bus = new v.default();
              },
              attached: function () {
                this.properties.value || this.handlePropsChange(0),
                  this.init(),
                  this.injectPageScroll();
              },
            }),
            e
          );
        }
        return (
          a(f, [
            {
              key: "injectPageScroll",
              value: function () {
                var e = this,
                  t = this.properties,
                  i = t.range,
                  r = t.vertical;
                if (i && r) {
                  var a = getCurrentPages() || [],
                    n = null;
                  if ((a && a.length - 1 >= 0 && (n = a[a.length - 1]), n)) {
                    var s = null == n ? void 0 : n.onPageScroll;
                    n.onPageScroll = function (t) {
                      null == s || s.call(e, t), e.observerScrollTop(t);
                    };
                  }
                }
              },
            },
            {
              key: "observerScrollTop",
              value: function (e) {
                var t = (e || {}).scrollTop;
                this.pageScrollTop = t;
              },
            },
            {
              key: "toggleA11yTips",
              value: function () {
                var e = this;
                this.setData({ isVisibleToScreenReader: !0 }),
                  setTimeout(function () {
                    e.setData({ isVisibleToScreenReader: !1 });
                  }, 2e3);
              },
            },
            {
              key: "renderLine",
              value: function (e) {
                var t = this.properties,
                  i = t.min,
                  r = t.max,
                  a = t.range,
                  n = this.data.maxRange;
                if (a) {
                  var s = (n * (e[0] - Number(i))) / (Number(r) - Number(i)),
                    u = (n * (Number(r) - e[1])) / (Number(r) - Number(i));
                  this.setLineStyle(s, u);
                } else this.setSingleBarWidth(e);
              },
            },
            {
              key: "triggerValue",
              value: function (e) {
                this.preval !== e &&
                  ((this.preval = e),
                  this._trigger("change", {
                    value: (0, c.trimValue)(e, this.properties),
                  }));
              },
            },
            {
              key: "handlePropsChange",
              value: function (e) {
                var t = this,
                  i = (0, c.trimValue)(e, this.properties),
                  r = function () {
                    t.setData({ _value: i });
                  };
                0 !== this.data.maxRange ? r() : this.init().then(r);
              },
            },
            {
              key: "valueToPosition",
              value: function (e) {
                var t = this.properties,
                  i = t.min,
                  r = t.max,
                  a = t.theme,
                  n = this.data,
                  s = n.blockSize,
                  u = n.maxRange,
                  l = "capsule" === a ? Number(s) / 2 : 0;
                return (
                  Math.round(
                    ((Number(e) - Number(i)) / (Number(r) - Number(i))) * u
                  ) + l
                );
              },
            },
            {
              key: "handleMark",
              value: function (e) {
                var t = this,
                  i = function (e) {
                    return e.map(function (e) {
                      return { val: e, left: t.valueToPosition(e) };
                    });
                  };
                if (
                  ((null == e ? void 0 : e.length) &&
                    Array.isArray(e) &&
                    this.setData({
                      isScale: !0,
                      scaleArray: i(e),
                      scaleTextArray: [],
                    }),
                  "[object Object]" === Object.prototype.toString.call(e))
                ) {
                  var r = Object.keys(e).map(function (e) {
                      return Number(e);
                    }),
                    a = r.map(function (t) {
                      return e[t];
                    });
                  this.setData({
                    isScale: r.length > 0,
                    scaleArray: i(r),
                    scaleTextArray: a,
                  });
                }
              },
            },
            {
              key: "setSingleBarWidth",
              value: function (e) {
                var t = this.valueToPosition(e);
                this.setData({ lineBarWidth: "".concat(t, "px") });
              },
            },
            {
              key: "init",
              value: function () {
                return (0, u.__awaiter)(
                  this,
                  void 0,
                  void 0,
                  i().mark(function e() {
                    var t, r, a, n, s, u, l, o, c, h, v, f, p;
                    return i().wrap(
                      function (e) {
                        for (;;)
                          switch ((e.prev = e.next)) {
                            case 0:
                              if (!this.data.__inited) {
                                e.next = 2;
                                break;
                              }
                              return e.abrupt("return");
                            case 2:
                              return (
                                (e.next = 4),
                                (0, d.getRect)(this, "#sliderLine")
                              );
                            case 4:
                              (t = e.sent),
                                (r = this.data.blockSize),
                                (a = this.properties),
                                (n = a.theme),
                                (s = a.vertical),
                                (u = Number(r) / 2),
                                (l = t.top),
                                (o = t.bottom),
                                (c = t.right),
                                (h = t.left),
                                (v = s ? o - l : c - h),
                                (p = s ? o : c),
                                (0 === (f = s ? l : h) && 0 === p) ||
                                  ("capsule" === n &&
                                    ((v = v - Number(r) - 6),
                                    (f -= u),
                                    (p -= u)),
                                  this.setData({
                                    maxRange: v,
                                    initialLeft: f,
                                    initialRight: p,
                                    __inited: !0,
                                  }),
                                  this.bus.emit("initial"));
                            case 16:
                            case "end":
                              return e.stop();
                          }
                      },
                      e,
                      this
                    );
                  })
                );
              },
            },
            {
              key: "stepValue",
              value: function (e) {
                var t = this.properties,
                  i = t.step,
                  r = t.min,
                  a = t.max,
                  n =
                    String(i).indexOf(".") > -1
                      ? String(i).length - String(i).indexOf(".") - 1
                      : 0;
                return (0, c.trimSingleValue)(
                  Number((Math.round(e / Number(i)) * Number(i)).toFixed(n)),
                  Number(r),
                  Number(a)
                );
              },
            },
            {
              key: "onSingleLineTap",
              value: function (e) {
                if (!this.properties.disabled) {
                  var i = -1 === this.data.identifier[0];
                  if (i) {
                    var r = t(e.changedTouches, 1)[0];
                    this.data.identifier[0] = r.identifier;
                  }
                  var a = this.getSingleChangeValue(e);
                  i && (this.data.identifier[0] = -1), this.triggerValue(a);
                }
              },
            },
            {
              key: "getSingleChangeValue",
              value: function (e) {
                var t = this,
                  i = this.properties,
                  r = i.min,
                  a = i.max,
                  n = i.theme,
                  s = i.vertical,
                  u = this.data,
                  l = u.initialLeft,
                  o = u.maxRange,
                  c = u.blockSize,
                  h = e.changedTouches.find(function (e) {
                    return e.identifier === t.data.identifier[0];
                  }),
                  d = this.getPagePosition(h),
                  v = 0;
                "capsule" === n
                  ? ((v = Number(c)), s && (v *= 2), (v += 6))
                  : s && (v = Number(c));
                var f,
                  p = d - l - v;
                return (
                  (f =
                    p <= 0
                      ? Number(r)
                      : p >= o
                      ? Number(a)
                      : (p / o) * (Number(a) - Number(r)) + Number(r)),
                  this.stepValue(f)
                );
              },
            },
            {
              key: "convertPosToValue",
              value: function (e, t) {
                var i = this.data.maxRange,
                  r = this.properties,
                  a = r.max,
                  n = r.min;
                return 0 === t
                  ? (e / i) * (Number(a) - Number(n)) + Number(n)
                  : Number(a) - (e / i) * (Number(a) - Number(n));
              },
            },
            {
              key: "onLineTap",
              value: function (e) {
                var i = this,
                  r = this.properties,
                  a = r.disabled,
                  n = r.theme,
                  s = r.vertical,
                  u = this.data,
                  l = u.initialLeft,
                  o = u.initialRight,
                  c = u.maxRange,
                  h = u.blockSize;
                if (!a) {
                  var v = t(e.changedTouches, 1)[0],
                    f = this.getPagePosition(v),
                    p = "capsule" === n ? Number(h) / 2 : 0;
                  f - l < 0 ||
                    -(f - o) > c + Number(h) ||
                    Promise.all([
                      (0, d.getRect)(this, "#leftDot"),
                      (0, d.getRect)(this, "#rightDot"),
                    ]).then(function (e) {
                      var r = t(e, 2),
                        a = r[0],
                        u = r[1],
                        c = i.pageScrollTop || 0,
                        d = s ? a.top + c : a.left,
                        v = Math.abs(f - d - p),
                        g = s ? u.top + c : u.left,
                        m = v < Math.abs(g - f + p),
                        b = 0;
                      if (
                        ("capsule" === n
                          ? ((b = Number(h)), s && (b *= 2), (b += 6))
                          : s && (b = Number(h)),
                        m)
                      ) {
                        var y = f - l - b,
                          N = i.convertPosToValue(y, 0);
                        i.triggerValue([i.stepValue(N), i.data._value[1]]);
                      } else {
                        var T = -(f - o);
                        s && (T += b / 2);
                        var S = i.convertPosToValue(T, 1);
                        i.triggerValue([i.data._value[0], i.stepValue(S)]);
                      }
                    });
                }
              },
            },
            {
              key: "onTouchStart",
              value: function (e) {
                this.triggerEvent("dragstart", { e: e });
                var i = t(e.changedTouches, 1)[0];
                "rightDot" === e.currentTarget.id
                  ? (this.data.identifier[1] = i.identifier)
                  : (this.data.identifier[0] = i.identifier);
              },
            },
            {
              key: "onTouchMoveLeft",
              value: function (t) {
                var i = this,
                  r = this.properties,
                  a = r.disabled,
                  n = r.theme,
                  s = r.vertical,
                  u = this.data,
                  l = u.initialLeft,
                  o = u._value,
                  c = u.blockSize;
                if (!a) {
                  var h = t.changedTouches.find(function (e) {
                      return e.identifier === i.data.identifier[0];
                    }),
                    d = this.getPagePosition(h),
                    v = 0;
                  "capsule" === n && (v += Number(c)),
                    s && (v += Number(c) + 6);
                  var f = d - l - v,
                    p = e(o),
                    g = this.convertPosToValue(f, 0);
                  (p[0] = this.stepValue(g)), this.triggerValue(p);
                }
              },
            },
            {
              key: "onTouchMoveRight",
              value: function (t) {
                var i = this,
                  r = this.properties,
                  a = r.disabled,
                  n = r.vertical,
                  s = this.data,
                  u = s.initialRight,
                  l = s._value,
                  o = s.blockSize;
                if (!a) {
                  var c = t.changedTouches.find(function (e) {
                      return e.identifier === i.data.identifier[1];
                    }),
                    h = this.getPagePosition(c),
                    d = 0;
                  n && (d += Number(o) / 2 + 6);
                  var v = -(h - u - d),
                    f = e(l),
                    p = this.convertPosToValue(v, 1);
                  (f[1] = this.stepValue(p)), this.triggerValue(f);
                }
              },
            },
            {
              key: "setLineStyle",
              value: function (e, i) {
                var r = this.properties.theme,
                  a = this.data,
                  n = a.blockSize,
                  s = a.maxRange,
                  u = "capsule" === r ? Number(n) / 2 : 0,
                  l = t(this.data._value, 2),
                  o = l[0],
                  c = l[1],
                  h = function (e) {
                    return parseInt(e, 10);
                  };
                this.setData({ dotTopValue: [o, c] }),
                  e + i <= s
                    ? this.setData({ lineLeft: h(e + u), lineRight: h(i + u) })
                    : this.setData({
                        lineLeft: h(s + u - i),
                        lineRight: h(s - e + 1.5 * u),
                      });
              },
            },
            {
              key: "onTouchEnd",
              value: function (e) {
                this.triggerEvent("dragend", { e: e, value: this.data._value }),
                  "rightDot" === e.currentTarget.id
                    ? (this.data.identifier[1] = -1)
                    : (this.data.identifier[0] = -1);
              },
            },
            {
              key: "getPagePosition",
              value: function (e) {
                var t = e.pageX,
                  i = e.pageY;
                return this.properties.vertical ? i : t;
              },
            },
          ]),
          f
        );
      })(l.SuperComponent),
      b = (m = (0, u.__decorate)([(0, l.wxComponent)()], m));
    exports.default = b;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/slider/slider.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/slider/slider.js");
