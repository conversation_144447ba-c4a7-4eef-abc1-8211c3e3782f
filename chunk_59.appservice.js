__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/toast/toast": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            showOverlay: new Array(1),
            overlayProps: new Array(12),
            preventScrollThrough: new Array(3),
            usingCustomNavbar: new Array(1),
          },
          K = U === true,
          c,
          g,
          i = (C) => {},
          j,
          k = (C, T, E, B, F, S, J) => {
            var $A = I(j);
            if (j && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    ariaHidden: true,
                    tClass:
                      D.classPrefix +
                      "__icon " +
                      D.classPrefix +
                      "__icon--" +
                      D.direction,
                  },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        {
                          tClass:
                            !!(U.classPrefix || U.classPrefix || U.direction) ||
                            undefined,
                        },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          h = (C, T, E, B, F, S, J) => {
            if (g === 1) {
              E(
                "t-loading",
                {},
                (N, C) => {
                  if (C) O(N, "theme", "circular");
                  var $A = D.direction === "row";
                  if (C || K || !!U.direction || ($A ? undefined : undefined))
                    O(N, "size", $A ? "48rpx" : "64rpx");
                  if (C) O(N, "loading", true);
                  if (C) O(N, "inherit-color", true);
                  if (C) O(N, "layout", "vertical");
                },
                i
              );
            } else if (g === 2) {
              j = "icon";
              B(j, k);
            }
          },
          l = (C, T) => {
            C || K || U.message ? T(Y(D.message)) : T();
          },
          f = (C, T, E, B, F, S) => {
            g = D.isLoading ? 1 : D._icon ? 2 : 0;
            B(g, h);
            S("icon");
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.direction) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__text " +
                      Y(D.classPrefix) +
                      "__text--" +
                      Y(D.direction)
                  );
                if (C) O(N, "aria-role", "alert");
              },
              l
            );
            S("message");
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.direction) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.classPrefix) +
                      "__content--" +
                      Y(D.direction)
                  );
              },
              f
            );
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.direction, U.theme, Q.a([U.message])]) ||
                      U.prefix ||
                      U.transitionClass
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          D.direction,
                          D.theme,
                          ["with-text", D.message],
                        ])
                      ) +
                        " class " +
                        Y(D.prefix) +
                        "-class " +
                        Y(D.transitionClass)
                    );
                  var $A = D.placement === "top";
                  var $B = D.placement === "bottom";
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!(
                          U.placement ||
                          ($A
                            ? undefined
                            : !!U.placement || ($B ? undefined : undefined))
                        ) || undefined,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        "top:" + ($A ? "25%" : $B ? "75%" : "45%"),
                        D.style,
                        D.customStyle,
                      ])
                    );
                  if (C)
                    R.v(N, "transitionend", "onTransitionEnd", !1, !1, !1, !1);
                  if (C) R.v(N, "touchstart", "loop", !0, !1, !1, !1);
                },
                e
              );
            }
          },
          m = (C) => {},
          b = (C, T, E, B) => {
            c = D.realVisible ? 1 : 0;
            B(c, d);
            E(
              "t-overlay",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.overlayProps || Z(U.overlayProps, "style")) ||
                  undefined
                )
                  R.y(N, (D.overlayProps && X(D.overlayProps).style) || "");
                A["overlayProps"][10] = A["overlayProps"][11] = (D, E, T) => {
                  R.y(N, (D.overlayProps && X(D.overlayProps).style) || "");
                };
                if (
                  C ||
                  K ||
                  !!(
                    U.realVisible ||
                    U.showOverlay ||
                    U.preventScrollThrough
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "visible",
                    D.realVisible && (D.showOverlay || D.preventScrollThrough)
                  );
                A["showOverlay"][0] = A["preventScrollThrough"][0] = (
                  D,
                  E,
                  T
                ) => {
                  O(
                    N,
                    "visible",
                    D.realVisible && (D.showOverlay || D.preventScrollThrough)
                  );
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(U.overlayProps || Z(U.overlayProps, "zIndex")) ||
                  undefined
                )
                  O(
                    N,
                    "z-index",
                    (D.overlayProps && X(D.overlayProps).zIndex) || 11000
                  );
                A["overlayProps"][0] = A["overlayProps"][1] = (D, E, T) => {
                  O(
                    N,
                    "z-index",
                    (D.overlayProps && X(D.overlayProps).zIndex) || 11000
                  );
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(U.overlayProps || Z(U.overlayProps, "duration")) ||
                  undefined
                )
                  O(
                    N,
                    "duration",
                    (D.overlayProps && X(D.overlayProps).duration) || 300
                  );
                A["overlayProps"][2] = A["overlayProps"][3] = (D, E, T) => {
                  O(
                    N,
                    "duration",
                    (D.overlayProps && X(D.overlayProps).duration) || 300
                  );
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(
                    U.overlayProps ||
                    Z(U.overlayProps, "usingCustomNavbar") ||
                    U.usingCustomNavbar
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "usingCustomNavbar",
                    (D.overlayProps && X(D.overlayProps).usingCustomNavbar) ||
                      D.usingCustomNavbar
                  );
                A["overlayProps"][4] =
                  A["overlayProps"][5] =
                  A["usingCustomNavbar"][0] =
                    (D, E, T) => {
                      O(
                        N,
                        "usingCustomNavbar",
                        (D.overlayProps &&
                          X(D.overlayProps).usingCustomNavbar) ||
                          D.usingCustomNavbar
                      );
                      E(N);
                    };
                var $A = D.preventScrollThrough;
                if (
                  C ||
                  K ||
                  !!U.preventScrollThrough ||
                  ($A
                    ? undefined
                    : !!(
                        U.overlayProps || Z(U.overlayProps, "backgroundColor")
                      ) || undefined)
                )
                  O(
                    N,
                    "backgroundColor",
                    $A
                      ? "transparent"
                      : (D.overlayProps && X(D.overlayProps).backgroundColor) ||
                          ""
                  );
                A["preventScrollThrough"][1] =
                  A["overlayProps"][6] =
                  A["overlayProps"][7] =
                    (D, E, T) => {
                      var $B = D.preventScrollThrough;
                      O(
                        N,
                        "backgroundColor",
                        $B
                          ? "transparent"
                          : (D.overlayProps &&
                              X(D.overlayProps).backgroundColor) ||
                              ""
                      );
                      E(N);
                    };
                if (
                  C ||
                  K ||
                  !!(
                    U.preventScrollThrough ||
                    U.overlayProps ||
                    Z(U.overlayProps, "preventScrollThrough")
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "preventScrollThrough",
                    D.preventScrollThrough ||
                      (D.overlayProps && X(D.overlayProps).preventScrollThrough)
                  );
                A["preventScrollThrough"][2] =
                  A["overlayProps"][8] =
                  A["overlayProps"][9] =
                    (D, E, T) => {
                      O(
                        N,
                        "preventScrollThrough",
                        D.preventScrollThrough ||
                          (D.overlayProps &&
                            X(D.overlayProps).preventScrollThrough)
                      );
                      E(N);
                    };
              },
              m
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/toast/toast";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/toast/toast.js";
define(
  "miniprogram_npm/tdesign-miniprogram/toast/toast.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      i = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      l = require("tslib"),
      s = require("../common/src/index"),
      a = d(require("../common/config")),
      n = d(require("./props")),
      o = d(require("../mixins/transition")),
      u = require("../common/utils"),
      c = d(require("../mixins/using-custom-navbar"));
    function d(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var h = a.default.prefix,
      m = "".concat(h, "-toast"),
      f = (function (l) {
        t(a, l);
        var s = r(a);
        function a() {
          var e;
          return (
            i(this, a),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
            ]),
            (e.options = { multipleSlots: !0 }),
            (e.behaviors = [(0, o.default)(), c.default]),
            (e.hideTimer = null),
            (e.data = { prefix: h, classPrefix: m, typeMapIcon: "" }),
            (e.properties = n.default),
            (e.lifetimes = {
              detached: function () {
                this.destroyed();
              },
            }),
            (e.pageLifetimes = {
              hide: function () {
                this.hide();
              },
            }),
            (e.methods = {
              show: function (e) {
                var i = this;
                this.hideTimer && clearTimeout(this.hideTimer);
                var t = {
                    loading: "loading",
                    success: "check-circle",
                    warning: "error-circle",
                    error: "close-circle",
                  }[null == e ? void 0 : e.theme],
                  r = {
                    direction: n.default.direction.value,
                    duration: n.default.duration.value,
                    icon: n.default.icon.value,
                    message: n.default.message.value,
                    placement: n.default.placement.value,
                    preventScrollThrough: n.default.preventScrollThrough.value,
                    theme: n.default.theme.value,
                  },
                  l = Object.assign(Object.assign(Object.assign({}, r), e), {
                    visible: !0,
                    isLoading: "loading" === (null == e ? void 0 : e.theme),
                    _icon: (0, u.calcIcon)(null != t ? t : e.icon),
                  }),
                  s = l.duration;
                this.setData(l),
                  s > 0 &&
                    (this.hideTimer = setTimeout(function () {
                      i.hide();
                    }, s));
              },
              hide: function () {
                var e, i;
                this.data.visible &&
                  (this.setData({ visible: !1 }),
                  null ===
                    (i =
                      null === (e = this.data) || void 0 === e
                        ? void 0
                        : e.close) ||
                    void 0 === i ||
                    i.call(e),
                  this.triggerEvent("close"));
              },
              destroyed: function () {
                this.hideTimer &&
                  (clearTimeout(this.hideTimer), (this.hideTimer = null)),
                  this.triggerEvent("destory");
              },
              loop: function () {},
            }),
            e
          );
        }
        return e(a);
      })(s.SuperComponent),
      p = (f = (0, l.__decorate)([(0, s.wxComponent)()], f));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/toast/toast.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/toast/toast.js");
