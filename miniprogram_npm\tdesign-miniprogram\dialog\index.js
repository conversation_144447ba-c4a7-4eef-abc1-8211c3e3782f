Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e,
  t = require("../../../@babel/runtime/helpers/typeof"),
  n = require("tslib"),
  s = (e = require("./props")) && e.__esModule ? e : { default: e },
  o = require("../common/utils");
var r = {
    actions: [],
    buttonLayout: s.default.buttonLayout.value,
    cancelBtn: s.default.cancelBtn.value,
    closeOnOverlayClick: s.default.closeOnOverlayClick.value,
    confirmBtn: s.default.confirmBtn.value,
    content: "",
    preventScrollThrough: s.default.preventScrollThrough.value,
    showOverlay: s.default.showOverlay.value,
    title: "",
    visible: s.default.visible.value,
  },
  i = {
    alert: function (e) {
      var t = Object.assign({}, e),
        s = t.context,
        i = t.selector,
        a = void 0 === i ? "#t-dialog" : i,
        c = (0, n.__rest)(t, ["context", "selector"]),
        l = (0, o.getInstance)(s, a);
      return l
        ? new Promise(function (e) {
            var t = Object.assign(
              Object.assign(Object.assign({}, r), l.properties),
              c
            );
            l.setData(
              Object.assign(Object.assign({ cancelBtn: "" }, t), {
                visible: !0,
              })
            ),
              (l._onConfirm = e);
          })
        : Promise.reject();
    },
    confirm: function (e) {
      var t = Object.assign({}, e),
        s = t.context,
        i = t.selector,
        a = void 0 === i ? "#t-dialog" : i,
        c = (0, n.__rest)(t, ["context", "selector"]),
        l = (0, o.getInstance)(s, a);
      return l
        ? new Promise(function (e, t) {
            var n = Object.assign(
              Object.assign(Object.assign({}, r), l.properties),
              c
            );
            l.setData(Object.assign(Object.assign({}, n), { visible: !0 })),
              (l._onConfirm = e),
              (l._onCancel = t);
          })
        : Promise.reject();
    },
    close: function (e) {
      var t = Object.assign({}, e),
        n = t.context,
        s = t.selector,
        r = void 0 === s ? "#t-dialog" : s,
        i = (0, o.getInstance)(n, r);
      return i ? (i.close(), Promise.resolve()) : Promise.reject();
    },
    action: function (e) {
      var s = Object.assign({}, e),
        i = s.context,
        a = s.selector,
        c = void 0 === a ? "#t-dialog" : a,
        l = (0, n.__rest)(s, ["context", "selector"]),
        u = (0, o.getInstance)(i, c);
      if (!u) return Promise.reject();
      var v = e.buttonLayout,
        b = void 0 === v ? "vertical" : v,
        g = e.actions,
        O = void 0 === g ? u.properties.actions : g,
        f = "vertical" === b ? 7 : 3;
      return (
        (!O || ("object" == t(O) && (0 === O.length || O.length > f))) &&
          console.warn("action 数量建议控制在1至".concat(f, "个")),
        new Promise(function (e) {
          var t = Object.assign(
            Object.assign(Object.assign({}, r), u.properties),
            l
          );
          u.setData(
            Object.assign(Object.assign({}, t), {
              buttonLayout: b,
              visible: !0,
            })
          ),
            (u._onAction = e);
        })
      );
    },
  };
exports.default = i;
