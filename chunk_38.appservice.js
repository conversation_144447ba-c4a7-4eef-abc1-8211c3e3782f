__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/overlay/overlay": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          e = (C, T, E, B, F, S) => {
            S("");
          },
          f = (C, T, E, B, F, S) => {
            S("");
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.prefix || U.transitionClass) || undefined)
                    L(
                      N,
                      Y(D.prefix) +
                        "-overlay " +
                        Y(D.transitionClass) +
                        " class"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!U.duration || undefined,
                        !!U._zIndex || undefined,
                        !!U.distanceTop || undefined,
                        U.computedStyle,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        "--td-overlay-transition-duration:" + D.duration + "ms",
                        "z-index:" + D._zIndex,
                        "top:" + D.distanceTop + "px",
                        D.computedStyle,
                        D.style,
                        D.customStyle,
                      ])
                    );
                  if (C) O(N, "catchtouchmove", "noop");
                  if (C || K || !!U.ariaRole || undefined)
                    O(N, "aria-role", D.ariaRole || "button");
                  if (C || K || !!U.ariaLabel || undefined)
                    O(N, "aria-label", D.ariaLabel || "关闭");
                  if (C) R.v(N, "tap", "handleClick", !1, !1, !1, !1);
                  if (C)
                    R.v(N, "transitionend", "onTransitionEnd", !1, !1, !1, !1);
                },
                e
              );
            } else if (c === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.prefix || U.transitionClass) || undefined)
                    L(
                      N,
                      Y(D.prefix) +
                        "-overlay " +
                        Y(D.transitionClass) +
                        " class"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!U._zIndex || undefined,
                        !!U.distanceTop || undefined,
                        U.computedStyle,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        "z-index:" + D._zIndex,
                        "top:" + D.distanceTop + "px",
                        D.computedStyle,
                        D.style,
                        D.customStyle,
                      ])
                    );
                  if (C || K || !!U.ariaRole || undefined)
                    O(N, "aria-role", D.ariaRole || "button");
                  if (C || K || !!U.ariaLabel || undefined)
                    O(N, "aria-label", D.ariaLabel || "关闭");
                  if (C) R.v(N, "tap", "handleClick", !1, !1, !1, !1);
                  if (C)
                    R.v(N, "transitionend", "onTransitionEnd", !1, !1, !1, !1);
                },
                f
              );
            }
          },
          b = (C, T, E, B) => {
            c =
              D.realVisible && D.preventScrollThrough
                ? 1
                : D.realVisible
                ? 2
                : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/overlay/overlay";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/overlay/overlay.js";
define(
  "miniprogram_npm/tdesign-miniprogram/overlay/overlay.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      o = require("../common/src/index"),
      u = c(require("../common/config")),
      s = c(require("./props")),
      a = c(require("../mixins/transition")),
      l = c(require("../mixins/using-custom-navbar"));
    function c(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var p = u.default.prefix,
      d = "".concat(p, "-overlay"),
      f = (function (n) {
        t(u, n);
        var o = i(u);
        function u() {
          var e;
          return (
            r(this, u),
            ((e = o.apply(this, arguments)).properties = s.default),
            (e.behaviors = [(0, a.default)(), l.default]),
            (e.data = {
              prefix: p,
              classPrefix: d,
              computedStyle: "",
              _zIndex: 11e3,
            }),
            (e.observers = {
              backgroundColor: function (e) {
                this.setData({
                  computedStyle: e ? "background-color: ".concat(e, ";") : "",
                });
              },
              zIndex: function (e) {
                0 !== e && this.setData({ _zIndex: e });
              },
            }),
            (e.methods = {
              handleClick: function () {
                this.triggerEvent("click", {
                  visible: !this.properties.visible,
                });
              },
              noop: function () {},
            }),
            e
          );
        }
        return e(u);
      })(o.SuperComponent),
      b = (f = (0, n.__decorate)([(0, o.wxComponent)()], f));
    exports.default = b;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/overlay/overlay.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/overlay/overlay.js");
