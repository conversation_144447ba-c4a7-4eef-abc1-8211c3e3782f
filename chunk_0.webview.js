__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "components/communityHelp/communityHelp": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { helpData: new Array(8) },
          K = U === true,
          f = (C) => {},
          e = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || Z(U.helpData, "imgSrc"))
                  O(N, "src", X(D.helpData).imgSrc);
                A["helpData"][0] = (D, E, T) => {
                  O(N, "src", X(D.helpData).imgSrc);
                  E(N);
                };
              },
              f
            );
          },
          i = (C, T) => {
            C || K || Z(U.helpData, "title")
              ? T(Y(X(D.helpData).title), (N) => {
                  A["helpData"][1] = (D, E, T) => {
                    T(N, Y(X(D.helpData).title));
                  };
                })
              : T();
          },
          h = (C, T, E) => {
            E("text", {}, (N, C) => {}, i);
          },
          k = (C, T) => {
            C || K || Z(U.helpData, "description")
              ? T(Y(X(D.helpData).description), (N) => {
                  A["helpData"][2] = (D, E, T) => {
                    T(N, Y(X(D.helpData).description));
                  };
                })
              : T();
          },
          j = (C, T, E) => {
            E("text", {}, (N, C) => {}, k);
          },
          m = (C, T) => {
            C || K || !!Z(U.helpData, "overTime") || undefined
              ? T(Y("截止时间:" + Y(X(D.helpData).overTime)), (N) => {
                  A["helpData"][3] = (D, E, T) => {
                    T(N, Y("截止时间:" + Y(X(D.helpData).overTime)));
                  };
                })
              : T();
          },
          l = (C, T, E) => {
            E("text", {}, (N, C) => {}, m);
          },
          o = (C, T) => {
            C || K || !!Z(U.helpData, "userLimit") || undefined
              ? T(Y("接单限制：" + Y(X(D.helpData).userLimit)), (N) => {
                  A["helpData"][4] = (D, E, T) => {
                    T(N, Y("接单限制：" + Y(X(D.helpData).userLimit)));
                  };
                })
              : T();
          },
          n = (C, T, E) => {
            E("text", {}, (N, C) => {}, o);
          },
          g = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_title");
              },
              h
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_ms");
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_time");
              },
              l
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_fw");
              },
              n
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_img");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info");
              },
              g
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top");
              },
              d
            );
          },
          s = (C) => {},
          r = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || Z(U.helpData, "providerAvatar"))
                  O(N, "src", X(D.helpData).providerAvatar);
                A["helpData"][5] = (D, E, T) => {
                  O(N, "src", X(D.helpData).providerAvatar);
                  E(N);
                };
              },
              s
            );
          },
          t = (C, T) => {
            C || K || Z(U.helpData, "providerName")
              ? T(Y(X(D.helpData).providerName), (N) => {
                  A["helpData"][6] = (D, E, T) => {
                    T(N, Y(X(D.helpData).providerName));
                  };
                })
              : T();
          },
          q = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_top_img");
              },
              r
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_top_username");
              },
              t
            );
          },
          w = (C, T) => {
            C ? T("报酬：") : T();
          },
          x = (C, T) => {
            C || K || !!Z(U.helpData, "price") || undefined
              ? T(Y("￥" + Y(X(D.helpData).price)), (N) => {
                  A["helpData"][7] = (D, E, T) => {
                    T(N, Y("￥" + Y(X(D.helpData).price)));
                  };
                })
              : T();
          },
          v = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "price-label");
              },
              w
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "price-value");
              },
              x
            );
          },
          u = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_middle_price");
              },
              v
            );
          },
          p = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_top");
              },
              q
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_middle");
              },
              u
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left");
              },
              c
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right");
              },
              p
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fuwu_list");
                if (C) O(N, "bindtap", "onHelpTap");
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/notice-bar/notice-bar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          g,
          i,
          j = (C, T, E, B, F, S, J) => {
            var $A = I(i);
            if (i && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-prefix-icon" },
                  X(D._prefixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._prefixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._prefixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          h = (C, T, E, B, F, S, J) => {
            if (g === 1) {
              i = "icon";
              B(i, j);
            }
          },
          f = (C, T, E, B, F, S) => {
            S("prefix-icon");
            g = D._prefixIcon ? 1 : 0;
            B(g, h);
          },
          l,
          p = (C, q, r, s, t, u, T, E) => {
            var w = (C, T) => {
                C || K || s ? T(Y(q)) : T();
              },
              v = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!U.classPrefix || undefined)
                      L(N, Y(D.classPrefix) + "__content--vertical-item");
                  },
                  w
                );
              };
            E("swiper-item", {}, (N, C) => {}, v);
          },
          o = (C, T, E, B, F) => {
            F(D.content, "index", U ? U.content : undefined, [0, "content"], p);
          },
          n = (C, T, E) => {
            E(
              "swiper",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.classPrefix) +
                      "__content--vertical"
                  );
                if (C) O(N, "autoplay", "true");
                if (C) O(N, "vertical", "true");
                if (C) O(N, "circular", "true");
                if (C || K || U.interval) O(N, "interval", D.interval);
                if (C) O(N, "display-multiple-items", "1");
                if (C) O(N, "bindchange", "onChange");
              },
              o
            );
          },
          r,
          s = (C, T) => {
            if (r === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          u,
          v = (C, T) => {
            if (u === 1) {
              C || K || U.operation ? T(Y(D.operation)) : T();
            }
          },
          t = (C, T, E, B, F, S) => {
            u = D.operation ? 1 : 0;
            B(u, v);
            S("operation");
          },
          q = (C, T, E, B, F, S) => {
            r = D.content ? 1 : 0;
            B(r, s);
            S("content");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__operation " +
                      Y(D.prefix) +
                      "-class-operation"
                  );
                if (C) R.v(N, "tap", "clickOperation", !0, !1, !1, !1);
              },
              t
            );
          },
          m = (C, T, E) => {
            if (l === 1) {
              E("view", {}, (N, C) => {}, n);
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = !D.marquee;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.prefix ||
                      U.marquee ||
                      ($A ? !!U.classPrefix || undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__content " +
                        Y(D.prefix) +
                        "-class-content " +
                        Y($A ? D.classPrefix + "__content-wrapable" : "")
                    );
                  if (C || K || U.animationData)
                    O(N, "animation", D.animationData);
                },
                q
              );
            }
          },
          k = (C, T, E, B) => {
            l =
              D.direction === "vertical" && P(X(a).isArray)(D.content) ? 1 : 0;
            B(l, m);
          },
          x,
          z,
          A0 = (C, T, E, B, F, S, J) => {
            var $A = I(z);
            if (z && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-suffix-icon" },
                  X(D._suffixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._suffixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._suffixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          y = (C, T, E, B, F, S, J) => {
            if (x === 1) {
              z = "icon";
              B(z, A0);
            }
          },
          w = (C, T, E, B, F, S) => {
            S("suffix-icon");
            x = D._suffixIcon ? 1 : 0;
            B(x, y);
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__prefix-icon");
                if (C) R.v(N, "tap", "clickPrefixIcon", !1, !1, !1, !1);
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__content-wrap");
                if (C) R.v(N, "tap", "clickContent", !1, !1, !1, !1);
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__suffix-icon");
                if (C) R.v(N, "tap", "clickSuffixIcon", !1, !1, !1, !1);
              },
              w
            );
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(U.classPrefix || U.classPrefix || U.theme || U.prefix) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        " " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.theme) +
                        " class " +
                        Y(D.prefix) +
                        "-class"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                },
                e
              );
            }
          },
          b = (C, T, E, B) => {
            c = D.visible ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/swiper-nav/swiper-nav": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          f = (C) => {},
          g = (C) => {},
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__btn--prev");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "上一张");
                if (C) R.d(N, "dir", "prev");
                if (C) R.v(N, "tap", "nav", !1, !1, !1, !1);
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__btn--next");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "下一张");
                if (C) R.d(N, "dir", "next");
                if (C) R.v(N, "tap", "nav", !1, !1, !1, !1);
              },
              g
            );
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                    L(
                      N,
                      "class " +
                        Y(D.prefix) +
                        "-class " +
                        Y(D.classPrefix) +
                        "__btn"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                },
                e
              );
            }
          },
          h,
          k,
          m = (C, n, o, p, q, r, T, E) => {
            var s = (C) => {};
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    U.type ||
                    Q.a([Q.a([!!(U.current || q) || undefined]), U.direction])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__" + D.type + "-item", [
                      ["active", D.current === o],
                      D.direction,
                    ])
                  );
              },
              s
            );
          },
          l = (C, T, E, B, F) => {
            if (k === 1) {
              F(D.total, "idx", U ? U.total : undefined, [0, "total"], m);
            }
          },
          n,
          o = (C, T) => {
            if (n === 1) {
              C || K || !!(U.current || U.total) || undefined
                ? T(Y(Y(D.current + 1) + "/" + Y(D.total)))
                : T();
            }
          },
          j = (C, T, E, B) => {
            k = D.type === "dots" || D.type === "dots-bar" ? 1 : 0;
            B(k, l);
            n = D.type === "fraction" ? 1 : 0;
            B(n, o);
          },
          i = (C, T, E) => {
            if (h === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.prefix ||
                      U.classPrefix ||
                      U.classPrefix ||
                      U.direction ||
                      U.classPrefix ||
                      U.type ||
                      U.classPrefix ||
                      U.paginationPosition
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      "class " +
                        Y(D.prefix) +
                        "-class " +
                        Y(D.classPrefix) +
                        " " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.direction) +
                        " " +
                        Y(D.classPrefix) +
                        "__" +
                        Y(D.type) +
                        " " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.paginationPosition)
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                },
                j
              );
            }
          },
          b = (C, T, E, B) => {
            c = D.showControls ? 1 : 0;
            B(c, d);
            h = D.total >= D.minShowNum ? 1 : 0;
            B(h, i);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/swiper/swiper": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/image"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/swiper/index"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            current: new Array(1),
            snapToEdge: new Array(1),
            loop: new Array(1),
            nextMargin: new Array(1),
            interval: new Array(1),
            duration: new Array(1),
            style: new Array(1),
            easingFunction: new Array(1),
            previousMargin: new Array(1),
            displayMultipleItems: new Array(1),
            customStyle: new Array(1),
            autoplay: new Array(1),
          },
          K = U === true,
          f = (C, g, h, i, j, k, T, E) => {
            var m,
              n = (C, T, E, B, F, S, J) => {
                var $A = I(m);
                var $B = P(X(b).isObject)(g);
                if (m && $A)
                  $A(
                    R,
                    C,
                    Object.assign(
                      {
                        tClass: P(X(a).getImageClass)(
                          D.prefix,
                          D.navCurrent,
                          h,
                          D.list
                        ),
                        style: "height: " + P(X(b).addUnit)(D.height),
                        src: $B ? X(g).value : g,
                        mode: "aspectFill",
                        dataset: h,
                      },
                      X(D.imageProps),
                      { bindload: "onImageLoad" }
                    ),
                    K ||
                      (U
                        ? U.imageProps === true ||
                          Object.assign(
                            {
                              tClass:
                                !!(
                                  Z(undefined, "getImageClass") ||
                                  U.prefix ||
                                  U.navCurrent ||
                                  j ||
                                  U.list
                                ) || undefined,
                              style:
                                !!(Z(undefined, "addUnit") || U.height) ||
                                undefined,
                              src:
                                !!(Z(undefined, "isObject") || i) ||
                                ($B ? Z(i, "value") : i),
                              dataset: j,
                            },
                            X(U.imageProps),
                            {}
                          )
                        : Object.create(null))
                  ).C(C, T, E, B, F, S, J);
              },
              l = (C, T, E, B, F, S, J) => {
                m = "image";
                B(m, n);
              };
            E(
              "swiper-item",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([
                        !!(
                          Z(undefined, "isPrev") ||
                          U.navCurrent ||
                          j ||
                          U.list
                        ) || undefined,
                      ]),
                      Q.a([
                        !!(
                          Z(undefined, "isNext") ||
                          U.navCurrent ||
                          j ||
                          U.list
                        ) || undefined,
                      ]),
                    ])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(b).cls)(D.classPrefix + "__item", [
                      ["preview", P(X(a).isPrev)(D.navCurrent, h, D.list)],
                      ["next", P(X(a).isNext)(D.navCurrent, h, D.list)],
                    ])
                  );
                if (C || K || !!(U.navCurrent || j) || undefined)
                  O(N, "aria-hidden", D.navCurrent !== h);
                if (C) O(N, "aria-role", "image");
                var $A = P(X(b).isObject)(g);
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "isObject") || i) ||
                  ($A ? Z(i, "ariaLabel") : undefined)
                )
                  O(N, "aria-label", $A ? X(g).ariaLabel : "");
                if (C || K || j) R.d(N, "index", h);
                if (C) R.v(N, "tap", "onTap", !1, !1, !1, !1);
              },
              l
            );
          },
          e = (C, T, E, B, F) => {
            F(D.list, "index", U ? U.list : undefined, [0, "list"], f);
          },
          g,
          i = (C) => {},
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "t-swiper-nav",
                {},
                (N, C) => {
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class", Y(D.prefix) + "-class-nav");
                  if (C || K || !!Z(U.navigation, "type") || undefined)
                    O(N, "type", X(D.navigation).type || "dots");
                  if (C || K || !!U.navCurrent || undefined)
                    O(N, "current", D.navCurrent || 0);
                  if (C || K || !!Z(U.list, "length") || undefined)
                    O(N, "total", X(D.list).length || 0);
                  if (C || K || !!U.direction || undefined)
                    O(N, "direction", D.direction || "horizontal");
                  if (C || K || !!U.paginationPosition || undefined)
                    O(
                      N,
                      "pagination-position",
                      D.paginationPosition || "bottom"
                    );
                  if (C || K || !!Z(U.navigation, "minShowNum") || undefined)
                    O(N, "min-show-num", X(D.navigation).minShowNum || 2);
                  if (C || K || !!Z(U.navigation, "showControls") || undefined)
                    O(
                      N,
                      "show-controls",
                      X(D.navigation).showControls || false
                    );
                  if (C)
                    R.v(N, "nav-btn-change", "onNavBtnChange", !1, !1, !1, !1);
                },
                i
              );
            }
          },
          d = (C, T, E, B, F, S) => {
            E(
              "swiper",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "-host");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "addUnit") || U.height) ||
                  undefined
                )
                  R.y(N, "height:" + Y(P(X(b).addUnit)(D.height)));
                if (C || K || U.autoplay) O(N, "autoplay", D.autoplay);
                A["autoplay"][0] = (D, E, T) => {
                  O(N, "autoplay", D.autoplay);
                  E(N);
                };
                if (C || K || U.current) O(N, "current", D.current);
                A["current"][0] = (D, E, T) => {
                  O(N, "current", D.current);
                  E(N);
                };
                if (C || K || U.interval) O(N, "interval", D.interval);
                A["interval"][0] = (D, E, T) => {
                  O(N, "interval", D.interval);
                  E(N);
                };
                if (C || K || U.duration) O(N, "duration", D.duration);
                A["duration"][0] = (D, E, T) => {
                  O(N, "duration", D.duration);
                  E(N);
                };
                if (C || K || U.loop) O(N, "circular", D.loop);
                A["loop"][0] = (D, E, T) => {
                  O(N, "circular", D.loop);
                  E(N);
                };
                if (C || K || !!U.direction || undefined)
                  O(N, "vertical", D.direction == "vertical");
                if (C || K || U.easingFunction)
                  O(N, "easing-function", D.easingFunction);
                A["easingFunction"][0] = (D, E, T) => {
                  O(N, "easing-function", D.easingFunction);
                  E(N);
                };
                if (C || K || U.previousMargin)
                  O(N, "previous-margin", D.previousMargin);
                A["previousMargin"][0] = (D, E, T) => {
                  O(N, "previous-margin", D.previousMargin);
                  E(N);
                };
                if (C || K || U.nextMargin) O(N, "next-margin", D.nextMargin);
                A["nextMargin"][0] = (D, E, T) => {
                  O(N, "next-margin", D.nextMargin);
                  E(N);
                };
                if (C || K || U.snapToEdge) O(N, "snap-to-edge", D.snapToEdge);
                A["snapToEdge"][0] = (D, E, T) => {
                  O(N, "snap-to-edge", D.snapToEdge);
                  E(N);
                };
                if (C || K || U.displayMultipleItems)
                  O(N, "display-multiple-items", D.displayMultipleItems);
                A["displayMultipleItems"][0] = (D, E, T) => {
                  O(N, "display-multiple-items", D.displayMultipleItems);
                  E(N);
                };
                if (C) O(N, "bindchange", "onChange");
              },
              e
            );
            g = D.navigation ? 1 : 0;
            B(g, h);
            S("navigation");
            S("nav");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(b)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "pages/index/index": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            current: new Array(1),
            autoplay: new Array(1),
            interval: new Array(1),
            appName: new Array(1),
            duration: new Array(1),
            showIncompleteNotice: new Array(1),
            swiperList: new Array(1),
            height: new Array(1),
          },
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || U.appName) O(N, "title", D.appName);
                A["appName"][0] = (D, E, T) => {
                  O(N, "title", D.appName);
                  E(N);
                };
                if (C || K || undefined) O(N, "back", false);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          f = (C) => {},
          e = (C, T, E) => {
            E(
              "t-swiper",
              {},
              (N, C) => {
                if (C || K || U.current) O(N, "current", D.current);
                A["current"][0] = (D, E, T) => {
                  O(N, "current", D.current);
                  E(N);
                };
                if (C || K || U.autoplay) O(N, "autoplay", D.autoplay);
                A["autoplay"][0] = (D, E, T) => {
                  O(N, "autoplay", D.autoplay);
                  E(N);
                };
                if (C || K || U.duration) O(N, "duration", D.duration);
                A["duration"][0] = (D, E, T) => {
                  O(N, "duration", D.duration);
                  E(N);
                };
                if (C || K || U.interval) O(N, "interval", D.interval);
                A["interval"][0] = (D, E, T) => {
                  O(N, "interval", D.interval);
                  E(N);
                };
                if (C || K || U.height) O(N, "height", D.height);
                A["height"][0] = (D, E, T) => {
                  O(N, "height", D.height);
                  E(N);
                };
                if (C || K || U.swiperList) O(N, "list", D.swiperList);
                A["swiperList"][0] = (D, E, T) => {
                  O(N, "list", D.swiperList);
                  E(N);
                };
                if (C || K || Q.b({})) O(N, "navigation", { type: "dots-bar" });
                if (C) R.v(N, "click", "onSwiperClick", !1, !1, !1, !1);
              },
              f
            );
          },
          i,
          l = (C) => {},
          k = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "32rpx");
                if (C) O(N, "text", "加载中...");
              },
              l
            );
          },
          n = (C, o, p, q, r, s, T, E) => {
            var t = (C) => {};
            E(
              "t-grid-item",
              {},
              (N, C) => {
                if (C || K || Z(q, "name")) O(N, "text", X(o).name);
                if (C || K || Z(q, "thumbnail")) O(N, "image", X(o).thumbnail);
                if (C) O(N, "t-class", "category-item");
                if (C) O(N, "t-class-image", "category-image");
                if (C) O(N, "bindtap", "onCategoryTap");
                if (C || K || q) R.d(N, "category", o);
              },
              t
            );
          },
          m = (C, T, E, B, F) => {
            F(
              D.categoryList,
              "id",
              U ? U.categoryList : undefined,
              [0, "categoryList"],
              n
            );
          },
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                k
              );
            } else {
              E(
                "t-grid",
                {},
                (N, C) => {
                  if (C || K || undefined) O(N, "column", 3);
                  if (C || K || undefined) O(N, "border", false);
                },
                m
              );
            }
          },
          o,
          r = (C) => {},
          q = (C, T, E) => {
            E(
              "t-empty",
              {},
              (N, C) => {
                if (C) O(N, "icon", "service");
                if (C) O(N, "description", "暂无分类");
              },
              r
            );
          },
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty-category");
                },
                q
              );
            }
          },
          h = (C, T, E, B) => {
            i = D.categoryLoading ? 1 : 0;
            B(i, j);
            o = X(D.categoryList).length === 0 && !D.categoryLoading ? 1 : 0;
            B(o, p);
          },
          g = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "category-grid");
              },
              h
            );
          },
          t = (C) => {},
          s = (C, T, E) => {
            E(
              "t-notice-bar",
              {},
              (N, C) => {
                if (C || K || U.showIncompleteNotice)
                  O(N, "visible", D.showIncompleteNotice);
                A["showIncompleteNotice"][0] = (D, E, T) => {
                  O(N, "visible", D.showIncompleteNotice);
                  E(N);
                };
                if (C) O(N, "theme", "warning");
                if (C)
                  O(N, "content", "需要补充认证信息后，才可以发布服务或求助");
                if (C) O(N, "bindtap", "goToEditProfile");
              },
              t
            );
          },
          w = (C) => {},
          v = (C, T, E) => {
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "value", "service");
                if (C) O(N, "label", "热门服务");
              },
              w
            );
          },
          u = (C, T, E) => {
            E(
              "t-tabs",
              {},
              (N, C) => {
                if (C || K || U.activeTab) O(N, "value", D.activeTab);
                if (C) O(N, "theme", "line");
                if (C || K || undefined) O(N, "space-evenly", false);
                if (C || K || undefined) O(N, "sticky", false);
                if (C) O(N, "t-class", "custom-tabs");
                if (C) O(N, "t-class-track", "custom-track");
                if (C) R.v(N, "change", "onTabChange", !1, !1, !1, !1);
              },
              v
            );
          },
          x,
          A0,
          D0 = (C) => {},
          C0 = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "40rpx");
                if (C) O(N, "text", "加载中...");
              },
              D0
            );
          },
          F0 = (C, G0, H0, I0, J0, K0, T, E) => {
            var L0 = (C) => {};
            E(
              "service-card",
              {},
              (N, C) => {
                if (C || K || I0) O(N, "service", G0);
                if (C) R.v(N, "contact", "handleContact", !1, !1, !1, !1);
                if (C) R.v(N, "confirm", "handleConfirm", !1, !1, !1, !1);
              },
              L0
            );
          },
          G0,
          J0,
          M0 = (C) => {},
          L0 = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "32rpx");
                if (C) O(N, "text", "加载更多...");
              },
              M0
            );
          },
          N0 = (C, T) => {
            C ? T("点击加载更多") : T();
          },
          K0 = (C, T, E) => {
            if (J0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-more");
                },
                L0
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "load-more-text");
                },
                N0
              );
            }
          },
          I0 = (C, T, E, B) => {
            J0 = D.serviceLoading ? 1 : 0;
            B(J0, K0);
          },
          H0 = (C, T, E) => {
            if (G0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "load-more");
                  if (C) O(N, "bindtap", "loadMoreServices");
                },
                I0
              );
            }
          },
          O0,
          Q0 = (C, T) => {
            C ? T("没有更多服务了") : T();
          },
          P0 = (C, T, E) => {
            if (O0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "no-more");
                },
                Q0
              );
            }
          },
          R0,
          U0 = (C) => {},
          T0 = (C, T, E) => {
            E(
              "t-empty",
              {},
              (N, C) => {
                if (C) O(N, "icon", "service");
                if (C) O(N, "description", "暂无服务");
              },
              U0
            );
          },
          S0 = (C, T, E) => {
            if (R0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty-state");
                },
                T0
              );
            }
          },
          E0 = (C, T, E, B, F) => {
            F(
              D.serviceList,
              "id",
              U ? U.serviceList : undefined,
              [0, "serviceList"],
              F0
            );
            G0 = D.serviceHasMore ? 1 : 0;
            B(G0, H0);
            O0 = !D.serviceHasMore && X(D.serviceList).length > 0 ? 1 : 0;
            B(O0, P0);
            R0 = X(D.serviceList).length === 0 && !D.serviceLoading ? 1 : 0;
            B(R0, S0);
          },
          B0 = (C, T, E) => {
            if (A0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                C0
              );
            } else {
              E("view", {}, (N, C) => {}, E0);
            }
          },
          z = (C, T, E, B) => {
            A0 = D.serviceLoading && X(D.serviceList).length === 0 ? 1 : 0;
            B(A0, B0);
          },
          y = (C, T, E) => {
            if (x === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                z
              );
            }
          },
          d = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "lb_box");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "nr_box");
              },
              g
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "nr_box");
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fl_box");
              },
              u
            );
            x = D.activeTab === "service" ? 1 : 0;
            B(x, y);
          },
          a = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["components/communityHelp/communityHelp.wxss"] = setCssToHead(
    [
      ".",
      [1],
      "fuwu_list{background-color:#fff;border:",
      [0, 5],
      " solid #f0f0f0;border-radius:",
      [0, 20],
      ";box-shadow:0 ",
      [0, 2],
      " ",
      [0, 10],
      " rgba(0,0,0,.05);display:-webkit-flex;display:flex;height:auto;-webkit-justify-content:space-between;justify-content:space-between;margin:",
      [0, 10],
      " auto;padding:",
      [0, 20],
      ";width:95%}\n.",
      [1],
      "info_left{width:70%}\n.",
      [1],
      "info_left_top,.",
      [1],
      "info_left_top_img{display:-webkit-flex;display:flex}\n.",
      [1],
      "info_left_top_img{-webkit-align-items:center;align-items:center;height:",
      [0, 150],
      ";-webkit-justify-content:center;justify-content:center;margin-right:",
      [0, 20],
      ";width:",
      [0, 150],
      "}\n.",
      [1],
      "info_left_top_img wx-image{border-radius:",
      [0, 10],
      ";height:100%;object-fit:cover;vertical-align:middle;width:100%}\n.",
      [1],
      "info_left_top_info{-webkit-flex:1;flex:1;max-width:calc(100% - ",
      [0, 170],
      ")}\n.",
      [1],
      "info_left_top_info_title{font-size:",
      [0, 32],
      ";font-weight:700;margin-bottom:",
      [0, 16],
      "}\n.",
      [1],
      "info_left_top_info_ms{color:#666;font-size:",
      [0, 24],
      ";margin-bottom:",
      [0, 12],
      "}\n.",
      [1],
      "info_left_top_info_fw,.",
      [1],
      "info_left_top_info_time{color:#999;font-size:",
      [0, 22],
      ";margin-bottom:",
      [0, 10],
      "}\n.",
      [1],
      "info_left_top_info_fw,.",
      [1],
      "info_left_top_info_ms,.",
      [1],
      "info_left_top_info_time,.",
      [1],
      "info_left_top_info_title{width:100%}\n.",
      [1],
      "info_left_top_info_fw wx-text,.",
      [1],
      "info_left_top_info_ms wx-text,.",
      [1],
      "info_left_top_info_time wx-text,.",
      [1],
      "info_left_top_info_title wx-text{display:block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%}\n.",
      [1],
      "info_right{-webkit-align-items:flex-end;align-items:flex-end;-webkit-flex-direction:column;flex-direction:column;width:25%}\n.",
      [1],
      "info_right,.",
      [1],
      "info_right_top{display:-webkit-flex;display:flex}\n.",
      [1],
      "info_right_top{-webkit-align-items:center;align-items:center}\n.",
      [1],
      "info_right_top_img{height:",
      [0, 50],
      ";margin-right:",
      [0, 10],
      ";width:",
      [0, 50],
      "}\n.",
      [1],
      "info_right_top_img wx-image{border-radius:50%;height:100%;width:100%}\n.",
      [1],
      "info_right_top_username{color:#f90;font-size:",
      [0, 26],
      "}\n.",
      [1],
      "info_right_middle{margin:",
      [0, 30],
      " 0}\n.",
      [1],
      "info_right_middle_price{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
      [1],
      "price-label{color:#ff4d4f;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "price-value{color:#f50;font-size:",
      [0, 32],
      ";font-weight:700}\n.",
      [1],
      "info_right_bottom{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:row;flex-direction:row;-webkit-justify-content:flex-end;justify-content:flex-end;margin-top:",
      [0, 20],
      "}\n.",
      [1],
      "margin-16{margin-bottom:0;margin-right:",
      [0, 16],
      "}\n.",
      [1],
      "margin-16:last-child{margin-right:0}\n",
    ],
    "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./components/communityHelp/communityHelp.wxss:1:1592)",
    { path: "./components/communityHelp/communityHelp.wxss" }
  );
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/notice-bar/notice-bar.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-notice-bar{-webkit-align-items:flex-start;align-items:flex-start;display:-webkit-flex;display:flex;font-size:var(--td-font-size-base,",
      [0, 28],
      ");padding:",
      [0, 26],
      " ",
      [0, 32],
      "}\n.",
      [1],
      "t-notice-bar__content-wrap{color:var(--td-notice-bar-font-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));-webkit-flex:1;flex:1;line-height:",
      [0, 44],
      ";overflow-x:hidden}\n.",
      [1],
      "t-notice-bar__content{display:inline-block;white-space:nowrap}\n.",
      [1],
      "t-notice-bar__content-wrapable{white-space:normal}\n.",
      [1],
      "t-notice-bar__content--vertical{display:block;height:",
      [0, 44],
      ";line-height:",
      [0, 44],
      "}\n.",
      [1],
      "t-notice-bar__content--vertical-item{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\n.",
      [1],
      "t-notice-bar__prefix-icon{color:inherit}\n.",
      [1],
      "t-notice-bar__prefix-icon:not(:empty){padding-right:var(--td-spacer,",
      [0, 16],
      ");width:",
      [0, 44],
      "}\n.",
      [1],
      "t-notice-bar__suffix-icon{color:var(--td-notice-bar-suffix-icon-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))))}\n.",
      [1],
      "t-notice-bar__prefix-icon,.",
      [1],
      "t-notice-bar__suffix-icon{font-size:",
      [0, 44],
      "}\n.",
      [1],
      "t-notice-bar__prefix-icon:empty,.",
      [1],
      "t-notice-bar__suffix-icon:empty{display:none}\n.",
      [1],
      "t-notice-bar__operation{color:var(--td-notice-bar-operation-font-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));display:-webkit-inline-flex;display:inline-flex;font-weight:700;vertical-align:top}\n.",
      [1],
      "t-notice-bar__operation:empty{display:none}\n.",
      [1],
      "t-notice-bar__suffix-icon:not(:empty){padding-left:var(--td-spacer,",
      [0, 16],
      ");width:",
      [0, 44],
      "}\n.",
      [1],
      "t-notice-bar--info{background-color:var(--td-notice-bar-info-bg-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));color:var(--td-notice-bar-info-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-notice-bar--success{background-color:var(--td-notice-bar-success-bg-color,var(--td-success-color-1,#e3f9e9));color:var(--td-notice-bar-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
      [1],
      "t-notice-bar--warning{background-color:var(--td-notice-bar-warning-bg-color,var(--td-warning-color-1,#fff1e9));color:var(--td-notice-bar-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
      [1],
      "t-notice-bar--error{background-color:var(--td-notice-bar-error-bg-color,var(--td-error-color-1,#fff0ed));color:var(--td-notice-bar-error-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n",
    ],
    undefined,
    { path: "./miniprogram_npm/tdesign-miniprogram/notice-bar/notice-bar.wxss" }
  );
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/swiper-nav/swiper-nav.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-swiper-nav__dots,.",
      [1],
      "t-swiper-nav__dots-bar{display:-webkit-flex;display:flex;-webkit-flex-direction:row;flex-direction:row}\n.",
      [1],
      "t-swiper-nav__dots-bar-item,.",
      [1],
      "t-swiper-nav__dots-item{background:var(--td-swiper-nav-dot-color,var(--td-font-white-2,hsla(0,0%,100%,.55)));border-radius:50%;height:var(--td-swiper-nav-dot-size,",
      [0, 12],
      ");margin:0 ",
      [0, 10],
      ";transition:all .4s ease-in;width:var(--td-swiper-nav-dot-size,",
      [0, 12],
      ")}\n.",
      [1],
      "t-swiper-nav__dots-bar-item--vertical,.",
      [1],
      "t-swiper-nav__dots-item--vertical{margin:",
      [0, 10],
      " 0}\n.",
      [1],
      "t-swiper-nav__dots-bar-item--active,.",
      [1],
      "t-swiper-nav__dots-item--active{background-color:var(--td-swiper-nav-dot-active-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
      [1],
      "t-swiper-nav__dots-bar-item--vertical.",
      [1],
      "t-swiper-nav__dots-bar-item--active{height:var(--td-swiper-nav-dots-bar-active-width,",
      [0, 40],
      ");width:var(--td-swiper-nav-dot-size,",
      [0, 12],
      ")}\n.",
      [1],
      "t-swiper-nav__dots-bar-item--active{background-color:var(--td-swiper-nav-dot-active-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)));border-radius:calc(var(--td-swiper-nav-dot-size,",
      [0, 12],
      ")/ 2);width:var(--td-swiper-nav-dots-bar-active-width,",
      [0, 40],
      ")}\n.",
      [1],
      "t-swiper-nav--left{left:",
      [0, 24],
      "}\n.",
      [1],
      "t-swiper-nav--left,.",
      [1],
      "t-swiper-nav--right{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.",
      [1],
      "t-swiper-nav--right{right:",
      [0, 24],
      "}\n.",
      [1],
      "t-swiper-nav--top-left{left:",
      [0, 24],
      ";position:absolute;top:",
      [0, 24],
      "}\n.",
      [1],
      "t-swiper-nav--top{left:50%;position:absolute;top:",
      [0, 24],
      ";-webkit-transform:translateX(-50%);transform:translateX(-50%)}\n.",
      [1],
      "t-swiper-nav--top-right{position:absolute;right:",
      [0, 24],
      ";top:",
      [0, 24],
      "}\n.",
      [1],
      "t-swiper-nav--bottom-left{bottom:",
      [0, 24],
      ";left:",
      [0, 24],
      ";position:absolute}\n.",
      [1],
      "t-swiper-nav--bottom{bottom:",
      [0, 24],
      ";left:50%;position:absolute;-webkit-transform:translateX(-50%);transform:translateX(-50%)}\n.",
      [1],
      "t-swiper-nav--bottom-right{bottom:",
      [0, 24],
      ";position:absolute;right:",
      [0, 24],
      "}\n.",
      [1],
      "t-swiper-nav--vertical{-webkit-flex-direction:column;flex-direction:column}\n.",
      [1],
      "t-swiper-nav__fraction{background:var(--td-swiper-nav-fraction-bg-color,var(--td-font-gray-3,rgba(0,0,0,.4)));border-radius:calc(var(--td-swiper-nav-fraction-height,",
      [0, 48],
      ")/ 2);color:var(--td-swiper-nav-fraction-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)));font-size:var(--td-swiper-nav-fraction-font-size,",
      [0, 24],
      ");height:var(--td-swiper-nav-fraction-height,",
      [0, 48],
      ");line-height:var(--td-swiper-nav-fraction-height,",
      [0, 48],
      ");padding:0 ",
      [0, 16],
      "}\n.",
      [1],
      "t-swiper-nav__btn{width:100%}\n.",
      [1],
      "t-swiper-nav__btn,.",
      [1],
      "t-swiper-nav__btn--next,.",
      [1],
      "t-swiper-nav__btn--prev{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.",
      [1],
      "t-swiper-nav__btn--next,.",
      [1],
      "t-swiper-nav__btn--prev{background:var(--td-swiper-nav-btn-bg-color,var(--td-font-gray-3,rgba(0,0,0,.4)));border-radius:50%;height:var(--td-swiper-nav-btn-size,",
      [0, 48],
      ");width:var(--td-swiper-nav-btn-size,",
      [0, 48],
      ")}\n.",
      [1],
      "t-swiper-nav__btn--next::after,.",
      [1],
      "t-swiper-nav__btn--prev::after{border-color:var(--td-swiper-nav-btn-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)));border-style:solid;content:\x22\x22;display:block;height:",
      [0, 12],
      ";left:50%;position:absolute;top:50%;width:",
      [0, 12],
      "}\n.",
      [1],
      "t-swiper-nav__btn--prev{left:",
      [0, 30],
      "}\n.",
      [1],
      "t-swiper-nav__btn--prev::after{border-width:",
      [0, 2],
      " 0 0 ",
      [0, 2],
      ";margin-left:",
      [0, 4],
      ";-webkit-transform:translate(-50%,-50%) rotateZ(-45deg);transform:translate(-50%,-50%) rotateZ(-45deg)}\n.",
      [1],
      "t-swiper-nav__btn--next{right:",
      [0, 30],
      "}\n.",
      [1],
      "t-swiper-nav__btn--next::after{border-width:",
      [0, 2],
      " ",
      [0, 2],
      " 0 0;margin-left:",
      [0, -4],
      ";-webkit-transform:translate(-50%,-50%) rotateZ(45deg);transform:translate(-50%,-50%) rotateZ(45deg)}\n",
    ],
    undefined,
    { path: "./miniprogram_npm/tdesign-miniprogram/swiper-nav/swiper-nav.wxss" }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/swiper/swiper.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-swiper{position:relative}\n.",
        [1],
        "t-swiper-host{border-radius:var(--td-swiper-radius,var(--td-radius-large,",
        [0, 18],
        "));overflow:hidden;-webkit-transform:translateY(0);transform:translateY(0)}\n.",
        [1],
        "t-swiper__item{-webkit-align-items:center;align-items:center;box-sizing:border-box;display:-webkit-flex;display:flex;padding:var(--td-swiper-item-padding,0)}\n.",
        [1],
        "t-swiper__image{transition:all .3s ease;width:100%}\n.",
        [1],
        "t-swiper__image-host{width:100%}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/swiper/swiper.wxss" }
    );
  __wxAppCode__["pages/index/index.wxss"] = setCssToHead(
    [
      "body{background-color:#f5f5f5;box-sizing:border-box;min-height:100vh;padding-bottom:",
      [0, 180],
      "}\n.",
      [1],
      "lb_box{background-color:#fff;margin:0 auto;padding:",
      [0, 10],
      ";width:95%}\n.",
      [1],
      "lb_box .",
      [1],
      "img{height:100%;width:100%}\n.",
      [1],
      "fl_box{--td-grid-item-image-width:",
      [0, 60],
      ";--td-grid-item-image-middle-width:",
      [0, 60],
      ";--td-grid-item-image-small-width:",
      [0, 60],
      ";margin:",
      [0, 10],
      " auto;width:95%}\n.",
      [1],
      "grid-item{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:flex-start;justify-content:flex-start}\n.",
      [1],
      "custom-image{-webkit-flex-shrink:0;flex-shrink:0;height:",
      [0, 60],
      "!important;width:",
      [0, 60],
      "!important}\n.",
      [1],
      "grid-item .",
      [1],
      "t-grid-item__content{height:100%}\n.",
      [1],
      "grid-item .",
      [1],
      "t-grid-item__content,.",
      [1],
      "grid-item .",
      [1],
      "t-grid-item__text{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
      [1],
      "grid-item .",
      [1],
      "t-grid-item__text{font-size:",
      [0, 32],
      ";font-weight:700;line-height:1.2}\n.",
      [1],
      "fenxiang_box{margin:",
      [0, 20],
      " auto;width:95%}\n.",
      [1],
      "fenxiang_box wx-image{height:",
      [0, 180],
      ";width:100%}\n.",
      [1],
      "tuijian_fl_box{margin:",
      [0, 10],
      " auto;width:95%}\n.",
      [1],
      "nr_box{margin:",
      [0, 20],
      " auto;width:95%}\n.",
      [1],
      "nr_box:last-child{margin-bottom:",
      [0, 40],
      "}\n.",
      [1],
      "active-tab,.",
      [1],
      "active-tab .",
      [1],
      "t-grid-item__text{color:#fa5252}\n.",
      [1],
      "active-tab .",
      [1],
      "t-grid-item__description{color:#b0abab;opacity:.8}\n.",
      [1],
      "loading-container{padding:",
      [0, 60],
      " 0}\n.",
      [1],
      "load-more,.",
      [1],
      "loading-container{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "load-more{margin-top:",
      [0, 20],
      ";padding:",
      [0, 40],
      " 0}\n.",
      [1],
      "loading-more{-webkit-align-items:center;align-items:center;color:#999;display:-webkit-flex;display:flex}\n.",
      [1],
      "load-more-text{background-color:#fff;border:",
      [0, 1],
      " solid #195abf;border-radius:",
      [0, 40],
      ";color:#195abf;font-size:",
      [0, 28],
      ";padding:",
      [0, 20],
      " ",
      [0, 40],
      "}\n.",
      [1],
      "no-more{color:#999;font-size:",
      [0, 24],
      ";padding:",
      [0, 40],
      " 0;text-align:center}\n.",
      [1],
      "empty-state{padding:",
      [0, 100],
      " 0}\n.",
      [1],
      "empty-state,.",
      [1],
      "refresh-container{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "refresh-container{color:#666;font-size:",
      [0, 28],
      ";padding:",
      [0, 20],
      " 0}\n.",
      [1],
      "refresh-text{margin-left:",
      [0, 10],
      "}\n.",
      [1],
      "category-title{color:#333;font-size:",
      [0, 32],
      ";font-weight:700;margin-bottom:",
      [0, 20],
      ";padding-left:",
      [0, 10],
      "}\n.",
      [1],
      "category-grid{background-color:#fff;border-radius:",
      [0, 20],
      ";box-shadow:0 ",
      [0, 2],
      " ",
      [0, 10],
      " rgba(0,0,0,.05);padding:",
      [0, 20],
      "}\n.",
      [1],
      "category-item{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center;padding:",
      [0, 20],
      " ",
      [0, 10],
      "}\n.",
      [1],
      "category-image{border-radius:",
      [0, 10],
      ";height:",
      [0, 60],
      "!important;margin-bottom:",
      [0, 10],
      ";width:",
      [0, 60],
      "!important}\n.",
      [1],
      "category-item .",
      [1],
      "t-grid-item__text{color:#333;font-size:",
      [0, 24],
      ";line-height:1.2;text-align:center}\n.",
      [1],
      "empty-category{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;padding:",
      [0, 60],
      " 0}\n.",
      [1],
      "category-item:active{background-color:#f5f5f5;border-radius:",
      [0, 10],
      "}\n.",
      [1],
      "custom-tabs{background-color:#eff2f5!important;-webkit-justify-content:flex-start!important;justify-content:flex-start!important}\n.",
      [1],
      "custom-tabs .",
      [1],
      "t-tabs__item{color:#1e1e1e!important}\n.",
      [1],
      "custom-tabs .",
      [1],
      "t-tabs__item--active{color:#ff4d4f!important}\n.",
      [1],
      "custom-track{background-color:#ff4d4f!important}\n",
    ],
    "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/index/index.wxss:1:914)",
    { path: "./pages/index/index.wxss" }
  );
}
