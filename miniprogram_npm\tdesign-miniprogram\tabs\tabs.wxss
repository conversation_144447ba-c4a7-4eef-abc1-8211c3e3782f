@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-tabs {
  flex-wrap: wrap;
  font-size: var(--td-tab-font-size, 28rpx);
  position: relative;
}
.t-tabs,
.t-tabs__wrapper {
  background: var(
    --td-tab-nav-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
}
.t-tabs__wrapper {
  display: -webkit-flex;
  display: flex;
  overflow: hidden;
}
.t-tabs__wrapper--card {
  --td-tab-border-color: transparent;
  background: var(
    --td-tab-item-tag-bg,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
}
.t-tabs__item {
  align-items: center;
  box-sizing: border-box;
  color: var(
    --td-tab-item-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-flex;
  display: flex;
  flex: none;
  font-weight: 400;
  height: var(--td-tab-item-height, 96rpx);
  justify-content: center;
  overflow: hidden;
  padding: 0 var(--td-spacer-2, 32rpx);
  position: relative;
  white-space: nowrap;
}
.t-tabs__item--active {
  color: var(
    --td-tab-item-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  font-weight: 600;
}
.t-tabs__item--disabled {
  color: var(
    --td-tab-item-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-tabs__item--evenly {
  flex: 1 0 auto;
}
.t-tabs__item-inner {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.t-tabs__item-inner--tag {
  background-color: var(
    --td-tab-item-tag-bg,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  border-radius: calc(var(--td-tab-item-tag-height, 64rpx) / 2);
  line-height: var(--td-tab-item-tag-height, 64rpx);
  padding: 0 var(--td-spacer-2, 32rpx);
  text-align: center;
  width: 100%;
}
.t-tabs__item-inner--active.t-tabs__item-inner--tag {
  background-color: var(
    --td-tab-item-tag-active-bg,
    var(--td-brand-color-light, var(--td-primary-color-1, #f2f3ff))
  );
}
.t-tabs__item--tag:not(.t-tabs__item--evenly) {
  padding: 0 calc(var(--td-spacer, 16rpx) / 2);
}
.t-tabs__item--tag:not(.t-tabs__item--evenly):first-child {
  margin-left: var(--td-spacer, 16rpx);
}
.t-tabs__item--tag:not(.t-tabs__item--evenly):last-child {
  padding-right: var(--td-spacer-1, 24rpx);
}
.t-tabs__item--tag {
  padding: 0 var(--td-spacer, 16rpx);
}
.t-tabs__item--card.t-tabs__item--active {
  background-color: var(
    --td-tab-nav-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  border-radius: var(--td-radius-large, 18rpx) var(--td-radius-large, 18rpx) 0 0;
}
.t-tabs__item--card.t-tabs__item--active:first-child {
  border-top-left-radius: 0;
}
.t-tabs__item--card.t-tabs__item--active:last-child {
  border-top-right-radius: 0;
}
.t-tabs__item--card.t-tabs__item--pre {
  border-bottom-right-radius: var(--td-radius-large, 18rpx);
}
.t-tabs__item-prefix,
.t-tabs__item-suffix {
  background-color: var(
    --td-tab-nav-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  bottom: 0;
  height: 18rpx;
  position: absolute;
  width: 18rpx;
}
.t-tabs__item-prefix::after,
.t-tabs__item-suffix::after {
  background-color: var(
    --td-tab-item-tag-bg,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  content: "";
  display: block;
  height: 100%;
  width: 100%;
}
.t-tabs__item-prefix {
  right: 0;
}
.t-tabs__item-prefix::after {
  border-bottom-right-radius: var(--td-radius-large, 18rpx);
}
.t-tabs__item-suffix {
  left: 0;
}
.t-tabs__item-suffix::after {
  border-bottom-left-radius: var(--td-radius-large, 18rpx);
}
.t-tabs__badge--active {
  --td-badge-content-text-color: var(
    --td-tab-item-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-tabs__badge--disabled {
  --td-badge-content-text-color: var(
    --td-tab-item-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-tabs__icon {
  font-size: var(--td-tab-icon-size, 36rpx);
  margin-right: calc(var(--td-spacer, 16rpx) / 4);
}
.t-tabs__content {
  overflow: hidden;
}
.t-tabs__nav {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  flex-wrap: nowrap;
  position: relative;
  user-select: none;
}
.t-tabs__nav.t-tabs__nav--evenly {
  width: 100%;
}
.t-tabs__track {
  background-color: var(
    --td-tab-track-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-radius: var(--td-tab-track-radius, 8rpx);
  bottom: 1rpx;
  font-weight: 600;
  height: var(--td-tab-track-thickness, 6rpx);
  left: 0;
  opacity: 0;
  position: absolute;
  width: var(--td-tab-track-width, 32rpx);
  z-index: 1;
}
.t-tabs__scroll {
  height: var(--td-tab-item-height, 96rpx);
}
.t-tabs__scroll,
.t-tabs__scroll--split {
  position: relative;
}
.t-tabs__scroll--split::after {
  background-color: var(
    --td-tab-border-color,
    var(--td-component-stroke, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: unset;
  transform: scaleY(0.5);
}
.t-tabs__scroll::-webkit-scrollbar {
  display: none;
}
.t-tabs__content {
  width: 100%;
}
.t-tabs__content-inner {
  display: block;
}
.t-tabs__content--animated .t-tabs__content-inner {
  display: -webkit-flex;
  display: flex;
  height: 100%;
  position: relative;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  width: 100%;
  will-change: left;
}
