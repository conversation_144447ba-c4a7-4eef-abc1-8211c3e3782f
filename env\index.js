var e = require("../@babel/runtime/helpers/classCallCheck"),
  r = require("../@babel/runtime/helpers/createClass"),
  t = require("./dev.js"),
  n = require("./test.js"),
  i = require("./prod.js"),
  u = (function () {
    function u() {
      e(this, u);
    }
    return (
      r(u, null, [
        {
          key: "getConfig",
          value: function () {
            var e = wx.getAccountInfoSync().miniProgram.envVersion;
            switch ((console.log("当前环境：", e), e)) {
              case "develop":
                return t;
              case "trial":
                return n;
              case "release":
                return i;
              default:
                return t;
            }
          },
        },
        {
          key: "resourceAppid",
          get: function () {
            return this.getConfig().resourceAppid;
          },
        },
        {
          key: "resourceEnv",
          get: function () {
            return this.getConfig().resourceEnv;
          },
        },
        {
          key: "serviceName",
          get: function () {
            return this.getConfig().serviceName;
          },
        },
        {
          key: "debug",
          get: function () {
            return this.getConfig().debug;
          },
        },
        {
          key: "appName",
          get: function () {
            return this.getConfig().appName;
          },
        },
        {
          key: "subscriptionTemplates",
          get: function () {
            return this.getConfig().subscriptionTemplates;
          },
        },
      ]),
      u
    );
  })();
module.exports = u;
