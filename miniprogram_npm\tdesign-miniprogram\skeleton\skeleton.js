Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/toConsumableArray"),
  t = require("../../../@babel/runtime/helpers/createClass"),
  r = require("../../../@babel/runtime/helpers/classCallCheck"),
  i = require("../../../@babel/runtime/helpers/inherits"),
  s = require("../../../@babel/runtime/helpers/createSuper"),
  a = require("tslib"),
  o = require("../common/src/index"),
  n = p(require("../common/config")),
  l = p(require("./props")),
  c = require("../common/utils"),
  u = require("../common/validator");
function p(e) {
  return e && e.__esModule ? e : { default: e };
}
var h = n.default.prefix,
  m = "".concat(h, "-skeleton"),
  d = {
    avatar: [{ type: "circle", size: "96rpx" }],
    image: [{ type: "rect", size: "144rpx" }],
    text: [
      [
        { width: "24%", height: "32rpx", marginRight: "32rpx" },
        { width: "76%", height: "32rpx" },
      ],
      1,
    ],
    paragraph: [1, 1, 1, { width: "55%" }],
  },
  f = (function (a) {
    i(n, a);
    var o = s(n);
    function n() {
      var t;
      return (
        r(this, n),
        ((t = o.apply(this, arguments)).externalClasses = [
          "".concat(h, "-class"),
          "".concat(h, "-class-col"),
          "".concat(h, "-class-row"),
        ]),
        (t.properties = l.default),
        (t.data = { prefix: h, classPrefix: m, parsedRowcols: [] }),
        (t.observers = {
          rowCol: function () {
            this.init();
          },
          "loading, delay": function () {
            this.isShowSkeleton();
          },
        }),
        (t.lifetimes = {
          attached: function () {
            this.init(), this.isShowSkeleton();
          },
        }),
        (t.methods = {
          init: function () {
            var t = this,
              r = this.properties,
              i = r.theme,
              s = r.rowCol,
              a = [];
            s.length
              ? a.push.apply(a, e(s))
              : a.push.apply(a, e(d[i || "text"]));
            var o = a.map(function (e) {
              if ((0, u.isNumber)(e))
                return [
                  { class: t.getColItemClass({ type: "text" }), style: {} },
                ];
              if (Array.isArray(e))
                return e.map(function (e) {
                  return Object.assign(Object.assign({}, e), {
                    class: t.getColItemClass(e),
                    style: t.getColItemStyle(e),
                  });
                });
              var r = e;
              return [
                Object.assign(Object.assign({}, r), {
                  class: t.getColItemClass(r),
                  style: t.getColItemStyle(r),
                }),
              ];
            });
            this.setData({ parsedRowcols: o });
          },
          getColItemClass: function (e) {
            return (0, c.classNames)([
              "".concat(m, "__col"),
              "".concat(m, "--type-").concat(e.type || "text"),
              "".concat(m, "--animation-").concat(this.properties.animation),
            ]);
          },
          getColItemStyle: function (e) {
            var t = {};
            return (
              [
                "width",
                "height",
                "marginRight",
                "marginLeft",
                "margin",
                "size",
                "background",
                "backgroundColor",
                "borderRadius",
              ].forEach(function (r) {
                if (r in e) {
                  var i,
                    s = (0, u.isNumber)(e[r]) ? "".concat(e[r], "px") : e[r];
                  "size" === r
                    ? ((i = [s, s]), (t.width = i[0]), (t.height = i[1]))
                    : (t[r] = s);
                }
              }),
              t
            );
          },
          isShowSkeleton: function () {
            var e = this,
              t = this.properties,
              r = t.loading,
              i = t.delay;
            r && 0 !== i
              ? setTimeout(function () {
                  e.setData({ isShow: r });
                }, i)
              : this.setData({ isShow: r });
          },
        }),
        t
      );
    }
    return t(n);
  })(o.SuperComponent),
  g = (f = (0, a.__decorate)([(0, o.wxComponent)()], f));
exports.default = g;
