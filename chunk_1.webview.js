__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "components/navigation-bar/navigation-bar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            innerPaddingRight: new Array(1),
            color: new Array(1),
            safeAreaTop: new Array(1),
            background: new Array(1),
            extClass: new Array(1),
            ios: new Array(1),
            leftWidth: new Array(1),
            displayStyle: new Array(1),
          },
          K = U === true,
          e,
          g,
          k = (C) => {},
          j = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C)
                  L(
                    N,
                    "weui-navigation-bar__button weui-navigation-bar__btn_goback"
                  );
              },
              k
            );
          },
          i = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-navigation-bar__btn_goback_wrapper");
                if (C) O(N, "bindtap", "back");
                if (C) O(N, "hover-class", "weui-active");
                if (C) O(N, "hover-stay-time", "100");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "返回");
              },
              j
            );
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C)
                    L(
                      N,
                      "weui-navigation-bar__buttons weui-navigation-bar__buttons_goback"
                    );
                },
                i
              );
            }
          },
          l,
          p = (C) => {},
          o = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C)
                  L(
                    N,
                    "weui-navigation-bar__button weui-navigation-bar__btn_home"
                  );
              },
              p
            );
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-navigation-bar__btn_home_wrapper");
                if (C) O(N, "bindtap", "home");
                if (C) O(N, "hover-class", "weui-active");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "首页");
              },
              o
            );
          },
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C)
                    L(
                      N,
                      "weui-navigation-bar__buttons weui-navigation-bar__buttons_home"
                    );
                },
                n
              );
            }
          },
          f = (C, T, E, B, F, S) => {
            if (e === 1) {
              g = D.back ? 1 : 0;
              B(g, h);
              l = D.homeButton ? 1 : 0;
              B(l, m);
            } else {
              S("left");
            }
          },
          d = (C, T, E, B) => {
            e = D.back || D.homeButton ? 1 : 0;
            B(e, f);
          },
          r,
          u = (C) => {},
          t = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-loading");
                if (C) O(N, "aria-role", "img");
                if (C) O(N, "aria-label", "加载中");
              },
              u
            );
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "weui-navigation-bar__loading");
                  if (C) O(N, "aria-role", "alert");
                },
                t
              );
            }
          },
          v,
          x = (C, T) => {
            C || K || U.title ? T(Y(D.title)) : T();
          },
          w = (C, T, E, B, F, S) => {
            if (v === 1) {
              E("text", {}, (N, C) => {}, x);
            } else {
              S("center");
            }
          },
          q = (C, T, E, B) => {
            r = D.loading ? 1 : 0;
            B(r, s);
            v = D.title ? 1 : 0;
            B(v, w);
          },
          y = (C, T, E, B, F, S) => {
            S("right");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-navigation-bar__left");
                if (C || K || !!U.leftWidth || undefined)
                  R.y(N, Y(D.leftWidth) + ";");
                A["leftWidth"][0] = (D, E, T) => {
                  R.y(N, Y(D.leftWidth) + ";");
                };
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-navigation-bar__center");
              },
              q
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "weui-navigation-bar__right");
              },
              y
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.ios;
                if (
                  C ||
                  K ||
                  !!(U.ios || ($A ? undefined : undefined)) ||
                  undefined
                )
                  L(
                    N,
                    "weui-navigation-bar__inner " + Y($A ? "ios" : "android")
                  );
                A["ios"][0] = (D, E, T) => {
                  var $B = D.ios;
                  L(
                    N,
                    "weui-navigation-bar__inner " + Y($B ? "ios" : "android")
                  );
                };
                if (
                  C ||
                  K ||
                  !!(
                    U.color ||
                    U.background ||
                    U.displayStyle ||
                    U.innerPaddingRight ||
                    U.safeAreaTop
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    "color:" +
                      Y(D.color) +
                      ";background:" +
                      Y(D.background) +
                      ";" +
                      Y(D.displayStyle) +
                      ";;;;" +
                      Y(D.innerPaddingRight) +
                      ";;;" +
                      Y(D.safeAreaTop) +
                      ";;"
                  );
                A["color"][0] =
                  A["background"][0] =
                  A["displayStyle"][0] =
                  A["innerPaddingRight"][0] =
                  A["safeAreaTop"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        "color:" +
                          Y(D.color) +
                          ";background:" +
                          Y(D.background) +
                          ";" +
                          Y(D.displayStyle) +
                          ";;;;" +
                          Y(D.innerPaddingRight) +
                          ";;;" +
                          Y(D.safeAreaTop) +
                          ";;"
                      );
                    };
              },
              c
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.extClass || undefined)
                  L(N, "weui-navigation-bar " + Y(D.extClass));
                A["extClass"][0] = (D, E, T) => {
                  L(N, "weui-navigation-bar " + Y(D.extClass));
                };
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["components/navigation-bar/navigation-bar.wxss"] = setCssToHead(
    [
      ".",
      [1],
      "weui-navigation-bar{--weui-FG-0:rgba(0,0,0,.9);--height:44px;--left:16px}\n.",
      [1],
      "weui-navigation-bar .",
      [1],
      "android{--height:48px}\n.",
      [1],
      "weui-navigation-bar{color:var(--weui-FG-0);-webkit-flex:none;flex:none;overflow:hidden}\n.",
      [1],
      "weui-navigation-bar__inner{height:calc(var(--height) + env(safe-area-inset-top));-webkit-justify-content:center;justify-content:center;left:0;padding-top:env(safe-area-inset-top);top:0;width:100%}\n.",
      [1],
      "weui-navigation-bar__inner,.",
      [1],
      "weui-navigation-bar__left{-webkit-align-items:center;align-items:center;box-sizing:border-box;display:-webkit-flex;display:flex;-webkit-flex-direction:row;flex-direction:row;position:relative}\n.",
      [1],
      "weui-navigation-bar__left{height:100%;padding-left:var(--left)}\n.",
      [1],
      "weui-navigation-bar__btn_goback_wrapper{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;margin:-11px -18px -11px -16px;padding:11px 18px 11px 16px}\n.",
      [1],
      "weui-navigation-bar__btn_goback_wrapper.",
      [1],
      "weui-active{opacity:.5}\n.",
      [1],
      "weui-navigation-bar__btn_goback{background-color:var(--weui-FG-0);font-size:12px;height:24px;-webkit-mask:url(\x22data:image/svg+xml;charset\x3dutf8,%3Csvg xmlns\x3d\x27http://www.w3.org/2000/svg\x27 width\x3d\x2712\x27 height\x3d\x2724\x27 viewBox\x3d\x270 0 12 24\x27%3E  %3Cpath fill-opacity\x3d\x27.9\x27 fill-rule\x3d\x27evenodd\x27 d\x3d\x27M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z\x27/%3E%3C/svg%3E\x22) no-repeat 50% 50%;mask:url(\x22data:image/svg+xml;charset\x3dutf8,%3Csvg xmlns\x3d\x27http://www.w3.org/2000/svg\x27 width\x3d\x2712\x27 height\x3d\x2724\x27 viewBox\x3d\x270 0 12 24\x27%3E  %3Cpath fill-opacity\x3d\x27.9\x27 fill-rule\x3d\x27evenodd\x27 d\x3d\x27M10 19.438L8.955 20.5l-7.666-7.79a1.02 1.02 0 0 1 0-1.42L8.955 3.5 10 4.563 2.682 12 10 19.438z\x27/%3E%3C/svg%3E\x22) no-repeat 50% 50%;-webkit-mask-size:cover;mask-size:cover;width:12px}\n.",
      [1],
      "weui-navigation-bar__center{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-flex-direction:row;flex-direction:row;font-size:17px;font-weight:700;height:100%;-webkit-justify-content:center;justify-content:center;position:relative;text-align:center}\n.",
      [1],
      "weui-navigation-bar__loading{-webkit-align-items:center;align-items:center;margin-right:4px}\n.",
      [1],
      "weui-loading{-webkit-animation:loading 1s linear infinite;animation:loading 1s linear infinite;background:transparent url(\x22data:image/svg+xml;charset\x3dutf-8,%3Csvg width\x3d\x2780\x27 height\x3d\x2780\x27 xmlns\x3d\x27http://www.w3.org/2000/svg\x27%3E%3Cdefs%3E%3ClinearGradient x1\x3d\x2794.087%25\x27 y1\x3d\x270%25\x27 x2\x3d\x2794.087%25\x27 y2\x3d\x2790.559%25\x27 id\x3d\x27a\x27%3E%3Cstop stop-color\x3d\x27%23606060\x27 stop-opacity\x3d\x270\x27 offset\x3d\x270%25\x27/%3E%3Cstop stop-color\x3d\x27%23606060\x27 stop-opacity\x3d\x27.3\x27 offset\x3d\x27100%25\x27/%3E%3C/linearGradient%3E%3ClinearGradient x1\x3d\x27100%25\x27 y1\x3d\x278.674%25\x27 x2\x3d\x27100%25\x27 y2\x3d\x2790.629%25\x27 id\x3d\x27b\x27%3E%3Cstop stop-color\x3d\x27%23606060\x27 offset\x3d\x270%25\x27/%3E%3Cstop stop-color\x3d\x27%23606060\x27 stop-opacity\x3d\x27.3\x27 offset\x3d\x27100%25\x27/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg fill\x3d\x27none\x27 fill-rule\x3d\x27evenodd\x27 opacity\x3d\x27.9\x27%3E%3Cpath d\x3d\x27M40 0c22.091 0 40 17.909 40 40S62.091 80 40 80v-7c18.225 0 33-14.775 33-33S58.225 7 40 7V0Z\x27 fill\x3d\x27url(%23a)\x27/%3E%3Cpath d\x3d\x27M40 0v7C21.775 7 7 21.775 7 40s14.775 33 33 33v7C17.909 80 0 62.091 0 40S17.909 0 40 0Z\x27 fill\x3d\x27url(%23b)\x27/%3E%3Ccircle fill\x3d\x27%23606060\x27 cx\x3d\x2740.5\x27 cy\x3d\x273.5\x27 r\x3d\x273.5\x27/%3E%3C/g%3E%3C/svg%3E\x22) no-repeat;background-size:100%;display:block;font-size:16px;height:16px;margin-left:0;width:16px}\n@-webkit-keyframes loading{from{-webkit-transform:rotate(0);transform:rotate(0)}\nto{-webkit-transform:rotate(1turn);transform:rotate(1turn)}\n}@keyframes loading{from{-webkit-transform:rotate(0);transform:rotate(0)}\nto{-webkit-transform:rotate(1turn);transform:rotate(1turn)}\n}",
    ],
    undefined,
    { path: "./components/navigation-bar/navigation-bar.wxss" }
  );
}
