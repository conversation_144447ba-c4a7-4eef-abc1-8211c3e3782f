.container {
  background-color: #fff;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 120rpx 30rpx 30rpx;
}
.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx auto;
  padding: 40rpx 30rpx;
  width: 95%;
}
.form-title {
  color: #333;
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 40rpx;
  text-align: center;
}
.form-item {
  margin-bottom: 30rpx;
  position: relative;
}
.form-label {
  align-items: center;
  color: #333;
  display: -webkit-flex;
  display: flex;
  font-size: 28rpx;
  margin-bottom: 15rpx;
}
.form-label.required::before {
  color: #ff4d4f;
  content: "*";
  font-size: 28rpx;
  margin-right: 8rpx;
}
.input-wrapper {
  position: relative;
}
.form-input {
  background-color: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-sizing: border-box;
  color: #333;
  font-size: 26rpx;
  height: 80rpx;
  padding: 0 20rpx;
  width: 100%;
}
.form-input:focus {
  border-color: #195abf;
}
.form-input::-webkit-input-placeholder {
  color: #999;
}
.form-input::placeholder {
  color: #999;
}
.picker-input {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  height: 80rpx;
  line-height: 80rpx;
}
.textarea-wrapper {
  position: relative;
}
.form-textarea {
  background-color: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-sizing: border-box;
  color: #333;
  font-size: 26rpx;
  min-height: 160rpx;
  padding: 20rpx;
  resize: none;
  width: 100%;
}
.form-textarea:focus {
  border-color: #195abf;
}
.form-textarea::-webkit-input-placeholder {
  color: #999;
}
.form-textarea::placeholder {
  color: #999;
}
.char-count-inner {
  background-color: #fff;
  color: #999;
  font-size: 22rpx;
  padding: 0 5rpx;
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}
.textarea-count {
  bottom: 20rpx;
  top: auto;
  transform: none;
}
.contact-options {
  margin-top: 10rpx;
}
.radio-item {
  align-items: center;
  color: #333;
  display: -webkit-flex;
  display: flex;
  font-size: 26rpx;
  margin-bottom: 20rpx;
}
.radio-item radio {
  margin-right: 15rpx;
  transform: scale(0.8);
}
.button-group {
  gap: 20rpx;
  justify-content: space-between;
  margin-top: 60rpx;
}
.btn,
.button-group {
  display: -webkit-flex;
  display: flex;
}
.btn {
  align-items: center;
  border: none;
  border-radius: 20rpx;
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  justify-content: center;
}
.btn-cancel {
  background-color: #fff;
  border: 3rpx solid #1e1e1e;
  color: #666;
}
.btn-save {
  background-color: #a5d8ff;
  border: 3rpx solid #1971c2;
  color: #1971c2;
}
.btn-publish {
  background-color: #ffec99;
  border: 3rpx solid #f08c00;
  color: #f08c00;
}
.btn:active {
  opacity: 0.8;
}
.thumbnail-section {
  margin-top: 15rpx;
}
.thumbnail-preview {
  align-items: center;
  background-color: #f8f9fa;
  border: 2rpx dashed #e0e0e0;
  border-radius: 10rpx;
  display: -webkit-flex;
  display: flex;
  height: 200rpx;
  justify-content: center;
  overflow: hidden;
  position: relative;
  width: 200rpx;
}
.selected-thumbnail {
  border-radius: 8rpx;
  height: 100%;
  width: 100%;
}
.thumbnail-placeholder {
  align-items: center;
  color: #999;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.placeholder-text {
  color: #999;
  font-size: 24rpx;
}
.thumbnail-modal {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  position: fixed;
  z-index: 9999;
}
.modal-mask,
.thumbnail-modal {
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
}
.modal-mask {
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
}
.modal-content {
  background-color: #fff;
  border-radius: 20rpx;
  max-width: 600rpx;
  padding: 40rpx;
  position: relative;
  width: 90%;
}
.modal-header {
  margin-bottom: 30rpx;
  text-align: center;
}
.modal-title {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}
.thumbnail-grid {
  flex-wrap: wrap;
  gap: 20rpx;
  justify-content: space-around;
  margin-bottom: 30rpx;
}
.thumbnail-grid,
.thumbnail-item {
  display: -webkit-flex;
  display: flex;
}
.thumbnail-item {
  align-items: center;
  border-radius: 10rpx;
  flex-direction: column;
  padding: 10rpx;
  position: relative;
  transition: all 0.3s ease;
  width: 150rpx;
}
.thumbnail-item:active {
  background-color: #f0f0f0;
  transform: scale(0.95);
}
.thumbnail-img {
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  height: 120rpx;
  width: 120rpx;
}
.thumbnail-name {
  color: #666;
  font-size: 24rpx;
  margin-top: 10rpx;
  text-align: center;
}
.selected-mark {
  align-items: center;
  background-color: #195abf;
  border-radius: 50%;
  color: #fff;
  font-size: 24rpx;
  font-weight: 700;
  height: 40rpx;
  position: absolute;
  right: 5rpx;
  top: 5rpx;
  width: 40rpx;
}
.modal-footer,
.selected-mark {
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.modal-footer {
  margin: 0 auto;
  width: 50%;
}
