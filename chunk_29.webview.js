__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/guide/guide": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (G["miniprogram_npm/tdesign-miniprogram/guide/content"] || {})._,
            H
          );
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b,
          f = (C) => {},
          h,
          i = (C, T, E, B, F, S, J) => {
            var $A = I(h);
            if (h && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    prefix: D.prefix,
                    classPrefix: D.classPrefix,
                    title: D.title,
                    body: D.body,
                    steps: D.steps,
                    current: D.current,
                    modeType: D.modeType,
                    hideSkip: D.hideSkip,
                    hideBack: D.hideBack,
                  },
                  X(D.buttonProps),
                  {}
                ),
                K ||
                  (U
                    ? U.buttonProps === true ||
                      Object.assign(
                        {
                          prefix: U.prefix,
                          classPrefix: U.classPrefix,
                          title: U.title,
                          body: U.body,
                          steps: U.steps,
                          current: U.current,
                          modeType: U.modeType,
                          hideSkip: U.hideSkip,
                          hideBack: U.hideBack,
                        },
                        X(U.buttonProps),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          g = (C, T, E, B, F, S, J) => {
            h = "content";
            B(h, i);
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.nonOverlay;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    U.classPrefix ||
                    U.nonOverlay ||
                    ($A ? !!U.classPrefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.prefix) +
                      "-class-reference " +
                      Y(D.classPrefix) +
                      "__reference " +
                      Y($A ? D.classPrefix + "__reference--nonoverlay" : "")
                  );
                if (C || K || U.referenceStyle) R.y(N, D.referenceStyle);
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.title || D.body;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    U.classPrefix ||
                    U.title ||
                    U.body ||
                    ($A
                      ? !!(U.classPrefix || U.modeType) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.prefix) +
                      "-class-popover " +
                      Y(D.classPrefix) +
                      "__container " +
                      Y($A ? D.classPrefix + "__container--" + D.modeType : "")
                  );
                if (C || K || U.popoverStyle) R.y(N, D.popoverStyle);
              },
              g
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
              },
              e
            );
          },
          m,
          n = (C, T, E, B, F, S, J) => {
            var $A = I(m);
            if (m && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    prefix: D.prefix,
                    classPrefix: D.classPrefix,
                    title: D.title,
                    body: D.body,
                    steps: D.steps,
                    current: D.current,
                    modeType: D.modeType,
                    hideSkip: D.hideSkip,
                  },
                  X(D.buttonProps),
                  {}
                ),
                K ||
                  (U
                    ? U.buttonProps === true ||
                      Object.assign(
                        {
                          prefix: U.prefix,
                          classPrefix: U.classPrefix,
                          title: U.title,
                          body: U.body,
                          steps: U.steps,
                          current: U.current,
                          modeType: U.modeType,
                          hideSkip: U.hideSkip,
                        },
                        X(U.buttonProps),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          l = (C, T, E, B, F, S, J) => {
            m = "content";
            B(m, n);
          },
          k = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.title || D.body;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    U.classPrefix ||
                    U.title ||
                    U.body ||
                    ($A
                      ? !!(U.classPrefix || U.modeType) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.prefix) +
                      "-class-popover " +
                      Y(D.classPrefix) +
                      "__container " +
                      Y($A ? D.classPrefix + "__container--" + D.modeType : "")
                  );
              },
              l
            );
          },
          j = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
              },
              k
            );
          },
          c = (C, T, E) => {
            if (b === 1) {
              E(
                "t-overlay",
                {},
                (N, C) => {
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C || K || U.usingCustomNavbar)
                    O(N, "using-custom-navbar", D.usingCustomNavbar);
                  if (C) O(N, "background-color", "transparent");
                  if (C || K || U.zIndex) O(N, "z-index", D.zIndex);
                },
                d
              );
            } else if (b === 2) {
              E(
                "t-popup",
                {},
                (N, C) => {
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C || K || !!U.nonOverlay || undefined)
                    O(N, "show-overlay", !D.nonOverlay);
                  if (C || K || U.usingCustomNavbar)
                    O(N, "using-custom-navbar", D.usingCustomNavbar);
                  if (C || K || U.zIndex) O(N, "z-index", D.zIndex);
                  if (C) O(N, "placement", "center");
                },
                j
              );
            }
          },
          a = (C, T, E, B) => {
            b = D.modeType === "popover" ? 1 : D.modeType === "dialog" ? 2 : 0;
            B(b, c);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/guide/guide.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-guide__reference{border-radius:var(--td-guide-reference-border-radius,var(--td-radius-default,",
        [0, 12],
        "));box-shadow:0 0 0 0 var(--td-guide-reference-mask-color,var(--td-font-gray-2,rgba(0,0,0,.6))),var(--td-guide-reference-mask-color,var(--td-font-gray-2,rgba(0,0,0,.6))) 0 0 0 5000px;position:absolute;transition:all var(--td-anim-duration-base,.2s) var(--td-anim-time-fn-ease-out,cubic-bezier(0,0,.15,1))}\n.",
        [1],
        "t-guide__reference--nonoverlay{border:var(--td-guide-reference-border,",
        [0, 4],
        " solid var(--td-brand-color,var(--td-primary-color-7,#0052d9)));box-shadow:none}\n.",
        [1],
        "t-guide__container{display:inline-block}\n.",
        [1],
        "t-guide__container--popover{border:var(--td-guide-popover-border,",
        [0, 2],
        " solid var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));border-radius:var(--td-guide-popover-border-radius,var(--td-radius-large,",
        [0, 18],
        "));box-shadow:var(--td-guide-popover-shadow,var(--td-shadow-3,0 6px 30px 5px rgba(0,0,0,.05),0 16px 24px 2px rgba(0,0,0,.04),0 8px 10px -5px rgba(0,0,0,.08)));max-width:var(--td-guide-popover-max-width,",
        [0, 540],
        ");min-width:var(--td-guide-popover-min-width,",
        [0, 480],
        ");padding:var(--td-guide-popover-padding,var(--td-spacer-2,",
        [0, 32],
        "))}\n.",
        [1],
        "t-guide__container--dialog,.",
        [1],
        "t-guide__container--popover{background-color:var(--td-guide-popover-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-guide__container--dialog{border-radius:var(--td-guide-dialog-border-radius,var(--td-radius-extra-large,",
        [0, 24],
        "));padding:var(--td-guide-dialog-padding,var(--td-spacer-3,",
        [0, 48],
        ") 0);width:var(--td-guide-dialog-width,",
        [0, 622],
        ")}\n.",
        [1],
        "t-guide__title--popover{font-size:var(--td-guide-popover-title-font-size,var(--td-font-size-m,",
        [0, 32],
        "));line-height:var(--td-guide-popover-title-line-height,",
        [0, 48],
        ");text-align:var(--td-guide-popover-title-text-align,left)}\n.",
        [1],
        "t-guide__title--dialog,.",
        [1],
        "t-guide__title--popover{color:var(--td-guide-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-weight:var(--td-guide-title-font-weight,600)}\n.",
        [1],
        "t-guide__title--dialog{font-size:var(--td-guide-dialog-title-font-size,",
        [0, 36],
        ");line-height:var(--td-guide-dialog-title-line-height,",
        [0, 52],
        ");text-align:var(--td-guide-dialog-title-text-align,center)}\n.",
        [1],
        "t-guide__body--popover{font-size:var(--td-guide-popover-body-font-size,var(--td-font-size-base,",
        [0, 28],
        "));line-height:var(--td-guide-popover-body-line-height,",
        [0, 44],
        ");margin-top:var(--td-guide-popover-body-margin-top,",
        [0, 8],
        ");text-align:var(--td-guide-popover-body-text-align,left)}\n.",
        [1],
        "t-guide__body--dialog,.",
        [1],
        "t-guide__body--popover{color:var(--td-guide-body-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))));font-weight:var(--td-guide-body-font-weight,400)}\n.",
        [1],
        "t-guide__body--dialog{font-size:var(--td-guide-dialog-body-font-size,var(--td-font-size-m,",
        [0, 32],
        "));line-height:var(--td-guide-dialog-body-line-height,",
        [0, 48],
        ");margin-top:var(--td-guide-dialog-body-margin-top,",
        [0, 16],
        ");text-align:var(--td-guide-dialog-body-text-align,center)}\n.",
        [1],
        "t-guide__footer{margin-top:var(--td-guide-footer-margin-top,var(--td-spacer-3,",
        [0, 48],
        "));text-align:var(--td-guide-footer-text-align,right)}\n.",
        [1],
        "t-guide__footer .",
        [1],
        "t-guide__button+.",
        [1],
        "t-guide__button{margin-left:var(--td-guide-footer-button-space,var(--td-spacer-1,",
        [0, 24],
        "))}\n.",
        [1],
        "t-guide__footer--dialog{display:-webkit-flex;display:flex;padding:var(--td-guide-dialog-footer-button-padding,0 var(--td-spacer-3,",
        [0, 48],
        "))}\n.",
        [1],
        "t-guide__footer--dialog .",
        [1],
        "t-guide__button:last-child{-webkit-flex-grow:1;flex-grow:1}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/guide/guide.wxss" }
    );
}
