Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.gradientColors2string =
    exports.getColorWithoutAlpha =
    exports.getColorObject =
    exports.genId =
    exports.genGradientPoint =
    exports.default =
    exports.Color =
      void 0);
var t,
  e = require("../../../../../@babel/runtime/helpers/slicedToArray"),
  r = require("../../../../../@babel/runtime/helpers/classCallCheck"),
  n = require("../../../../../@babel/runtime/helpers/createClass"),
  a =
    (t = require("tinycolor2/esm/tinycolor")) && t.__esModule
      ? t
      : { default: t },
  i = require("./cmyk"),
  s = require("./gradient");
var o = Math.round,
  u = function (t) {
    return (0, a.default)(t).toRgb();
  },
  c = function (t) {
    var e = t.points,
      r = t.degree;
    return "linear-gradient(".concat(r, "deg,").concat(
      e
        .sort(function (t, e) {
          return t.left - e.left;
        })
        .map(function (t) {
          return ""
            .concat(t.color, " ")
            .concat(Math.round(100 * t.left) / 100, "%");
        })
        .join(","),
      ")"
    );
  };
exports.gradientColors2string = c;
exports.getColorWithoutAlpha = function (t) {
  return (0, a.default)(t).setAlpha(1).toHexString();
};
var l = function () {
  return (1 + 4294967295 * Math.random()).toString(16);
};
exports.genId = l;
var h = function (t, e) {
  return { id: l(), left: t, color: e };
};
exports.genGradientPoint = h;
var d = (function () {
  function t(e) {
    r(this, t),
      (this.states = { s: 100, v: 100, h: 100, a: 1 }),
      (this.gradientStates = {
        colors: [],
        degree: 0,
        selectedId: null,
        css: "",
      }),
      this.update(e);
  }
  return (
    n(
      t,
      [
        {
          key: "update",
          value: function (t) {
            var e,
              r,
              n = (0, s.parseGradientString)(t);
            if (this.isGradient && !n) {
              var i = (0, a.default)(t).toHsv();
              return (this.states = i), void this.updateCurrentGradientColor();
            }
            (this.originColor = t), (this.isGradient = !1);
            var o = t;
            if (n) {
              this.isGradient = !0;
              var u = n,
                c = u.points.map(function (t) {
                  return h(t.left, t.color);
                });
              (this.gradientStates = {
                colors: c,
                degree: u.degree,
                selectedId:
                  (null === (e = c[0]) || void 0 === e ? void 0 : e.id) || null,
              }),
                (this.gradientStates.css = this.linearGradient),
                (o =
                  null === (r = this.gradientSelectedPoint) || void 0 === r
                    ? void 0
                    : r.color);
            }
            this.updateStates(o);
          },
        },
        {
          key: "saturation",
          get: function () {
            return this.states.s;
          },
          set: function (t) {
            (this.states.s = Math.max(0, Math.min(100, t))),
              this.updateCurrentGradientColor();
          },
        },
        {
          key: "value",
          get: function () {
            return this.states.v;
          },
          set: function (t) {
            (this.states.v = Math.max(0, Math.min(100, t))),
              this.updateCurrentGradientColor();
          },
        },
        {
          key: "hue",
          get: function () {
            return this.states.h;
          },
          set: function (t) {
            (this.states.h = Math.max(0, Math.min(360, t))),
              this.updateCurrentGradientColor();
          },
        },
        {
          key: "alpha",
          get: function () {
            return this.states.a;
          },
          set: function (t) {
            (this.states.a = Math.max(
              0,
              Math.min(1, Math.round(100 * t) / 100)
            )),
              this.updateCurrentGradientColor();
          },
        },
        {
          key: "rgb",
          get: function () {
            var t = u(this.states),
              e = t.r,
              r = t.g,
              n = t.b;
            return "rgb("
              .concat(o(e), ", ")
              .concat(o(r), ", ")
              .concat(o(n), ")");
          },
        },
        {
          key: "rgba",
          get: function () {
            var t = u(this.states),
              e = t.r,
              r = t.g,
              n = t.b,
              a = t.a;
            return "rgba("
              .concat(o(e), ", ")
              .concat(o(r), ", ")
              .concat(o(n), ", ")
              .concat(a, ")");
          },
        },
        {
          key: "hsv",
          get: function () {
            var t = this.getHsva(),
              e = t.h,
              r = t.s,
              n = t.v;
            return "hsv(".concat(e, ", ").concat(r, "%, ").concat(n, "%)");
          },
        },
        {
          key: "hsva",
          get: function () {
            var t = this.getHsva(),
              e = t.h,
              r = t.s,
              n = t.v,
              a = t.a;
            return "hsva("
              .concat(e, ", ")
              .concat(r, "%, ")
              .concat(n, "%, ")
              .concat(a, ")");
          },
        },
        {
          key: "hsl",
          get: function () {
            var t = this.getHsla(),
              e = t.h,
              r = t.s,
              n = t.l;
            return "hsl(".concat(e, ", ").concat(r, "%, ").concat(n, "%)");
          },
        },
        {
          key: "hsla",
          get: function () {
            var t = this.getHsla(),
              e = t.h,
              r = t.s,
              n = t.l,
              a = t.a;
            return "hsla("
              .concat(e, ", ")
              .concat(r, "%, ")
              .concat(n, "%, ")
              .concat(a, ")");
          },
        },
        {
          key: "hex",
          get: function () {
            return (0, a.default)(this.states).toHexString();
          },
        },
        {
          key: "hex8",
          get: function () {
            return (0, a.default)(this.states).toHex8String();
          },
        },
        {
          key: "cmyk",
          get: function () {
            var t = this.getCmyk(),
              e = t.c,
              r = t.m,
              n = t.y,
              a = t.k;
            return "cmyk("
              .concat(e, ", ")
              .concat(r, ", ")
              .concat(n, ", ")
              .concat(a, ")");
          },
        },
        {
          key: "css",
          get: function () {
            return this.isGradient ? this.linearGradient : this.rgba;
          },
        },
        {
          key: "linearGradient",
          get: function () {
            var t = this.gradientColors,
              e = this.gradientDegree;
            return c({ points: t, degree: e });
          },
        },
        {
          key: "gradientColors",
          get: function () {
            return this.gradientStates.colors;
          },
          set: function (t) {
            (this.gradientStates.colors = t),
              (this.gradientStates.css = this.linearGradient);
          },
        },
        {
          key: "gradientSelectedId",
          get: function () {
            return this.gradientStates.selectedId;
          },
          set: function (t) {
            var e;
            t !== this.gradientSelectedId &&
              ((this.gradientStates.selectedId = t),
              this.updateStates(
                null === (e = this.gradientSelectedPoint) || void 0 === e
                  ? void 0
                  : e.color
              ));
          },
        },
        {
          key: "gradientDegree",
          get: function () {
            return this.gradientStates.degree;
          },
          set: function (t) {
            (this.gradientStates.degree = Math.max(0, Math.min(360, t))),
              (this.gradientStates.css = this.linearGradient);
          },
        },
        {
          key: "gradientSelectedPoint",
          get: function () {
            var t = this.gradientColors,
              e = this.gradientSelectedId;
            return t.find(function (t) {
              return t.id === e;
            });
          },
        },
        {
          key: "getFormatsColorMap",
          value: function () {
            return {
              HEX: this.hex,
              CMYK: this.cmyk,
              RGB: this.rgb,
              RGBA: this.rgba,
              HSL: this.hsl,
              HSLA: this.hsla,
              HSV: this.hsv,
              HSVA: this.hsva,
              CSS: this.css,
              HEX8: this.hex8,
            };
          },
        },
        {
          key: "updateCurrentGradientColor",
          value: function () {
            var t = this.isGradient,
              e = this.gradientColors,
              r = this.gradientSelectedId,
              n = e.length,
              a = this.gradientSelectedPoint;
            if (!t || 0 === n || !a) return !1;
            var i = e.findIndex(function (t) {
                return t.id === r;
              }),
              s = Object.assign(Object.assign({}, a), { color: this.rgba });
            return e.splice(i, 1, s), (this.gradientColors = e.slice()), this;
          },
        },
        {
          key: "updateStates",
          value: function (t) {
            var e = (0, a.default)((0, i.cmykInputToColor)(t)).toHsv();
            this.states = e;
          },
        },
        {
          key: "getRgba",
          value: function () {
            var t = u(this.states),
              e = t.r,
              r = t.g,
              n = t.b,
              a = t.a;
            return { r: o(e), g: o(r), b: o(n), a: a };
          },
        },
        {
          key: "getCmyk",
          value: function () {
            var t = this.getRgba(),
              r = t.r,
              n = t.g,
              a = t.b,
              s = (0, i.rgb2cmyk)(r, n, a),
              u = e(s, 4),
              c = u[0],
              l = u[1],
              h = u[2],
              d = u[3];
            return {
              c: o(100 * c),
              m: o(100 * l),
              y: o(100 * h),
              k: o(100 * d),
            };
          },
        },
        {
          key: "getHsva",
          value: function () {
            var t = (function (t) {
                return (0, a.default)(t).toHsv();
              })(this.states),
              e = t.h,
              r = t.s,
              n = t.v,
              i = t.a;
            return {
              h: (e = o(e)),
              s: (r = o(100 * r)),
              v: (n = o(100 * n)),
              a: (i *= 1),
            };
          },
        },
        {
          key: "getHsla",
          value: function () {
            var t = (function (t) {
                return (0, a.default)(t).toHsl();
              })(this.states),
              e = t.h,
              r = t.s,
              n = t.l,
              i = t.a;
            return {
              h: (e = o(e)),
              s: (r = o(100 * r)),
              l: (n = o(100 * n)),
              a: (i *= 1),
            };
          },
        },
        {
          key: "equals",
          value: function (t) {
            return a.default.equals(this.rgba, t);
          },
        },
      ],
      [
        {
          key: "isValid",
          value: function (t) {
            return (
              !!(0, s.parseGradientString)(t) || (0, a.default)(t).isValid()
            );
          },
        },
        {
          key: "hsva2color",
          value: function (t, e, r, n) {
            return (0, a.default)({ h: t, s: e, v: r, a: n }).toHsvString();
          },
        },
        {
          key: "hsla2color",
          value: function (t, e, r, n) {
            return (0, a.default)({ h: t, s: e, l: r, a: n }).toHslString();
          },
        },
        {
          key: "rgba2color",
          value: function (t, e, r, n) {
            return (0, a.default)({ r: t, g: e, b: r, a: n }).toHsvString();
          },
        },
        {
          key: "hex2color",
          value: function (t, e) {
            var r = (0, a.default)(t);
            return r.setAlpha(e), r.toHexString();
          },
        },
        {
          key: "object2color",
          value: function (t, e) {
            if ("CMYK" === e) {
              var r = t.c,
                n = t.m,
                i = t.y,
                s = t.k;
              return "cmyk("
                .concat(r, ", ")
                .concat(n, ", ")
                .concat(i, ", ")
                .concat(s, ")");
            }
            return (0, a.default)(t, { format: e }).toRgbString();
          },
        },
      ]
    ),
    t
  );
})();
(exports.Color = d),
  (d.isGradientColor = function (t) {
    return !!(0, s.isGradientColor)(t);
  }),
  (d.compare = function (t, e) {
    var r = d.isGradientColor(t),
      n = d.isGradientColor(e);
    return r && n
      ? c((0, s.parseGradientString)(t)) === c((0, s.parseGradientString)(e))
      : !r && !n && a.default.equals(t, e);
  });
var g = [
  "alpha",
  "css",
  "hex",
  "hex8",
  "hsl",
  "hsla",
  "hsv",
  "hsva",
  "rgb",
  "rgba",
  "saturation",
  "value",
  "isGradient",
];
exports.getColorObject = function (t) {
  if (!t) return null;
  var e = Object.create(null);
  return (
    g.forEach(function (r) {
      return (e[r] = t[r]);
    }),
    t.isGradient && (e.linearGradient = t.linearGradient),
    e
  );
};
var f = d;
exports.default = f;
