__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/image-viewer/image-viewer": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          f = (C) => {},
          g,
          k = (C, l, m, n, o, p, T, E) => {
            var r = (C) => {},
              q = (C, T, E) => {
                E(
                  "t-image",
                  {},
                  (N, C) => {
                    if (C || K || !!U.classPrefix || undefined)
                      L(N, Y(D.classPrefix) + "__image");
                    var $A = m;
                    if (
                      C ||
                      K ||
                      !!(o || Z(Z(U.imagesStyle, $A), "style")) ||
                      undefined
                    )
                      R.y(N, X(X(D.imagesStyle)[$A]).style || "");
                    if (C) O(N, "t-class", "t-image--external");
                    if (C) O(N, "mode", "aspectFit");
                    if (C) O(N, "lazy", true);
                    if (C || K || n) O(N, "src", l);
                    if (C) O(N, "bindload", "onImageLoadSuccess");
                    if (C || K || o) R.d(N, "index", m);
                  },
                  r
                );
              };
            E(
              "swiper-item",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__preview-image");
              },
              q
            );
          },
          j = (C, T, E, B, F) => {
            F(D.images, "index", U ? U.images : undefined, [0, "images"], k);
          },
          i = (C, T, E) => {
            E(
              "swiper",
              {},
              (N, C) => {
                if (C) L(N, "swiper");
                var $A = D.currentSwiperIndex;
                if (
                  C ||
                  K ||
                  !!U.currentSwiperIndex ||
                  Z(Z(U.swiperStyle, $A), "style")
                )
                  R.y(N, X(X(D.swiperStyle)[$A]).style);
                if (C || K || undefined) O(N, "autoplay", false);
                if (C || K || U.currentSwiperIndex)
                  O(N, "current", D.currentSwiperIndex);
                if (C) O(N, "bindchange", "onSwiperChange");
                if (C) O(N, "bindtap", "onClose");
                if (C) O(N, "tabindex", "0");
              },
              j
            );
          },
          n,
          p,
          q = (C, T, E, B, F, S, J) => {
            var $A = I(p);
            if (p && $A)
              $A(
                R,
                C,
                Object.assign({}, X(D._closeBtn), {}),
                K ||
                  (U
                    ? U._closeBtn === true ||
                      Object.assign({}, X(U._closeBtn), {})
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          o = (C, T, E, B, F, S, J) => {
            if (n === 1) {
              p = "icon";
              B(p, q);
            }
          },
          m = (C, T, E, B, F, S) => {
            S("close-btn");
            n = D._closeBtn ? 1 : 0;
            B(n, o);
          },
          r,
          t = (C, T) => {
            C ||
            K ||
            !!(U.currentSwiperIndex || Z(U.images, "length")) ||
            undefined
              ? T(Y(Y(D.currentSwiperIndex + 1) + "/" + Y(X(D.images).length)))
              : T();
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__nav-index");
                },
                t
              );
            }
          },
          v,
          w = (C, T, E, B, F, S, J) => {
            var $A = I(v);
            if (v && $A)
              $A(
                R,
                C,
                Object.assign({}, X(D._deleteBtn), {}),
                K ||
                  (U
                    ? U._deleteBtn === true ||
                      Object.assign({}, X(U._deleteBtn), {})
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          u = (C, T, E, B, F, S, J) => {
            S("delete-btn");
            v = "icon";
            B(v, w);
          },
          l = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__nav-close");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "关闭");
                if (C) R.v(N, "tap", "onClose", !0, !1, !1, !1);
              },
              m
            );
            r = D.showIndex ? 1 : 0;
            B(r, s);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__nav-delete");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "删除");
                if (C) R.v(N, "tap", "onDelete", !1, !1, !1, !1);
              },
              u
            );
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__content");
                },
                i
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__nav");
                },
                l
              );
            }
          },
          e = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__mask");
                if (C || K || !!U.backgroundColor || undefined)
                  R.y(N, "background-color: " + D.backgroundColor);
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "关闭");
                if (C) R.d(N, "source", "overlay");
                if (C) R.v(N, "tap", "onClose", !1, !1, !1, !1);
              },
              f
            );
            g = D.images && X(D.images).length ? 1 : 0;
            B(g, h);
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([U.style, U.customStyle, !!U.maskTop || undefined])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        D.style,
                        D.customStyle,
                        "--td-image-viewer-top: " + D.maskTop + "px",
                      ])
                    );
                  if (C || K || undefined) O(N, "aria-modal", true);
                  if (C) O(N, "aria-role", "dialog");
                  if (C) O(N, "aria-label", "图片查看器");
                  if (C) O(N, "catchtouchmove", "true");
                  if (C || K || U.classPrefix) R.i(N, D.classPrefix);
                },
                e
              );
            }
          },
          b = (C, T, E, B) => {
            c = D.visible ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/image-viewer/image-viewer";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/image-viewer/image-viewer.js";
define(
  "miniprogram_npm/tdesign-miniprogram/image-viewer/image-viewer.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/defineProperty"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      s = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      a = require("../common/src/index"),
      o = require("../common/utils"),
      l = u(require("../common/config")),
      c = u(require("./props"));
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var d = l.default.prefix,
      h = "".concat(d, "-image-viewer"),
      p = (function (n) {
        r(l, n);
        var a = s(l);
        function l() {
          var i;
          return (
            t(this, l),
            ((i = a.apply(this, arguments)).externalClasses = [
              "".concat(d, "-class"),
            ]),
            (i.properties = Object.assign({}, c.default)),
            (i.data = {
              prefix: d,
              classPrefix: h,
              currentSwiperIndex: 0,
              windowHeight: 0,
              windowWidth: 0,
              swiperStyle: {},
              imagesStyle: {},
              maskTop: 0,
            }),
            (i.options = { multipleSlots: !0 }),
            (i.controlledProps = [{ key: "visible", event: "close" }]),
            (i.observers = {
              "visible,initialIndex,images": function (e, t, i) {
                e &&
                  (null == i ? void 0 : i.length) &&
                  this.setData({
                    currentSwiperIndex: t >= i.length ? i.length - 1 : t,
                  });
              },
              closeBtn: function (e) {
                this.setData({ _closeBtn: (0, o.calcIcon)(e, "close") });
              },
              deleteBtn: function (e) {
                this.setData({ _deleteBtn: (0, o.calcIcon)(e, "delete") });
              },
            }),
            (i.methods = {
              calcMaskTop: function () {
                if (this.data.usingCustomNavbar) {
                  var e =
                      (null === wx || void 0 === wx
                        ? void 0
                        : wx.getMenuButtonBoundingClientRect()) || null,
                    t = o.systemInfo.statusBarHeight;
                  e && t && this.setData({ maskTop: e.top - t + e.bottom });
                }
              },
              saveScreenSize: function () {
                var e = o.systemInfo.windowHeight,
                  t = o.systemInfo.windowWidth;
                this.setData({ windowHeight: e, windowWidth: t });
              },
              calcImageDisplayStyle: function (e, t) {
                var i = this.data,
                  r = i.windowWidth,
                  s = i.windowHeight,
                  n = e / t;
                if (e < r && t < s)
                  return {
                    styleObj: {
                      width: 2 * e + "rpx",
                      height: 2 * t + "rpx",
                      left: "50%",
                      transform: "translate(-50%, -50%)",
                    },
                  };
                if (n >= 1)
                  return {
                    styleObj: { width: "100vw", height: (r / n) * 2 + "rpx" },
                  };
                var a = n * s * 2;
                return a < r
                  ? {
                      styleObj: {
                        width: "".concat(a, "rpx"),
                        height: "100vh",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                      },
                    }
                  : {
                      styleObj: {
                        width: "100vw",
                        height: (r / e) * t * 2 + "rpx",
                      },
                    };
              },
              onImageLoadSuccess: function (t) {
                var i = t.detail,
                  r = i.width,
                  s = i.height,
                  n = t.currentTarget.dataset.index,
                  a = this.calcImageDisplayStyle(r, s),
                  l = a.mode,
                  c = a.styleObj,
                  u = this.data.imagesStyle,
                  d = this.data.swiperStyle;
                this.setData({
                  swiperStyle: Object.assign(
                    Object.assign({}, d),
                    e({}, n, { style: "height: ".concat(c.height) })
                  ),
                  imagesStyle: Object.assign(
                    Object.assign({}, u),
                    e({}, n, {
                      mode: l,
                      style: (0, o.styles)(Object.assign({}, c)),
                    })
                  ),
                });
              },
              onSwiperChange: function (e) {
                var t = e.detail.current;
                this.setData({ currentSwiperIndex: t }),
                  this._trigger("change", { index: t });
              },
              onClose: function (e) {
                var t = e.currentTarget.dataset.source;
                this._trigger("close", {
                  visible: !1,
                  trigger: t || "button",
                  index: this.data.currentSwiperIndex,
                });
              },
              onDelete: function () {
                this._trigger("delete", {
                  index: this.data.currentSwiperIndex,
                });
              },
            }),
            i
          );
        }
        return (
          i(l, [
            {
              key: "ready",
              value: function () {
                this.saveScreenSize(), this.calcMaskTop();
              },
            },
          ]),
          l
        );
      })(a.SuperComponent),
      g = (p = (0, n.__decorate)([(0, a.wxComponent)()], p));
    exports.default = g;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/image-viewer/image-viewer.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/image-viewer/image-viewer.js");
