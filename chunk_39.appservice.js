__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/popup/popup": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/popup/popup"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          d,
          i,
          k = (C) => {},
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "name", "close");
                  if (C) O(N, "size", "64rpx");
                },
                k
              );
            }
          },
          h = (C, T, E, B, F, S) => {
            i = D.closeBtn ? 1 : 0;
            B(i, j);
            S("close-btn", (N) => {
              if (C || K || !!U.classPrefix || undefined)
                R.l(N, "class", Y(D.classPrefix) + "-slot");
            });
          },
          g = (C, T, E, B, F, S) => {
            S("content");
            S("");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__close");
                if (C) R.v(N, "tap", "handleClose", !1, !1, !1, !1);
              },
              h
            );
          },
          f = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                var $A = D.overlayProps;
                if (
                  C ||
                  K ||
                  !!(
                    U.preventScrollThrough ||
                    U.overlayProps ||
                    ($A
                      ? !!Z(U.overlayProps, "preventScrollThrough") || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  R.d(
                    N,
                    "prevention",
                    D.preventScrollThrough ||
                      ($A ? !!X(D.overlayProps).preventScrollThrough : false)
                  );
                if (C || K || Z(undefined, "onContentTouchMove"))
                  R.v(N, "touchmove", X(a).onContentTouchMove, !1, !1, !1, !0, [
                    1,
                    "miniprogram_npm/tdesign-miniprogram/popup/popup",
                    "onContentTouchMove",
                  ]);
              },
              g
            );
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.placement]) ||
                      U.transitionClass ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(P(X(b).cls)(D.classPrefix, [D.placement])) +
                        " " +
                        Y(D.transitionClass) +
                        " class " +
                        Y(D.prefix) +
                        "-class"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!(
                          Z(undefined, "getPopupStyles") ||
                          U.zIndex ||
                          U.distanceTop ||
                          U.placement
                        ) || undefined,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(b)._style)([
                        P(X(a).getPopupStyles)(
                          D.zIndex,
                          D.distanceTop,
                          D.placement
                        ),
                        D.style,
                        D.customStyle,
                      ])
                    );
                  if (C)
                    R.v(N, "transitionend", "onTransitionEnd", !1, !1, !1, !1);
                },
                f
              );
            }
          },
          l,
          n = (C) => {},
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "t-overlay",
                {},
                (N, C) => {
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C || K || U.usingCustomNavbar)
                    O(N, "usingCustomNavbar", D.usingCustomNavbar);
                  if (
                    C ||
                    K ||
                    !!(U.overlayProps || Z(U.overlayProps, "zIndex")) ||
                    undefined
                  )
                    O(
                      N,
                      "z-index",
                      (D.overlayProps && X(D.overlayProps).zIndex) || 11000
                    );
                  if (
                    C ||
                    K ||
                    !!(U.overlayProps || Z(U.overlayProps, "duration")) ||
                    undefined
                  )
                    O(
                      N,
                      "duration",
                      (D.overlayProps && X(D.overlayProps).duration) || 300
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      U.overlayProps || Z(U.overlayProps, "backgroundColor")
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "background-color",
                      (D.overlayProps && X(D.overlayProps).backgroundColor) ||
                        ""
                    );
                  var $A = D.overlayProps;
                  if (
                    C ||
                    K ||
                    !!(
                      U.preventScrollThrough ||
                      U.overlayProps ||
                      ($A
                        ? !!Z(U.overlayProps, "preventScrollThrough") ||
                          undefined
                        : undefined)
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "prevent-scroll-through",
                      D.preventScrollThrough ||
                        ($A ? !!X(D.overlayProps).preventScrollThrough : false)
                    );
                  if (
                    C ||
                    K ||
                    !!(U.overlayProps || Z(U.overlayProps, "style")) ||
                    undefined
                  )
                    O(
                      N,
                      "custom-style",
                      (D.overlayProps && X(D.overlayProps).style) || ""
                    );
                  if (C) R.v(N, "tap", "handleOverlayClick", !1, !1, !1, !1);
                  if (C) R.i(N, "popup-overlay");
                },
                n
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.realVisible ? 1 : 0;
            B(d, e);
            l = D.showOverlay ? 1 : 0;
            B(l, m);
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/popup/popup";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/popup/popup.js";
define(
  "miniprogram_npm/tdesign-miniprogram/popup/popup.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      t = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      l = require("../common/src/index"),
      n = c(require("../common/config")),
      a = c(require("./props")),
      o = c(require("../mixins/transition")),
      u = c(require("../mixins/using-custom-navbar"));
    function c(e) {
      return e && e.__esModule ? e : { default: e };
    }
    delete a.default.visible;
    var p = n.default.prefix,
      d = "".concat(p, "-popup"),
      b = (function (s) {
        i(n, s);
        var l = t(n);
        function n() {
          var e;
          return (
            r(this, n),
            ((e = l.apply(this, arguments)).externalClasses = [
              "".concat(p, "-class"),
              "".concat(p, "-class-content"),
            ]),
            (e.behaviors = [(0, o.default)(), u.default]),
            (e.options = { multipleSlots: !0 }),
            (e.properties = a.default),
            (e.data = { prefix: p, classPrefix: d }),
            (e.methods = {
              handleOverlayClick: function () {
                this.properties.closeOnOverlayClick &&
                  this.triggerEvent("visible-change", {
                    visible: !1,
                    trigger: "overlay",
                  });
              },
              handleClose: function () {
                this.triggerEvent("visible-change", {
                  visible: !1,
                  trigger: "close-btn",
                });
              },
            }),
            e
          );
        }
        return e(n);
      })(l.SuperComponent),
      f = (b = (0, s.__decorate)([(0, l.wxComponent)()], b));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/popup/popup.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/popup/popup.js");
