__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/divider/divider": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            align: new Array(1),
            prefix: new Array(2),
            dashed: new Array(1),
            layout: new Array(2),
            customStyle: new Array(1),
            style: new Array(1),
            classPrefix: new Array(6),
            dividerStyle: new Array(1),
          },
          K = U === true,
          f,
          h = (C, T) => {
            C || K || U.content ? T(Y(D.content)) : T();
          },
          g = (C, T, E, B, F, S) => {
            if (f === 1) {
              E("view", {}, (N, C) => {}, h);
            } else {
              S("content");
            }
          },
          e = (C, T, E, B) => {
            f = D.content ? 1 : 0;
            B(f, g);
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(
                    N,
                    Y(D.prefix) +
                      "-class-content " +
                      Y(D.classPrefix) +
                      "__content"
                  );
                A["prefix"][1] = A["classPrefix"][5] = (D, E, T) => {
                  L(
                    N,
                    Y(D.prefix) +
                      "-class-content " +
                      Y(D.classPrefix) +
                      "__content"
                  );
                };
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.dashed;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.prefix ||
                    U.classPrefix ||
                    U.layout ||
                    U.classPrefix ||
                    U.align ||
                    U.dashed ||
                    ($A ? !!U.classPrefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      " class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.layout) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.align) +
                      " " +
                      Y($A ? D.classPrefix + "--dashed" : "")
                  );
                A["classPrefix"][1] =
                  A["prefix"][0] =
                  A["classPrefix"][2] =
                  A["layout"][1] =
                  A["classPrefix"][3] =
                  A["align"][0] =
                  A["dashed"][0] =
                  A["classPrefix"][4] =
                    (D, E, T) => {
                      var $B = D.dashed;
                      L(
                        N,
                        Y(D.classPrefix) +
                          " class " +
                          Y(D.prefix) +
                          "-class " +
                          Y(D.classPrefix) +
                          "--" +
                          Y(D.layout) +
                          " " +
                          Y(D.classPrefix) +
                          "--" +
                          Y(D.align) +
                          " " +
                          Y($B ? D.classPrefix + "--dashed" : "")
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([U.dividerStyle, U.style, U.customStyle])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([D.dividerStyle, D.style, D.customStyle])
                  );
                A["dividerStyle"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([D.dividerStyle, D.style, D.customStyle])
                      );
                    };
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.layout === "vertical";
                if (
                  C ||
                  K ||
                  !!U.layout ||
                  ($A ? !!U.classPrefix || undefined : undefined)
                )
                  L(N, $A ? D.classPrefix + "--vertical-center" : "");
                A["layout"][0] = A["classPrefix"][0] = (D, E, T) => {
                  var $B = D.layout === "vertical";
                  L(N, $B ? D.classPrefix + "--vertical-center" : "");
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/divider/divider";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/divider/divider.js";
define(
  "miniprogram_npm/tdesign-miniprogram/divider/divider.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      o = require("../common/src/index"),
      l = a(require("../common/config")),
      n = a(require("./props"));
    function a(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = l.default.prefix,
      c = "".concat(u, "-divider"),
      p = (function (s) {
        t(l, s);
        var o = i(l);
        function l() {
          var e;
          return (
            r(this, l),
            ((e = o.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-content"),
            ]),
            (e.options = { multipleSlots: !0 }),
            (e.properties = n.default),
            (e.data = { prefix: u, classPrefix: c }),
            (e.observers = {
              lineColor: function () {
                this.setStyle();
              },
            }),
            (e.methods = {
              setStyle: function () {
                var e = this.properties.lineColor,
                  r = "" + (e ? "border-color: ".concat(e, ";") : "");
                this.setData({ dividerStyle: r });
              },
            }),
            e
          );
        }
        return e(l);
      })(o.SuperComponent),
      d = (p = (0, s.__decorate)([(0, o.wxComponent)()], p));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/divider/divider.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/divider/divider.js");
