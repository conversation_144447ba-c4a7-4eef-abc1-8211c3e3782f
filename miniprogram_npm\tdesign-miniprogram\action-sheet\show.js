Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.show = exports.close = exports.ActionSheetTheme = void 0);
var e,
  t,
  o = require("tslib"),
  s = require("../common/utils");
(exports.ActionSheetTheme = e),
  ((t = e || (exports.ActionSheetTheme = e = {})).List = "list"),
  (t.Grid = "grid");
exports.show = function (e) {
  var t = Object.assign({}, e),
    r = t.context,
    c = t.selector,
    i = void 0 === c ? "#t-action-sheet" : c,
    n = (0, o.__rest)(t, ["context", "selector"]),
    l = (0, s.getInstance)(r, i);
  if (l) return l.show(Object.assign({}, n)), l;
  console.error("未找到组件,请确认 selector && context 是否正确");
};
exports.close = function (e) {
  var t = Object.assign({}, e),
    o = t.context,
    r = t.selector,
    c = void 0 === r ? "#t-action-sheet" : r,
    i = (0, s.getInstance)(o, c);
  i && i.close();
};
