__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/service-order-list/service-order-list": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["pages/service-order-list/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { serviceInfo: new Array(1) },
          K = U === true,
          d = (C) => {},
          c = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || !!Z(U.serviceInfo, "service_name") || undefined)
                  O(N, "title", X(D.serviceInfo).service_name || "预约管理");
                A["serviceInfo"][0] = (D, E, T) => {
                  O(N, "title", X(D.serviceInfo).service_name || "预约管理");
                  E(N);
                };
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              d
            );
          },
          h = (C) => {},
          i = (C) => {},
          j = (C) => {},
          k = (C) => {},
          g = (C, T, E) => {
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已下单");
                if (C) O(N, "value", "0");
              },
              h
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已接单");
                if (C) O(N, "value", "1");
              },
              i
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已完成");
                if (C) O(N, "value", "2");
              },
              j
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已取消");
                if (C) O(N, "value", "3");
              },
              k
            );
          },
          f = (C, T, E) => {
            E(
              "t-tabs",
              {},
              (N, C) => {
                if (C || K || undefined) O(N, "defaultValue", 0);
                if (C) O(N, "t-class", "custom-tabs");
                if (C) O(N, "t-class-track", "custom-track");
                if (C) R.v(N, "change", "onTabsChange", !1, !1, !1, !1);
                if (C) R.v(N, "click", "onTabsClick", !1, !1, !1, !1);
              },
              g
            );
          },
          l,
          o = (C, T) => {
            C ? T("加载中...") : T();
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loading_text");
              },
              o
            );
          },
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_container");
                },
                n
              );
            }
          },
          p,
          s = (C, t, u, v, w, x, T, E) => {
            var B0 = (C) => {},
              D0 = (C, T) => {
                C || K || Z(Z(v, "customer"), "nickname")
                  ? T(Y(X(X(t).customer).nickname))
                  : T();
              },
              E0 = (C, T) => {
                var $A = X(X(t).customer).neighbor_type === "owner";
                var $B = X(X(t).customer).neighbor_type === "resident";
                C ||
                K ||
                !!Z(Z(v, "customer"), "neighbor_type") ||
                ($A
                  ? undefined
                  : !!Z(Z(v, "customer"), "neighbor_type") ||
                    ($B ? undefined : undefined))
                  ? T(Y($A ? "业主" : $B ? "住户" : "其他"))
                  : T();
              },
              C0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_name");
                  },
                  D0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_type");
                  },
                  E0
                );
              },
              A0 = (C, T, E) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_avatar");
                    if (C || K || !!Z(Z(v, "customer"), "avatar") || undefined)
                      O(
                        N,
                        "src",
                        X(X(t).customer).avatar || "/assets/tx/短发职业女.png"
                      );
                  },
                  B0
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_details");
                  },
                  C0
                );
              },
              G0 = (C, T) => {
                var $A = X(t).order_status;
                C ||
                K ||
                !!Z(v, "order_status") ||
                Z(Z(U.statusMap, $A), "text")
                  ? T(Y(X(X(D.statusMap)[$A]).text))
                  : T();
              },
              F0 = (C, T, E) => {
                E(
                  "t-tag",
                  {},
                  (N, C) => {
                    if (C) O(N, "variant", "light");
                    var $A = X(t).order_status;
                    if (
                      C ||
                      K ||
                      !!Z(v, "order_status") ||
                      Z(Z(U.statusMap, $A), "theme")
                    )
                      O(N, "theme", X(X(D.statusMap)[$A]).theme);
                  },
                  G0
                );
              },
              z = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_info");
                  },
                  A0
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_status");
                  },
                  F0
                );
              },
              J0 = (C, T) => {
                C ? T("下单时间：") : T();
              },
              K0 = (C, T) => {
                C ||
                K ||
                !!(Z(undefined, "formatTime") || Z(v, "order_time")) ||
                undefined
                  ? T(Y(P(X(a).formatTime)(X(t).order_time)))
                  : T();
              },
              I0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  J0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  K0
                );
              },
              L0,
              O0 = (C, T) => {
                C ? T("地址：") : T();
              },
              P0 = (C, T) => {
                C ||
                K ||
                !!(
                  Z(Z(Z(v, "customer"), "address"), "building") ||
                  Z(Z(Z(v, "customer"), "address"), "unit") ||
                  Z(Z(Z(v, "customer"), "address"), "room")
                ) ||
                undefined
                  ? T(
                      Y(
                        Y(X(X(X(t).customer).address).building) +
                          "-" +
                          Y(X(X(X(t).customer).address).unit) +
                          "-" +
                          Y(X(X(X(t).customer).address).room)
                      )
                    )
                  : T();
              },
              N0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  O0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  P0
                );
              },
              M0 = (C, T, E) => {
                if (L0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    N0
                  );
                }
              },
              Q0,
              T0 = (C, T) => {
                C ? T("接单时间：") : T();
              },
              U0 = (C, T) => {
                C || K || !!(U.formatTime || Z(v, "accept_time")) || undefined
                  ? T(Y(P(D.formatTime)(X(t).accept_time)))
                  : T();
              },
              S0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  T0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  U0
                );
              },
              R0 = (C, T, E) => {
                if (Q0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    S0
                  );
                }
              },
              V0,
              Y0 = (C, T) => {
                C ? T("完成时间：") : T();
              },
              Z0 = (C, T) => {
                C || K || !!(U.formatTime || Z(v, "complete_time")) || undefined
                  ? T(Y(P(D.formatTime)(X(t).complete_time)))
                  : T();
              },
              X0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  Y0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  Z0
                );
              },
              W0 = (C, T, E) => {
                if (V0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    X0
                  );
                }
              },
              H0 = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "detail_row");
                  },
                  I0
                );
                L0 = X(X(t).customer).address ? 1 : 0;
                B(L0, M0);
                Q0 = X(t).accept_time ? 1 : 0;
                B(Q0, R0);
                V0 = X(t).complete_time ? 1 : 0;
                B(V0, W0);
              },
              a0,
              e0 = (C, T) => {
                C || K || Z(v, "remark") ? T(Y(X(t).remark)) : T();
              },
              d0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "content_text");
                  },
                  e0
                );
              },
              c0 = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "content_box");
                  },
                  d0
                );
              },
              b0 = (C, T, E) => {
                if (a0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "order_content");
                    },
                    c0
                  );
                }
              },
              f0,
              j0 = (C, T) => {
                C ? T("联系客户") : T();
              },
              i0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, j0);
              },
              k0,
              n0 = (C, T) => {
                C ? T("取消订单") : T();
              },
              m0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, n0);
              },
              l0 = (C, T, E) => {
                if (k0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "quxiao-btn");
                      if (C || K || Z(v, "order_id"))
                        R.d(N, "orderId", X(t).order_id);
                      if (C) R.v(N, "tap", "onCancelOrder", !1, !1, !1, !1);
                    },
                    m0
                  );
                }
              },
              o0,
              r0 = (C, T) => {
                C ? T("接单") : T();
              },
              q0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, r0);
              },
              p0 = (C, T, E) => {
                if (o0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "accept-btn");
                      if (C || K || Z(v, "order_id"))
                        R.d(N, "orderId", X(t).order_id);
                      if (C) R.v(N, "tap", "onAcceptOrder", !1, !1, !1, !1);
                    },
                    q0
                  );
                }
              },
              s0,
              v0 = (C, T) => {
                C ? T("确认完成") : T();
              },
              u0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, v0);
              },
              t0 = (C, T, E) => {
                if (s0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "confirm-btn");
                      if (C || K || Z(v, "order_id"))
                        R.d(N, "orderId", X(t).order_id);
                      if (C) R.v(N, "tap", "onCompleteOrder", !1, !1, !1, !1);
                    },
                    u0
                  );
                }
              },
              h0 = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "contact-btn");
                    if (C || K || Z(v, "order_id"))
                      R.d(N, "orderId", X(t).order_id);
                    if (C || K || Z(Z(v, "customer"), "contact_info"))
                      R.d(N, "phone", X(X(t).customer).contact_info);
                    if (C) R.v(N, "tap", "onContactCustomer", !1, !1, !1, !1);
                  },
                  i0
                );
                k0 = X(t).order_status === "ordered" ? 1 : 0;
                B(k0, l0);
                o0 = X(t).order_status === "ordered" ? 1 : 0;
                B(o0, p0);
                s0 = X(t).order_status === "accepted" ? 1 : 0;
                B(s0, t0);
              },
              g0 = (C, T, E) => {
                if (f0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "fwdd_list_bottom");
                    },
                    h0
                  );
                }
              },
              y = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_header");
                  },
                  z
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_details");
                  },
                  H0
                );
                a0 = X(t).remark ? 1 : 0;
                B(a0, b0);
                f0 =
                  X(t).order_status === "ordered" ||
                  X(t).order_status === "accepted"
                    ? 1
                    : 0;
                B(f0, g0);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "order_card");
              },
              y
            );
          },
          t,
          w = (C, T) => {
            C ? T("暂无订单") : T();
          },
          v = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "empty_text");
              },
              w
            );
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty_container");
                },
                v
              );
            }
          },
          x,
          A0 = (C, T) => {
            C ? T("上拉加载更多") : T();
          },
          z = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loading_text");
              },
              A0
            );
          },
          y = (C, T, E) => {
            if (x === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_more");
                },
                z
              );
            }
          },
          B0,
          E0 = (C, T) => {
            C ? T("没有更多数据了") : T();
          },
          D0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loading_text");
              },
              E0
            );
          },
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_more");
                },
                D0
              );
            }
          },
          r = (C, T, E, B, F) => {
            F(
              D.filteredOrderList,
              "order_id",
              U ? U.filteredOrderList : undefined,
              [0, "filteredOrderList"],
              s
            );
            t = D.isEmpty ? 1 : 0;
            B(t, u);
            var $A = D.currentTab;
            x =
              X(D.filteredOrderList).length > 0 &&
              X(X(D.tabPagination)[$A]).has_more
                ? 1
                : 0;
            B(x, y);
            var $B = D.currentTab;
            B0 =
              X(D.filteredOrderList).length > 0 &&
              !X(X(D.tabPagination)[$B]).has_more
                ? 1
                : 0;
            B(B0, C0);
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                r
              );
            }
          },
          e = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "tags_box");
              },
              f
            );
            l = D.loading ? 1 : 0;
            B(l, m);
            p = !D.loading ? 1 : 0;
            B(p, q);
          },
          b = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, c);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              e
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/service-order-list/service-order-list";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/service-order-list/service-order-list.js";
define(
  "pages/service-order-list/service-order-list.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      t = require("../../@babel/runtime/helpers/toConsumableArray"),
      r = require("../../@babel/runtime/helpers/asyncToGenerator");
    Page({
      data: {
        serviceId: "",
        currentTab: "ordered",
        orderList: [],
        filteredOrderList: [],
        loading: !0,
        isEmpty: !1,
        pagination: {
          current_page: 1,
          page_size: 10,
          total_count: 0,
          total_pages: 0,
          has_next_page: !1,
          has_prev_page: !1,
        },
        tabPagination: {
          ordered: { current_page: 1, has_more: !0 },
          accepted: { current_page: 1, has_more: !0 },
          completed: { current_page: 1, has_more: !0 },
          cancelled: { current_page: 1, has_more: !0 },
        },
        statusMap: {
          ordered: {
            text: "已下单",
            theme: "primary",
            showActions: ["contact", "accept"],
          },
          accepted: {
            text: "已接单",
            theme: "warning",
            showActions: ["contact", "complete"],
          },
          completed: {
            text: "已完成",
            theme: "success",
            showActions: ["contact"],
          },
          cancelled: { text: "已取消", theme: "default", showActions: [] },
        },
      },
      onLoad: function (e) {
        console.log("Service Order List Page Loaded", e),
          e.serviceId
            ? (this.setData({ serviceId: e.serviceId }), this.loadOrderList())
            : (wx.showToast({ title: "服务ID不存在", icon: "error" }),
              setTimeout(function () {
                wx.navigateBack();
              }, 1500));
      },
      loadOrderList: function () {
        var a = arguments,
          n = this;
        return r(
          e().mark(function r() {
            var o, s, c, i, d, u, l, p, h, g;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      if (
                        ((o = a.length > 0 && void 0 !== a[0] && a[0]),
                        (s = getApp()),
                        o || n.setData({ loading: !0 }),
                        (e.prev = 3),
                        (c = s.globalData.userInfo.data.id))
                      ) {
                        e.next = 7;
                        break;
                      }
                      throw new Error("用户未登录");
                    case 7:
                      return (
                        (i = n.data.currentTab),
                        (d = o ? n.data.tabPagination[i].current_page + 1 : 1),
                        (u = i),
                        (e.next = 12),
                        s.call({
                          path: "/api/mp/serviceOrder/service/".concat(
                            n.data.serviceId
                          ),
                          method: "GET",
                          data: {
                            user_id: c,
                            order_status: u,
                            page: d,
                            page_size: 10,
                          },
                        })
                      );
                    case 12:
                      if (!(l = e.sent) || 0 !== l.code) {
                        e.next = 22;
                        break;
                      }
                      (p = l.data.orders || []),
                        (h = l.data.pagination || {
                          current_page: 1,
                          page_size: 10,
                          total_count: 0,
                          total_pages: 0,
                          has_next_page: !1,
                          has_prev_page: !1,
                        }),
                        console.log("订单列表获取成功:", p),
                        ((g = n.data.tabPagination)[i] = {
                          current_page: h.current_page,
                          has_more: h.has_next_page,
                        }),
                        o
                          ? n.setData({
                              orderList: [].concat(t(n.data.orderList), t(p)),
                              filteredOrderList: [].concat(
                                t(n.data.filteredOrderList),
                                t(p)
                              ),
                              pagination: h,
                              tabPagination: g,
                              loading: !1,
                            })
                          : n.setData({
                              orderList: p,
                              filteredOrderList: p,
                              pagination: h,
                              tabPagination: g,
                              loading: !1,
                              isEmpty: 0 === p.length,
                            }),
                        (e.next = 23);
                      break;
                    case 22:
                      throw new Error(l.message || "获取订单列表失败");
                    case 23:
                      e.next = 30;
                      break;
                    case 25:
                      (e.prev = 25),
                        (e.t0 = e.catch(3)),
                        console.error("获取订单列表失败：", e.t0),
                        o || n.setData({ loading: !1, isEmpty: !0 }),
                        wx.showToast({
                          title: e.t0.message || "加载失败",
                          icon: "error",
                        });
                    case 30:
                    case "end":
                      return e.stop();
                  }
              },
              r,
              null,
              [[3, 25]]
            );
          })
        )();
      },
      onTabsChange: function (e) {
        var t = "ordered";
        switch (e.detail.value) {
          case "0":
            t = "ordered";
            break;
          case "1":
            t = "accepted";
            break;
          case "2":
            t = "completed";
            break;
          case "3":
            t = "cancelled";
        }
        this.setData({ currentTab: t }),
          this.loadOrderList(),
          console.log("Change tab, current tab is ".concat(t));
      },
      onTabsClick: function (e) {
        console.log("Click tab, tab-panel value is ".concat(e.detail.value));
      },
      filterOrdersByTab: function (e) {
        var t = this.data.orderList,
          r = [];
        switch (e) {
          case "all":
            r = t;
            break;
          case "ongoing":
            r = t.filter(function (e) {
              return (
                "ordered" === e.order_status || "accepted" === e.order_status
              );
            });
            break;
          case "completed":
            r = t.filter(function (e) {
              return (
                "completed" === e.order_status || "cancelled" === e.order_status
              );
            });
        }
        this.setData({ filteredOrderList: r, isEmpty: 0 === r.length });
      },
      getOrderStatusInfo: function (e) {
        return (
          this.data.statusMap[e] || {
            text: "未知状态",
            theme: "default",
            showActions: [],
          }
        );
      },
      onContactCustomer: function (e) {
        var t = e.currentTarget.dataset.phone;
        t
          ? wx.makePhoneCall({
              phoneNumber: t,
              fail: function (e) {
                console.error("拨打电话失败：", e),
                  wx.showToast({ title: "拨打失败", icon: "error" });
              },
            })
          : wx.showToast({ title: "客户电话不存在", icon: "none" });
      },
      onAcceptOrder: function (t) {
        var a = this;
        return r(
          e().mark(function r() {
            var n, o, s, c;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      if (
                        ((n = t.currentTarget.dataset.orderId),
                        (o = getApp()),
                        (e.prev = 2),
                        wx.showLoading({ title: "处理中..." }),
                        (s = o.getUserData()),
                        console.log("当前用户ID：", s),
                        s && s.data.id)
                      ) {
                        e.next = 8;
                        break;
                      }
                      throw new Error("无法获取用户信息");
                    case 8:
                      return (
                        (e.next = 10),
                        o.call({
                          path: "/api/mp/serviceOrder/".concat(n, "/accept"),
                          method: "PUT",
                          data: { user_id: s.data.id },
                        })
                      );
                    case 10:
                      if (!(c = e.sent) || 0 !== c.code) {
                        e.next = 17;
                        break;
                      }
                      return (
                        wx.showToast({ title: "接单成功", icon: "success" }),
                        (e.next = 15),
                        a.loadOrderList()
                      );
                    case 15:
                      e.next = 18;
                      break;
                    case 17:
                      throw new Error(c.message || "接单失败");
                    case 18:
                      e.next = 24;
                      break;
                    case 20:
                      (e.prev = 20),
                        (e.t0 = e.catch(2)),
                        console.error("接单失败：", e.t0),
                        wx.showToast({
                          title: e.t0.message || "接单失败",
                          icon: "error",
                        });
                    case 24:
                      return (e.prev = 24), wx.hideLoading(), e.finish(24);
                    case 27:
                    case "end":
                      return e.stop();
                  }
              },
              r,
              null,
              [[2, 20, 24, 27]]
            );
          })
        )();
      },
      onCompleteOrder: function (t) {
        var a = this;
        return r(
          e().mark(function n() {
            var o, s;
            return e().wrap(function (n) {
              for (;;)
                switch ((n.prev = n.next)) {
                  case 0:
                    (o = t.currentTarget.dataset.orderId),
                      (s = getApp()),
                      wx.showModal({
                        title: "确认完成",
                        content: "",
                        editable: !0,
                        placeholderText: "请填写备注信息",
                        success: (function () {
                          var t = r(
                            e().mark(function t(r) {
                              var n, c;
                              return e().wrap(
                                function (e) {
                                  for (;;)
                                    switch ((e.prev = e.next)) {
                                      case 0:
                                        if (!r.confirm) {
                                          e.next = 25;
                                          break;
                                        }
                                        if (
                                          ((e.prev = 1),
                                          wx.showLoading({
                                            title: "处理中...",
                                          }),
                                          (n = s.getUserData()) && n.data.id)
                                        ) {
                                          e.next = 6;
                                          break;
                                        }
                                        throw new Error("无法获取用户信息");
                                      case 6:
                                        return (
                                          (e.next = 8),
                                          s.call({
                                            path: "/api/mp/serviceOrder/".concat(
                                              o,
                                              "/complete"
                                            ),
                                            method: "PUT",
                                            data: {
                                              user_id: n.data.id,
                                              completion_note: r.content || "",
                                            },
                                          })
                                        );
                                      case 8:
                                        if (!(c = e.sent) || 0 !== c.code) {
                                          e.next = 15;
                                          break;
                                        }
                                        return (
                                          wx.showToast({
                                            title: "订单已完成",
                                            icon: "success",
                                          }),
                                          (e.next = 13),
                                          a.loadOrderList()
                                        );
                                      case 13:
                                        e.next = 16;
                                        break;
                                      case 15:
                                        throw new Error(
                                          c.message || "操作失败"
                                        );
                                      case 16:
                                        e.next = 22;
                                        break;
                                      case 18:
                                        (e.prev = 18),
                                          (e.t0 = e.catch(1)),
                                          console.error("完成订单失败：", e.t0),
                                          wx.showToast({
                                            title: e.t0.message || "操作失败",
                                            icon: "error",
                                          });
                                      case 22:
                                        return (
                                          (e.prev = 22),
                                          wx.hideLoading(),
                                          e.finish(22)
                                        );
                                      case 25:
                                      case "end":
                                        return e.stop();
                                    }
                                },
                                t,
                                null,
                                [[1, 18, 22, 25]]
                              );
                            })
                          );
                          return function (e) {
                            return t.apply(this, arguments);
                          };
                        })(),
                      });
                  case 3:
                  case "end":
                    return n.stop();
                }
            }, n);
          })
        )();
      },
      onCancelOrder: function (t) {
        var a = this;
        return r(
          e().mark(function n() {
            var o, s;
            return e().wrap(function (n) {
              for (;;)
                switch ((n.prev = n.next)) {
                  case 0:
                    (o = t.currentTarget.dataset.orderId),
                      (s = getApp()),
                      wx.showModal({
                        title: "确认取消",
                        content: "",
                        editable: !0,
                        placeholderText: "请输入取消原因（选填）",
                        success: (function () {
                          var t = r(
                            e().mark(function t(r) {
                              var n, c;
                              return e().wrap(
                                function (e) {
                                  for (;;)
                                    switch ((e.prev = e.next)) {
                                      case 0:
                                        if (!r.confirm) {
                                          e.next = 25;
                                          break;
                                        }
                                        if (
                                          ((e.prev = 1),
                                          wx.showLoading({
                                            title: "处理中...",
                                          }),
                                          (n = s.getUserData()) && n.data.id)
                                        ) {
                                          e.next = 6;
                                          break;
                                        }
                                        throw new Error("无法获取用户信息");
                                      case 6:
                                        return (
                                          (e.next = 8),
                                          s.call({
                                            path: "/api/mp/serviceOrder/".concat(
                                              o,
                                              "/cancel"
                                            ),
                                            method: "PUT",
                                            data: {
                                              user_id: n.data.id,
                                              cancel_reason: r.content || "",
                                            },
                                          })
                                        );
                                      case 8:
                                        if (!(c = e.sent) || 0 !== c.code) {
                                          e.next = 15;
                                          break;
                                        }
                                        return (
                                          wx.showToast({
                                            title: "订单已取消",
                                            icon: "success",
                                          }),
                                          (e.next = 13),
                                          a.loadOrderList()
                                        );
                                      case 13:
                                        e.next = 16;
                                        break;
                                      case 15:
                                        throw new Error(
                                          c.message || "取消失败"
                                        );
                                      case 16:
                                        e.next = 22;
                                        break;
                                      case 18:
                                        (e.prev = 18),
                                          (e.t0 = e.catch(1)),
                                          console.error("取消订单失败：", e.t0),
                                          wx.showToast({
                                            title: e.t0.message || "取消失败",
                                            icon: "error",
                                          });
                                      case 22:
                                        return (
                                          (e.prev = 22),
                                          wx.hideLoading(),
                                          e.finish(22)
                                        );
                                      case 25:
                                      case "end":
                                        return e.stop();
                                    }
                                },
                                t,
                                null,
                                [[1, 18, 22, 25]]
                              );
                            })
                          );
                          return function (e) {
                            return t.apply(this, arguments);
                          };
                        })(),
                      });
                  case 3:
                  case "end":
                    return n.stop();
                }
            }, n);
          })
        )();
      },
      updateOrderStatus: function (t, a, n) {
        var o = this;
        return r(
          e().mark(function r() {
            var s, c;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return (
                        (s = getApp()),
                        (e.prev = 1),
                        wx.showLoading({ title: "处理中..." }),
                        (e.next = 5),
                        s.call({
                          path: "/api/mp/serviceOrder/".concat(t, "/status"),
                          method: "PUT",
                          data: { order_status: a },
                        })
                      );
                    case 5:
                      if (!(c = e.sent) || 0 !== c.code) {
                        e.next = 12;
                        break;
                      }
                      return (
                        wx.showToast({ title: n, icon: "success" }),
                        (e.next = 10),
                        o.loadOrderList()
                      );
                    case 10:
                      e.next = 13;
                      break;
                    case 12:
                      throw new Error(c.message || "操作失败");
                    case 13:
                      e.next = 19;
                      break;
                    case 15:
                      (e.prev = 15),
                        (e.t0 = e.catch(1)),
                        console.error("更新订单状态失败：", e.t0),
                        wx.showToast({
                          title: e.t0.message || "操作失败",
                          icon: "error",
                        });
                    case 19:
                      return (e.prev = 19), wx.hideLoading(), e.finish(19);
                    case 22:
                    case "end":
                      return e.stop();
                  }
              },
              r,
              null,
              [[1, 15, 19, 22]]
            );
          })
        )();
      },
      formatTime: function (e) {
        if (!e) return "";
        var t = new Date(e),
          r = t.getFullYear(),
          a = String(t.getMonth() + 1).padStart(2, "0"),
          n = String(t.getDate()).padStart(2, "0"),
          o = String(t.getHours()).padStart(2, "0"),
          s = String(t.getMinutes()).padStart(2, "0");
        return ""
          .concat(r, "-")
          .concat(a, "-")
          .concat(n, " ")
          .concat(o, ":")
          .concat(s);
      },
      onPullDownRefresh: function () {
        this.loadOrderList().finally(function () {
          wx.stopPullDownRefresh();
        });
      },
      onReachBottom: function () {
        var e = this.data.currentTab,
          t = this.data.tabPagination[e];
        t && t.has_more
          ? this.loadOrderList(!0)
          : wx.showToast({ title: "没有更多数据了", icon: "none" });
      },
      onShow: function () {
        this.data.serviceId && this.loadOrderList();
      },
    });
  },
  {
    isPage: true,
    isComponent: true,
    currentFile: "pages/service-order-list/service-order-list.js",
  }
);
require("pages/service-order-list/service-order-list.js");
