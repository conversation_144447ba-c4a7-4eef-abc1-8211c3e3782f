__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/textarea/textarea": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/textarea/textarea"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            selectionEnd: new Array(1),
            placeholderClass: new Array(1),
            bordered: new Array(1),
            showConfirmBar: new Array(1),
            placeholder: new Array(1),
            autofocus: new Array(1),
            cursor: new Array(1),
            confirmHold: new Array(1),
            selectionStart: new Array(1),
            allowInputOverMax: new Array(1),
            placeholderStyle: new Array(1),
            style: new Array(1),
            cursorSpacing: new Array(1),
            holdKeyboard: new Array(1),
            adjustPosition: new Array(1),
            confirmType: new Array(1),
            autosize: new Array(2),
            disabled: new Array(2),
            disableDefaultPadding: new Array(1),
            fixed: new Array(1),
            readonly: new Array(1),
            customStyle: new Array(1),
            value: new Array(1),
            focus: new Array(1),
          },
          K = U === true,
          f,
          g = (C, T) => {
            if (f === 1) {
              C || K || U.label ? T(Y(D.label)) : T();
            }
          },
          e = (C, T, E, B, F, S) => {
            f = D.label ? 1 : 0;
            B(f, g);
            S("label");
          },
          i = (C) => {},
          j,
          l = (C, T) => {
            C || K || !!(U.count || U.maxcharacter || U.maxlength) || undefined
              ? T(Y(Y(D.count) + " / " + Y(D.maxcharacter || D.maxlength)))
              : T();
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__indicator " +
                        Y(D.prefix) +
                        "-class-indicator"
                    );
                },
                l
              );
            }
          },
          h = (C, T, E, B) => {
            E(
              "textarea",
              {},
              (N, C) => {
                var $A = D.disabled;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.disabled ||
                    ($A ? !!U.prefix || undefined : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__wrapper-inner " +
                      Y($A ? D.prefix + "-is-disabled" : "") +
                      " " +
                      Y(D.prefix) +
                      "-class-textarea"
                  );
                A["disabled"][1] = (D, E, T) => {
                  var $B = D.disabled;
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__wrapper-inner " +
                      Y($B ? D.prefix + "-is-disabled" : "") +
                      " " +
                      Y(D.prefix) +
                      "-class-textarea"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "textareaStyle") || U.autosize) ||
                  undefined
                )
                  R.y(N, P(X(b).textareaStyle)(D.autosize));
                A["autosize"][1] = (D, E, T) => {
                  R.y(N, P(X(b).textareaStyle)(D.autosize));
                };
                var $B = D.allowInputOverMax;
                if (
                  C ||
                  K ||
                  !!U.allowInputOverMax ||
                  ($B ? undefined : U.maxlength)
                )
                  O(N, "maxlength", $B ? -1 : D.maxlength);
                A["allowInputOverMax"][0] = (D, E, T) => {
                  var $C = D.allowInputOverMax;
                  O(N, "maxlength", $C ? -1 : D.maxlength);
                  E(N);
                };
                if (C || K || !!(U.disabled || U.readonly) || undefined)
                  O(N, "disabled", D.disabled || D.readonly);
                A["disabled"][0] = A["readonly"][0] = (D, E, T) => {
                  O(N, "disabled", D.disabled || D.readonly);
                  E(N);
                };
                if (C || K || U.placeholder) O(N, "placeholder", D.placeholder);
                A["placeholder"][0] = (D, E, T) => {
                  O(N, "placeholder", D.placeholder);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.placeholderClass) ||
                  undefined
                )
                  O(
                    N,
                    "placeholder-class",
                    Y(D.classPrefix) + "__placeholder " + Y(D.placeholderClass)
                  );
                A["placeholderClass"][0] = (D, E, T) => {
                  O(
                    N,
                    "placeholder-class",
                    Y(D.classPrefix) + "__placeholder " + Y(D.placeholderClass)
                  );
                  E(N);
                };
                if (C || K || U.placeholderStyle)
                  O(N, "placeholder-style", D.placeholderStyle);
                A["placeholderStyle"][0] = (D, E, T) => {
                  O(N, "placeholder-style", D.placeholderStyle);
                  E(N);
                };
                if (C || K || U.value) O(N, "value", D.value);
                A["value"][0] = (D, E, T) => {
                  O(N, "value", D.value);
                  E(N);
                };
                if (C || K || !!U.autosize || undefined)
                  O(N, "auto-height", !!D.autosize);
                A["autosize"][0] = (D, E, T) => {
                  O(N, "auto-height", !!D.autosize);
                  E(N);
                };
                if (C || K || U.autofocus) O(N, "auto-focus", D.autofocus);
                A["autofocus"][0] = (D, E, T) => {
                  O(N, "auto-focus", D.autofocus);
                  E(N);
                };
                if (C || K || U.fixed) O(N, "fixed", D.fixed);
                A["fixed"][0] = (D, E, T) => {
                  O(N, "fixed", D.fixed);
                  E(N);
                };
                if (C || K || U.focus) O(N, "focus", D.focus);
                A["focus"][0] = (D, E, T) => {
                  O(N, "focus", D.focus);
                  E(N);
                };
                if (C || K || U.cursor) O(N, "cursor", D.cursor);
                A["cursor"][0] = (D, E, T) => {
                  O(N, "cursor", D.cursor);
                  E(N);
                };
                if (C || K || U.cursorSpacing)
                  O(N, "cursor-spacing", D.cursorSpacing);
                A["cursorSpacing"][0] = (D, E, T) => {
                  O(N, "cursor-spacing", D.cursorSpacing);
                  E(N);
                };
                if (C || K || U.adjustPosition)
                  O(N, "adjust-position", D.adjustPosition);
                A["adjustPosition"][0] = (D, E, T) => {
                  O(N, "adjust-position", D.adjustPosition);
                  E(N);
                };
                if (C || K || U.confirmType)
                  O(N, "confirm-type", D.confirmType);
                A["confirmType"][0] = (D, E, T) => {
                  O(N, "confirm-type", D.confirmType);
                  E(N);
                };
                if (C || K || U.confirmHold)
                  O(N, "confirm-hold", D.confirmHold);
                A["confirmHold"][0] = (D, E, T) => {
                  O(N, "confirm-hold", D.confirmHold);
                  E(N);
                };
                if (C || K || U.disableDefaultPadding)
                  O(N, "disable-default-padding", D.disableDefaultPadding);
                A["disableDefaultPadding"][0] = (D, E, T) => {
                  O(N, "disable-default-padding", D.disableDefaultPadding);
                  E(N);
                };
                if (C || K || U.showConfirmBar)
                  O(N, "show-confirm-bar", D.showConfirmBar);
                A["showConfirmBar"][0] = (D, E, T) => {
                  O(N, "show-confirm-bar", D.showConfirmBar);
                  E(N);
                };
                if (C || K || U.selectionStart)
                  O(N, "selection-start", D.selectionStart);
                A["selectionStart"][0] = (D, E, T) => {
                  O(N, "selection-start", D.selectionStart);
                  E(N);
                };
                if (C || K || U.selectionEnd)
                  O(N, "selection-end", D.selectionEnd);
                A["selectionEnd"][0] = (D, E, T) => {
                  O(N, "selection-end", D.selectionEnd);
                  E(N);
                };
                if (C || K || U.holdKeyboard)
                  O(N, "hold-keyboard", D.holdKeyboard);
                A["holdKeyboard"][0] = (D, E, T) => {
                  O(N, "hold-keyboard", D.holdKeyboard);
                  E(N);
                };
                if (C) O(N, "bindinput", "onInput");
                if (C) O(N, "bindfocus", "onFocus");
                if (C) O(N, "bindblur", "onBlur");
                if (C) O(N, "bindconfirm", "onConfirm");
                if (C) O(N, "bindlinechange", "onLineChange");
                if (C)
                  R.v(
                    N,
                    "keyboardheightchange",
                    "onKeyboardHeightChange",
                    !1,
                    !1,
                    !1,
                    !1
                  );
              },
              i
            );
            j = D.indicator && (D.maxcharacter > 0 || D.maxlength > 0) ? 1 : 0;
            B(j, k);
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__label " + Y(D.prefix) + "-class-label"
                  );
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrapper");
              },
              h
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.bordered;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.bordered ||
                    ($A ? !!U.classPrefix || undefined : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y($A ? D.classPrefix + "--border" : "") +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["bordered"][0] = (D, E, T) => {
                  var $B = D.bordered;
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y($B ? D.classPrefix + "--border" : "") +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/textarea/textarea";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/textarea/textarea.js";
define(
  "miniprogram_npm/tdesign-miniprogram/textarea/textarea.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      i = require("../common/src/index"),
      u = c(require("../common/config")),
      l = c(require("./props")),
      s = require("../common/utils");
    function c(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = u.default.prefix,
      h = "".concat(o, "-textarea"),
      p = (function (n) {
        a(u, n);
        var i = r(u);
        function u() {
          var e;
          return (
            t(this, u),
            ((e = i.apply(this, arguments)).options = { multipleSlots: !0 }),
            (e.behaviors = ["wx://form-field"]),
            (e.externalClasses = [
              "".concat(o, "-class"),
              "".concat(o, "-class-textarea"),
              "".concat(o, "-class-label"),
              "".concat(o, "-class-indicator"),
            ]),
            (e.properties = l.default),
            (e.data = { prefix: o, classPrefix: h, count: 0 }),
            (e.observers = {
              value: function (e) {
                this.updateCount(null != e ? e : this.properties.defaultValue);
              },
            }),
            (e.lifetimes = {
              ready: function () {
                var e,
                  t = this.properties,
                  a = t.value,
                  r = t.defaultValue;
                this.updateValue(
                  null !== (e = null != a ? a : r) && void 0 !== e ? e : ""
                );
              },
            }),
            (e.methods = {
              updateCount: function (e) {
                var t = this.properties,
                  a = t.maxcharacter,
                  r = t.maxlength,
                  n = this.calculateValue(e, a, r).count;
                this.setData({ count: n });
              },
              updateValue: function (e) {
                var t = this.properties,
                  a = t.maxcharacter,
                  r = t.maxlength,
                  n = this.calculateValue(e, a, r),
                  i = n.value,
                  u = n.count;
                this.setData({ value: i, count: u });
              },
              calculateValue: function (e, t, a) {
                var r = this.properties.allowInputOverMax;
                if (t > 0 && !Number.isNaN(t)) {
                  var n = (0, s.getCharacterLength)(
                      "maxcharacter",
                      e,
                      r ? 1 / 0 : t
                    ),
                    i = n.length;
                  return { value: n.characters, count: i };
                }
                if (a > 0 && !Number.isNaN(a)) {
                  var u = (0, s.getCharacterLength)(
                      "maxlength",
                      e,
                      r ? 1 / 0 : a
                    ),
                    l = u.length;
                  return { value: u.characters, count: l };
                }
                return { value: e, count: e ? String(e).length : 0 };
              },
              onInput: function (e) {
                var t = e.detail,
                  a = t.value,
                  r = t.cursor;
                this.updateValue(a),
                  this.triggerEvent("change", {
                    value: this.data.value,
                    cursor: r,
                  });
              },
              onFocus: function (e) {
                this.triggerEvent("focus", Object.assign({}, e.detail));
              },
              onBlur: function (e) {
                this.triggerEvent("blur", Object.assign({}, e.detail));
              },
              onConfirm: function (e) {
                this.triggerEvent("enter", Object.assign({}, e.detail));
              },
              onLineChange: function (e) {
                this.triggerEvent("line-change", Object.assign({}, e.detail));
              },
              onKeyboardHeightChange: function (e) {
                this.triggerEvent("keyboardheightchange", e.detail);
              },
            }),
            e
          );
        }
        return e(u);
      })(i.SuperComponent),
      g = (p = (0, n.__decorate)([(0, i.wxComponent)()], p));
    exports.default = g;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/textarea/textarea.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/textarea/textarea.js");
