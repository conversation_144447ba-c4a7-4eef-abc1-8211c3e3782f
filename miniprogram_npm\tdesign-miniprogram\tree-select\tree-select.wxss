@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-tree-select {
  background-color: var(
    --td-tree-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  display: -webkit-flex;
  display: flex;
}
.t-tree-select__column {
  color: var(
    --td-tree-colum-text-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  width: var(--td-tree-colum-width, 206rpx);
}
.t-tree-select__column--left {
  background: var(
    --td-tree-root-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
}
.t-tree-select__column--right {
  flex: 1;
}
.t-tree-select__column::-webkit-scrollbar {
  color: transparent;
  display: none;
  height: 0;
  width: 0;
}
.t-tree-select__item {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  font-size: var(--td-tree-item-font-size, 32rpx);
  height: var(--td-tree-item-height, 112rpx);
  line-height: var(--td-tree-item-height, 112rpx);
  overflow: hidden;
  padding-left: 32rpx;
  text-overflow: ellipsis;
}
.t-tree-select__item--active {
  color: var(
    --td-tree-item-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  font-weight: 600;
}
.t-tree-select-column {
  height: auto;
  width: 100%;
}
