__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/tab-bar-item/tab-bar-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            theme: new Array(1),
            shape: new Array(1),
            ariaLabel: new Array(1),
            isChecked: new Array(2),
            crowded: new Array(1),
            style: new Array(1),
            split: new Array(1),
            customStyle: new Array(1),
          },
          K = U === true,
          f,
          i,
          j = (C, T, E, B, F, S, J) => {
            var $A = I(i);
            var $B = D.iconOnly;
            if (i && $A)
              $A(
                R,
                C,
                Object.assign({ size: $B ? 24 : 20 }, X(D._icon), {}),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { size: !!U.iconOnly || ($B ? undefined : undefined) },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          h = (C, T, E, B, F, S, J) => {
            i = "icon";
            B(i, j);
          },
          k,
          l = (C, T, E, B, F, S, J) => {
            var $A = I(k);
            var $B = D.iconOnly;
            if (k && $A)
              $A(
                R,
                C,
                Object.assign(
                  { ariaHidden: !D.iconOnly, size: $B ? 24 : 20 },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        {
                          ariaHidden: !!U.iconOnly || undefined,
                          size: !!U.iconOnly || ($B ? undefined : undefined),
                        },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          g = (C, T, E, B, F, S, J) => {
            if (f === 1) {
              E(
                "t-badge",
                {},
                (N, C) => {
                  if (C || K || !!Z(U.badgeProps, "count") || undefined)
                    O(N, "count", X(D.badgeProps).count || 0);
                  if (C || K || !!Z(U.badgeProps, "maxCount") || undefined)
                    O(N, "max-count", X(D.badgeProps).maxCount || 99);
                  if (C || K || !!Z(U.badgeProps, "dot") || undefined)
                    O(N, "dot", X(D.badgeProps).dot || false);
                  if (C || K || !!Z(U.badgeProps, "content") || undefined)
                    O(N, "content", X(D.badgeProps).content || "");
                  if (C || K || !!Z(U.badgeProps, "size") || undefined)
                    O(N, "size", X(D.badgeProps).size || "medium");
                  if (C || K || Z(U.badgeProps, "visible"))
                    O(N, "visible", X(D.badgeProps).visible);
                  if (
                    C ||
                    K ||
                    !!(Z(U.badgeProps, "offset") || Q.a([])) ||
                    undefined
                  )
                    O(N, "offset", X(D.badgeProps).offset || [0, 0]);
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class-count", D.prefix + "-badge-class");
                },
                h
              );
            } else if (f === 2) {
              k = "icon";
              B(k, l);
            }
          },
          e = (C, T, E, B, F, S) => {
            f =
              X(D.badgeProps).dot || X(D.badgeProps).count
                ? 1
                : !!D.icon
                ? 2
                : 0;
            B(f, g);
            S("icon");
          },
          n,
          p = (C) => {},
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "name", "view-list");
                  if (C) O(N, "size", "32rpx");
                  if (C || K || !!U.classPrefix || undefined)
                    O(N, "t-class", Y(D.classPrefix) + "__icon-menu");
                },
                p
              );
            }
          },
          m = (C, T, E, B, F, S) => {
            n = D.hasChildren ? 1 : 0;
            B(n, o);
            S("");
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon");
                var $A = D.iconOnly;
                if (
                  C ||
                  K ||
                  !!(U.iconOnly || ($A ? undefined : undefined)) ||
                  undefined
                )
                  R.y(N, "height:" + Y($A ? 24 : 20) + "px");
                if (
                  C ||
                  K ||
                  !!(Z(U.badgeProps, "dot") || Z(U.badgeProps, "count")) ||
                  undefined
                )
                  O(
                    N,
                    "aria-hidden",
                    X(D.badgeProps).dot || X(D.badgeProps).count
                  );
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!U.icon || undefined])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__text", [["small", !!D.icon]])
                  );
              },
              m
            );
          },
          q,
          t = (C, u, v, w, x, y, T, E) => {
            var A0,
              C0 = (C) => {},
              B0 = (C, T, E) => {
                if (A0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C || K || !!U.classPrefix || undefined)
                        L(N, Y(D.classPrefix) + "__spread-item-split");
                    },
                    C0
                  );
                }
              },
              D0 = (C, T) => {
                C || K || Z(w, "label") ? T(Y(X(u).label)) : T();
              },
              z = (C, T, E, B) => {
                A0 = v !== 0 ? 1 : 0;
                B(A0, B0);
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!U.classPrefix || undefined)
                      L(N, Y(D.classPrefix) + "__spread-item-text");
                    if (C || K || !!(Z(w, "value") || x) || undefined)
                      R.d(N, "value", X(u).value || v);
                  },
                  D0
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__spread-item");
                if (C || K || !!U.classPrefix || undefined)
                  O(
                    N,
                    "hover-class",
                    Y(D.classPrefix) + "__spread-item--active"
                  );
                if (C || K || undefined) O(N, "hover-stay-time", 200);
                if (C) O(N, "aria-role", "tab");
                if (C || K || !!(Z(w, "value") || x) || undefined)
                  R.d(N, "value", X(u).value || v);
                if (C) R.v(N, "tap", "selectChild", !1, !1, !1, !1);
              },
              z
            );
          },
          s = (C, T, E, B, F) => {
            F(
              D.subTabBar,
              "value",
              U ? U.subTabBar : undefined,
              [0, "subTabBar"],
              t
            );
          },
          r = (C, T, E) => {
            if (q === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__spread");
                },
                s
              );
            }
          },
          c = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.isChecked]), U.theme])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__content", [
                      ["checked", D.isChecked],
                      D.theme,
                    ])
                  );
                A["isChecked"][1] = A["theme"][0] = (D, E, T) => {
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__content", [
                      ["checked", D.isChecked],
                      D.theme,
                    ])
                  );
                };
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "hover-class", Y(D.classPrefix) + "__content--active");
                if (C || K || undefined) O(N, "hover-stay-time", 200);
                if (C) O(N, "bindtap", "toggle");
                var $A = (!D.hasChildren || !D.isSpread) && D.isChecked;
                if (
                  C ||
                  K ||
                  !!(U.hasChildren || U.isSpread || U.isChecked) ||
                  ($A ? undefined : undefined)
                )
                  O(N, "aria-selected", $A ? true : false);
                A["isChecked"][0] = (D, E, T) => {
                  var $B = (!D.hasChildren || !D.isSpread) && D.isChecked;
                  O(N, "aria-selected", $B ? true : false);
                  E(N);
                };
                var $B = D.hasChildren && D.isSpread;
                if (
                  C ||
                  K ||
                  !!(U.hasChildren || U.isSpread) ||
                  ($B ? undefined : undefined)
                )
                  O(N, "aria-expanded", $B ? true : "");
                var $C = D.hasChildren;
                if (C || K || !!U.hasChildren || ($C ? undefined : undefined))
                  O(N, "aria-role", $C ? "button" : "tab");
                var $D = X(D.badgeProps).dot || X(D.badgeProps).count;
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    Z(U.badgeProps, "dot") ||
                    Z(U.badgeProps, "count") ||
                    ($D
                      ? !!(
                          Z(undefined, "getBadgeAriaLabel") ||
                          U.badgeProps === true ||
                          Q.b(Object.assign({}, X(U.badgeProps), {}))
                        ) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($D
                        ? P(X(a).getBadgeAriaLabel)(
                            Object.assign({}, X(D.badgeProps), {})
                          )
                        : "")
                  );
                A["ariaLabel"][0] = (D, E, T) => {
                  var $E = X(D.badgeProps).dot || X(D.badgeProps).count;
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($E
                        ? P(X(a).getBadgeAriaLabel)(
                            Object.assign({}, X(D.badgeProps), {})
                          )
                        : "")
                  );
                  E(N);
                };
              },
              d
            );
            q = D.hasChildren && D.isSpread ? 1 : 0;
            B(q, r);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([U.split]),
                      Q.a([!!U.icon || undefined]),
                      Q.a([U.crowded]),
                      U.shape,
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["split", D.split],
                        ["text-only", !D.icon],
                        ["crowded", D.crowded],
                        D.shape,
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["split"][0] =
                  A["crowded"][0] =
                  A["shape"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(
                          P(X(a).cls)(D.classPrefix, [
                            ["split", D.split],
                            ["text-only", !D.icon],
                            ["crowded", D.crowded],
                            D.shape,
                          ])
                        ) +
                          " class " +
                          Y(D.prefix) +
                          "-class"
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/tab-bar-item/tab-bar-item.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-tab-bar-item{background-color:var(--td-tab-bar-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));box-sizing:border-box;-webkit-flex:1;flex:1;height:var(--td-tab-bar-height,",
      [0, 80],
      ");margin:",
      [0, 16],
      " 0;padding:0 ",
      [0, 24],
      ";position:relative;-webkit-user-select:none;user-select:none}\n.",
      [1],
      "t-tab-bar-item--text-only{font-size:",
      [0, 32],
      "}\n.",
      [1],
      "t-tab-bar-item--split+.",
      [1],
      "t-tab-bar-item--split::before{border-left:1px solid var(--td-tab-bar-border-color,var(--td-border-color,var(--td-gray-color-3,#e7e7e7)));bottom:0;bottom:",
      [0, 16],
      ";box-sizing:border-box;content:\x22 \x22;left:0;pointer-events:none;position:absolute;top:0;top:",
      [0, 16],
      ";-webkit-transform:scaleX(.5);transform:scaleX(.5)}\n.",
      [1],
      "t-tab-bar-item--crowded{padding:0 ",
      [0, 16],
      "}\n.",
      [1],
      "t-tab-bar-item--round{border-radius:99px}\n.",
      [1],
      "t-tab-bar-item__content{-webkit-align-items:center;align-items:center;border-radius:",
      [0, 16],
      ";color:var(--td-tab-bar-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;height:100%;-webkit-justify-content:center;justify-content:center;width:100%}\n.",
      [1],
      "t-tab-bar-item__content--checked{color:var(--td-tab-bar-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));font-weight:600}\n.",
      [1],
      "t-tab-bar-item__content--tag{border-radius:99px}\n.",
      [1],
      "t-tab-bar-item__content--tag.",
      [1],
      "t-tab-bar-item__content--checked{background-color:var(--td-tab-bar-active-bg,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)))}\n.",
      [1],
      "t-tab-bar-item__icon{display:contents}\n.",
      [1],
      "t-tab-bar-item__icon:empty{display:none}\n.",
      [1],
      "t-tab-bar-item__text{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
      [1],
      "t-tab-bar-item__text--small{font-size:",
      [0, 20],
      ";line-height:",
      [0, 32],
      "}\n.",
      [1],
      "t-tab-bar-item__icon-menu{margin-right:",
      [0, 8],
      "}\n.",
      [1],
      "t-tab-bar-item__spread{background-color:var(--td-tab-bar-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));border-radius:",
      [0, 12],
      ";box-shadow:var(--td-tab-bar-spread-shadow,var(--td-shadow-3,0 6px 30px 5px rgba(0,0,0,.05),0 16px 24px 2px rgba(0,0,0,.04),0 8px 10px -5px rgba(0,0,0,.08)));color:var(--td-tab-bar-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));left:7%;position:absolute;top:0;-webkit-transform:translate3d(0,calc(-100% - ",
      [0, 32],
      "),0);transform:translate3d(0,calc(-100% - ",
      [0, 32],
      "),0);width:86%;z-index:1}\n.",
      [1],
      "t-tab-bar-item__spread::before{border:",
      [0, 16],
      " solid transparent;border-top:",
      [0, 16],
      " solid var(--td-tab-bar-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));bottom:0;content:\x22\x22;display:block;height:0;left:50%;position:absolute;-webkit-transform:translate3d(-50%,",
      [0, 32],
      ",0);transform:translate3d(-50%,",
      [0, 32],
      ",0);width:0}\n.",
      [1],
      "t-tab-bar-item__spread-item{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;height:",
      [0, 96],
      ";-webkit-justify-content:flex-start;justify-content:flex-start;position:relative;width:100%}\n.",
      [1],
      "t-tab-bar-item__spread-item--active{background-color:var(--td-tab-bar-hover-bg-color,rgba(0,0,0,.05))}\n.",
      [1],
      "t-tab-bar-item__spread-item-split{background-color:var(--td-tab-bar-spread-border-color,var(--td-border-color,var(--td-gray-color-3,#e7e7e7)));box-sizing:border-box;content:\x22 \x22;height:1px;pointer-events:none;-webkit-transform:translateY(.5);transform:translateY(.5);width:80%}\n.",
      [1],
      "t-tab-bar-item__spread-item-text{padding-top:",
      [0, 24],
      "}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/tab-bar-item/tab-bar-item.wxss",
    }
  );
}
