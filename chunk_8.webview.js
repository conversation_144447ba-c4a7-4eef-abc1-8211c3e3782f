__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/button/button": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            className: new Array(1),
            hoverStopPropagation: new Array(1),
            sendMessageTitle: new Array(1),
            showMessageCard: new Array(1),
            hoverStayTime: new Array(1),
            hoverClass: new Array(1),
            tId: new Array(1),
            hoverStartTime: new Array(1),
            sendMessageImg: new Array(1),
            type: new Array(1),
            lang: new Array(1),
            openType: new Array(1),
            appParameter: new Array(1),
            sessionFrom: new Array(1),
            disabled: new Array(3),
            ariaLabel: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            customDataset: new Array(1),
            sendMessagePath: new Array(1),
          },
          K = U === true,
          d,
          f,
          g = (C, T, E, B, F, S, J) => {
            var $A = I(f);
            if (f && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix + "__icon " + D.prefix + "-class-icon",
                    ariaHidden: true,
                    name: D.iconName,
                  },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        {
                          tClass: !!(U.classPrefix || U.prefix) || undefined,
                          name: U.iconName,
                        },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          e = (C, T, E, B, F, S, J) => {
            if (d === 1) {
              f = "icon";
              B(f, g);
            }
          },
          h,
          j = (C) => {},
          i = (C, T, E) => {
            if (h === 1) {
              E(
                "t-loading",
                {},
                (N, C) => {
                  if (C || K || !!Z(U.loadingProps, "delay") || undefined)
                    O(N, "delay", X(D.loadingProps).delay || 0);
                  if (C || K || !!Z(U.loadingProps, "duration") || undefined)
                    O(N, "duration", X(D.loadingProps).duration || 800);
                  if (C || K || !!Z(U.loadingProps, "indicator") || undefined)
                    O(N, "indicator", X(D.loadingProps).indicator || true);
                  if (
                    C ||
                    K ||
                    !!Z(U.loadingProps, "inheritColor") ||
                    undefined
                  )
                    O(
                      N,
                      "inheritColor",
                      X(D.loadingProps).inheritColor || true
                    );
                  if (C || K || !!Z(U.loadingProps, "layout") || undefined)
                    O(N, "layout", X(D.loadingProps).layout || "horizontal");
                  if (C || K || !!Z(U.loadingProps, "pause") || undefined)
                    O(N, "pause", X(D.loadingProps).pause || false);
                  if (C || K || !!Z(U.loadingProps, "progress") || undefined)
                    O(N, "progress", X(D.loadingProps).progress || 0);
                  if (C || K || !!Z(U.loadingProps, "reverse") || undefined)
                    O(N, "reverse", X(D.loadingProps).reverse || false);
                  if (C || K || !!Z(U.loadingProps, "size") || undefined)
                    O(N, "size", X(D.loadingProps).size || "40rpx");
                  if (C || K || !!Z(U.loadingProps, "text") || undefined)
                    O(N, "text", X(D.loadingProps).text || "");
                  if (C || K || !!Z(U.loadingProps, "theme") || undefined)
                    O(N, "theme", X(D.loadingProps).theme || "circular");
                  if (C) O(N, "loading", true);
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    O(
                      N,
                      "t-class",
                      Y(D.classPrefix) +
                        "__loading " +
                        Y(D.classPrefix) +
                        "__loading--wrapper"
                    );
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    O(
                      N,
                      "t-class-indicator",
                      Y(D.classPrefix) +
                        "__loading--indicator " +
                        Y(D.prefix) +
                        "-class-loading"
                    );
                },
                j
              );
            }
          },
          l,
          m = (C, T) => {
            if (l === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          k = (C, T, E, B, F, S) => {
            S("content");
            l = D.content ? 1 : 0;
            B(l, m);
            S("");
          },
          c = (C, T, E, B, F, S) => {
            d = D._icon ? 1 : 0;
            B(d, e);
            h = D.loading ? 1 : 0;
            B(h, i);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__content");
              },
              k
            );
            S("suffix");
          },
          b = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C || K || !!U.className || undefined)
                  L(N, "class " + Y(D.className));
                A["className"][0] = (D, E, T) => {
                  L(N, "class " + Y(D.className));
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                var $A = D.disabled || D.loading;
                if (
                  C ||
                  K ||
                  !!(U.disabled || U.loading) ||
                  ($A ? undefined : U.type)
                )
                  O(N, "form-type", $A ? "" : D.type);
                A["disabled"][0] = A["type"][0] = (D, E, T) => {
                  var $B = D.disabled || D.loading;
                  O(N, "form-type", $B ? "" : D.type);
                  E(N);
                };
                var $B = D.disabled || D.loading;
                if (
                  C ||
                  K ||
                  !!(U.disabled || U.loading) ||
                  ($B ? undefined : U.openType)
                )
                  O(N, "open-type", $B ? "" : D.openType);
                A["disabled"][1] = A["openType"][0] = (D, E, T) => {
                  var $C = D.disabled || D.loading;
                  O(N, "open-type", $C ? "" : D.openType);
                  E(N);
                };
                if (C || K || U.hoverStopPropagation)
                  O(N, "hover-stop-propagation", D.hoverStopPropagation);
                A["hoverStopPropagation"][0] = (D, E, T) => {
                  O(N, "hover-stop-propagation", D.hoverStopPropagation);
                  E(N);
                };
                if (C || K || U.hoverStartTime)
                  O(N, "hover-start-time", D.hoverStartTime);
                A["hoverStartTime"][0] = (D, E, T) => {
                  O(N, "hover-start-time", D.hoverStartTime);
                  E(N);
                };
                if (C || K || U.hoverStayTime)
                  O(N, "hover-stay-time", D.hoverStayTime);
                A["hoverStayTime"][0] = (D, E, T) => {
                  O(N, "hover-stay-time", D.hoverStayTime);
                  E(N);
                };
                if (C || K || U.lang) O(N, "lang", D.lang);
                A["lang"][0] = (D, E, T) => {
                  O(N, "lang", D.lang);
                  E(N);
                };
                if (C || K || U.sessionFrom)
                  O(N, "session-from", D.sessionFrom);
                A["sessionFrom"][0] = (D, E, T) => {
                  O(N, "session-from", D.sessionFrom);
                  E(N);
                };
                var $C = D.disabled || D.loading;
                if (
                  C ||
                  K ||
                  !!(U.disabled || U.loading) ||
                  ($C
                    ? undefined
                    : !!(U.hoverClass || U.classPrefix) || undefined)
                )
                  O(
                    N,
                    "hover-class",
                    $C ? "" : D.hoverClass || D.classPrefix + "--hover"
                  );
                A["disabled"][2] = A["hoverClass"][0] = (D, E, T) => {
                  var $D = D.disabled || D.loading;
                  O(
                    N,
                    "hover-class",
                    $D ? "" : D.hoverClass || D.classPrefix + "--hover"
                  );
                  E(N);
                };
                if (C || K || U.sendMessageTitle)
                  O(N, "send-message-title", D.sendMessageTitle);
                A["sendMessageTitle"][0] = (D, E, T) => {
                  O(N, "send-message-title", D.sendMessageTitle);
                  E(N);
                };
                if (C || K || U.sendMessagePath)
                  O(N, "send-message-path", D.sendMessagePath);
                A["sendMessagePath"][0] = (D, E, T) => {
                  O(N, "send-message-path", D.sendMessagePath);
                  E(N);
                };
                if (C || K || U.sendMessageImg)
                  O(N, "send-message-img", D.sendMessageImg);
                A["sendMessageImg"][0] = (D, E, T) => {
                  O(N, "send-message-img", D.sendMessageImg);
                  E(N);
                };
                if (C || K || U.appParameter)
                  O(N, "app-parameter", D.appParameter);
                A["appParameter"][0] = (D, E, T) => {
                  O(N, "app-parameter", D.appParameter);
                  E(N);
                };
                if (C || K || U.showMessageCard)
                  O(N, "show-message-card", D.showMessageCard);
                A["showMessageCard"][0] = (D, E, T) => {
                  O(N, "show-message-card", D.showMessageCard);
                  E(N);
                };
                if (C || K || U.ariaLabel) O(N, "aria-label", D.ariaLabel);
                A["ariaLabel"][0] = (D, E, T) => {
                  O(N, "aria-label", D.ariaLabel);
                  E(N);
                };
                if (C || K || U.customDataset)
                  R.d(N, "custom", D.customDataset);
                A["customDataset"][0] = (D, E, T) => {
                  R.d(N, "custom", D.customDataset);
                  E(N);
                };
                if (C) R.v(N, "tap", "handleTap", !0, !1, !1, !1);
                if (C) R.v(N, "getuserinfo", "getuserinfo", !1, !1, !1, !1);
                if (C) R.v(N, "contact", "contact", !1, !1, !1, !1);
                if (C)
                  R.v(N, "getphonenumber", "getphonenumber", !1, !1, !1, !1);
                if (C) R.v(N, "error", "error", !1, !1, !1, !1);
                if (C) R.v(N, "opensetting", "opensetting", !1, !1, !1, !1);
                if (C) R.v(N, "launchapp", "launchapp", !1, !1, !1, !1);
                if (C) R.v(N, "chooseavatar", "chooseavatar", !1, !1, !1, !1);
                if (C)
                  R.v(
                    N,
                    "agreeprivacyauthorization",
                    "agreeprivacyauthorization",
                    !1,
                    !1,
                    !1,
                    !1
                  );
                if (C || K || U.tId) R.i(N, D.tId);
                A["tId"][0] = (D, E, T) => {
                  R.i(N, D.tId);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/button/button.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-button--size-extra-small{font-size:var(--td-button-extra-small-font-size,var(--td-font-size-base,",
        [0, 28],
        "));height:var(--td-button-extra-small-height,",
        [0, 56],
        ");line-height:var(--td-button-extra-small-height,",
        [0, 56],
        ");padding-left:var(--td-button-extra-small-padding-horizontal,",
        [0, 16],
        ");padding-right:var(--td-button-extra-small-padding-horizontal,",
        [0, 16],
        ")}\n.",
        [1],
        "t-button--size-extra-small .",
        [1],
        "t-button__icon{font-size:var(--td-button-extra-small-icon-font-size,",
        [0, 36],
        ")}\n.",
        [1],
        "t-button--size-small{font-size:var(--td-button-small-font-size,var(--td-font-size-base,",
        [0, 28],
        "));height:var(--td-button-small-height,",
        [0, 64],
        ");line-height:var(--td-button-small-height,",
        [0, 64],
        ");padding-left:var(--td-button-small-padding-horizontal,",
        [0, 24],
        ");padding-right:var(--td-button-small-padding-horizontal,",
        [0, 24],
        ")}\n.",
        [1],
        "t-button--size-small .",
        [1],
        "t-button__icon{font-size:var(--td-button-small-icon-font-size,",
        [0, 36],
        ")}\n.",
        [1],
        "t-button--size-medium{font-size:var(--td-button-medium-font-size,var(--td-font-size-m,",
        [0, 32],
        "));height:var(--td-button-medium-height,",
        [0, 80],
        ");line-height:var(--td-button-medium-height,",
        [0, 80],
        ");padding-left:var(--td-button-medium-padding-horizontal,",
        [0, 32],
        ");padding-right:var(--td-button-medium-padding-horizontal,",
        [0, 32],
        ")}\n.",
        [1],
        "t-button--size-medium .",
        [1],
        "t-button__icon{font-size:var(--td-button-medium-icon-font-size,",
        [0, 40],
        ")}\n.",
        [1],
        "t-button--size-large{font-size:var(--td-button-large-font-size,var(--td-font-size-m,",
        [0, 32],
        "));height:var(--td-button-large-height,",
        [0, 96],
        ");line-height:var(--td-button-large-height,",
        [0, 96],
        ");padding-left:var(--td-button-large-padding-horizontal,",
        [0, 40],
        ");padding-right:var(--td-button-large-padding-horizontal,",
        [0, 40],
        ")}\n.",
        [1],
        "t-button--size-large .",
        [1],
        "t-button__icon{font-size:var(--td-button-large-icon-font-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-button--default{background-color:var(--td-button-default-bg-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));border-color:var(--td-button-default-border-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));color:var(--td-button-default-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-button--default::after{border-color:var(--td-button-default-border-color,var(--td-bg-color-component,var(--td-gray-color-3,#e7e7e7)));border-width:var(--td-button-border-width,",
        [0, 4],
        ")}\n.",
        [1],
        "t-button--default.",
        [1],
        "t-button--hover{z-index:0}\n.",
        [1],
        "t-button--default.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--default.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-default-active-bg-color,var(--td-bg-color-component-active,var(--td-gray-color-6,#a6a6a6)));border-color:var(--td-button-default-active-border-color,var(--td-bg-color-component-active,var(--td-gray-color-6,#a6a6a6)))}\n.",
        [1],
        "t-button--default.",
        [1],
        "t-button--disabled{background-color:var(--td-button-default-disabled-bg,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)));color:var(--td-button-default-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-button--default.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--default.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-default-disabled-border-color,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)))}\n.",
        [1],
        "t-button--primary{background-color:var(--td-button-primary-bg-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-color:var(--td-button-primary-border-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));color:var(--td-button-primary-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-button--primary::after{border-color:var(--td-button-primary-border-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-width:var(--td-button-border-width,",
        [0, 4],
        ")}\n.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--hover{z-index:0}\n.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-primary-active-bg-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)));border-color:var(--td-button-primary-active-border-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled{background-color:var(--td-button-primary-disabled-bg,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)));color:var(--td-button-primary-disabled-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-primary-disabled-border-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-button--light{background-color:var(--td-button-light-bg-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));border-color:var(--td-button-light-border-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));color:var(--td-button-light-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-button--light::after{border-color:var(--td-button-light-border-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));border-width:var(--td-button-border-width,",
        [0, 4],
        ")}\n.",
        [1],
        "t-button--light.",
        [1],
        "t-button--hover{z-index:0}\n.",
        [1],
        "t-button--light.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--light.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-light-active-bg-color,var(--td-brand-color-light-active,var(--td-primary-color-2,#d9e1ff)));border-color:var(--td-button-light-active-border-color,var(--td-brand-color-light-active,var(--td-primary-color-2,#d9e1ff)))}\n.",
        [1],
        "t-button--light.",
        [1],
        "t-button--disabled{background-color:var(--td-button-light-disabled-bg,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));color:var(--td-button-light-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-button--light.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--light.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-light-disabled-border-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)))}\n.",
        [1],
        "t-button--danger{background-color:var(--td-button-danger-bg-color,var(--td-error-color,var(--td-error-color-6,#d54941)));border-color:var(--td-button-danger-border-color,var(--td-error-color,var(--td-error-color-6,#d54941)));color:var(--td-button-danger-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-button--danger::after{border-color:var(--td-button-danger-border-color,var(--td-error-color,var(--td-error-color-6,#d54941)));border-width:var(--td-button-border-width,",
        [0, 4],
        ")}\n.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--hover{z-index:0}\n.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-danger-active-bg-color,var(--td-error-color-active,var(--td-error-color-7,#ad352f)));border-color:var(--td-button-danger-active-border-color,var(--td-error-color-active,var(--td-error-color-7,#ad352f)))}\n.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled{background-color:var(--td-button-danger-disabled-bg,var(--td-error-color-3,#ffb9b0));color:var(--td-button-danger-disabled-color,var(--td-font-white-1,#fff))}\n.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-danger-disabled-border-color,var(--td-error-color-3,#ffb9b0))}\n.",
        [1],
        "t-button{-webkit-tap-highlight-color:transparent;-webkit-align-items:center;align-items:center;-webkit-appearance:none;background-image:none;border-radius:var(--td-button-border-radius,var(--td-radius-default,",
        [0, 12],
        "));box-sizing:border-box;cursor:pointer;display:-webkit-inline-flex;display:inline-flex;font-family:PingFang SC,Microsoft YaHei,Arial Regular;font-weight:var(--td-button-font-weight,600);-webkit-justify-content:center;justify-content:center;outline:0;position:relative;text-align:center;touch-action:manipulation;transition:all .3s;-webkit-user-select:none;user-select:none;vertical-align:top;white-space:nowrap}\n.",
        [1],
        "t-button::after{border-radius:calc(var(--td-button-border-radius,var(--td-radius-default,",
        [0, 12],
        ")) * 2)}\n.",
        [1],
        "t-button--text{background-color:initial;color:var(--td-button-default-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-button--text,.",
        [1],
        "t-button--text::after{border:0}\n.",
        [1],
        "t-button--text.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--text.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-default-text-active-bg-color,var(--td-bg-color-container-active,var(--td-gray-color-3,#e7e7e7)))}\n.",
        [1],
        "t-button--text.",
        [1],
        "t-button--primary{background-color:initial;color:var(--td-button-primary-text-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-button--text.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--text.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-primary-text-active-bg-color,var(--td-bg-color-container-active,var(--td-gray-color-3,#e7e7e7)))}\n.",
        [1],
        "t-button--text.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled{background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff));color:var(--td-button-primary-text-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-button--text.",
        [1],
        "t-button--danger{background-color:initial;color:var(--td-button-danger-text-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-button--text.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--text.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-danger-text-active-bg-color,var(--td-bg-color-container-active,var(--td-gray-color-3,#e7e7e7)))}\n.",
        [1],
        "t-button--text.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled{background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff));color:var(--td-button-danger-text-disabled-color,var(--td-button-danger-disabled-color,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-button--text.",
        [1],
        "t-button--light{background-color:initial;color:var(--td-button-light-text-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-button--text.",
        [1],
        "t-button--light.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--text.",
        [1],
        "t-button--light.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-light-text-active-bg-color,var(--td-bg-color-container-active,var(--td-gray-color-3,#e7e7e7)))}\n.",
        [1],
        "t-button--text.",
        [1],
        "t-button--disabled{color:var(--td-button-default-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-button--outline{background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff));color:var(--td-button-default-outline-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-button--outline,.",
        [1],
        "t-button--outline::after{border-color:var(--td-button-default-outline-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-default-outline-active-bg-color,var(--td-bg-color-container-active,var(--td-gray-color-3,#e7e7e7)));border-color:var(--td-button-default-outline-active-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--disabled{color:var(--td-button-default-outline-disabled-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-default-outline-disabled-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--primary{color:var(--td-button-primary-outline-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--primary,.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--primary::after{border-color:var(--td-button-primary-outline-border-color,var(--td-button-primary-outline-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9))))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--hover{color:var(--td-button-primary-outline-active-border-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-primary-outline-active-bg-color,var(--td-bg-color-container-active,var(--td-gray-color-3,#e7e7e7)));border-color:var(--td-button-primary-outline-active-border-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled{background-color:initial;color:var(--td-button-primary-outline-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-primary-outline-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--danger{color:var(--td-button-danger-outline-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--danger,.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--danger::after{border-color:var(--td-button-danger-outline-border-color,var(--td-button-danger-outline-color,var(--td-error-color,var(--td-error-color-6,#d54941))))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--hover{color:var(--td-button-danger-outline-active-border-color,var(--td-error-color-active,var(--td-error-color-7,#ad352f)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-danger-outline-active-bg-color,var(--td-bg-color-container-active,var(--td-gray-color-3,#e7e7e7)));border-color:var(--td-button-danger-outline-active-border-color,var(--td-error-color-active,var(--td-error-color-7,#ad352f)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled{background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff));color:var(--td-button-danger-outline-disabled-color,var(--td-error-color-3,#ffb9b0))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-danger-outline-disabled-color,var(--td-error-color-3,#ffb9b0))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--light{background-color:var(--td-button-light-outline-bg-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)));color:var(--td-button-light-outline-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--light,.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--light::after{border-color:var(--td-button-light-outline-border-color,var(--td-button-light-outline-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9))))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--light.",
        [1],
        "t-button--hover{color:var(--td-button-light-outline-active-border-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--light.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--light.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-light-outline-active-bg-color,var(--td-brand-color-light-active,var(--td-primary-color-2,#d9e1ff)));border-color:var(--td-button-light-outline-active-border-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--light.",
        [1],
        "t-button--disabled{background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff));color:var(--td-button-light-outline-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--light.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--outline.",
        [1],
        "t-button--light.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-light-outline-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-button--dashed{background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff));border-style:dashed;border-width:",
        [0, 2],
        "}\n.",
        [1],
        "t-button--dashed::after{border:0}\n.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--hover::after{background-color:var(--td-button-default-outline-active-bg-color,var(--td-bg-color-container-active,var(--td-gray-color-3,#e7e7e7)));border-color:var(--td-button-default-outline-active-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)))}\n.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--primary{color:var(--td-button-primary-dashed-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--primary,.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--primary::after{border-color:var(--td-button-primary-dashed-border-color,var(--td-button-primary-dashed-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9))))}\n.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled{background-color:var(--td-bg-color-container,var(--td-font-white-1,#fff));color:var(--td-button-primary-dashed-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-primary-dashed-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--danger{color:var(--td-button-danger-dashed-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--danger,.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--danger::after{border-color:var(--td-button-danger-dashed-border-color,var(--td-button-danger-dashed-color,var(--td-error-color,var(--td-error-color-6,#d54941))))}\n.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled{background-color:initial;color:var(--td-button-danger-dashed-disabled-color,var(--td-button-danger-disabled-color,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-button--dashed.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-danger-dashed-disabled-color,var(--td-button-danger-disabled-color,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-button--ghost{background-color:initial;color:var(--td-button-ghost-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-button--ghost,.",
        [1],
        "t-button--ghost::after{border-color:var(--td-button-ghost-border-color,var(--td-button-ghost-color,var(--td-text-color-anti,var(--td-font-white-1,#fff))))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--default.",
        [1],
        "t-button--hover{color:var(--td-button-ghost-hover-color,var(--td-font-white-2,hsla(0,0%,100%,.55)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--default.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--default.",
        [1],
        "t-button--hover::after{background-color:initial;border-color:var(--td-button-ghost-hover-color,var(--td-font-white-2,hsla(0,0%,100%,.55)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary{color:var(--td-button-ghost-primary-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary::after{border-color:var(--td-button-ghost-primary-border-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--hover{color:var(--td-button-ghost-primary-hover-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--hover::after{background-color:initial;border-color:var(--td-button-ghost-primary-hover-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--text.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--text.",
        [1],
        "t-button--hover::after{background-color:var(--td-gray-color-10,#4b4b4b)}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled{background-color:initial;color:var(--td-button-ghost-disabled-color,var(--td-font-white-4,hsla(0,0%,100%,.22)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--primary.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-ghost-disabled-color,var(--td-font-white-4,hsla(0,0%,100%,.22)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger{color:var(--td-button-ghost-danger-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger::after{border-color:var(--td-button-ghost-danger-border-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--hover{color:var(--td-button-ghost-danger-hover-color,var(--td-error-color-active,var(--td-error-color-7,#ad352f)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--hover::after{background-color:initial;border-color:var(--td-button-ghost-danger-hover-color,var(--td-error-color-active,var(--td-error-color-7,#ad352f)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--text.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--text.",
        [1],
        "t-button--hover::after{background-color:var(--td-gray-color-10,#4b4b4b)}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled{background-color:initial;color:var(--td-button-ghost-disabled-color,var(--td-font-white-4,hsla(0,0%,100%,.22)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--danger.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-ghost-disabled-color,var(--td-font-white-4,hsla(0,0%,100%,.22)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--default.",
        [1],
        "t-button--text.",
        [1],
        "t-button--hover,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--default.",
        [1],
        "t-button--text.",
        [1],
        "t-button--hover::after{background-color:var(--td-gray-color-10,#4b4b4b)}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--default.",
        [1],
        "t-button--disabled{background-color:initial;color:var(--td-button-ghost-disabled-color,var(--td-font-white-4,hsla(0,0%,100%,.22)))}\n.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--default.",
        [1],
        "t-button--disabled,.",
        [1],
        "t-button--ghost.",
        [1],
        "t-button--default.",
        [1],
        "t-button--disabled::after{border-color:var(--td-button-ghost-disabled-color,var(--td-font-white-4,hsla(0,0%,100%,.22)))}\n.",
        [1],
        "t-button__icon+.",
        [1],
        "t-button__content:not(:empty),.",
        [1],
        "t-button__loading+.",
        [1],
        "t-button__content:not(:empty){margin-left:",
        [0, 8],
        "}\n.",
        [1],
        "t-button__icon{border-radius:var(--td-button-icon-border-radius,",
        [0, 8],
        ")}\n.",
        [1],
        "t-button--round.",
        [1],
        "t-button--size-large{border-radius:calc(var(--td-button-large-height,",
        [0, 96],
        ")/ 2)}\n.",
        [1],
        "t-button--round.",
        [1],
        "t-button--size-large::after{border-radius:var(--td-button-large-height,",
        [0, 96],
        ")}\n.",
        [1],
        "t-button--round.",
        [1],
        "t-button--size-medium{border-radius:calc(var(--td-button-medium-height,",
        [0, 80],
        ")/ 2)}\n.",
        [1],
        "t-button--round.",
        [1],
        "t-button--size-medium::after{border-radius:var(--td-button-medium-height,",
        [0, 80],
        ")}\n.",
        [1],
        "t-button--round.",
        [1],
        "t-button--size-small{border-radius:calc(var(--td-button-small-height,",
        [0, 64],
        ")/ 2)}\n.",
        [1],
        "t-button--round.",
        [1],
        "t-button--size-small::after{border-radius:var(--td-button-small-height,",
        [0, 64],
        ")}\n.",
        [1],
        "t-button--round.",
        [1],
        "t-button--size-extra-small{border-radius:calc(var(--td-button-extra-small-height,",
        [0, 56],
        ")/ 2)}\n.",
        [1],
        "t-button--round.",
        [1],
        "t-button--size-extra-small::after{border-radius:var(--td-button-extra-small-height,",
        [0, 56],
        ")}\n.",
        [1],
        "t-button--square{padding:0}\n.",
        [1],
        "t-button--square.",
        [1],
        "t-button--size-large{width:var(--td-button-large-height,",
        [0, 96],
        ")}\n.",
        [1],
        "t-button--square.",
        [1],
        "t-button--size-medium{width:var(--td-button-medium-height,",
        [0, 80],
        ")}\n.",
        [1],
        "t-button--square.",
        [1],
        "t-button--size-small{width:var(--td-button-small-height,",
        [0, 64],
        ")}\n.",
        [1],
        "t-button--square.",
        [1],
        "t-button--size-extra-small{width:var(--td-button-extra-small-height,",
        [0, 56],
        ")}\n.",
        [1],
        "t-button--circle{border-radius:50%;padding:0}\n.",
        [1],
        "t-button--circle.",
        [1],
        "t-button--size-large{width:var(--td-button-large-height,",
        [0, 96],
        ")}\n.",
        [1],
        "t-button--circle.",
        [1],
        "t-button--size-large::after{border-radius:50%}\n.",
        [1],
        "t-button--circle.",
        [1],
        "t-button--size-medium{width:var(--td-button-medium-height,",
        [0, 80],
        ")}\n.",
        [1],
        "t-button--circle.",
        [1],
        "t-button--size-medium::after{border-radius:50%}\n.",
        [1],
        "t-button--circle.",
        [1],
        "t-button--size-small{width:var(--td-button-small-height,",
        [0, 64],
        ")}\n.",
        [1],
        "t-button--circle.",
        [1],
        "t-button--size-small::after{border-radius:50%}\n.",
        [1],
        "t-button--circle.",
        [1],
        "t-button--size-extra-small{width:var(--td-button-extra-small-height,",
        [0, 56],
        ")}\n.",
        [1],
        "t-button--circle.",
        [1],
        "t-button--size-extra-small::after{border-radius:50%}\n.",
        [1],
        "t-button--block{display:-webkit-flex;display:flex;width:100%}\n.",
        [1],
        "t-button--disabled{cursor:not-allowed}\n.",
        [1],
        "t-button__loading--wrapper{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
        [1],
        "t-button.",
        [1],
        "t-button--hover::after{z-index:-1}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/button/button.wxss" }
    );
}
