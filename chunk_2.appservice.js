__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "components/service-card-my/service-card-my": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          g = (C) => {},
          f = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || Z(U.MyserviceData, "thumbnail"))
                  O(N, "src", X(D.MyserviceData).thumbnail);
              },
              g
            );
          },
          j = (C, T) => {
            C || K || Z(U.MyserviceData, "name")
              ? T(Y(X(D.MyserviceData).name))
              : T();
          },
          i = (C, T, E) => {
            E("text", {}, (N, C) => {}, j);
          },
          l = (C, T) => {
            C || K || Z(U.MyserviceData, "description")
              ? T(Y(X(D.MyserviceData).description))
              : T();
          },
          k = (C, T, E) => {
            E("text", {}, (N, C) => {}, l);
          },
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_title");
              },
              i
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_ms");
              },
              k
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_img");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info");
                if (C) R.v(N, "tap", "onServiceClick", !1, !1, !1, !1);
              },
              h
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top");
              },
              e
            );
          },
          o,
          q = (C, T) => {
            var $A = X(D.MyserviceData).audit_status === "pending";
            C ||
            K ||
            !!Z(U.MyserviceData, "audit_status") ||
            ($A ? undefined : undefined)
              ? T(Y($A ? "待审核" : "审核不通过"))
              : T();
          },
          r = (C, T) => {
            var $A = X(D.MyserviceData).publish_status === "online";
            C ||
            K ||
            !!Z(U.MyserviceData, "publish_status") ||
            ($A ? undefined : undefined)
              ? T(Y($A ? "已上线" : "已下架"))
              : T();
          },
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  var $A = X(D.MyserviceData).audit_status === "pending";
                  if (
                    C ||
                    K ||
                    !!Z(U.MyserviceData, "audit_status") ||
                    ($A ? undefined : undefined)
                  )
                    O(N, "theme", $A ? "warning" : "danger");
                },
                q
              );
            } else {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  var $A = X(D.MyserviceData).publish_status === "online";
                  if (
                    C ||
                    K ||
                    !!Z(U.MyserviceData, "publish_status") ||
                    ($A ? undefined : undefined)
                  )
                    O(N, "theme", $A ? "success" : "warning");
                },
                r
              );
            }
          },
          n = (C, T, E, B) => {
            o = X(D.MyserviceData).audit_status !== "approved" ? 1 : 0;
            B(o, p);
          },
          t = (C, T) => {
            var $A = X(D.MyserviceData).price;
            C ||
            K ||
            !!Z(U.MyserviceData, "price") ||
            ($A ? Z(U.MyserviceData, "price") : undefined)
              ? T(Y($A ? X(D.MyserviceData).price : "面议"))
              : T();
          },
          s = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_middle_price");
              },
              t
            );
          },
          m = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_bottom");
              },
              n
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_middle");
              },
              s
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right");
              },
              m
            );
          },
          w = (C, T) => {
            C || K || !!Z(U.MyserviceData, "ongoing_order_count") || undefined
              ? T(Y("进行中：" + Y(X(D.MyserviceData).ongoing_order_count)))
              : T();
          },
          v = (C, T, E) => {
            E("text", {}, (N, C) => {}, w);
          },
          y = (C, T) => {
            C ? T("编辑") : T();
          },
          x = (C, T, E) => {
            E("text", {}, (N, C) => {}, y);
          },
          z,
          C0 = (C, T) => {
            var $A = D.isOfflineLoading;
            var $B = D.isOnlineLoading;
            var $C = X(D.MyserviceData).publish_status === "online";
            C ||
            K ||
            !!U.isOfflineLoading ||
            ($A
              ? undefined
              : !!U.isOnlineLoading ||
                ($B
                  ? undefined
                  : !!Z(U.MyserviceData, "publish_status") ||
                    ($C ? undefined : undefined)))
              ? T(Y($A ? "下架中..." : $B ? "上架中..." : $C ? "下架" : "上架"))
              : T();
          },
          B0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                var $A = D.isOfflineLoading || D.isOnlineLoading;
                if (
                  C ||
                  K ||
                  !!(U.isOfflineLoading || U.isOnlineLoading) ||
                  ($A ? undefined : undefined)
                )
                  L(N, $A ? "loading" : "");
              },
              C0
            );
          },
          E0 = (C, T) => {
            C ? T("下架") : T();
          },
          D0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "disabled");
              },
              E0
            );
          },
          A0 = (C, T, E) => {
            if (z === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "caozuo_item");
                  if (C) R.v(N, "tap", "onPublishStatusClick", !1, !1, !1, !1);
                },
                B0
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "caozuo_item");
                },
                D0
              );
            }
          },
          u = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "caozuo_item");
                if (C) R.v(N, "tap", "onOrderClick", !1, !1, !1, !1);
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "caozuo_item");
                if (C) R.v(N, "tap", "onEditClick", !1, !1, !1, !1);
              },
              x
            );
            z = X(D.MyserviceData).audit_status === "approved" ? 1 : 0;
            B(z, A0);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fuwu_list");
              },
              c
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fuwu_caozuo");
              },
              u
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "my_service");
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "components/user-order-card/user-order-card": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          e = (C) => {},
          f = (C, T) => {
            C || K || Z(Z(U.orderData, "service_provider"), "nickname")
              ? T(Y(X(X(D.orderData).service_provider).nickname))
              : T();
          },
          g = (C, T) => {
            C ||
            K ||
            !!(
              Z(Z(Z(U.orderData, "service_provider"), "address"), "building") ||
              Z(Z(Z(U.orderData, "service_provider"), "address"), "unit") ||
              Z(Z(Z(U.orderData, "service_provider"), "address"), "room")
            ) ||
            undefined
              ? T(
                  Y(
                    Y(X(X(X(D.orderData).service_provider).address).building) +
                      "-" +
                      Y(X(X(X(D.orderData).service_provider).address).unit) +
                      "-" +
                      Y(X(X(X(D.orderData).service_provider).address).room)
                  )
                )
              : T();
          },
          d = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) L(N, "avatar");
                if (C || K || Z(Z(U.orderData, "service_provider"), "avatar"))
                  O(N, "src", X(X(D.orderData).service_provider).avatar);
              },
              e
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "name");
              },
              f
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "id");
              },
              g
            );
          },
          i = (C, T) => {
            var $A =
              X(X(D.orderData).service_provider).auth_status === "verified";
            var $B =
              X(X(D.orderData).service_provider).neighbor_type === "owner";
            var $C =
              X(X(D.orderData).service_provider).neighbor_type === "tenant";
            var $D =
              X(X(D.orderData).service_provider).neighbor_type === "resident";
            var $E =
              X(X(D.orderData).service_provider).auth_status === "unverified";
            C ||
            K ||
            !!Z(Z(U.orderData, "service_provider"), "auth_status") ||
            ($A
              ? !!Z(Z(U.orderData, "service_provider"), "neighbor_type") ||
                ($B
                  ? undefined
                  : !!Z(Z(U.orderData, "service_provider"), "neighbor_type") ||
                    ($C
                      ? undefined
                      : !!Z(
                          Z(U.orderData, "service_provider"),
                          "neighbor_type"
                        ) || ($D ? undefined : undefined)))
              : !!Z(Z(U.orderData, "service_provider"), "auth_status") ||
                ($E ? undefined : undefined))
              ? T(
                  Y(
                    $A
                      ? $B
                        ? "业主"
                        : $C
                        ? "租客"
                        : $D
                        ? "住户"
                        : "其他"
                      : $E
                      ? "未认证"
                      : "信息不全"
                  )
                )
              : T();
          },
          h = (C, T, E) => {
            E(
              "t-tag",
              {},
              (N, C) => {
                if (C) L(N, "margin-16");
                if (C) O(N, "variant", "light");
                var $A =
                  X(X(D.orderData).service_provider).auth_status === "verified";
                var $B =
                  X(X(D.orderData).service_provider).auth_status ===
                  "unverified";
                if (
                  C ||
                  K ||
                  !!Z(Z(U.orderData, "service_provider"), "auth_status") ||
                  ($A
                    ? undefined
                    : !!Z(Z(U.orderData, "service_provider"), "auth_status") ||
                      ($B ? undefined : undefined))
                )
                  O(N, "theme", $A ? "success" : $B ? "warning" : "default");
              },
              i
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top_left");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top_right");
              },
              h
            );
          },
          l = (C) => {},
          k = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) L(N, "service-icon");
                if (C || K || Z(Z(U.orderData, "service"), "thumbnail"))
                  O(N, "src", X(X(D.orderData).service).thumbnail);
              },
              l
            );
          },
          o = (C, T) => {
            C || K || Z(Z(U.orderData, "service"), "name")
              ? T(Y(X(X(D.orderData).service).name))
              : T();
          },
          n = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service-title");
              },
              o
            );
          },
          p,
          s = (C, T) => {
            C || K || Z(Z(U.orderData, "service"), "description")
              ? T(Y(X(X(D.orderData).service).description))
              : T();
          },
          r = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service-description");
              },
              s
            );
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "attachment-box");
                },
                r
              );
            }
          },
          m = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service-title-box");
              },
              n
            );
            p = X(X(D.orderData).service).description ? 1 : 0;
            B(p, q);
          },
          v,
          x = (C) => {},
          w = (C, T, E) => {
            if (v === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (C) L(N, "price-symbol");
                },
                x
              );
            }
          },
          y = (C, T) => {
            var $A = X(X(D.orderData).service).price !== null;
            C ||
            K ||
            !!Z(Z(U.orderData, "service"), "price") ||
            ($A ? Z(Z(U.orderData, "service"), "price") : undefined)
              ? T(Y($A ? X(X(D.orderData).service).price : "面议"))
              : T();
          },
          u = (C, T, E, B) => {
            v = X(X(D.orderData).service).price !== null ? 1 : 0;
            B(v, w);
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "price-value");
              },
              y
            );
          },
          t = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "price-box");
              },
              u
            );
          },
          j = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_left");
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_midle");
              },
              m
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_right");
              },
              t
            );
          },
          B0 = (C, T) => {
            C ? T("联系商家") : T();
          },
          A0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, B0);
          },
          D0 = (C, T) => {
            C ||
            K ||
            !!Z(Z(U.orderData, "order_statistics"), "in_progress_count") ||
            undefined
              ? T(
                  Y(
                    "进行中：" +
                      Y(X(X(D.orderData).order_statistics).in_progress_count)
                  )
                )
              : T();
          },
          C0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, D0);
          },
          F0 = (C, T) => {
            C ||
            K ||
            !!Z(Z(U.orderData, "order_statistics"), "completed_count") ||
            undefined
              ? T(
                  Y(
                    "已完成：" +
                      Y(X(X(D.orderData).order_statistics).completed_count)
                  )
                )
              : T();
          },
          E0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, F0);
          },
          z = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "contact-btn");
                if (C) R.v(N, "tap", "onContactCustomer", !1, !1, !1, !1);
              },
              A0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "contact-btn");
                if (C) R.d(N, "status", "in_progress");
                if (C) R.v(N, "tap", "onNavigateToOrderList", !1, !1, !1, !1);
              },
              C0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "contact-btn");
                if (C) R.d(N, "status", "completed");
                if (C) R.v(N, "tap", "onNavigateToOrderList", !1, !1, !1, !1);
              },
              E0
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top");
              },
              c
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info");
                if (C)
                  R.v(N, "tap", "onNavigateToServiceDetail", !1, !1, !1, !1);
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_bottom");
              },
              z
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list");
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "pages/my/my": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "我的");
                if (C || K || undefined) O(N, "back", false);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          g = (C) => {},
          f = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || !!Z(U.myInfo, "avatar") || undefined)
                  O(
                    N,
                    "src",
                    X(D.myInfo).avatar || "/assets/tx/时尚职业男.png"
                  );
              },
              g
            );
          },
          i = (C, T) => {
            C || K || !!Z(U.myInfo, "nickname") || undefined
              ? T(Y(X(D.myInfo).nickname || "暂无昵称"))
              : T();
          },
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box_midle_username");
              },
              i
            );
          },
          l,
          n = (C, T) => {
            C || K || Z(Z(U.myInfo, "neighbor_type"), "label")
              ? T(Y(X(X(D.myInfo).neighbor_type).label))
              : T();
          },
          o = (C, T) => {
            C || K || Z(Z(U.myInfo, "auth_status"), "label")
              ? T(Y(X(X(D.myInfo).auth_status).label))
              : T();
          },
          p = (C, T) => {
            C || K || Z(Z(U.myInfo, "auth_status"), "label")
              ? T(Y(X(X(D.myInfo).auth_status).label))
              : T();
          },
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  if (C) O(N, "theme", "success");
                },
                n
              );
            } else if (l === 2) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  if (C) O(N, "theme", "warning");
                },
                o
              );
            } else if (l === 3) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  if (C) O(N, "theme", "danger");
                },
                p
              );
            }
          },
          k = (C, T, E, B) => {
            l =
              X(X(D.myInfo).auth_status).value === "verified"
                ? 1
                : X(X(D.myInfo).auth_status).value === "incomplete"
                ? 2
                : X(X(D.myInfo).auth_status).value === "unverified"
                ? 3
                : 0;
            B(l, m);
          },
          q = (C, T) => {
            C ? T("编辑") : T();
          },
          j = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box_right_tag");
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "edit-button");
                if (C) O(N, "bindtap", "navigateToEditProfile");
              },
              q
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box_left");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box_midle");
              },
              h
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box_right");
              },
              j
            );
          },
          u = (C) => {},
          v = (C) => {},
          t = (C, T, E) => {
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "我的服务");
                if (C) O(N, "value", "0");
              },
              u
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "预约记录");
                if (C) O(N, "value", "1");
              },
              v
            );
          },
          s = (C, T, E) => {
            E(
              "t-tabs",
              {},
              (N, C) => {
                if (C || K || undefined) O(N, "defaultValue", 0);
                if (C) O(N, "t-class", "custom-tabs");
                if (C) O(N, "t-class-track", "custom-track");
                if (C) R.v(N, "change", "onTabsChange", !1, !1, !1, !1);
                if (C) R.v(N, "click", "onTabsClick", !1, !1, !1, !1);
              },
              t
            );
          },
          r = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "tags_box");
              },
              s
            );
          },
          x,
          A0,
          D0 = (C, T) => {
            C ? T("加载中...") : T();
          },
          C0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, D0);
          },
          F0 = (C, T) => {
            C ? T("暂无发布的服务") : T();
          },
          E0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, F0);
          },
          H0 = (C, I0, J0, K0, L0, M0, T, E) => {
            var N0 = (C) => {};
            E(
              "service-card-my",
              {},
              (N, C) => {
                if (C || K || K0) O(N, "MyserviceData", I0);
              },
              N0
            );
          },
          G0 = (C, T, E, B, F) => {
            F(
              D.userServices,
              "id",
              U ? U.userServices : undefined,
              [0, "userServices"],
              H0
            );
          },
          B0 = (C, T, E) => {
            if (A0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                C0
              );
            } else if (A0 === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty-state");
                },
                E0
              );
            } else {
              E("view", {}, (N, C) => {}, G0);
            }
          },
          z = (C, T, E, B) => {
            A0 = D.loading ? 1 : X(D.userServices).length === 0 ? 2 : 0;
            B(A0, B0);
          },
          y = (C, T, E) => {
            if (x === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                z
              );
            }
          },
          I0,
          L0 = (C, M0, N0, O0, P0, Q0, T, E) => {
            var R0 = (C) => {};
            E(
              "user-order-card",
              {},
              (N, C) => {
                if (C || K || O0) O(N, "orderData", M0);
                if (C) R.v(N, "contact", "handleContact", !1, !1, !1, !1);
                if (C) R.v(N, "confirm", "handleConfirm", !1, !1, !1, !1);
                if (C)
                  R.v(
                    N,
                    "orderStatusChanged",
                    "handleOrderStatusChanged",
                    !1,
                    !1,
                    !1,
                    !1
                  );
              },
              R0
            );
          },
          M0,
          P0 = (C, T) => {
            C ? T("暂无预约记录") : T();
          },
          O0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, P0);
          },
          N0 = (C, T, E) => {
            if (M0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty-state");
                },
                O0
              );
            }
          },
          Q0,
          T0 = (C, T) => {
            C ? T("加载中...") : T();
          },
          S0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, T0);
          },
          R0 = (C, T, E) => {
            if (Q0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-state");
                },
                S0
              );
            }
          },
          K0 = (C, T, E, B, F) => {
            F(
              D.userOrders,
              "order_id",
              U ? U.userOrders : undefined,
              [0, "userOrders"],
              L0
            );
            M0 = X(D.userOrders).length === 0 && !D.loading ? 1 : 0;
            B(M0, N0);
            Q0 = D.loading ? 1 : 0;
            B(Q0, R0);
          },
          J0 = (C, T, E) => {
            if (I0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                K0
              );
            }
          },
          w = (C, T, E, B) => {
            x = D.activeTab === 0 ? 1 : 0;
            B(x, y);
            I0 = D.activeTab === 1 ? 1 : 0;
            B(I0, J0);
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "dt_box");
              },
              r
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "dt_box");
              },
              w
            );
          },
          a = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "components/service-card-my/service-card-my";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "components/service-card-my/service-card-my.js";
define(
  "components/service-card-my/service-card-my.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      t = require("../../@babel/runtime/helpers/asyncToGenerator");
    Component({
      properties: {
        MyserviceData: {
          type: Object,
          value: {
            id: 0,
            name: "默认标题",
            description: "默认描述",
            thumbnail: "/assets/img/汉堡包.png",
            price: 0,
            order_count: 0,
            ongoing_order_count: 0,
            positive_rate: 0,
            publish_status: "online",
            audit_status: "approved",
            category: { id: 1, name: "默认分类" },
            createdAt: "",
            updatedAt: "",
          },
        },
      },
      data: { isOfflineLoading: !1, isOnlineLoading: !1 },
      methods: {
        onServiceClick: function () {
          var e = this;
          this.navigating ||
            ((this.navigating = !0),
            wx.navigateTo({
              url: "/pages/service-info/service-info?id=".concat(
                this.data.MyserviceData.id
              ),
              complete: function () {
                e.navigating = !1;
              },
            }));
        },
        onOrderClick: function () {
          var e = this;
          this.navigating ||
            ((this.navigating = !0),
            wx.navigateTo({
              url: "/pages/service-order-list/service-order-list?serviceId=".concat(
                this.data.MyserviceData.id
              ),
              complete: function () {
                e.navigating = !1;
              },
            }));
        },
        onEditClick: function () {
          var e = this;
          this.navigating ||
            ((this.navigating = !0),
            wx.navigateTo({
              url: "/pages/edit-service/edit-service?id=".concat(
                this.data.MyserviceData.id
              ),
              complete: function () {
                e.navigating = !1;
              },
            }));
        },
        onPublishStatusClick: function () {
          var e = this.data.MyserviceData;
          "approved" === e.audit_status
            ? "online" === e.publish_status
              ? this.offlineService()
              : this.onlineService()
            : wx.showToast({ title: "服务未通过审核，无法操作", icon: "none" });
        },
        onlineService: function () {
          var a = this;
          return t(
            e().mark(function t() {
              var i, n, r;
              return e().wrap(
                function (e) {
                  for (;;)
                    switch ((e.prev = e.next)) {
                      case 0:
                        if (
                          ((i = a.data.MyserviceData), !a.data.isOnlineLoading)
                        ) {
                          e.next = 3;
                          break;
                        }
                        return e.abrupt("return");
                      case 3:
                        return (
                          (e.next = 5),
                          new Promise(function (e) {
                            wx.showModal({
                              title: "确认上架",
                              content:
                                "确定要上架这个服务吗？上架后用户将可以看到并预订此服务。",
                              success: e,
                            });
                          })
                        );
                      case 5:
                        if (e.sent.confirm) {
                          e.next = 8;
                          break;
                        }
                        return e.abrupt("return");
                      case 8:
                        if (
                          (a.setData({ isOnlineLoading: !0 }),
                          (e.prev = 9),
                          (n = getApp()).globalData.openid)
                        ) {
                          e.next = 13;
                          break;
                        }
                        throw new Error("用户未登录，请重新登录");
                      case 13:
                        return (
                          (e.next = 15),
                          n.call({
                            path: "/api/mp/services/".concat(i.id, "/online"),
                            method: "PUT",
                            data: { openid: n.globalData.openid },
                          })
                        );
                      case 15:
                        if (0 !== (r = e.sent).code) {
                          e.next = 22;
                          break;
                        }
                        a.setData({
                          "MyserviceData.publish_status": "online",
                          "MyserviceData.updatedAt": r.data.updatedAt,
                        }),
                          wx.showToast({ title: "上架成功", icon: "success" }),
                          a.triggerEvent("statusChanged", {
                            serviceId: i.id,
                            newStatus: "online",
                            serviceData: r.data,
                          }),
                          (e.next = 23);
                        break;
                      case 22:
                        throw new Error(r.message || "上架失败");
                      case 23:
                        e.next = 29;
                        break;
                      case 25:
                        (e.prev = 25),
                          (e.t0 = e.catch(9)),
                          console.error("上架服务失败：", e.t0),
                          wx.showToast({
                            title: e.t0.message || "上架失败，请重试",
                            icon: "error",
                          });
                      case 29:
                        return (
                          (e.prev = 29),
                          a.setData({ isOnlineLoading: !1 }),
                          e.finish(29)
                        );
                      case 32:
                      case "end":
                        return e.stop();
                    }
                },
                t,
                null,
                [[9, 25, 29, 32]]
              );
            })
          )();
        },
        offlineService: function () {
          var a = this;
          return t(
            e().mark(function t() {
              var i, n, r;
              return e().wrap(
                function (e) {
                  for (;;)
                    switch ((e.prev = e.next)) {
                      case 0:
                        if (
                          ((i = a.data.MyserviceData), !a.data.isOfflineLoading)
                        ) {
                          e.next = 3;
                          break;
                        }
                        return e.abrupt("return");
                      case 3:
                        return (
                          (e.next = 5),
                          new Promise(function (e) {
                            wx.showModal({
                              title: "确认下架",
                              content:
                                "确定要下架这个服务吗？下架后用户将无法看到此服务。",
                              success: e,
                            });
                          })
                        );
                      case 5:
                        if (e.sent.confirm) {
                          e.next = 8;
                          break;
                        }
                        return e.abrupt("return");
                      case 8:
                        if (
                          (a.setData({ isOfflineLoading: !0 }),
                          (e.prev = 9),
                          (n = getApp()).globalData.openid)
                        ) {
                          e.next = 13;
                          break;
                        }
                        throw new Error("用户未登录，请重新登录");
                      case 13:
                        return (
                          (e.next = 15),
                          n.call({
                            path: "/api/mp/services/".concat(i.id, "/offline"),
                            method: "DELETE",
                            data: { openid: n.globalData.openid },
                          })
                        );
                      case 15:
                        if (0 !== (r = e.sent).code) {
                          e.next = 22;
                          break;
                        }
                        a.setData({
                          "MyserviceData.publish_status": "offline",
                          "MyserviceData.updatedAt": r.data.updatedAt,
                        }),
                          wx.showToast({ title: "下架成功", icon: "success" }),
                          a.triggerEvent("statusChanged", {
                            serviceId: i.id,
                            newStatus: "offline",
                            serviceData: r.data,
                          }),
                          (e.next = 23);
                        break;
                      case 22:
                        throw new Error(r.message || "下架失败");
                      case 23:
                        e.next = 29;
                        break;
                      case 25:
                        (e.prev = 25),
                          (e.t0 = e.catch(9)),
                          console.error("下架服务失败：", e.t0),
                          wx.showToast({
                            title: e.t0.message || "下架失败，请重试",
                            icon: "error",
                          });
                      case 29:
                        return (
                          (e.prev = 29),
                          a.setData({ isOfflineLoading: !1 }),
                          e.finish(29)
                        );
                      case 32:
                      case "end":
                        return e.stop();
                    }
                },
                t,
                null,
                [[9, 25, 29, 32]]
              );
            })
          )();
        },
      },
    });
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "components/service-card-my/service-card-my.js",
  }
);
require("components/service-card-my/service-card-my.js");
__wxRoute = "components/user-order-card/user-order-card";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "components/user-order-card/user-order-card.js";
define(
  "components/user-order-card/user-order-card.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Component({
      properties: {
        orderData: {
          type: Object,
          value: {
            order_id: "",
            order_status: "",
            order_time: "",
            accept_time: "",
            cancel_time: null,
            complete_time: null,
            remark: "",
            service: {
              service_id: "",
              name: "",
              thumbnail: "",
              description: "",
              price: "0.00",
            },
            service_provider: {
              user_id: "",
              nickname: "",
              avatar: "",
              contact_info: "",
              auth_status: "",
              neighbor_type: "",
              address: { building: "", unit: "", room: "" },
              order_statistics: { in_progress: 0, completed: 0 },
            },
          },
        },
      },
      methods: {
        onContactCustomer: function () {
          var e = this.properties.orderData.service_provider.contact_info;
          e
            ? wx.makePhoneCall({
                phoneNumber: e,
                fail: function (e) {
                  wx.showToast({ title: "拨号失败", icon: "error" });
                },
              })
            : wx.showToast({ title: "暂无联系方式", icon: "none" });
        },
        onNavigateToServiceDetail: function () {
          var e = this.properties.orderData.service.service_id;
          e
            ? wx.navigateTo({
                url: "/pages/service-info/service-info?id=".concat(e),
                fail: function (e) {
                  console.error("跳转失败：", e),
                    wx.showToast({ title: "跳转失败", icon: "error" });
                },
              })
            : wx.showToast({ title: "服务信息不完整", icon: "none" });
        },
        onNavigateToOrderList: function (e) {
          var r = this.properties.orderData,
            t = r.service.service_id,
            o = r.service.name;
          if (t) {
            var i = e.currentTarget.dataset.status;
            wx.navigateTo({
              url: "/pages/my-order-list/my-order-list?serviceId="
                .concat(t, "&serviceName=")
                .concat(encodeURIComponent(o), "&status=")
                .concat(i),
              fail: function (e) {
                console.error("跳转失败：", e),
                  wx.showToast({ title: "跳转失败", icon: "error" });
              },
            });
          } else wx.showToast({ title: "服务信息不完整", icon: "none" });
        },
        onConfirmDelivery: function () {
          this.triggerEvent("confirmDelivery", {
            orderId: this.properties.orderData.order_id,
          });
        },
      },
    });
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "components/user-order-card/user-order-card.js",
  }
);
require("components/user-order-card/user-order-card.js");
__wxRoute = "pages/my/my";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/my/my.js";
define(
  "pages/my/my.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var e,
      r = require("../../@babel/runtime/helpers/defineProperty"),
      t = require("../../@babel/runtime/helpers/objectSpread2"),
      a = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      n = require("../../@babel/runtime/helpers/asyncToGenerator");
    Page(
      (r(
        (e = {
          data: {
            activeTab: 0,
            myInfo: {},
            userServices: [],
            userOrders: [],
            orderPagination: {
              current_page: 1,
              total_pages: 1,
              total_count: 0,
              limit: 10,
              has_next: !1,
              has_prev: !1,
            },
            pagination: {
              current_page: 1,
              total_pages: 1,
              total_count: 0,
              limit: 10,
              has_next: !1,
              has_prev: !1,
            },
            loading: !1,
          },
          onLoad: function (e) {
            this.loadUserInfo(), this.loadUserServices(), this.loadUserOrders();
          },
          onShow: function () {
            "function" == typeof this.getTabBar &&
              this.getTabBar() &&
              this.getTabBar().setData({ selected: 2 }),
              this.loadUserInfo(),
              this.loadUserServices(),
              this.loadUserOrders();
          },
          loadUserInfo: function () {
            var e = getApp();
            if (e.isUserLoggedIn()) {
              var r = e.globalData.userInfo.data || {};
              console.log("个人信息:", r), this.setData({ myInfo: r });
            } else console.log("用户未登录");
          },
          loadUserServices: function () {
            var e = arguments,
              r = this;
            return n(
              a().mark(function t() {
                var n, s, o, i, c, l, u, d;
                return a().wrap(
                  function (t) {
                    for (;;)
                      switch ((t.prev = t.next)) {
                        case 0:
                          if (
                            ((n = e.length > 0 && void 0 !== e[0] ? e[0] : 1),
                            (s = getApp()).isUserLoggedIn())
                          ) {
                            t.next = 5;
                            break;
                          }
                          return (
                            console.log("用户未登录，无法获取服务列表"),
                            t.abrupt("return")
                          );
                        case 5:
                          if (
                            ((o = s.globalData.userInfo.data || {}), (i = o.id))
                          ) {
                            t.next = 10;
                            break;
                          }
                          return (
                            console.log("用户ID不存在"), t.abrupt("return")
                          );
                        case 10:
                          return (
                            r.setData({ loading: !0 }),
                            (t.prev = 11),
                            (t.next = 14),
                            s.call({
                              path: "/api/mp/services/user/".concat(i),
                              method: "GET",
                              data: { page: n, limit: r.data.pagination.limit },
                            })
                          );
                        case 14:
                          (c = t.sent),
                            console.log("用户服务列表获取成功:", c),
                            0 === c.code && c.data
                              ? ((l = c.data),
                                (u = l.services),
                                (d = l.pagination),
                                r.setData({
                                  userServices: u || [],
                                  pagination: d || r.data.pagination,
                                  loading: !1,
                                }))
                              : (console.error("获取服务列表失败:", c.message),
                                wx.showToast({
                                  title: c.message || "获取服务列表失败",
                                  icon: "error",
                                }),
                                r.setData({ loading: !1 })),
                            (t.next = 24);
                          break;
                        case 19:
                          (t.prev = 19),
                            (t.t0 = t.catch(11)),
                            console.error("获取用户服务列表失败:", t.t0),
                            wx.showToast({
                              title: "网络请求失败",
                              icon: "error",
                            }),
                            r.setData({ loading: !1 });
                        case 24:
                        case "end":
                          return t.stop();
                      }
                  },
                  t,
                  null,
                  [[11, 19]]
                );
              })
            )();
          },
          refreshUserServices: function () {
            var e = this;
            return n(
              a().mark(function r() {
                return a().wrap(function (r) {
                  for (;;)
                    switch ((r.prev = r.next)) {
                      case 0:
                        return (r.next = 2), e.loadUserServices(1);
                      case 2:
                      case "end":
                        return r.stop();
                    }
                }, r);
              })
            )();
          },
          loadMoreServices: function () {
            var e = this;
            return n(
              a().mark(function r() {
                var t;
                return a().wrap(function (r) {
                  for (;;)
                    switch ((r.prev = r.next)) {
                      case 0:
                        if (!e.data.loading && e.data.pagination.has_next) {
                          r.next = 2;
                          break;
                        }
                        return r.abrupt("return");
                      case 2:
                        return (
                          (t = e.data.pagination.current_page + 1),
                          (r.next = 5),
                          e.loadUserServices(t)
                        );
                      case 5:
                      case "end":
                        return r.stop();
                    }
                }, r);
              })
            )();
          },
          onTabsClick: function (e) {
            var r = e.detail.value;
            this.setData({ activeTab: Number(r) });
          },
          navigateToEditProfile: function () {
            wx.navigateTo({ url: "/pages/edit-profile/edit-profile" });
          },
          loadUserOrders: function () {
            var e = arguments,
              r = this;
            return n(
              a().mark(function t() {
                var n, s, o, i, c, l, u, d, p, f;
                return a().wrap(
                  function (t) {
                    for (;;)
                      switch ((t.prev = t.next)) {
                        case 0:
                          if (
                            ((n = e.length > 0 && void 0 !== e[0] ? e[0] : 1),
                            (s = e.length > 1 && void 0 !== e[1] ? e[1] : ""),
                            (o = getApp()).isUserLoggedIn())
                          ) {
                            t.next = 6;
                            break;
                          }
                          return (
                            console.log("用户未登录，无法获取订单列表"),
                            t.abrupt("return")
                          );
                        case 6:
                          if (
                            ((i = o.globalData.userInfo.data || {}),
                            (c = i.id),
                            console.log("111", c),
                            c)
                          ) {
                            t.next = 12;
                            break;
                          }
                          return (
                            console.log("用户ID不存在"), t.abrupt("return")
                          );
                        case 12:
                          return (
                            r.setData({ loading: !0 }),
                            (t.prev = 13),
                            (l = "/api/mp/serviceOrder/user/".concat(c)),
                            (u = []),
                            n > 1 && u.push("page=".concat(n)),
                            s && u.push("order_status=".concat(s)),
                            u.length > 0 && (l += "?" + u.join("&")),
                            (t.next = 21),
                            o.call({ path: l, method: "GET" })
                          );
                        case 21:
                          (d = t.sent),
                            console.log("用户订单列表获取成功:", d),
                            0 === d.code && d.data
                              ? ((p = d.data.services),
                                (f = d.data.service_count),
                                console.log("订单列表111:", p),
                                r.setData({
                                  userOrders: p || [],
                                  "orderPagination.total_count": f || 0,
                                  loading: !1,
                                }))
                              : (console.error("获取订单列表失败:", d.message),
                                wx.showToast({
                                  title: d.message || "获取订单列表失败",
                                  icon: "error",
                                }),
                                r.setData({ loading: !1 })),
                            (t.next = 31);
                          break;
                        case 26:
                          (t.prev = 26),
                            (t.t0 = t.catch(13)),
                            console.error("获取用户订单列表失败:", t.t0),
                            wx.showToast({
                              title: "网络请求失败",
                              icon: "error",
                            }),
                            r.setData({ loading: !1 });
                        case 31:
                        case "end":
                          return t.stop();
                      }
                  },
                  t,
                  null,
                  [[13, 26]]
                );
              })
            )();
          },
          refreshUserOrders: function () {
            var e = this;
            return n(
              a().mark(function r() {
                return a().wrap(function (r) {
                  for (;;)
                    switch ((r.prev = r.next)) {
                      case 0:
                        return (r.next = 2), e.loadUserOrders(1);
                      case 2:
                      case "end":
                        return r.stop();
                    }
                }, r);
              })
            )();
          },
          filterOrdersByStatus: function (e) {
            var r = this;
            return n(
              a().mark(function t() {
                return a().wrap(function (t) {
                  for (;;)
                    switch ((t.prev = t.next)) {
                      case 0:
                        return (t.next = 2), r.loadUserOrders(1, e);
                      case 2:
                      case "end":
                        return t.stop();
                    }
                }, t);
              })
            )();
          },
          handleContact: function (e) {
            var r = e.detail;
            console.log("联系商家:", r);
          },
          handleConfirm: function (e) {
            var r = e.detail;
            console.log("确认送达:", r);
          },
          handleOrderStatusChanged: function (e) {
            var r = e.detail,
              a = r.orderId,
              n = r.status,
              s = r.data;
            console.log("订单状态变更:", { orderId: a, status: n, data: s });
            var o = this.data.userOrders.map(function (e) {
              return e.order_id === a
                ? t(
                    t(
                      t({}, e),
                      {},
                      { order_status: n },
                      "cancelled" === n && { cancel_time: s.cancel_time }
                    ),
                    "completed" === n && { complete_time: s.complete_time }
                  )
                : e;
            });
            this.setData({ userOrders: o });
          },
          onTabsChange: function (e) {
            var r = e.detail.value;
            this.setData({ activeTab: Number(r) }),
              0 === r
                ? this.refreshUserServices()
                : 3 === r && this.refreshUserOrders();
          },
        }),
        "onTabsClick",
        function (e) {
          var r = e.detail.value;
          this.setData({ activeTab: Number(r) });
        }
      ),
      r(e, "navigateToEditProfile", function () {
        wx.navigateTo({ url: "/pages/edit-profile/edit-profile" });
      }),
      r(e, "onPullDownRefresh", function () {
        var e = this;
        return n(
          a().mark(function r() {
            return a().wrap(
              function (r) {
                for (;;)
                  switch ((r.prev = r.next)) {
                    case 0:
                      if (
                        (console.log("用户触发下拉刷新"),
                        (r.prev = 1),
                        e.loadUserInfo(),
                        0 !== e.data.activeTab)
                      ) {
                        r.next = 8;
                        break;
                      }
                      return (r.next = 6), e.refreshUserServices();
                    case 6:
                      r.next = 11;
                      break;
                    case 8:
                      if (1 !== e.data.activeTab) {
                        r.next = 11;
                        break;
                      }
                      return (r.next = 11), e.refreshUserOrders();
                    case 11:
                      wx.showToast({
                        title: "刷新成功",
                        icon: "success",
                        duration: 1500,
                      }),
                        (r.next = 18);
                      break;
                    case 14:
                      (r.prev = 14),
                        (r.t0 = r.catch(1)),
                        console.error("下拉刷新失败:", r.t0),
                        wx.showToast({
                          title: "刷新失败",
                          icon: "error",
                          duration: 1500,
                        });
                    case 18:
                      return (
                        (r.prev = 18), wx.stopPullDownRefresh(), r.finish(18)
                      );
                    case 21:
                    case "end":
                      return r.stop();
                  }
              },
              r,
              null,
              [[1, 14, 18, 21]]
            );
          })
        )();
      }),
      e)
    );
  },
  { isPage: true, isComponent: true, currentFile: "pages/my/my.js" }
);
require("pages/my/my.js");
