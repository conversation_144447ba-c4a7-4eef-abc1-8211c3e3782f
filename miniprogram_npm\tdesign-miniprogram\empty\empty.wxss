@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-empty {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
}
.t-empty__icon {
  color: var(
    --td-empty-icon-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  font-size: 192rpx;
}
.t-empty__thumb + .t-empty__description:not(:empty) {
  margin-top: var(--td-empty-description-margin-top, var(--td-spacer-2, 32rpx));
}
.t-empty__description {
  color: var(
    --td-empty-description-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  font-size: var(
    --td-empty-description-font-size,
    var(--td-font-size-base, 28rpx)
  );
  line-height: var(--td-empty-description-line-height, 44rpx);
  text-align: center;
  white-space: pre-wrap;
}
.t-empty__description + .t-empty__actions:not(:empty) {
  margin-top: var(--td-empty-action-margin-top, var(--td-spacer-4, 64rpx));
}
