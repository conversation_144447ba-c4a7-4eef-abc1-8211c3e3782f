__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/collapse/collapse": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            theme: new Array(1),
            customStyle: new Array(1),
            border: new Array(1),
            classPrefix: new Array(1),
            prefix: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.border]), U.theme])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          ["hairline--top-bottom", D.border],
                          D.theme,
                        ])
                      )
                  );
                A["prefix"][0] =
                  A["classPrefix"][0] =
                  A["border"][0] =
                  A["theme"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        "class " +
                          Y(D.prefix) +
                          "-class " +
                          Y(
                            P(X(a).cls)(D.classPrefix, [
                              ["hairline--top-bottom", D.border],
                              D.theme,
                            ])
                          )
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/collapse/collapse";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/collapse/collapse.js";
define(
  "miniprogram_npm/tdesign-miniprogram/collapse/collapse.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      i = require("../common/src/index"),
      s = l(require("../common/config")),
      u = l(require("./props"));
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = s.default.prefix,
      p = "".concat(o, "-collapse"),
      c = (function (n) {
        t(s, n);
        var i = a(s);
        function s() {
          var e;
          return (
            r(this, s),
            ((e = i.apply(this, arguments)).externalClasses = [
              "".concat(o, "-class"),
            ]),
            (e.relations = {
              "../collapse-panel/collapse-panel": { type: "descendant" },
            }),
            (e.controlledProps = [{ key: "value", event: "change" }]),
            (e.properties = u.default),
            (e.data = { prefix: o, classPrefix: p }),
            (e.observers = {
              "value, expandMutex ": function () {
                this.updateExpanded();
              },
            }),
            (e.methods = {
              updateExpanded: function () {
                var e = this;
                this.$children.forEach(function (r) {
                  r.updateExpanded(e.properties.value);
                });
              },
              switch: function (e) {
                var r,
                  t = this.properties,
                  a = t.expandMutex,
                  n = t.value;
                (r =
                  n.indexOf(e) > -1
                    ? n.filter(function (r) {
                        return r !== e;
                      })
                    : a
                    ? [e]
                    : n.concat(e)),
                  this._trigger("change", { value: r });
              },
            }),
            e
          );
        }
        return e(s);
      })(i.SuperComponent),
      d = (c = (0, n.__decorate)([(0, i.wxComponent)()], c));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/collapse/collapse.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/collapse/collapse.js");
