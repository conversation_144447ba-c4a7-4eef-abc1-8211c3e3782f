Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  r = require("../../../@babel/runtime/helpers/classCallCheck"),
  t = require("../../../@babel/runtime/helpers/inherits"),
  i = require("../../../@babel/runtime/helpers/createSuper"),
  u = require("tslib"),
  a = require("../common/src/index"),
  s = o(require("../common/config")),
  n = o(require("./props"));
function o(e) {
  return e && e.__esModule ? e : { default: e };
}
var l = s.default.prefix,
  c = (function (u) {
    t(s, u);
    var a = i(s);
    function s() {
      var e;
      return (
        r(this, s),
        ((e = a.apply(this, arguments)).externalClasses = []),
        (e.properties = n.default),
        (e.data = { prefix: l }),
        (e.relations = {
          "../col/col": {
            type: "child",
            linked: function (e) {
              var r = this.data.gutter;
              r && e.setData({ gutter: r });
            },
          },
        }),
        (e.observers = {
          gutter: function () {
            this.setGutter();
          },
        }),
        (e.methods = {
          setGutter: function () {
            var e = this.data.gutter;
            this.$children.forEach(function (r) {
              r.setData({ gutter: e });
            });
          },
        }),
        e
      );
    }
    return e(s);
  })(a.SuperComponent),
  p = (c = (0, u.__decorate)([(0, a.wxComponent)()], c));
exports.default = p;
