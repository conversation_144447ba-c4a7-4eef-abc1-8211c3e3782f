Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/classCallCheck"),
  t = require("../../../@babel/runtime/helpers/createClass"),
  s = (function () {
    function s() {
      e(this, s), (this.listeners = new Map()), (this.emitted = new Set());
    }
    return (
      t(s, [
        {
          key: "on",
          value: function (e, t) {
            if (this.emitted.has(e)) t();
            else {
              var s = this.listeners.get(e) || [];
              s.push(t), this.listeners.set(e, s);
            }
          },
        },
        {
          key: "emit",
          value: function (e) {
            var t = this.listeners.get(e);
            t &&
              t.forEach(function (e) {
                return e();
              }),
              this.emitted.add(e);
          },
        },
      ]),
      s
    );
  })();
exports.default = s;
