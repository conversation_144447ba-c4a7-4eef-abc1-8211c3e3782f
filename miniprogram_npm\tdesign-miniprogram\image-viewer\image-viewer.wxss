@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-image-viewer {
  bottom: 0;
  left: 0;
  overflow: hidden;
  position: fixed;
  right: 0;
  top: var(--td-image-viewer-top, var(--td-position-fixed-top, 0));
  transform: translateZ(0);
  z-index: 1001;
}
.t-image-viewer__mask {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1000;
}
.t-image-viewer__content {
  display: inline-block;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100vw;
  z-index: 1005;
}
.t-image-viewer .swiper {
  outline: 0;
}
.t-image-viewer__image {
  display: inline-block;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
}
.t-image-viewer .t-image--external {
  display: block;
  height: inherit;
  width: inherit;
}
.t-image-viewer__nav {
  align-items: center;
  background-color: var(
    --td-image-viewer-nav-bg-color,
    var(--td-font-gray-3, rgba(0, 0, 0, 0.4))
  );
  color: var(
    --td-image-viewer-nav-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
  display: -webkit-flex;
  display: flex;
  height: var(--td-image-viewer-nav-height, 96rpx);
  justify-content: space-between;
  left: 0;
  position: absolute;
  width: 100%;
  z-index: 1005;
}
.t-image-viewer__nav-close {
  margin-left: var(
    --td-image-viewer-close-margin-left,
    var(--td-spacer-1, 24rpx)
  );
}
.t-image-viewer__nav-close:empty {
  display: none;
}
.t-image-viewer__nav-delete {
  margin-right: var(
    --td-image-viewer-delete-margin-right,
    var(--td-spacer-1, 24rpx)
  );
}
.t-image-viewer__nav-delete:empty {
  display: none;
}
.t-image-viewer__nav-close,
.t-image-viewer__nav-delete {
  font-size: 48rpx;
}
.t-image-viewer__nav-index {
  flex: 1;
  font-size: var(
    --td-image-viewer-nav-index-font-size,
    var(--td-font-size-base, 28rpx)
  );
  text-align: center;
}
