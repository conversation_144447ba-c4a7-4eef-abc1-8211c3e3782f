@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.limit-title-row {
  -webkit-box-orient: vertical;
  display: -webkit-box;
  overflow: hidden;
}
.t-radio {
  background: var(
    --td-radio-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  display: -webkit-inline-flex;
  display: inline-flex;
  font-size: var(--td-radio-font-size, 32rpx);
  position: relative;
  vertical-align: middle;
}
.t-radio:focus {
  outline: 0;
}
.t-radio--block {
  display: -webkit-flex;
  display: flex;
  padding: var(--td-radio-vertical-padding, 32rpx);
}
.t-radio--right {
  flex-direction: row-reverse;
}
.t-radio__icon {
  color: var(
    --td-radio-icon-color,
    var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
  );
  font-size: var(--td-radio-icon-size, 48rpx);
  height: var(--td-radio-icon-size, 48rpx);
  margin-top: calc(
    (
        var(--td-radio-label-line-height, 48rpx) -
          var(--td-radio-icon-size, 48rpx)
      ) / 2
  );
  overflow: hidden;
  position: relative;
  width: var(--td-radio-icon-size, 48rpx);
}
.t-radio__icon:empty {
  display: none;
}
.t-radio__icon--left {
  margin-right: 16rpx;
}
.t-radio__icon--checked {
  color: var(
    --td-radio-icon-checked-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-radio__icon--disabled {
  color: var(
    --td-radio-icon-disabled-color,
    var(--td-brand-color-disabled, var(--td-primary-color-3, #b5c7ff))
  );
  cursor: not-allowed;
}
.t-radio__icon-circle {
  box-sizing: border-box;
  height: var(--td-radio-icon-size, 48rpx);
  width: var(--td-radio-icon-size, 48rpx);
}
.t-radio__icon-circle::after {
  border: 3px solid
    var(
      --td-radio-icon-color,
      var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
    );
  border-radius: 50%;
  box-sizing: border-box;
  content: "";
  height: calc(200% - 6rpx);
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.5);
  width: calc(200% - 6rpx);
}
.t-radio__icon-circle--disabled::after {
  background: var(
    --td-radio-icon-disabled-bg-color,
    var(--td-bg-color-component-disabled, var(--td-gray-color-2, #eee))
  );
}
.t-radio__icon-line::after,
.t-radio__icon-line::before {
  background: var(
    --td-radio-icon-checked-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-radius: 2rpx;
  content: "";
  display: block;
  position: absolute;
  transform-origin: top center;
  width: 5rpx;
}
.t-radio__icon-line::before {
  height: 16rpx;
  left: 8rpx;
  top: 22rpx;
  transform: rotate(-45deg);
}
.t-radio__icon-line::after {
  height: 26rpx;
  right: 8rpx;
  top: 14rpx;
  transform: rotate(45deg);
}
.t-radio__icon-line--disabled::after,
.t-radio__icon-line--disabled::before {
  background: var(
    --td-radio-icon-disabled-color,
    var(--td-brand-color-disabled, var(--td-primary-color-3, #b5c7ff))
  );
}
.t-radio__icon-dot {
  align-items: center;
  border: 6rpx solid
    var(
      --td-radio-icon-checked-color,
      var(--td-brand-color, var(--td-primary-color-7, #0052d9))
    );
  border-radius: 50%;
  box-sizing: border-box;
  display: -webkit-flex;
  display: flex;
  height: calc((var(--td-radio-icon-size, 48rpx) - 6rpx) * 2);
  justify-content: center;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.5);
  width: calc((var(--td-radio-icon-size, 48rpx) - 6rpx) * 2);
}
.t-radio__icon-dot::after {
  background: var(
    --td-radio-icon-checked-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-radius: 50%;
  content: "";
  display: block;
  height: var(--td-radio-icon-size, 48rpx);
  width: var(--td-radio-icon-size, 48rpx);
}
.t-radio__icon-dot--disabled {
  border-color: var(
    --td-radio-icon-disabled-color,
    var(--td-brand-color-disabled, var(--td-primary-color-3, #b5c7ff))
  );
}
.t-radio__icon-dot--disabled::after {
  background: var(
    --td-radio-icon-disabled-color,
    var(--td-brand-color-disabled, var(--td-primary-color-3, #b5c7ff))
  );
}
.t-radio__image {
  line-height: var(--td-radio-icon-size, 48rpx);
}
.t-radio-icon__image {
  height: var(--td-radio-icon-size, 48rpx);
  vertical-align: sub;
  width: var(--td-radio-icon-size, 48rpx);
}
.t-radio__content {
  flex: 1;
}
.t-radio__content:empty {
  display: none;
}
.t-radio__title {
  -webkit-box-orient: vertical;
  color: var(
    --td-radio-label-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-box;
  line-height: var(--td-radio-label-line-height, 48rpx);
  overflow: hidden;
}
.t-radio__title--checked {
  color: var(
    --td-radio-label-checked-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
}
.t-radio__title--disabled {
  color: var(
    --td-radio-label-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
  cursor: not-allowed;
}
.t-radio__description {
  -webkit-box-orient: vertical;
  color: var(
    --td-radio-content-color,
    var(--td-text-color-secondary, var(--td-font-gray-2, rgba(0, 0, 0, 0.6)))
  );
  display: -webkit-box;
  font-size: var(--td-radio-content-font-size, 28rpx);
  line-height: var(--td-radio-content-line-height, 44rpx);
  overflow: hidden;
}
.t-radio__description--checked {
  color: var(
    --td-radio-content-checked-color,
    var(--td-text-color-secondary, var(--td-font-gray-2, rgba(0, 0, 0, 0.6)))
  );
}
.t-radio__description--disabled {
  color: var(
    --td-radio-content-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
  cursor: not-allowed;
}
.t-radio__description:empty {
  display: none;
}
.t-radio__title + .t-radio__description {
  margin-top: 8rpx;
}
.t-radio__border {
  background: var(
    --td-radio-border-color,
    var(--td-component-stroke, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  height: 1px;
  left: 96rpx;
  position: absolute;
  right: 0;
  transform: scaleY(0.5);
}
.t-radio__border--right {
  left: 32rpx;
}
