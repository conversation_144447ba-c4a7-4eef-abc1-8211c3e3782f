__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/transition/transition": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            transitionClass: new Array(1),
            prefix: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            classPrefix: new Array(1),
            visible: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.prefix || U.classPrefix || U.transitionClass) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(D.classPrefix) +
                      " " +
                      Y(D.transitionClass)
                  );
                A["prefix"][0] =
                  A["classPrefix"][0] =
                  A["transitionClass"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        "class " +
                          Y(D.prefix) +
                          "-class " +
                          Y(D.classPrefix) +
                          " " +
                          Y(D.transitionClass)
                      );
                    };
                var $A = D.visible;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([
                      !!U.visible || ($A ? undefined : undefined),
                      U.style,
                      U.customStyle,
                    ])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([
                      $A ? "" : "display: none",
                      D.style,
                      D.customStyle,
                    ])
                  );
                A["visible"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      var $B = D.visible;
                      R.y(
                        N,
                        P(X(a)._style)([
                          $B ? "" : "display: none",
                          D.style,
                          D.customStyle,
                        ])
                      );
                    };
                if (C)
                  R.v(N, "transitionend", "onTransitionEnd", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/transition/transition.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-transition-enter{opacity:0}\n.",
      [1],
      "t-transition-enter-to{opacity:1;transition:opacity 1s}\n.",
      [1],
      "t-transition-leave{opacity:1}\n.",
      [1],
      "t-transition-leave-to{opacity:0;transition:opacity 1s}\n",
    ],
    undefined,
    { path: "./miniprogram_npm/tdesign-miniprogram/transition/transition.wxss" }
  );
}
