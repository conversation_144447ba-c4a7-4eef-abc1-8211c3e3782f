__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/collapse-panel/collapse-panel":
      (() => {
        var H = {};
        var S;
        var I = (P) => {
          if (!S) S = Object.assign({}, H);
          return S[P];
        };
        var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
        H[""] = (R, C, D, U) => {
          R.setFnFilter(Q.A, Q.B);
          if (typeof R.setEventListenerWrapper === "function")
            R.setEventListenerWrapper(Q.C);
          var L = R.c,
            M = R.m,
            O = R.r,
            A = {
              customStyle: new Array(1),
              classPrefix: new Array(7),
              style: new Array(1),
              headerRightContent: new Array(1),
              placement: new Array(4),
              prefix: new Array(3),
              headerLeftIcon: new Array(1),
              animation: new Array(1),
              content: new Array(1),
              ultimateDisabled: new Array(5),
              expanded: new Array(5),
              ultimateExpandIcon: new Array(1),
              header: new Array(1),
            },
            K = U === true,
            e = (C, T, E, B, F, S) => {
              S("header-left-icon", (N) => {}, "left-icon");
              S("header", (N) => {}, "title");
              S("header-right-content", (N) => {}, "note");
              S("expand-icon", (N) => {}, "right-icon");
            },
            d = (C, T, E) => {
              E(
                "t-cell",
                {},
                (N, C) => {
                  if (C || K || U.header) O(N, "title", D.header);
                  A["header"][0] = (D, E, T) => {
                    O(N, "title", D.header);
                    E(N);
                  };
                  if (C || K || U.headerRightContent)
                    O(N, "note", D.headerRightContent);
                  A["headerRightContent"][0] = (D, E, T) => {
                    O(N, "note", D.headerRightContent);
                    E(N);
                  };
                  if (C) O(N, "bordered", true);
                  if (C || K || U.headerLeftIcon)
                    O(N, "left-icon", D.headerLeftIcon);
                  A["headerLeftIcon"][0] = (D, E, T) => {
                    O(N, "left-icon", D.headerLeftIcon);
                    E(N);
                  };
                  var $A = D.ultimateExpandIcon;
                  var $B = D.expanded;
                  if (
                    C ||
                    K ||
                    !!U.ultimateExpandIcon ||
                    ($A
                      ? !!U.expanded || ($B ? undefined : undefined)
                      : undefined)
                  )
                    O(
                      N,
                      "right-icon",
                      $A ? ($B ? "chevron-up" : "chevron-down") : ""
                    );
                  A["ultimateExpandIcon"][0] = A["expanded"][1] = (D, E, T) => {
                    var $C = D.ultimateExpandIcon;
                    var $D = D.expanded;
                    O(
                      N,
                      "right-icon",
                      $C ? ($D ? "chevron-up" : "chevron-down") : ""
                    );
                    E(N);
                  };
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.placement, Q.a([U.expanded])]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "t-class",
                      Y(
                        P(X(a).cls)(D.classPrefix + "__header", [
                          D.placement,
                          ["expanded", D.expanded],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-header"
                    );
                  A["classPrefix"][3] =
                    A["placement"][1] =
                    A["expanded"][2] =
                    A["prefix"][1] =
                      (D, E, T) => {
                        O(
                          N,
                          "t-class",
                          Y(
                            P(X(a).cls)(D.classPrefix + "__header", [
                              D.placement,
                              ["expanded", D.expanded],
                            ])
                          ) +
                            " " +
                            Y(D.prefix) +
                            "-class-header"
                        );
                        E(N);
                      };
                  var $C = D.ultimateDisabled;
                  if (
                    C ||
                    K ||
                    !!(U.ultimateDisabled || ($C ? undefined : undefined)) ||
                    undefined
                  )
                    O(
                      N,
                      "t-class-title",
                      "class-title " + Y($C ? "class-title--disabled" : "")
                    );
                  A["ultimateDisabled"][1] = (D, E, T) => {
                    var $D = D.ultimateDisabled;
                    O(
                      N,
                      "t-class-title",
                      "class-title " + Y($D ? "class-title--disabled" : "")
                    );
                    E(N);
                  };
                  var $D = D.ultimateDisabled;
                  if (
                    C ||
                    K ||
                    !!(U.ultimateDisabled || ($D ? undefined : undefined)) ||
                    undefined
                  )
                    O(
                      N,
                      "t-class-note",
                      "class-note " + Y($D ? "class-note--disabled" : "")
                    );
                  A["ultimateDisabled"][2] = (D, E, T) => {
                    var $E = D.ultimateDisabled;
                    O(
                      N,
                      "t-class-note",
                      "class-note " + Y($E ? "class-note--disabled" : "")
                    );
                    E(N);
                  };
                  var $E = D.ultimateDisabled;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.placement ||
                      U.ultimateDisabled ||
                      ($E ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "t-class-right-icon",
                      "class-right-icon " +
                        Y(D.classPrefix) +
                        "__arrow--" +
                        Y(D.placement) +
                        " " +
                        Y($E ? "class-right-icon--disabled" : "")
                    );
                  A["classPrefix"][4] =
                    A["placement"][2] =
                    A["ultimateDisabled"][3] =
                      (D, E, T) => {
                        var $F = D.ultimateDisabled;
                        O(
                          N,
                          "t-class-right-icon",
                          "class-right-icon " +
                            Y(D.classPrefix) +
                            "__arrow--" +
                            Y(D.placement) +
                            " " +
                            Y($F ? "class-right-icon--disabled" : "")
                        );
                        E(N);
                      };
                  if (C) O(N, "t-class-hover", "class-header-hover");
                },
                e
              );
            },
            g = (C, T, E, B, F, S) => {
              C || K || U.content
                ? T(Y(D.content), (N) => {
                    A["content"][0] = (D, E, T) => {
                      T(N, Y(D.content));
                    };
                  })
                : T();
              S("");
              S("content");
            },
            f = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([U.ultimateDisabled]),
                        Q.a([U.expanded]),
                        U.placement,
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__content", [
                          ["disabled", D.ultimateDisabled],
                          ["expanded", D.expanded],
                          D.placement,
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-content"
                    );
                  A["classPrefix"][6] =
                    A["ultimateDisabled"][4] =
                    A["expanded"][4] =
                    A["placement"][3] =
                    A["prefix"][2] =
                      (D, E, T) => {
                        L(
                          N,
                          Y(
                            P(X(a).cls)(D.classPrefix + "__content", [
                              ["disabled", D.ultimateDisabled],
                              ["expanded", D.expanded],
                              D.placement,
                            ])
                          ) +
                            " " +
                            Y(D.prefix) +
                            "-class-content"
                        );
                      };
                },
                g
              );
            },
            c = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__title");
                  A["classPrefix"][2] = (D, E, T) => {
                    L(N, Y(D.classPrefix) + "__title");
                  };
                  if (C) O(N, "aria-role", "button");
                  if (C || K || U.expanded) O(N, "aria-expanded", D.expanded);
                  A["expanded"][0] = (D, E, T) => {
                    O(N, "aria-expanded", D.expanded);
                    E(N);
                  };
                  if (C || K || U.ultimateDisabled)
                    O(N, "aria-disabled", D.ultimateDisabled);
                  A["ultimateDisabled"][0] = (D, E, T) => {
                    O(N, "aria-disabled", D.ultimateDisabled);
                    E(N);
                  };
                  if (C) R.v(N, "tap", "onClick", !1, !1, !1, !1);
                },
                d
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__wrapper");
                  A["classPrefix"][5] = (D, E, T) => {
                    L(N, Y(D.classPrefix) + "__wrapper");
                  };
                  if (C || K || U.animation) O(N, "animation", D.animation);
                  A["animation"][0] = (D, E, T) => {
                    O(N, "animation", D.animation);
                    E(N);
                  };
                  var $A = D.expanded;
                  if (C || K || !!U.expanded || ($A ? undefined : undefined))
                    O(N, "aria-hidden", $A ? "" : true);
                  A["expanded"][3] = (D, E, T) => {
                    var $B = D.expanded;
                    O(N, "aria-hidden", $B ? "" : true);
                    E(N);
                  };
                },
                f
              );
            },
            b = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.placement ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      "class " +
                        Y(D.classPrefix) +
                        " " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.placement) +
                        " " +
                        Y(D.prefix) +
                        "-class"
                    );
                  A["classPrefix"][0] =
                    A["classPrefix"][1] =
                    A["placement"][0] =
                    A["prefix"][0] =
                      (D, E, T) => {
                        L(
                          N,
                          "class " +
                            Y(D.classPrefix) +
                            " " +
                            Y(D.classPrefix) +
                            "--" +
                            Y(D.placement) +
                            " " +
                            Y(D.prefix) +
                            "-class"
                        );
                      };
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  };
                },
                c
              );
            };
          return { C: b, B: A };
        };
        return Object.assign(
          function (R) {
            return H[R];
          },
          { _: H }
        );
      })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/collapse-panel/collapse-panel.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-collapse-panel{background-color:var(--td-collapse-panel-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)))}\n.",
      [1],
      "t-collapse-panel--disabled{pointer-events:none}\n.",
      [1],
      "t-collapse-panel--disabled .",
      [1],
      "t-collapse-panel__content,.",
      [1],
      "t-collapse-panel--disabled .",
      [1],
      "t-collapse-panel__header{opacity:.3}\n.",
      [1],
      "t-collapse-panel--top{display:-webkit-flex;display:flex;-webkit-flex-direction:column-reverse;flex-direction:column-reverse}\n.",
      [1],
      "t-collapse-panel__header{-webkit-align-items:center;align-items:center;color:var(--td-collapse-header-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex;height:var(--td-collapse-header-height,auto);-webkit-justify-content:space-between;justify-content:space-between;padding-left:var(--td-collapse-horizontal-padding,",
      [0, 32],
      ");position:relative}\n.",
      [1],
      "t-collapse-panel__header--top{position:relative}\n.",
      [1],
      "t-collapse-panel__header--top::after{background-color:var(--td-collapse-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));bottom:unset;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
      [1],
      "t-collapse-panel__header--bottom{position:relative}\n.",
      [1],
      "t-collapse-panel__header--bottom::after{background-color:var(--td-collapse-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
      [1],
      "t-collapse-panel__header::after{left:",
      [0, 32],
      "}\n.",
      [1],
      "t-collapse-panel__header-right{-webkit-align-items:center;align-items:center;display:-webkit-inline-flex;display:inline-flex;height:100%}\n.",
      [1],
      "t-collapse-panel__header-icon{color:var(--td-collapse-icon-color,var(--td-font-gray-3,rgba(0,0,0,.4)));height:100%;padding-left:8px;padding-right:8px;width:44px}\n.",
      [1],
      "t-collapse-panel__extra{font-size:var(--td-collapse-extra-font-size,var(--td-font-size-m,",
      [0, 32],
      "))}\n.",
      [1],
      "t-collapse-panel__body{position:relative}\n.",
      [1],
      "t-collapse-panel__body::after{background-color:var(--td-collapse-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
      [1],
      "t-collapse-panel__wrapper{height:0;overflow:hidden}\n.",
      [1],
      "t-collapse-panel__content{color:var(--td-collapse-content-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:var(--td-collapse-content-font-size,var(--td-font-size-base,",
      [0, 28],
      "));line-height:var(--td-collapse-content-line-height,1.5);padding:var(--td-collapse-content-padding,",
      [0, 32],
      ")}\n.",
      [1],
      "t-collapse-panel__content--disabled{color:var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26)))}\n.",
      [1],
      "t-collapse-panel__content--expanded.",
      [1],
      "t-collapse-panel__content--bottom{position:relative}\n.",
      [1],
      "t-collapse-panel__content--expanded.",
      [1],
      "t-collapse-panel__content--bottom::after{background-color:var(--td-collapse-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
      [1],
      "t-collapse-panel__content--expanded.",
      [1],
      "t-collapse-panel__content--top{position:relative}\n.",
      [1],
      "t-collapse-panel__content--expanded.",
      [1],
      "t-collapse-panel__content--top::after{background-color:var(--td-collapse-border-color,var(--td-border-level-1-color,var(--td-gray-color-3,#e7e7e7)));bottom:unset;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
      [1],
      "t-collapse-panel__arrow--top{-webkit-transform:rotate(180deg);transform:rotate(180deg)}\n.",
      [1],
      "class-title{font-size:var(--td-collapse-title-font-size,var(--td-font-size-m,",
      [0, 32],
      "))}\n.",
      [1],
      "class-note--disabled,.",
      [1],
      "class-right-icon--disabled,.",
      [1],
      "class-title--disabled{color:var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26)))}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/collapse-panel/collapse-panel.wxss",
    }
  );
}
