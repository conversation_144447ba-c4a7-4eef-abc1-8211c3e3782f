Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  r = require("../../../@babel/runtime/helpers/inherits"),
  a = require("../../../@babel/runtime/helpers/createSuper"),
  i = require("tslib"),
  s = require("../common/src/index"),
  n = u(require("../common/config")),
  l = u(require("./props"));
function u(e) {
  return e && e.__esModule ? e : { default: e };
}
var o = n.default.prefix,
  c = "".concat(o, "-side-bar-item"),
  d = (function (i) {
    r(n, i);
    var s = a(n);
    function n() {
      var e;
      return (
        t(this, n),
        ((e = s.apply(this, arguments)).externalClasses = [
          "".concat(o, "-class"),
        ]),
        (e.properties = Object.assign(Object.assign({}, l.default), {
          tId: { type: String },
        })),
        (e.relations = {
          "../side-bar/side-bar": {
            type: "parent",
            linked: function (e) {
              (this.parent = e), this.updateActive(e.data.value);
            },
          },
        }),
        (e.observers = {
          icon: function (e) {
            this.setData({ _icon: "string" == typeof e ? { name: e } : e });
          },
        }),
        (e.data = {
          classPrefix: c,
          prefix: o,
          active: !1,
          isPre: !1,
          isNext: !1,
        }),
        (e.methods = {
          updateActive: function (e) {
            var t = e === this.data.value;
            this.setData({ active: t });
          },
          handleClick: function () {
            var e;
            if (!this.data.disabled) {
              var t = this.data,
                r = t.value,
                a = t.label;
              null === (e = this.parent) ||
                void 0 === e ||
                e.doChange({ value: r, label: a });
            }
          },
        }),
        e
      );
    }
    return e(n);
  })(s.SuperComponent),
  p = (d = (0, i.__decorate)([(0, s.wxComponent)()], d));
exports.default = p;
