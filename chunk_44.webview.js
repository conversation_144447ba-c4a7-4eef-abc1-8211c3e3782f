__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/result/result": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            customStyle: new Array(1),
            theme: new Array(1),
          },
          K = U === true,
          e,
          g = (C) => {},
          h,
          i = (C, T, E, B, F, S, J) => {
            var $A = I(h);
            if (h && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.classPrefix + "__icon" },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.classPrefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class", Y(D.prefix) + "-class-image");
                  if (C || K || U.image) O(N, "src", D.image);
                  if (C) O(N, "mode", "aspectFit");
                },
                g
              );
            } else if (e === 2) {
              h = "icon";
              B(h, i);
            }
          },
          d = (C, T, E, B, F, S) => {
            e = D.image ? 1 : D._icon ? 2 : 0;
            B(e, f);
            S("image");
          },
          k,
          l = (C, T) => {
            if (k === 1) {
              C || K || U.title ? T(Y(D.title)) : T();
            }
          },
          j = (C, T, E, B, F, S) => {
            k = D.title ? 1 : 0;
            B(k, l);
            S("title");
          },
          n,
          o = (C, T) => {
            if (n === 1) {
              C || K || U.description ? T(Y(D.description)) : T();
            }
          },
          m = (C, T, E, B, F, S) => {
            n = D.description ? 1 : 0;
            B(n, o);
            S("description");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__thumb");
                if (C) O(N, "aria-hidden", "true");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__title " + Y(D.prefix) + "-class-title"
                  );
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__description " +
                      Y(D.prefix) +
                      "-class-description"
                  );
              },
              m
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.theme || U.prefix) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix) +
                      "--theme-" +
                      Y(D.theme) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["theme"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix) +
                      "--theme-" +
                      Y(D.theme) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/result/result.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-result{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column}\n.",
        [1],
        "t-result__icon{font-size:",
        [0, 160],
        "}\n.",
        [1],
        "t-result__title{color:var(--td-result-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:var(--td-result-title-font-size,var(--td-font-size-xl,",
        [0, 40],
        "));font-weight:700;line-height:var(--td-result-title-line-height,",
        [0, 56],
        ")}\n.",
        [1],
        "t-result__thumb:not(:empty)+.",
        [1],
        "t-result__title:not(:empty){margin-top:var(--td-result-title-margin-top,var(--td-spacer-1,",
        [0, 24],
        "))}\n.",
        [1],
        "t-result__description{color:var(--td-result-description-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))));font-size:var(--td-result-description-font-size,var(--td-font-size-base,",
        [0, 28],
        "));line-height:var(--td-result-description-line-height,",
        [0, 44],
        ");text-align:center}\n.",
        [1],
        "t-result__title+.",
        [1],
        "t-result__description:not(:empty){margin-top:var(--td-result-description-margin-top,var(--td-spacer,",
        [0, 16],
        "))}\n.",
        [1],
        "t-result--theme-default{color:var(--td-result-icon-default-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-result--theme-success{color:var(--td-result-icon-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
        [1],
        "t-result--theme-warning{color:var(--td-result-icon-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
        [1],
        "t-result--theme-error{color:var(--td-result-icon-error-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/result/result.wxss" }
    );
}
