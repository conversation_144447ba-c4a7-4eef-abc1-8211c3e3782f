Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.isValidDate =
    exports.isSameDate =
    exports.getMonthDateRect =
    exports.getDateRect =
    exports.getDate =
      void 0);
var e = function (e) {
  var t = new Date(e);
  return {
    year: t.getFullYear(),
    month: t.getMonth(),
    date: t.getDate(),
    day: t.getDay(),
    time: t.getTime(),
  };
};
exports.getDateRect = e;
exports.isSameDate = function (t, a) {
  return (
    (t instanceof Date || "number" == typeof t) && (t = e(t)),
    (a instanceof Date || "number" == typeof a) && (a = e(a)),
    ["year", "month", "date"].every(function (e) {
      return t[e] === a[e];
    })
  );
};
exports.getMonthDateRect = function (t) {
  var a = e(t),
    n = a.year,
    r = a.month;
  return {
    year: n,
    month: r,
    weekdayOfFirstDay: new Date(n, r, 1).getDay(),
    lastDate: new Date(+new Date(n, r + 1, 1) - 864e5).getDate(),
  };
};
exports.isValidDate = function (e) {
  return "number" == typeof e || e instanceof Date;
};
exports.getDate = function () {
  for (
    var t = new Date(), a = arguments.length, n = new Array(a), r = 0;
    r < a;
    r++
  )
    n[r] = arguments[r];
  if (0 === n.length) return t;
  if (1 === n.length && n[0] <= 1e3) {
    var o = e(t),
      D = o.year,
      i = o.month,
      s = o.date;
    return new Date(D, i + n[0], s);
  }
  return Date.apply(null, n);
};
