Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  r = require("../../../@babel/runtime/helpers/classCallCheck"),
  t = require("../../../@babel/runtime/helpers/inherits"),
  i = require("../../../@babel/runtime/helpers/createSuper"),
  c = require("tslib"),
  s = require("../common/src/index"),
  n = l(require("./props")),
  o = l(require("../common/config")),
  a = require("../common/utils");
function l(e) {
  return e && e.__esModule ? e : { default: e };
}
var u = o.default.prefix,
  p = "".concat(u, "-result"),
  f = {
    default: "error-circle",
    success: "check-circle",
    warning: "error-circle",
    error: "close-circle",
  },
  m = (function (c) {
    t(o, c);
    var s = i(o);
    function o() {
      var e;
      return (
        r(this, o),
        ((e = s.apply(this, arguments)).options = { multipleSlots: !0 }),
        (e.externalClasses = [
          "".concat(u, "-class"),
          "".concat(u, "-class-image"),
          "".concat(u, "-class-title"),
          "".concat(u, "-class-description"),
        ]),
        (e.properties = n.default),
        (e.data = { prefix: u, classPrefix: p }),
        (e.lifetimes = {
          ready: function () {
            this.initIcon();
          },
        }),
        (e.observers = {
          "icon, theme": function () {
            this.initIcon();
          },
        }),
        (e.methods = {
          initIcon: function () {
            var e = this.properties,
              r = e.icon,
              t = e.theme;
            this.setData({ _icon: (0, a.calcIcon)(r, f[t]) });
          },
        }),
        e
      );
    }
    return e(o);
  })(s.SuperComponent),
  d = (m = (0, c.__decorate)([(0, s.wxComponent)()], m));
exports.default = d;
