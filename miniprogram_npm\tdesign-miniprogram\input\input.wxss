@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-input {
  align-items: var(--td-input-align-items, center);
  background-color: var(
    --td-input-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  display: -webkit-flex;
  display: flex;
  flex: 1;
  overflow: hidden;
  padding: var(--td-input-vertical-padding, 32rpx);
}
.t-input--border {
  position: relative;
}
.t-input--border::after {
  background-color: var(
    --td-input-border-color,
    var(--td-component-stroke, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: unset;
  transform: scaleY(0.5);
}
.t-input--border:after {
  left: var(--td-input-border-left-space, 32rpx);
  right: var(--td-input-border-right-space, 0);
}
.t-input--layout-vertical {
  align-items: start;
  flex-direction: column;
}
.t-input__wrap--prefix {
  display: -webkit-flex;
  display: flex;
}
.t-input__icon--prefix {
  color: var(
    --td-input-prefix-icon-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  font-size: 48rpx;
}
.t-input__label:not(:empty) {
  word-wrap: break-word;
  color: var(
    --td-input-label-text-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  font-size: var(--td-font-size-m, 32rpx);
  line-height: 48rpx;
  margin-right: var(--td-spacer-2, 32rpx);
  max-width: var(--td-input-label-max-width, 5em);
  min-width: var(--td-input-label-min-width, 2em);
}
.t-input--layout-vertical .t-input__label:not(:empty) {
  font-size: var(--td-font-size-base, 28rpx);
  padding-bottom: 8rpx;
}
.t-input__icon--prefix:not(:empty) + .t-input__label:not(:empty) {
  padding-left: 8rpx;
}
.t-input__label:not(:empty) + .t-input__wrap {
  margin-left: var(--td-spacer-2, 32rpx);
}
.t-input__icon--prefix:not(:empty) + .t-input__label:empty {
  margin-right: var(--td-spacer-2, 32rpx);
}
.t-input__wrap {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  flex-shrink: 1;
  flex: 1;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}
.t-input__wrap .t-input__content {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  font-size: var(--td-font-size-m, 32rpx);
  line-height: 48rpx;
  width: 100%;
}
.t-input__wrap--clearable-icon,
.t-input__wrap--suffix,
.t-input__wrap--suffix-icon {
  flex: 0 0 auto;
  padding-left: var(--td-spacer-1, 24rpx);
}
.t-input__wrap--clearable-icon:empty,
.t-input__wrap--suffix-icon:empty,
.t-input__wrap--suffix:empty {
  display: none;
}
.t-input__wrap--clearable-icon,
.t-input__wrap--suffix-icon {
  color: var(
    --td-input-suffix-icon-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  font-size: 48rpx;
}
.t-input__wrap--suffix {
  color: var(
    --td-input-suffix-text-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  font-size: var(--td-font-size-m, 32rpx);
}
.t-input__icon--prefix:empty,
.t-input__tips:empty,
.t-input__wrap--clearable-icon:empty,
.t-input__wrap--suffix-icon:empty,
.t-input__wrap--suffix:empty {
  display: none;
}
.t-input__control {
  background-color: initial;
  border: 0;
  box-sizing: border-box;
  color: var(
    --td-input-default-text-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: block;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  min-height: 48rpx;
  min-width: 0;
  padding: 0;
  resize: none;
  width: 100%;
}
.t-input__control--disabled {
  -webkit-text-fill-color: currentColor;
  color: var(
    --td-input-disabled-text-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
  cursor: not-allowed;
  opacity: 1;
}
.t-input__control--read-only {
  cursor: default;
}
.t-input--left {
  text-align: left;
}
.t-input--right {
  text-align: right;
}
.t-input--center {
  text-align: center;
}
.t-input__placeholder {
  color: var(
    --td-input-placeholder-text-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  font-size: var(
    --td-input-placeholder-text-font-size,
    var(--td-font-size-m, 32rpx)
  );
}
.t-input__placeholder--disabled {
  color: var(
    --td-input-disabled-text-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-input__tips {
  font-size: var(--td-font-size-s, 24rpx);
  line-height: 40rpx;
  padding-top: 8rpx;
}
.t-input--default + .t-input__tips {
  color: var(
    --td-input-default-tips-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
}
.t-input--success + .t-input__tips {
  color: var(
    --td-input-success-tips-color,
    var(--td-success-color, var(--td-success-color-5, #2ba471))
  );
}
.t-input--warning + .t-input__tips {
  color: var(
    --td-input-warning-tips-color,
    var(--td-warning-color, var(--td-warning-color-5, #e37318))
  );
}
.t-input--error + .t-input__tips {
  color: var(
    --td-input-error-tips-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
}
