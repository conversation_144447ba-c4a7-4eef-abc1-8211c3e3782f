Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e,
  t,
  a = require("../../../@babel/runtime/helpers/toConsumableArray"),
  n = require("../../../@babel/runtime/helpers/classCallCheck"),
  o = require("../../../@babel/runtime/helpers/createClass"),
  i = require("../../../@babel/runtime/helpers/inherits"),
  r = require("../../../@babel/runtime/helpers/createSuper"),
  l = require("tslib"),
  u = d(require("../common/config")),
  s = require("../common/src/index"),
  c = d(require("./props")),
  h = d(require("./locale/dayjs"));
function d(e) {
  return e && e.__esModule ? e : { default: e };
}
var f = require("dayjs"),
  m = require("dayjs/plugin/localeData");
f.extend(m), f.locale("zh-cn");
var g,
  v,
  p =
    (null === (e = h.default[f.locale()]) || void 0 === e ? void 0 : e.key) ||
    (null === (t = h.default.default) || void 0 === t ? void 0 : t.key),
  y = u.default.prefix,
  M = "".concat(y, "-date-time-picker");
((v = g || (g = {})).YEAR = "year"),
  (v.MONTH = "month"),
  (v.DATE = "date"),
  (v.HOUR = "hour"),
  (v.MINUTE = "minute"),
  (v.SECOND = "second");
var D = ["year", "month", "date"],
  O = ["hour", "minute", "second"],
  C = [].concat(D, O),
  b = (function (e) {
    i(l, e);
    var t = r(l);
    function l() {
      var e;
      return (
        n(this, l),
        ((e = t.apply(this, arguments)).properties = c.default),
        (e.externalClasses = [
          "".concat(y, "-class"),
          "".concat(y, "-class-confirm"),
          "".concat(y, "-class-cancel"),
          "".concat(y, "-class-title"),
        ]),
        (e.options = { multipleSlots: !0 }),
        (e.observers = {
          "start, end, value": function () {
            this.updateColumns();
          },
          customLocale: function (e) {
            e &&
              h.default[e].key &&
              this.setData({
                locale: h.default[e].i18n,
                dayjsLocale: h.default[e].key,
              });
          },
          mode: function (e) {
            var t = this.getFullModeArray(e);
            this.setData({ fullModes: t }), this.updateColumns();
          },
        }),
        (e.date = null),
        (e.data = {
          prefix: y,
          classPrefix: M,
          columns: [],
          columnsValue: [],
          fullModes: [],
          locale: h.default[p].i18n,
          dayjsLocale: h.default[p].key,
        }),
        (e.controlledProps = [{ key: "value", event: "change" }]),
        (e.methods = {
          updateColumns: function () {
            this.date = this.getParseDate();
            var e = this.getValueCols(),
              t = e.columns,
              a = e.columnsValue;
            this.setData({ columns: t, columnsValue: a });
          },
          getDaysOfWeekInMonth: function (e) {
            for (
              var t = this.data,
                a = t.locale,
                n = t.dayjsLocale,
                o = e.startOf("month"),
                i = e.endOf("month"),
                r = [],
                l = 0;
              l <= i.diff(o, "days");
              l += 1
            ) {
              var u = o.add(l, "days").locale(n).format("ddd");
              r.push({
                value: "".concat(l + 1),
                label: ""
                  .concat(l + 1)
                  .concat(a.date || "", " ")
                  .concat(u),
              });
            }
            return r;
          },
          getParseDate: function () {
            var e = this.properties,
              t = e.value,
              a = e.defaultValue,
              n = this.getMinDate(),
              o = t || a;
            if (this.isTimeMode()) {
              var i = f(n).format("YYYY-MM-DD");
              o = f("".concat(i, " ").concat(o));
            }
            var r = f(o || n);
            return r.isValid() ? r : n;
          },
          normalize: function (e, t) {
            return e && f(e).isValid() ? f(e) : t;
          },
          getMinDate: function () {
            return this.normalize(
              this.properties.start,
              f().subtract(10, "year")
            );
          },
          getMaxDate: function () {
            return this.normalize(this.properties.end, f().add(10, "year"));
          },
          getDateRect: function () {
            var e =
                arguments.length > 0 && void 0 !== arguments[0]
                  ? arguments[0]
                  : "default",
              t =
                this[
                  { min: "getMinDate", max: "getMaxDate", default: "getDate" }[
                    e
                  ]
                ]();
            return ["year", "month", "date", "hour", "minute", "second"].map(
              function (e) {
                var a;
                return null === (a = t[e]) || void 0 === a ? void 0 : a.call(t);
              }
            );
          },
          getDate: function () {
            return this.clipDate(
              (null == this ? void 0 : this.date) || this.getMinDate()
            );
          },
          clipDate: function (e) {
            var t = this.getMinDate(),
              a = this.getMaxDate();
            return f(Math.min(Math.max(t.valueOf(), e.valueOf()), a.valueOf()));
          },
          setYear: function (e, t) {
            var a = e.date(),
              n = e.year(t).daysInMonth();
            return e.date(Math.min(a.valueOf(), n.valueOf())).year(t);
          },
          setMonth: function (e, t) {
            var a = e.date(),
              n = e.month(t).daysInMonth();
            return e.date(Math.min(a.valueOf(), n.valueOf())).month(t);
          },
          getColumnOptions: function () {
            var e = this,
              t = this.data,
              a = t.fullModes,
              n = t.filter,
              o = [];
            return (
              null == a ||
                a.forEach(function (t) {
                  var a = e.getOptionByType(t);
                  "function" == typeof n ? o.push(n(t, a)) : o.push(a);
                }),
              o
            );
          },
          getOptionByType: function (e) {
            var t,
              a = this.data,
              n = a.locale,
              o = a.steps,
              i = a.showWeek,
              r = [],
              l = this.getOptionEdge("min", e),
              u = this.getOptionEdge("max", e),
              s =
                null !== (t = null == o ? void 0 : o[e]) && void 0 !== t
                  ? t
                  : 1,
              c = f().locale(this.data.dayjsLocale).localeData().monthsShort();
            if ("date" === e && i) return this.getDaysOfWeekInMonth(this.date);
            for (var h = l; h <= u; h += s)
              r.push({
                value: "".concat(h),
                label: "month" === e ? c[h] : "".concat(h + n[e]),
              });
            return r;
          },
          getYearOptions: function (e) {
            for (
              var t = this.data.locale,
                a = e.minDateYear,
                n = e.maxDateYear,
                o = [],
                i = a;
              i <= n;
              i += 1
            )
              o.push({ value: "".concat(i), label: "".concat(i + t.year) });
            return o;
          },
          getOptionEdge: function (e, t) {
            for (
              var a = this.getDateRect(),
                n = this.getDateRect(e),
                o = {
                  month: [0, 11],
                  date: [1, this.getDate().daysInMonth()],
                  hour: [0, 23],
                  minute: [0, 59],
                  second: [0, 59],
                },
                i = ["year", "month", "date", "hour", "minute", "second"],
                r = 0,
                l = a.length;
              r < l;
              r += 1
            ) {
              if (i[r] === t) return n[r];
              if (n[r] !== a[r]) return o[t]["min" === e ? 0 : 1];
            }
            return o[t]["min" === e ? 0 : 1];
          },
          getMonthOptions: function () {
            for (
              var e = [],
                t = this.getOptionEdge("min", "month"),
                a = this.getOptionEdge("max", "month"),
                n = f.monthsShort(),
                o = t;
              o <= a;
              o += 1
            )
              e.push({ value: "".concat(o), label: n[o] });
            return e;
          },
          getDayOptions: function () {
            for (
              var e = this.data.locale,
                t = [],
                a = this.getOptionEdge("min", "date"),
                n = this.getOptionEdge("max", "date"),
                o = a;
              o <= n;
              o += 1
            )
              t.push({ value: "".concat(o), label: "".concat(o + e.day) });
            return t;
          },
          getHourOptions: function () {
            for (
              var e = this.data.locale,
                t = [],
                a = this.getOptionEdge("min", "hour"),
                n = this.getOptionEdge("max", "hour"),
                o = a;
              o <= n;
              o += 1
            )
              t.push({ value: "".concat(o), label: "".concat(o + e.hour) });
            return t;
          },
          getMinuteOptions: function () {
            for (
              var e = this.data.locale,
                t = [],
                a = this.getOptionEdge("min", "minute"),
                n = this.getOptionEdge("max", "minute"),
                o = a;
              o <= n;
              o += 1
            )
              t.push({ value: "".concat(o), label: "".concat(o + e.minute) });
            return t;
          },
          getValueCols: function () {
            return {
              columns: this.getColumnOptions(),
              columnsValue: this.getColumnsValue(),
            };
          },
          getColumnsValue: function () {
            var e = this.data.fullModes,
              t = this.getDate(),
              a = [];
            return (
              null == e ||
                e.forEach(function (e) {
                  a.push("".concat(t[e]()));
                }),
              a
            );
          },
          getNewDate: function (e, t) {
            var a = this.getDate();
            switch (t) {
              case g.YEAR:
                a = this.setYear(a, e);
                break;
              case g.MONTH:
                a = this.setMonth(a, e);
                break;
              case g.DATE:
                a = a.date(e);
                break;
              case g.HOUR:
                a = a.hour(e);
                break;
              case g.MINUTE:
                a = a.minute(e);
                break;
              case g.SECOND:
                a = a.second(e);
            }
            return this.clipDate(a);
          },
          onColumnChange: function (e) {
            var t = null == e ? void 0 : e.detail,
              a = t.value,
              n = t.column,
              o = this.data,
              i = o.fullModes,
              r = o.format,
              l = null == a ? void 0 : a[n],
              u = null == i ? void 0 : i[n],
              s = this.getNewDate(parseInt(l, 10), u);
            this.date = s;
            var c = this.getValueCols(),
              h = c.columns,
              d = c.columnsValue;
            this.setData({ columns: h, columnsValue: d });
            var f = this.getDate(),
              m = r ? f.format(r) : f.valueOf();
            this.triggerEvent("pick", { value: m });
          },
          onConfirm: function () {
            var e = this.properties.format,
              t = this.getDate(),
              a = e ? t.format(e) : t.valueOf();
            this._trigger("change", { value: a }),
              this.triggerEvent("confirm", { value: a }),
              this.resetColumns();
          },
          onCancel: function () {
            this.resetColumns(), this.triggerEvent("cancel");
          },
          onVisibleChange: function (e) {
            e.detail.visible || this.resetColumns();
          },
          onClose: function (e) {
            var t = e.detail.trigger;
            this.triggerEvent("close", { trigger: t });
          },
          resetColumns: function () {
            var e = this.getParseDate();
            this.date = e;
            var t = this.getValueCols(),
              a = t.columns,
              n = t.columnsValue;
            this.setData({ columns: a, columnsValue: n });
          },
        }),
        e
      );
    }
    return (
      o(l, [
        {
          key: "getFullModeArray",
          value: function (e) {
            if ("string" == typeof e || e instanceof String)
              return this.getFullModeByModeString(e, C);
            if (Array.isArray(e)) {
              if (1 === (null == e ? void 0 : e.length))
                return this.getFullModeByModeString(e[0], C);
              if (2 === (null == e ? void 0 : e.length))
                return [].concat(
                  a(this.getFullModeByModeString(e[0], D)),
                  a(this.getFullModeByModeString(e[1], O))
                );
            }
          },
        },
        {
          key: "getFullModeByModeString",
          value: function (e, t) {
            if (!e) return [];
            var a =
              null == t
                ? void 0
                : t.findIndex(function (t) {
                    return e === t;
                  });
            return null == t ? void 0 : t.slice(0, a + 1);
          },
        },
        {
          key: "isTimeMode",
          value: function () {
            return this.data.fullModes[0] === g.HOUR;
          },
        },
      ]),
      l
    );
  })(s.SuperComponent),
  E = (b = (0, l.__decorate)([(0, s.wxComponent)()], b));
exports.default = E;
