Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/defineProperty"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  i = require("../../../@babel/runtime/helpers/createClass"),
  r = require("../../../@babel/runtime/helpers/inherits"),
  s = require("../../../@babel/runtime/helpers/createSuper"),
  n = require("tslib"),
  a = require("../common/src/index"),
  o = require("../common/utils"),
  l = u(require("../common/config")),
  c = u(require("./props"));
function u(e) {
  return e && e.__esModule ? e : { default: e };
}
var d = l.default.prefix,
  h = "".concat(d, "-image-viewer"),
  p = (function (n) {
    r(l, n);
    var a = s(l);
    function l() {
      var i;
      return (
        t(this, l),
        ((i = a.apply(this, arguments)).externalClasses = [
          "".concat(d, "-class"),
        ]),
        (i.properties = Object.assign({}, c.default)),
        (i.data = {
          prefix: d,
          classPrefix: h,
          currentSwiperIndex: 0,
          windowHeight: 0,
          windowWidth: 0,
          swiperStyle: {},
          imagesStyle: {},
          maskTop: 0,
        }),
        (i.options = { multipleSlots: !0 }),
        (i.controlledProps = [{ key: "visible", event: "close" }]),
        (i.observers = {
          "visible,initialIndex,images": function (e, t, i) {
            e &&
              (null == i ? void 0 : i.length) &&
              this.setData({
                currentSwiperIndex: t >= i.length ? i.length - 1 : t,
              });
          },
          closeBtn: function (e) {
            this.setData({ _closeBtn: (0, o.calcIcon)(e, "close") });
          },
          deleteBtn: function (e) {
            this.setData({ _deleteBtn: (0, o.calcIcon)(e, "delete") });
          },
        }),
        (i.methods = {
          calcMaskTop: function () {
            if (this.data.usingCustomNavbar) {
              var e =
                  (null === wx || void 0 === wx
                    ? void 0
                    : wx.getMenuButtonBoundingClientRect()) || null,
                t = o.systemInfo.statusBarHeight;
              e && t && this.setData({ maskTop: e.top - t + e.bottom });
            }
          },
          saveScreenSize: function () {
            var e = o.systemInfo.windowHeight,
              t = o.systemInfo.windowWidth;
            this.setData({ windowHeight: e, windowWidth: t });
          },
          calcImageDisplayStyle: function (e, t) {
            var i = this.data,
              r = i.windowWidth,
              s = i.windowHeight,
              n = e / t;
            if (e < r && t < s)
              return {
                styleObj: {
                  width: 2 * e + "rpx",
                  height: 2 * t + "rpx",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                },
              };
            if (n >= 1)
              return {
                styleObj: { width: "100vw", height: (r / n) * 2 + "rpx" },
              };
            var a = n * s * 2;
            return a < r
              ? {
                  styleObj: {
                    width: "".concat(a, "rpx"),
                    height: "100vh",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                  },
                }
              : {
                  styleObj: { width: "100vw", height: (r / e) * t * 2 + "rpx" },
                };
          },
          onImageLoadSuccess: function (t) {
            var i = t.detail,
              r = i.width,
              s = i.height,
              n = t.currentTarget.dataset.index,
              a = this.calcImageDisplayStyle(r, s),
              l = a.mode,
              c = a.styleObj,
              u = this.data.imagesStyle,
              d = this.data.swiperStyle;
            this.setData({
              swiperStyle: Object.assign(
                Object.assign({}, d),
                e({}, n, { style: "height: ".concat(c.height) })
              ),
              imagesStyle: Object.assign(
                Object.assign({}, u),
                e({}, n, {
                  mode: l,
                  style: (0, o.styles)(Object.assign({}, c)),
                })
              ),
            });
          },
          onSwiperChange: function (e) {
            var t = e.detail.current;
            this.setData({ currentSwiperIndex: t }),
              this._trigger("change", { index: t });
          },
          onClose: function (e) {
            var t = e.currentTarget.dataset.source;
            this._trigger("close", {
              visible: !1,
              trigger: t || "button",
              index: this.data.currentSwiperIndex,
            });
          },
          onDelete: function () {
            this._trigger("delete", { index: this.data.currentSwiperIndex });
          },
        }),
        i
      );
    }
    return (
      i(l, [
        {
          key: "ready",
          value: function () {
            this.saveScreenSize(), this.calcMaskTop();
          },
        },
      ]),
      l
    );
  })(a.SuperComponent),
  g = (p = (0, n.__decorate)([(0, a.wxComponent)()], p));
exports.default = g;
