Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  i = require("../../../@babel/runtime/helpers/inherits"),
  s = require("../../../@babel/runtime/helpers/createSuper"),
  r = require("tslib"),
  n = require("../common/utils"),
  a = require("../common/src/index"),
  o = u(require("../common/config")),
  c = require("./show"),
  l = u(require("./props")),
  h = u(require("../mixins/using-custom-navbar"));
function u(e) {
  return e && e.__esModule ? e : { default: e };
}
var d = o.default.prefix,
  g = "".concat(d, "-action-sheet"),
  m = (function (r) {
    i(o, r);
    var a = s(o);
    function o() {
      var e;
      return (
        t(this, o),
        ((e = a.apply(this, arguments)).behaviors = [h.default]),
        (e.externalClasses = [
          "".concat(d, "-class"),
          "".concat(d, "-class-content"),
          "".concat(d, "-class-cancel"),
        ]),
        (e.properties = Object.assign({}, l.default)),
        (e.data = {
          prefix: d,
          classPrefix: g,
          gridThemeItems: [],
          currentSwiperIndex: 0,
          defaultPopUpProps: {},
          defaultPopUpzIndex: 11500,
        }),
        (e.controlledProps = [{ key: "visible", event: "visible-change" }]),
        (e.observers = {
          "visible, items": function (e) {
            e && this.init();
          },
        }),
        (e.methods = {
          init: function () {
            this.memoInitialData(), this.splitGridThemeActions();
          },
          memoInitialData: function () {
            this.initialData = Object.assign(
              Object.assign({}, this.properties),
              this.data
            );
          },
          splitGridThemeActions: function () {
            this.data.theme === c.ActionSheetTheme.Grid &&
              this.setData({
                gridThemeItems: (0, n.chunk)(this.data.items, this.data.count),
              });
          },
          show: function (e) {
            this.setData(
              Object.assign(
                Object.assign(Object.assign({}, this.initialData), e),
                { visible: !0 }
              )
            ),
              this.splitGridThemeActions(),
              (this.autoClose = !0),
              this._trigger("visible-change", { visible: !0 });
          },
          close: function () {
            this.triggerEvent("close", { trigger: "command" }),
              this._trigger("visible-change", { visible: !1 });
          },
          onPopupVisibleChange: function (e) {
            e.detail.visible ||
              (this.triggerEvent("close", { trigger: "overlay" }),
              this._trigger("visible-change", { visible: !1 })),
              this.autoClose &&
                (this.setData({ visible: !1 }), (this.autoClose = !1));
          },
          onSwiperChange: function (e) {
            var t = e.detail.current;
            this.setData({ currentSwiperIndex: t });
          },
          onSelect: function (e) {
            var t = this.data,
              i = t.currentSwiperIndex,
              s = t.items,
              r = t.gridThemeItems,
              n = t.count,
              a = t.theme,
              o = e.currentTarget.dataset.index,
              l = a === c.ActionSheetTheme.Grid,
              h = l ? r[i][o] : s[o],
              u = l ? o + i * n : o;
            h &&
              (this.triggerEvent("selected", { selected: h, index: u }),
              h.disabled ||
                (this.triggerEvent("close", { trigger: "select" }),
                this._trigger("visible-change", { visible: !1 })));
          },
          onCancel: function () {
            this.triggerEvent("cancel"),
              this.autoClose &&
                (this.setData({ visible: !1 }), (this.autoClose = !1));
          },
        }),
        e
      );
    }
    return e(o);
  })(a.SuperComponent);
m.show = c.show;
var p = (m = (0, r.__decorate)([(0, a.wxComponent)()], m));
exports.default = p;
