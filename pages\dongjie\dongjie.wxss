page {
  align-items: center;
  background-color: #fff;
  box-sizing: border-box;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: center;
  padding: 0 60rpx;
}
.title {
  color: #000;
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 80rpx;
  text-align: center;
}
.loing_img {
  margin-bottom: 60rpx;
  text-align: center;
}
.loing_img image {
  border-radius: 20rpx;
  height: 300rpx;
  width: 300rpx;
}
.miaoshu {
  color: #ff9500;
  font-size: 28rpx;
  line-height: 1.5;
  text-align: center;
}
