__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/service-info/service-info": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || !!Z(U.serviceData, "name") || undefined)
                  O(N, "title", X(D.serviceData).name || "服务详情");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          d,
          i = (C, T) => {
            C || K || !!Z(U.serviceData, "description") || undefined
              ? T(Y(X(D.serviceData).description || "暂无服务说明"))
              : T();
          },
          h = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "description_text");
              },
              i
            );
          },
          g = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "description_box");
              },
              h
            );
          },
          j,
          m = (C, n, o, p, q, r, T, E) => {
            var t = (C) => {},
              s = (C, T, E) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "service_image");
                    if (C || K || p) O(N, "src", n);
                    if (C) O(N, "mode", "widthFix");
                    if (C) O(N, "bindtap", "previewImage");
                    if (C || K || p) R.d(N, "src", n);
                  },
                  t
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_item");
              },
              s
            );
          },
          l = (C, T, E, B, F) => {
            F(
              X(D.serviceData).detail_images,
              "index",
              U ? Z(U.serviceData, "detail_images") : undefined,
              [0, "serviceData", "detail_images"],
              m
            );
          },
          p = (C, T) => {
            C ? T("暂无图片") : T();
          },
          o = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder_text");
              },
              p
            );
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_placeholder");
              },
              o
            );
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "image_section");
                },
                l
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "image_section");
                },
                n
              );
            }
          },
          r = (C, T) => {
            var $A = X(D.serviceData).audit_status !== "approved";
            var $B = X(D.serviceData).publish_status !== "online";
            C ||
            K ||
            !!Z(U.serviceData, "audit_status") ||
            ($A
              ? undefined
              : !!Z(U.serviceData, "publish_status") ||
                ($B ? undefined : undefined))
              ? T(Y($A ? "服务未通过审核" : $B ? "服务已下架" : "立即预约"))
              : T();
          },
          q = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                var $A =
                  X(D.serviceData).audit_status !== "approved" ||
                  X(D.serviceData).publish_status !== "online";
                if (
                  C ||
                  K ||
                  !!(
                    Z(U.serviceData, "audit_status") ||
                    Z(U.serviceData, "publish_status") ||
                    ($A ? undefined : undefined)
                  ) ||
                  undefined
                )
                  L(N, "order_button_text " + Y($A ? "disabled" : ""));
                var $B =
                  X(D.serviceData).audit_status === "approved" &&
                  X(D.serviceData).publish_status === "online";
                if (
                  C ||
                  K ||
                  !!(
                    Z(U.serviceData, "audit_status") ||
                    Z(U.serviceData, "publish_status")
                  ) ||
                  ($B ? undefined : undefined)
                )
                  O(N, "bindtap", $B ? "showOrderModal" : "");
              },
              r
            );
          },
          f = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service_description_section");
              },
              g
            );
            j =
              X(D.serviceData).detail_images &&
              X(X(D.serviceData).detail_images).length > 0
                ? 1
                : 0;
            B(j, k);
            E(
              "view",
              {},
              (N, C) => {
                var $A =
                  X(D.serviceData).audit_status !== "approved" ||
                  X(D.serviceData).publish_status !== "online";
                if (
                  C ||
                  K ||
                  !!(
                    Z(U.serviceData, "audit_status") ||
                    Z(U.serviceData, "publish_status") ||
                    ($A ? undefined : undefined)
                  ) ||
                  undefined
                )
                  L(N, "bottom_actions " + Y($A ? "disabled" : ""));
              },
              q
            );
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "service_info_container");
                },
                f
              );
            }
          },
          s,
          v = (C, T) => {
            C ? T("加载中...") : T();
          },
          u = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loading_text");
              },
              v
            );
          },
          t = (C, T, E) => {
            if (s === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_container");
                },
                u
              );
            }
          },
          w,
          B0 = (C, T) => {
            C ? T("填写订单备注") : T();
          },
          C0 = (C, T) => {
            C ? T("×") : T();
          },
          A0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "modal_title");
              },
              B0
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "modal_close");
                if (C) O(N, "bindtap", "hideOrderModal");
              },
              C0
            );
          },
          F0 = (C, T) => {
            C || K || Z(U.serviceData, "name")
              ? T(Y(X(D.serviceData).name))
              : T();
          },
          G0 = (C, T) => {
            var $A = X(D.serviceData).price;
            C ||
            K ||
            !!Z(U.serviceData, "price") ||
            ($A ? !!Z(U.serviceData, "price") || undefined : undefined)
              ? T(Y($A ? "¥" + X(D.serviceData).price : "价格面议"))
              : T();
          },
          E0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service_name");
              },
              F0
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service_price");
              },
              G0
            );
          },
          I0 = (C, T) => {
            C ? T("备注信息（选填）") : T();
          },
          J0 = (C) => {},
          K0 = (C, T) => {
            C || K || !!Z(U.orderRemark, "length") || undefined
              ? T(Y(Y(X(D.orderRemark).length) + "/200"))
              : T();
          },
          H0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "remark_label");
              },
              I0
            );
            E(
              "textarea",
              {},
              (N, C) => {
                if (C) L(N, "remark_input");
                if (C) O(N, "placeholder", "请输入您的特殊需求或备注信息...");
                if (C || K || U.orderRemark) O(N, "value", D.orderRemark);
                if (C) O(N, "bindinput", "onRemarkInput");
                if (C) O(N, "maxlength", "200");
                if (C || K || undefined) O(N, "show-confirm-bar", false);
              },
              J0
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "remark_count");
              },
              K0
            );
          },
          D0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service_info_summary");
              },
              E0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "remark_section");
              },
              H0
            );
          },
          M0 = (C, T) => {
            C ? T("取消") : T();
          },
          N0 = (C, T) => {
            C ? T("立即预约") : T();
          },
          L0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "cancel_button");
                if (C) O(N, "bindtap", "hideOrderModal");
              },
              M0
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "confirm_button");
                if (C) O(N, "bindtap", "confirmOrder");
              },
              N0
            );
          },
          z = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal_header");
              },
              A0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal_content");
              },
              D0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal_footer");
              },
              L0
            );
          },
          y = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal_container");
                if (C) O(N, "catchtap", "stopPropagation");
              },
              z
            );
          },
          x = (C, T, E) => {
            if (w === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "modal_overlay");
                  if (C) O(N, "bindtap", "hideOrderModal");
                },
                y
              );
            }
          },
          a = (C, T, E, B) => {
            E("page-meta", {}, (N, C) => {}, b);
            d = !D.loading ? 1 : 0;
            B(d, e);
            s = D.loading ? 1 : 0;
            B(s, t);
            w = D.showModal ? 1 : 0;
            B(w, x);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/service-info/service-info.wxss"] = setCssToHead(
    [
      ".",
      [1],
      "service_description_section{margin-bottom:",
      [0, 40],
      "}\n.",
      [1],
      "section_title{color:#333;font-size:",
      [0, 32],
      ";font-weight:500;margin-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "description_box{background-color:#f5f5f5;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 15],
      ";padding:",
      [0, 25],
      "}\n.",
      [1],
      "description_text{color:#666;display:block;font-size:",
      [0, 28],
      ";line-height:1.6;margin-bottom:",
      [0, 10],
      "}\n.",
      [1],
      "description_text:last-child{margin-bottom:0}\n.",
      [1],
      "image_section{-webkit-flex:1;flex:1}\n.",
      [1],
      "image_item{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;margin:0 auto ",
      [0, 20],
      ";width:100%}\n.",
      [1],
      "service_image{border-radius:",
      [0, 15],
      ";display:block;margin:0 auto;width:100%}\n.",
      [1],
      "image_placeholder{-webkit-align-items:center;align-items:center;background-color:#f5f5f5;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 15],
      ";display:-webkit-flex;display:flex;height:",
      [0, 300],
      ";-webkit-justify-content:center;justify-content:center;margin-bottom:",
      [0, 20],
      ";width:100%}\n.",
      [1],
      "image_placeholder:last-child{margin-bottom:0}\n.",
      [1],
      "placeholder_text{color:#999;font-size:",
      [0, 28],
      "}\n.",
      [1],
      "bottom_actions{border-top:",
      [0, 2],
      " solid #f0f0f0;bottom:0;box-shadow:0 ",
      [0, -2],
      " ",
      [0, 10],
      " rgba(0,0,0,.05);height:",
      [0, 60],
      ";left:0;padding:",
      [0, 30],
      ";position:fixed;right:0;z-index:999}\n.",
      [1],
      "bottom_actions,.",
      [1],
      "order_button_text{-webkit-align-items:center;align-items:center;background-color:#ff4d4f;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "order_button_text{color:#fff;font-size:",
      [0, 32],
      ";font-weight:500;width:100%}\n.",
      [1],
      "service_info_container{background-color:#fff;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;min-height:100vh;padding:",
      [0, 190],
      " ",
      [0, 30],
      " ",
      [0, 150],
      "}\n.",
      [1],
      "contact_section{-webkit-flex:1;flex:1;margin-right:",
      [0, 20],
      "}\n.",
      [1],
      "contact_text{color:#195abf;font-size:",
      [0, 28],
      "}\n.",
      [1],
      "order_button_section{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-justify-content:flex-end;justify-content:flex-end}\n.",
      [1],
      "custom_order_button{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;height:",
      [0, 80],
      ";-webkit-justify-content:center;justify-content:center;padding:0 ",
      [0, 40],
      "}\n.",
      [1],
      "custom_order_button:active{background-color:rgba(255,77,79,.1)}\n.",
      [1],
      "service_info_container{padding-bottom:",
      [0, 120],
      "}\n.",
      [1],
      "modal_overlay{-webkit-align-items:center;align-items:center;background-color:rgba(0,0,0,.5);display:-webkit-flex;display:flex;height:100%;-webkit-justify-content:center;justify-content:center;left:0;position:fixed;top:0;width:100%;z-index:1000}\n.",
      [1],
      "modal_container{background-color:#fff;border-radius:",
      [0, 20],
      ";box-shadow:0 ",
      [0, 10],
      " ",
      [0, 30],
      " rgba(0,0,0,.2);max-width:",
      [0, 600],
      ";overflow:hidden;width:90%}\n.",
      [1],
      "modal_header{-webkit-align-items:center;align-items:center;border-bottom:",
      [0, 1],
      " solid #f0f0f0;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between;padding:",
      [0, 30],
      "}\n.",
      [1],
      "modal_title{color:#333;font-size:",
      [0, 32],
      ";font-weight:700}\n.",
      [1],
      "modal_close{-webkit-align-items:center;align-items:center;color:#999;display:-webkit-flex;display:flex;font-size:",
      [0, 40],
      ";height:",
      [0, 40],
      ";-webkit-justify-content:center;justify-content:center;width:",
      [0, 40],
      "}\n.",
      [1],
      "modal_content{padding:",
      [0, 30],
      "}\n.",
      [1],
      "service_info_summary{-webkit-align-items:center;align-items:center;background-color:#f8f9fa;border-radius:",
      [0, 10],
      ";display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between;margin-bottom:",
      [0, 30],
      ";padding:",
      [0, 20],
      "}\n.",
      [1],
      "service_name{color:#333;font-size:",
      [0, 28],
      ";font-weight:500}\n.",
      [1],
      "service_price{color:#195abf;font-size:",
      [0, 32],
      ";font-weight:700}\n.",
      [1],
      "remark_section{margin-top:",
      [0, 20],
      "}\n.",
      [1],
      "remark_label{color:#333;display:block;font-size:",
      [0, 28],
      ";margin-bottom:",
      [0, 15],
      "}\n.",
      [1],
      "remark_input{background-color:#fafafa;border:",
      [0, 1],
      " solid #e0e0e0;border-radius:",
      [0, 10],
      ";box-sizing:border-box;color:#333;font-size:",
      [0, 26],
      ";min-height:",
      [0, 120],
      ";padding:",
      [0, 20],
      ";width:100%}\n.",
      [1],
      "remark_count{color:#999;display:block;font-size:",
      [0, 24],
      ";margin-top:",
      [0, 10],
      ";text-align:right}\n.",
      [1],
      "modal_footer{border-top:",
      [0, 1],
      " solid #f0f0f0;display:-webkit-flex;display:flex}\n.",
      [1],
      "cancel_button,.",
      [1],
      "confirm_button{border:none;border-radius:0;-webkit-flex:1;flex:1;font-size:",
      [0, 30],
      ";height:",
      [0, 100],
      "}\n.",
      [1],
      "cancel_button{background-color:#f5f5f5;color:#666}\n.",
      [1],
      "confirm_button{background-color:#ff4d4f;color:#fff}\n.",
      [1],
      "cancel_button::after,.",
      [1],
      "confirm_button::after{border:none}\n",
    ],
    undefined,
    { path: "./pages/service-info/service-info.wxss" }
  );
}
