__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/check-tag/check-tag": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            className: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
          },
          K = U === true,
          e,
          g,
          h = (C, T, E, B, F, S, J) => {
            var $A = I(g);
            if (g && $A)
              $A(
                R,
                C,
                Object.assign({ tClass: D.prefix + "-icon" }, X(D._icon), {}),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              g = "icon";
              B(g, h);
            }
          },
          d = (C, T, E, B, F, S) => {
            e = D._icon ? 1 : 0;
            B(e, f);
            S("icon");
          },
          j,
          k = (C, T) => {
            if (j === 1) {
              var $A = D.checked;
              var $B = 0;
              var $C = 1;
              C ||
              K ||
              !!U.checked ||
              ($A ? Z(U.content, $B) : Z(U.content, $C))
                ? T(Y($A ? X(D.content)[$B] : X(D.content)[$C]))
                : T();
            } else {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          i = (C, T, E, B, F, S) => {
            S("");
            S("content");
            j = P(X(a).isArray)(D.content) && X(D.content).length == 2 ? 1 : 0;
            B(j, k);
          },
          l,
          n = (C) => {},
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__icon-close");
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class", Y(D.prefix) + "-icon");
                  if (C) O(N, "name", "close");
                  if (C) O(N, "aria-role", "button");
                  if (C) O(N, "aria-label", "关闭");
                  if (C) R.v(N, "tap", "onClose", !0, !1, !1, !1);
                },
                n
              );
            }
          },
          c = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon");
                if (C || K || undefined) O(N, "aria-hidden", true);
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__text");
              },
              i
            );
            l = D.closable ? 1 : 0;
            B(l, m);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.className || U.prefix) || undefined)
                  L(N, Y(D.className) + " class " + Y(D.prefix) + "-class");
                A["className"][0] = (D, E, T) => {
                  L(N, Y(D.className) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) R.v(N, "tap", "onClick", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/check-tag/check-tag";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/check-tag/check-tag.js";
define(
  "miniprogram_npm/tdesign-miniprogram/check-tag/check-tag.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      c = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      r = require("../common/src/index"),
      i = l(require("../common/config")),
      n = l(require("./props")),
      o = require("../common/utils");
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = i.default.prefix,
      d = "".concat(u, "-tag"),
      h = (function (s) {
        a(i, s);
        var r = c(i);
        function i() {
          var e;
          return (
            t(this, i),
            ((e = r.apply(this, arguments)).data = {
              prefix: u,
              classPrefix: d,
              className: "",
            }),
            (e.properties = n.default),
            (e.externalClasses = ["".concat(u, "-class")]),
            (e.controlledProps = [{ key: "checked", event: "change" }]),
            (e.options = { multipleSlots: !0 }),
            (e.lifetimes = {
              attached: function () {
                this.setClass();
              },
            }),
            (e.observers = {
              "size, disabled, checked": function () {
                this.setClass();
              },
              icon: function (e) {
                this.setData({ _icon: (0, o.calcIcon)(e) });
              },
            }),
            (e.methods = {
              setClass: function () {
                var e = this.data.classPrefix,
                  t = this.properties,
                  a = t.size,
                  c = t.variant,
                  s = t.disabled,
                  r = t.checked,
                  i = t.shape,
                  n = (0, o.classNames)([
                    e,
                    "".concat(e, "--checkable"),
                    s ? "".concat(e, "--disabled") : "",
                    r ? "".concat(e, "--checked") : "",
                    "".concat(e, "--").concat(r ? "primary" : "default"),
                    "".concat(e, "--").concat(a),
                    "".concat(e, "--").concat(c),
                    "".concat(e, "--").concat(i),
                  ]);
                this.setData({ className: n });
              },
              onClick: function () {
                if (!this.data.disabled) {
                  var e = this.data.checked;
                  this._trigger("click"),
                    this._trigger("change", { checked: !e });
                }
              },
              onClose: function (e) {
                this.data.disabled || this._trigger("close", e);
              },
            }),
            e
          );
        }
        return e(i);
      })(r.SuperComponent),
      p = (h = (0, s.__decorate)([(0, r.wxComponent)()], h));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/check-tag/check-tag.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/check-tag/check-tag.js");
