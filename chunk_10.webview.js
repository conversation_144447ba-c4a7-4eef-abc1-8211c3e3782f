__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/cascader/cascader": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            visible: new Array(1),
            style: new Array(1),
            title: new Array(1),
          },
          K = U === true,
          e = (C, T, E, B, F, S) => {
            S("title");
            C || K || U.title
              ? T(Y(D.title), (N) => {
                  A["title"][0] = (D, E, T) => {
                    T(N, Y(D.title));
                  };
                })
              : T();
          },
          g,
          i = (C) => {},
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "size", "48rpx");
                  if (C) O(N, "name", "close");
                },
                i
              );
            }
          },
          f = (C, T, E, B, F, S) => {
            S("close-btn");
            g = D.closeBtn ? 1 : 0;
            B(g, h);
          },
          k,
          m,
          p = (C, q, r, s, t, u, T, E) => {
            var w = (C) => {},
              x = (C, T) => {
                C || K || s ? T(Y(q)) : T();
              },
              y = (C) => {},
              v = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    var $A = q !== D.placeholder;
                    var $B = r === X(D.steps).length - 1;
                    if (
                      C ||
                      K ||
                      !!(
                        U.name ||
                        U.name ||
                        s ||
                        U.placeholder ||
                        ($A ? undefined : undefined) ||
                        U.name ||
                        t ||
                        Z(U.steps, "length") ||
                        ($B ? undefined : undefined)
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        Y(D.name) +
                          "__step-dot " +
                          Y(D.name) +
                          "__step-dot--" +
                          Y($A ? "active" : "") +
                          " " +
                          Y(D.name) +
                          "__step-dot--" +
                          Y($B ? "last" : "")
                      );
                  },
                  w
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    var $A = r === D.stepIndex;
                    if (
                      C ||
                      K ||
                      !!(
                        U.name ||
                        U.name ||
                        t ||
                        U.stepIndex ||
                        ($A ? undefined : undefined)
                      ) ||
                      undefined
                    )
                      L(
                        N,
                        Y(D.name) +
                          "__step-label " +
                          Y(D.name) +
                          "__step-label--" +
                          Y($A ? "active" : "")
                      );
                  },
                  x
                );
                E(
                  "t-icon",
                  {},
                  (N, C) => {
                    if (C) O(N, "name", "chevron-right");
                    if (C) O(N, "size", "44rpx");
                    if (C || K || !!U.name || undefined)
                      O(N, "t-class", Y(D.name) + "__step-arrow");
                  },
                  y
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined) L(N, Y(D.name) + "__step");
                if (C || K || t) R.d(N, "index", r);
                if (C) R.v(N, "tap", "onStepClick", !1, !1, !1, !1);
              },
              v
            );
          },
          o = (C, T, E, B, F) => {
            F(D.steps, "index", U ? U.steps : undefined, [0, "steps"], p);
          },
          n = (C, T, E) => {
            if (m === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.name || undefined)
                    L(N, Y(D.name) + "__steps");
                },
                o
              );
            }
          },
          q,
          t = (C, u, v, w, x, y, T, E) => {
            var z = (C) => {};
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C || K || x) O(N, "value", v);
                if (C || K || w) O(N, "label", u);
              },
              z
            );
          },
          s = (C, T, E, B, F) => {
            F(D.steps, "index", U ? U.steps : undefined, [0, "steps"], t);
          },
          r = (C, T, E) => {
            if (q === 1) {
              E(
                "t-tabs",
                {},
                (N, C) => {
                  if (C || K || U.stepIndex) O(N, "value", D.stepIndex);
                  if (C || K || undefined) O(N, "space-evenly", false);
                  if (C) R.v(N, "change", "onTabChange", !1, !1, !1, !1);
                  if (C) R.i(N, "tabs");
                },
                s
              );
            }
          },
          l = (C, T, E, B) => {
            if (k === 1) {
              m = D.theme == "step" ? 1 : 0;
              B(m, n);
              q = D.theme == "tab" ? 1 : 0;
              B(q, r);
            }
          },
          u,
          w = (C, T) => {
            var $A = D.stepIndex;
            C || K || !!U.stepIndex || Z(U.subTitles, $A)
              ? T(Y(X(D.subTitles)[$A]))
              : T();
          },
          v = (C, T, E) => {
            if (u === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.name || undefined)
                    L(N, Y(D.name) + "__options-title");
                },
                w
              );
            }
          },
          y = (C, z, A0, B0, C0, D0, T, E) => {
            var G0 = (C) => {},
              F0 = (C, T, E) => {
                E(
                  "t-radio-group",
                  {},
                  (N, C) => {
                    var $A = A0;
                    if (C || K || !!C0 || Z(U.selectedValue, $A))
                      O(N, "value", X(D.selectedValue)[$A]);
                    if (C || K || U.keys) O(N, "keys", D.keys);
                    if (C || K || B0) O(N, "options", z);
                    if (C) O(N, "placement", "right");
                    if (C) O(N, "icon", "line");
                    if (C) O(N, "borderless", true);
                    if (C || K || C0) R.d(N, "level", A0);
                    if (C) R.v(N, "change", "handleSelect", !1, !1, !1, !1);
                  },
                  G0
                );
              },
              E0 = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!C0 || undefined)
                      L(N, "cascader-radio-group-" + Y(A0));
                  },
                  F0
                );
              };
            E(
              "scroll-view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined)
                  L(N, Y(D.name) + "__options");
                if (C || K || !!U._optionsHeight || undefined)
                  R.y(N, "height:" + Y(D._optionsHeight) + "px");
                if (C) O(N, "scroll-y", true);
                var $A = A0;
                if (C || K || !!C0 || Z(U.scrollTopList, $A))
                  O(N, "scroll-top", X(D.scrollTopList)[$A]);
                if (C) O(N, "type", "list");
              },
              E0
            );
          },
          x = (C, T, E, B, F) => {
            F(D.items, "index", U ? U.items : undefined, [0, "items"], y);
          },
          j = (C, T, E, B) => {
            k = D.steps && X(D.steps).length ? 1 : 0;
            B(k, l);
            var $A = D.stepIndex;
            u = D.subTitles && X(D.subTitles)[$A] ? 1 : 0;
            B(u, v);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined)
                  L(N, Y(D.name) + "__options-container");
                if (
                  C ||
                  K ||
                  !!(Z(U.items, "length") || U.stepIndex) ||
                  undefined
                )
                  R.y(
                    N,
                    "width:" +
                      Y(X(D.items).length + 1) +
                      "00vw;transform:translateX(-" +
                      Y(D.stepIndex) +
                      "00vw)"
                  );
              },
              x
            );
          },
          d = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined)
                  L(N, Y(D.name) + "__title");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined)
                  L(N, Y(D.name) + "__close-btn");
                if (C) R.v(N, "tap", "onClose", !1, !1, !1, !1);
              },
              f
            );
            S("header");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.name || undefined)
                  L(N, Y(D.name) + "__content");
              },
              j
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || U.name) L(N, D.name);
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "t-popup",
              {},
              (N, C) => {
                if (C) L(N, "class");
                if (C || K || U.visible) O(N, "visible", D.visible);
                A["visible"][0] = (D, E, T) => {
                  O(N, "visible", D.visible);
                  E(N);
                };
                if (C) O(N, "placement", "bottom");
                if (C)
                  R.v(N, "visible-change", "onVisibleChange", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/cascader/cascader.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-cascader{--td-radio-icon-checked-color:var(--td-cascader-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));--td-tab-item-active-color:var(--td-cascader-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));--td-tab-track-color:var(--td-cascader-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));background-color:var(--td-cascader-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));border-radius:var(--td-radius-extra-large,",
        [0, 24],
        ") var(--td-radius-extra-large,",
        [0, 24],
        ") 0 0;color:var(--td-cascader-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column}\n.",
        [1],
        "t-cascader__close-btn{position:absolute;right:",
        [0, 32],
        ";top:",
        [0, 24],
        "}\n.",
        [1],
        "t-cascader__title{font-size:var(--td-cascder-title-font-size,",
        [0, 36],
        ");font-weight:700;line-height:var(--td-cascader-title-height,",
        [0, 26],
        ");padding:var(--td-cascader-title-padding,var(--td-spacer-2,",
        [0, 32],
        "));position:relative;text-align:center}\n.",
        [1],
        "t-cascader__content{display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;height:var(--td-cascader-content-height,78vh);width:100%}\n.",
        [1],
        "t-cascader__options{width:100vw}\n.",
        [1],
        "t-cascader__options-title{box-sizing:border-box;color:var(--td-cascader-options-title-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-font-size-base,",
        [0, 28],
        ");line-height:",
        [0, 44],
        ";padding-left:var(--td-spacer-2,",
        [0, 32],
        ");padding-top:",
        [0, 40],
        "}\n.",
        [1],
        "t-cascader__options-container{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;transition:all .3s ease}\n.",
        [1],
        "t-cascader__step{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;height:var(--td-cascader-step-height,",
        [0, 88],
        ")}\n.",
        [1],
        "t-cascader__steps{padding:0 ",
        [0, 32],
        " ",
        [0, 10],
        ";position:relative}\n.",
        [1],
        "t-cascader__steps::after{background-color:var(--td-cascader-border-color,var(--td-component-stroke,var(--td-gray-color-3,#e7e7e7)));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
        [1],
        "t-cascader__step-dot{border:",
        [0, 2],
        " solid var(--td-cascader-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:50%;box-sizing:border-box;height:var(--td-cascader-step-dot-size,",
        [0, 16],
        ");position:relative;width:var(--td-cascader-step-dot-size,",
        [0, 16],
        ")}\n.",
        [1],
        "t-cascader__step-dot:not(.",
        [1],
        "t-cascader__step-dot--last)::after{content:\x22\x22;display:block;height:",
        [0, 36],
        ";left:50%;position:absolute;top:calc(var(--td-cascader-step-dot-size,",
        [0, 16],
        ") + ",
        [0, 14],
        ");-webkit-transform:translateX(-50%);transform:translateX(-50%);width:",
        [0, 2],
        "}\n.",
        [1],
        "t-cascader__step-dot--active,.",
        [1],
        "t-cascader__step-dot:not(.",
        [1],
        "t-cascader__step-dot--last)::after{background:var(--td-cascader-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-cascader__step-dot--active{border-color:var(--td-cascader-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-cascader__step-label{font-size:var(--td-font-size-m,",
        [0, 32],
        ");padding-left:var(--td-spacer-2,",
        [0, 32],
        ")}\n.",
        [1],
        "t-cascader__step-label--active{color:var(--td-cascader-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));font-weight:600}\n.",
        [1],
        "t-cascader__step-arrow{color:var(--td-cascader-step-arrow-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));margin-left:auto}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/cascader/cascader.wxss" }
    );
}
