@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-pull-down-refresh {
  height: 100%;
  overflow: hidden;
}
.t-pull-down-refresh__track {
  position: relative;
}
.t-pull-down-refresh__track--loosing {
  transition: -webkit-transform 0.24s ease;
  transition: transform 0.24s ease;
  transition: transform 0.24s ease, -webkit-transform 0.24s ease;
}
.t-pull-down-refresh__tips {
  align-items: center;
  color: var(
    --td-pull-down-refresh-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  font-size: 24rpx;
  justify-content: center;
  overflow: hidden;
  position: absolute;
  top: 0;
  transform: translateY(-100%);
  width: 100%;
}
.t-pull-down-refresh__tips--loosing {
  transition: height 0.24s ease;
}
.t-pull-down-refresh__text {
  margin: 16rpx 0 0;
}
