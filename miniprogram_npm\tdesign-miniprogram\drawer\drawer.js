Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  r = require("../../../@babel/runtime/helpers/classCallCheck"),
  t = require("../../../@babel/runtime/helpers/inherits"),
  i = require("../../../@babel/runtime/helpers/createSuper"),
  s = require("tslib"),
  a = require("../common/src/index"),
  l = o(require("../common/config")),
  n = o(require("./props")),
  u = o(require("../mixins/using-custom-navbar"));
function o(e) {
  return e && e.__esModule ? e : { default: e };
}
var c = l.default.prefix,
  p = "".concat(c, "-drawer"),
  d = (function (s) {
    t(l, s);
    var a = i(l);
    function l() {
      var e;
      return (
        r(this, l),
        ((e = a.apply(this, arguments)).behaviors = [u.default]),
        (e.externalClasses = []),
        (e.options = { multipleSlots: !0 }),
        (e.properties = n.default),
        (e.data = { classPrefix: p }),
        (e.methods = {
          visibleChange: function (e) {
            var r = e.detail.visible,
              t = this.data.showOverlay;
            this.setData({ visible: r }),
              r || this.triggerEvent("close", { trigger: "overlay" }),
              t && this.triggerEvent("overlay-click", { visible: r });
          },
          itemClick: function (e) {
            var r = e.currentTarget.dataset,
              t = r.index,
              i = r.item;
            this.triggerEvent("item-click", { index: t, item: i });
          },
        }),
        e
      );
    }
    return e(l);
  })(a.SuperComponent),
  v = (d = (0, s.__decorate)([(0, a.wxComponent)()], d));
exports.default = v;
