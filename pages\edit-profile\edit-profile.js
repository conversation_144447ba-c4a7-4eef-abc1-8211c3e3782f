var a = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  e = require("../../@babel/runtime/helpers/asyncToGenerator");
Page({
  data: {
    formData: {
      avatar: "",
      avatarFileID: "",
      nickname: "",
      genderIndex: null,
      bio: "",
    },
    genderOptions: ["男", "女"],
    showAvatarModal: !1,
    avatarList: [],
  },
  onLoad: function (a) {
    var e = getApp().globalData.userInfo.data || {};
    console.log("用户信息:", e),
      e &&
        (this.setData({
          "formData.nickname": e.nickname || "",
          "formData.genderIndex":
            e.gender && "male" === e.gender.value
              ? 0
              : e.gender && "female" === e.gender.value
              ? 1
              : null,
          "formData.bio": e.bio || "",
          "formData.avatarFileID": e.avatar || "",
        }),
        e.avatar && this.loadUserAvatar(e.avatar)),
      this.fetchDefaultAvatars();
  },
  loadUserAvatar: function (t) {
    var n = this;
    return e(
      a().mark(function e() {
        var r, o;
        return a().wrap(
          function (a) {
            for (;;)
              switch ((a.prev = a.next)) {
                case 0:
                  return (
                    (a.prev = 0), (r = getApp()), (a.next = 4), r.getTempFile(t)
                  );
                case 4:
                  (o = a.sent) &&
                    o.fileList &&
                    o.fileList.length > 0 &&
                    n.setData({ "formData.avatar": o.fileList[0].tempFileURL }),
                    (a.next = 11);
                  break;
                case 8:
                  (a.prev = 8),
                    (a.t0 = a.catch(0)),
                    console.error("获取用户头像失败:", a.t0);
                case 11:
                case "end":
                  return a.stop();
              }
          },
          e,
          null,
          [[0, 8]]
        );
      })
    )();
  },
  fetchDefaultAvatars: function () {
    var t = this;
    return e(
      a().mark(function e() {
        var n, r, o, i, s, c;
        return a().wrap(
          function (a) {
            for (;;)
              switch ((a.prev = a.next)) {
                case 0:
                  return (
                    (a.prev = 0),
                    wx.showLoading({ title: "加载头像中...", mask: !0 }),
                    (n = getApp()),
                    (a.next = 5),
                    n.call({
                      path: "/api/mp/images/default-avatars",
                      method: "GET",
                    })
                  );
                case 5:
                  if ((r = a.sent) && r.data && Array.isArray(r.data)) {
                    a.next = 8;
                    break;
                  }
                  throw new Error("获取头像列表失败");
                case 8:
                  if (
                    ((o = r.data),
                    console.log("获取到的fileIDs:", o),
                    (i = o.map(function (a) {
                      return a.file_id;
                    })),
                    console.log("提取的file_id列表:", i),
                    !(i.length > 0))
                  ) {
                    a.next = 24;
                    break;
                  }
                  return (a.next = 15), n.getTempFile(i);
                case 15:
                  if (!(s = a.sent) || !s.fileList) {
                    a.next = 21;
                    break;
                  }
                  (c = s.fileList.map(function (a) {
                    return { src: a.tempFileURL, fileID: a.fileID };
                  })),
                    t.setData({ avatarList: c }),
                    (a.next = 22);
                  break;
                case 21:
                  throw new Error("获取临时文件链接失败");
                case 22:
                  a.next = 25;
                  break;
                case 24:
                  wx.showToast({ title: "暂无可用头像", icon: "none" });
                case 25:
                  a.next = 31;
                  break;
                case 27:
                  (a.prev = 27),
                    (a.t0 = a.catch(0)),
                    console.error("获取默认头像失败:", a.t0),
                    wx.showToast({ title: "获取头像失败", icon: "none" });
                case 31:
                  return (a.prev = 31), wx.hideLoading(), a.finish(31);
                case 34:
                case "end":
                  return a.stop();
              }
          },
          e,
          null,
          [[0, 27, 31, 34]]
        );
      })
    )();
  },
  onNicknameInput: function (a) {
    this.setData({ "formData.nickname": a.detail.value });
  },
  onNicknameBlur: function (a) {
    var e = a.detail.value;
    e &&
      0 === e.trim().length &&
      wx.showToast({ title: "昵称不能为空", icon: "none" });
  },
  onGenderChange: function (a) {
    this.setData({ "formData.genderIndex": parseInt(a.detail.value) });
  },
  onCancel: function () {
    wx.showModal({
      title: "提示",
      content: "确定要取消编辑吗？",
      success: function (a) {
        a.confirm && wx.navigateBack();
      },
    });
  },
  onSubmit: function () {
    if (this.validateForm()) {
      wx.showLoading({ title: "保存中..." });
      var a = {
          avatar: this.data.formData.avatarFileID,
          nickname: this.data.formData.nickname,
          gender: {
            label: this.data.genderOptions[this.data.formData.genderIndex],
            value: 0 === this.data.formData.genderIndex ? "male" : "female",
          },
          bio: this.data.formData.bio,
        },
        e = getApp(),
        t = e.globalData.openid;
      if (!t)
        return (
          wx.hideLoading(),
          void wx.showToast({ title: "用户未登录", icon: "error" })
        );
      e.call({
        path: "/api/mp/auth/user/".concat(t),
        method: "PUT",
        data: {
          avatar: a.avatar,
          nickname: a.nickname,
          gender: a.gender.value,
          bio: a.bio,
        },
      })
        .then(function (a) {
          wx.hideLoading(),
            0 === a.code
              ? e
                  .refreshUserInfo()
                  .then(function () {
                    wx.showToast({
                      title: "保存成功",
                      icon: "success",
                      success: function () {
                        setTimeout(function () {
                          wx.navigateBack();
                        }, 1500);
                      },
                    });
                  })
                  .catch(function (a) {
                    console.error("刷新用户信息失败:", a),
                      wx.showToast({
                        title: "保存成功，但刷新信息失败",
                        icon: "none",
                      });
                  })
              : wx.showToast({ title: a.message || "保存失败", icon: "error" });
        })
        .catch(function (a) {
          console.error("更新用户信息失败:", a),
            wx.hideLoading(),
            wx.showToast({ title: "保存失败", icon: "error" });
        });
    }
  },
  validateForm: function () {
    var a = this.data.formData,
      e = a.nickname,
      t = a.genderIndex;
    return e && 0 !== e.trim().length
      ? e.trim().length < 2 || e.trim().length > 20
        ? (wx.showToast({ title: "昵称长度应在2-20个字符之间", icon: "none" }),
          !1)
        : null != t || (wx.showToast({ title: "请选择性别", icon: "none" }), !1)
      : (wx.showToast({ title: "请输入昵称", icon: "none" }), !1);
  },
  showAvatarSelector: function () {
    this.setData({ showAvatarModal: !0 });
  },
  hideAvatarSelector: function () {
    this.setData({ showAvatarModal: !1 });
  },
  selectAvatar: function (a) {
    var e = a.currentTarget.dataset.src,
      t = a.currentTarget.dataset.fileid;
    this.setData({
      "formData.avatar": e,
      "formData.avatarFileID": t,
      showAvatarModal: !1,
    }),
      wx.showToast({ title: "头像已选择", icon: "success", duration: 1500 });
  },
});
