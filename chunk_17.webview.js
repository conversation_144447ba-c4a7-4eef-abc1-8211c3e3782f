__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/collapse/collapse": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            theme: new Array(1),
            customStyle: new Array(1),
            border: new Array(1),
            classPrefix: new Array(1),
            prefix: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.border]), U.theme])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          ["hairline--top-bottom", D.border],
                          D.theme,
                        ])
                      )
                  );
                A["prefix"][0] =
                  A["classPrefix"][0] =
                  A["border"][0] =
                  A["theme"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        "class " +
                          Y(D.prefix) +
                          "-class " +
                          Y(
                            P(X(a).cls)(D.classPrefix, [
                              ["hairline--top-bottom", D.border],
                              D.theme,
                            ])
                          )
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/collapse/collapse.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-collapse--card{border-radius:var(--td-radius-large,",
        [0, 18],
        ");margin:0 ",
        [0, 32],
        ";overflow:hidden}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/collapse/collapse.wxss" }
    );
}
