__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/collapse-panel/collapse-panel":
      (() => {
        var H = {};
        var S;
        var I = (P) => {
          if (!S) S = Object.assign({}, H);
          return S[P];
        };
        var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
        H[""] = (R, C, D, U) => {
          R.setFnFilter(Q.A, Q.B);
          if (typeof R.setEventListenerWrapper === "function")
            R.setEventListenerWrapper(Q.C);
          var L = R.c,
            M = R.m,
            O = R.r,
            A = {
              customStyle: new Array(1),
              classPrefix: new Array(7),
              style: new Array(1),
              headerRightContent: new Array(1),
              placement: new Array(4),
              prefix: new Array(3),
              headerLeftIcon: new Array(1),
              animation: new Array(1),
              content: new Array(1),
              ultimateDisabled: new Array(5),
              expanded: new Array(5),
              ultimateExpandIcon: new Array(1),
              header: new Array(1),
            },
            K = U === true,
            e = (C, T, E, B, F, S) => {
              S("header-left-icon", (N) => {}, "left-icon");
              S("header", (N) => {}, "title");
              S("header-right-content", (N) => {}, "note");
              S("expand-icon", (N) => {}, "right-icon");
            },
            d = (C, T, E) => {
              E(
                "t-cell",
                {},
                (N, C) => {
                  if (C || K || U.header) O(N, "title", D.header);
                  A["header"][0] = (D, E, T) => {
                    O(N, "title", D.header);
                    E(N);
                  };
                  if (C || K || U.headerRightContent)
                    O(N, "note", D.headerRightContent);
                  A["headerRightContent"][0] = (D, E, T) => {
                    O(N, "note", D.headerRightContent);
                    E(N);
                  };
                  if (C) O(N, "bordered", true);
                  if (C || K || U.headerLeftIcon)
                    O(N, "left-icon", D.headerLeftIcon);
                  A["headerLeftIcon"][0] = (D, E, T) => {
                    O(N, "left-icon", D.headerLeftIcon);
                    E(N);
                  };
                  var $A = D.ultimateExpandIcon;
                  var $B = D.expanded;
                  if (
                    C ||
                    K ||
                    !!U.ultimateExpandIcon ||
                    ($A
                      ? !!U.expanded || ($B ? undefined : undefined)
                      : undefined)
                  )
                    O(
                      N,
                      "right-icon",
                      $A ? ($B ? "chevron-up" : "chevron-down") : ""
                    );
                  A["ultimateExpandIcon"][0] = A["expanded"][1] = (D, E, T) => {
                    var $C = D.ultimateExpandIcon;
                    var $D = D.expanded;
                    O(
                      N,
                      "right-icon",
                      $C ? ($D ? "chevron-up" : "chevron-down") : ""
                    );
                    E(N);
                  };
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.placement, Q.a([U.expanded])]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "t-class",
                      Y(
                        P(X(a).cls)(D.classPrefix + "__header", [
                          D.placement,
                          ["expanded", D.expanded],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-header"
                    );
                  A["classPrefix"][3] =
                    A["placement"][1] =
                    A["expanded"][2] =
                    A["prefix"][1] =
                      (D, E, T) => {
                        O(
                          N,
                          "t-class",
                          Y(
                            P(X(a).cls)(D.classPrefix + "__header", [
                              D.placement,
                              ["expanded", D.expanded],
                            ])
                          ) +
                            " " +
                            Y(D.prefix) +
                            "-class-header"
                        );
                        E(N);
                      };
                  var $C = D.ultimateDisabled;
                  if (
                    C ||
                    K ||
                    !!(U.ultimateDisabled || ($C ? undefined : undefined)) ||
                    undefined
                  )
                    O(
                      N,
                      "t-class-title",
                      "class-title " + Y($C ? "class-title--disabled" : "")
                    );
                  A["ultimateDisabled"][1] = (D, E, T) => {
                    var $D = D.ultimateDisabled;
                    O(
                      N,
                      "t-class-title",
                      "class-title " + Y($D ? "class-title--disabled" : "")
                    );
                    E(N);
                  };
                  var $D = D.ultimateDisabled;
                  if (
                    C ||
                    K ||
                    !!(U.ultimateDisabled || ($D ? undefined : undefined)) ||
                    undefined
                  )
                    O(
                      N,
                      "t-class-note",
                      "class-note " + Y($D ? "class-note--disabled" : "")
                    );
                  A["ultimateDisabled"][2] = (D, E, T) => {
                    var $E = D.ultimateDisabled;
                    O(
                      N,
                      "t-class-note",
                      "class-note " + Y($E ? "class-note--disabled" : "")
                    );
                    E(N);
                  };
                  var $E = D.ultimateDisabled;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.placement ||
                      U.ultimateDisabled ||
                      ($E ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "t-class-right-icon",
                      "class-right-icon " +
                        Y(D.classPrefix) +
                        "__arrow--" +
                        Y(D.placement) +
                        " " +
                        Y($E ? "class-right-icon--disabled" : "")
                    );
                  A["classPrefix"][4] =
                    A["placement"][2] =
                    A["ultimateDisabled"][3] =
                      (D, E, T) => {
                        var $F = D.ultimateDisabled;
                        O(
                          N,
                          "t-class-right-icon",
                          "class-right-icon " +
                            Y(D.classPrefix) +
                            "__arrow--" +
                            Y(D.placement) +
                            " " +
                            Y($F ? "class-right-icon--disabled" : "")
                        );
                        E(N);
                      };
                  if (C) O(N, "t-class-hover", "class-header-hover");
                },
                e
              );
            },
            g = (C, T, E, B, F, S) => {
              C || K || U.content
                ? T(Y(D.content), (N) => {
                    A["content"][0] = (D, E, T) => {
                      T(N, Y(D.content));
                    };
                  })
                : T();
              S("");
              S("content");
            },
            f = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([U.ultimateDisabled]),
                        Q.a([U.expanded]),
                        U.placement,
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__content", [
                          ["disabled", D.ultimateDisabled],
                          ["expanded", D.expanded],
                          D.placement,
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-content"
                    );
                  A["classPrefix"][6] =
                    A["ultimateDisabled"][4] =
                    A["expanded"][4] =
                    A["placement"][3] =
                    A["prefix"][2] =
                      (D, E, T) => {
                        L(
                          N,
                          Y(
                            P(X(a).cls)(D.classPrefix + "__content", [
                              ["disabled", D.ultimateDisabled],
                              ["expanded", D.expanded],
                              D.placement,
                            ])
                          ) +
                            " " +
                            Y(D.prefix) +
                            "-class-content"
                        );
                      };
                },
                g
              );
            },
            c = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__title");
                  A["classPrefix"][2] = (D, E, T) => {
                    L(N, Y(D.classPrefix) + "__title");
                  };
                  if (C) O(N, "aria-role", "button");
                  if (C || K || U.expanded) O(N, "aria-expanded", D.expanded);
                  A["expanded"][0] = (D, E, T) => {
                    O(N, "aria-expanded", D.expanded);
                    E(N);
                  };
                  if (C || K || U.ultimateDisabled)
                    O(N, "aria-disabled", D.ultimateDisabled);
                  A["ultimateDisabled"][0] = (D, E, T) => {
                    O(N, "aria-disabled", D.ultimateDisabled);
                    E(N);
                  };
                  if (C) R.v(N, "tap", "onClick", !1, !1, !1, !1);
                },
                d
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__wrapper");
                  A["classPrefix"][5] = (D, E, T) => {
                    L(N, Y(D.classPrefix) + "__wrapper");
                  };
                  if (C || K || U.animation) O(N, "animation", D.animation);
                  A["animation"][0] = (D, E, T) => {
                    O(N, "animation", D.animation);
                    E(N);
                  };
                  var $A = D.expanded;
                  if (C || K || !!U.expanded || ($A ? undefined : undefined))
                    O(N, "aria-hidden", $A ? "" : true);
                  A["expanded"][3] = (D, E, T) => {
                    var $B = D.expanded;
                    O(N, "aria-hidden", $B ? "" : true);
                    E(N);
                  };
                },
                f
              );
            },
            b = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.placement ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      "class " +
                        Y(D.classPrefix) +
                        " " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.placement) +
                        " " +
                        Y(D.prefix) +
                        "-class"
                    );
                  A["classPrefix"][0] =
                    A["classPrefix"][1] =
                    A["placement"][0] =
                    A["prefix"][0] =
                      (D, E, T) => {
                        L(
                          N,
                          "class " +
                            Y(D.classPrefix) +
                            " " +
                            Y(D.classPrefix) +
                            "--" +
                            Y(D.placement) +
                            " " +
                            Y(D.prefix) +
                            "-class"
                        );
                      };
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  };
                },
                c
              );
            };
          return { C: b, B: A };
        };
        return Object.assign(
          function (R) {
            return H[R];
          },
          { _: H }
        );
      })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/collapse-panel/collapse-panel";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/collapse-panel/collapse-panel.js";
define(
  "miniprogram_npm/tdesign-miniprogram/collapse-panel/collapse-panel.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0),
      require("../../../@babel/runtime/helpers/Arrayincludes");
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      r = require("tslib"),
      n = require("../common/src/index"),
      s = p(require("../common/config")),
      l = p(require("./props")),
      o = require("../common/utils");
    function p(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = s.default.prefix,
      d = "".concat(u, "-collapse-panel"),
      c = (function (r) {
        a(s, r);
        var n = i(s);
        function s() {
          var e;
          return (
            t(this, s),
            ((e = n.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-content"),
              "".concat(u, "-class-header"),
            ]),
            (e.options = { multipleSlots: !0 }),
            (e.relations = {
              "../collapse/collapse": {
                type: "ancestor",
                linked: function (e) {
                  var t = e.properties,
                    a = t.value,
                    i = t.expandIcon,
                    r = t.disabled;
                  this.setData({
                    ultimateExpandIcon:
                      null == this.properties.expandIcon
                        ? i
                        : this.properties.expandIcon,
                    ultimateDisabled:
                      null == this.properties.disabled
                        ? r
                        : this.properties.disabled,
                  }),
                    this.updateExpanded(a);
                },
              },
            }),
            (e.properties = l.default),
            (e.data = {
              prefix: u,
              expanded: !1,
              classPrefix: d,
              classBasePrefix: u,
              ultimateExpandIcon: !1,
              ultimateDisabled: !1,
            }),
            (e.observers = {
              disabled: function (e) {
                this.setData({ ultimateDisabled: !!e });
              },
            }),
            (e.methods = {
              updateExpanded: function () {
                var e =
                  arguments.length > 0 && void 0 !== arguments[0]
                    ? arguments[0]
                    : [];
                if (this.$parent) {
                  var t = this.properties.value,
                    a = this.$parent.data.defaultExpandAll,
                    i = a ? !this.data.expanded : e.includes(t);
                  i !== this.properties.expanded &&
                    (this.setData({ expanded: i }), this.updateStyle(i));
                }
              },
              updateStyle: function (e) {
                var t = this;
                return (0, o.getRect)(this, ".".concat(d, "__content"))
                  .then(function (e) {
                    return e.height;
                  })
                  .then(function (a) {
                    var i = wx.createAnimation({
                      duration: 0,
                      timingFunction: "ease-in-out",
                    });
                    e
                      ? i
                          .height(a)
                          .top(0)
                          .step({ duration: 300 })
                          .height("auto")
                          .step()
                      : i
                          .height(a)
                          .top(1)
                          .step({ duration: 1 })
                          .height(0)
                          .step({ duration: 300 }),
                      t.setData({ animation: i.export() });
                  });
              },
              onClick: function () {
                var e = this.data.ultimateDisabled,
                  t = this.properties.value;
                e ||
                  (this.$parent.data.defaultExpandAll
                    ? this.updateExpanded()
                    : this.$parent.switch(t));
              },
            }),
            e
          );
        }
        return e(s);
      })(n.SuperComponent),
      h = (c = (0, r.__decorate)([(0, n.wxComponent)()], c));
    exports.default = h;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/collapse-panel/collapse-panel.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/collapse-panel/collapse-panel.js");
