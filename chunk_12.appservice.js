__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/cell/cell": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            arrow: new Array(1),
            ariaLabel: new Array(1),
            bordered: new Array(1),
            align: new Array(1),
            customStyle: new Array(1),
            isLastChild: new Array(1),
            style: new Array(1),
            hover: new Array(1),
            ariaRole: new Array(1),
          },
          K = U === true,
          e,
          g,
          h = (C, T, E, B, F, S, J) => {
            var $A = I(g);
            if (g && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix +
                      "__left-icon " +
                      D.prefix +
                      "-class-left-icon",
                  },
                  X(D._leftIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._leftIcon === true ||
                      Object.assign(
                        { tClass: !!(U.classPrefix || U.prefix) || undefined },
                        X(U._leftIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              g = "icon";
              B(g, h);
            }
          },
          i,
          k = (C) => {},
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (C) O(N, "shape", "round");
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    O(
                      N,
                      "t-class",
                      Y(D.classPrefix) +
                        "__left-image " +
                        Y(D.prefix) +
                        "-class-image"
                    );
                  if (C || K || U.image) O(N, "src", D.image);
                },
                k
              );
            }
          },
          d = (C, T, E, B, F, S) => {
            e = D._leftIcon ? 1 : 0;
            B(e, f);
            S("left-icon");
            i = D.image ? 1 : 0;
            B(i, j);
            S("image");
          },
          n,
          o = (C, T) => {
            if (n === 1) {
              C || K || U.title ? T(Y(D.title)) : T();
            }
          },
          p,
          r = (C, T) => {
            C ? T("\u{a0}*") : T();
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "--required");
                  if (C) O(N, "decode", true);
                },
                r
              );
            }
          },
          m = (C, T, E, B, F, S) => {
            n = D.title ? 1 : 0;
            B(n, o);
            S("title");
            p = D.required ? 1 : 0;
            B(p, q);
          },
          t,
          v = (C, T) => {
            C || K || U.description ? T(Y(D.description)) : T();
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__description-text");
                },
                v
              );
            }
          },
          s = (C, T, E, B, F, S) => {
            t = D.description ? 1 : 0;
            B(t, u);
            S("description");
          },
          l = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__title-text " +
                      Y(D.prefix) +
                      "-class-title"
                  );
              },
              m
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__description " +
                      Y(D.prefix) +
                      "-class-description"
                  );
              },
              s
            );
          },
          x,
          z = (C, T) => {
            C || K || U.note ? T(Y(D.note)) : T();
          },
          y = (C, T, E) => {
            if (x === 1) {
              E("text", {}, (N, C) => {}, z);
            }
          },
          w = (C, T, E, B, F, S) => {
            x = D.note ? 1 : 0;
            B(x, y);
            S("note");
          },
          B0,
          D0,
          E0 = (C, T, E, B, F, S, J) => {
            var $A = I(D0);
            if (D0 && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix +
                      "__right-icon " +
                      D.prefix +
                      "-class-right-icon",
                  },
                  X(D._arrow),
                  {}
                ),
                K ||
                  (U
                    ? U._arrow === true ||
                      Object.assign(
                        { tClass: !!(U.classPrefix || U.prefix) || undefined },
                        X(U._arrow),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          F0,
          H0,
          I0 = (C, T, E, B, F, S, J) => {
            var $A = I(H0);
            if (H0 && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix +
                      "__right-icon " +
                      D.prefix +
                      "-class-right-icon",
                  },
                  X(D._rightIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._rightIcon === true ||
                      Object.assign(
                        { tClass: !!(U.classPrefix || U.prefix) || undefined },
                        X(U._rightIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          G0 = (C, T, E, B, F, S, J) => {
            if (F0 === 1) {
              H0 = "icon";
              B(H0, I0);
            }
          },
          C0 = (C, T, E, B, F, S, J) => {
            if (B0 === 1) {
              D0 = "icon";
              B(D0, E0);
            } else {
              F0 = D._rightIcon ? 1 : 0;
              B(F0, G0);
              S("right-icon");
            }
          },
          A0 = (C, T, E, B) => {
            B0 = D._arrow ? 1 : 0;
            B(B0, C0);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__left " + Y(D.prefix) + "-class-left"
                  );
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__title " +
                      Y(D.prefix) +
                      "-class-center"
                  );
              },
              l
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__note " + Y(D.prefix) + "-class-note"
                  );
              },
              w
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.align]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__right", [D.align])) +
                      " " +
                      Y(D.prefix) +
                      "-class-right"
                  );
                A["align"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__right", [D.align])) +
                      " " +
                      Y(D.prefix) +
                      "-class-right"
                  );
                };
              },
              A0
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!(U.bordered || U.isLastChild) || undefined])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          ["borderless", !D.bordered || D.isLastChild],
                        ])
                      )
                  );
                A["bordered"][0] = A["isLastChild"][0] = (D, E, T) => {
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          ["borderless", !D.bordered || D.isLastChild],
                        ])
                      )
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                var $A = D.hover;
                if (
                  C ||
                  K ||
                  !!U.hover ||
                  ($A ? !!U.classPrefix || undefined : undefined)
                )
                  O(N, "hover-class", $A ? D.classPrefix + "--hover" : "");
                A["hover"][0] = (D, E, T) => {
                  var $B = D.hover;
                  O(N, "hover-class", $B ? D.classPrefix + "--hover" : "");
                  E(N);
                };
                if (C) O(N, "hover-stay-time", "70");
                var $B = D.arrow;
                if (
                  C ||
                  K ||
                  !!(U.ariaRole || U.arrow || ($B ? undefined : undefined)) ||
                  undefined
                )
                  O(N, "aria-role", D.ariaRole || ($B ? "button" : ""));
                A["ariaRole"][0] = A["arrow"][0] = (D, E, T) => {
                  var $C = D.arrow;
                  O(N, "aria-role", D.ariaRole || ($C ? "button" : ""));
                  E(N);
                };
                if (C || K || U.ariaLabel) O(N, "aria-label", D.ariaLabel);
                A["ariaLabel"][0] = (D, E, T) => {
                  O(N, "aria-label", D.ariaLabel);
                  E(N);
                };
                if (C) R.v(N, "tap", "onClick", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/cell/cell";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/cell/cell.js";
define(
  "miniprogram_npm/tdesign-miniprogram/cell/cell.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/defineProperty"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/createClass"),
      c = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      s = require("../common/src/index"),
      o = u(require("../common/config")),
      a = u(require("./props")),
      l = require("../common/utils");
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var p = o.default.prefix,
      h = "".concat(p, "-cell"),
      f = (function (i) {
        c(o, i);
        var s = n(o);
        function o() {
          var e;
          return (
            t(this, o),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(p, "-class"),
              "".concat(p, "-class-title"),
              "".concat(p, "-class-description"),
              "".concat(p, "-class-note"),
              "".concat(p, "-class-hover"),
              "".concat(p, "-class-image"),
              "".concat(p, "-class-left"),
              "".concat(p, "-class-left-icon"),
              "".concat(p, "-class-center"),
              "".concat(p, "-class-right"),
              "".concat(p, "-class-right-icon"),
            ]),
            (e.relations = { "../cell-group/cell-group": { type: "parent" } }),
            (e.options = { multipleSlots: !0 }),
            (e.properties = a.default),
            (e.data = { prefix: p, classPrefix: h, isLastChild: !1 }),
            (e.observers = {
              leftIcon: function (e) {
                this.setIcon("_leftIcon", e, "");
              },
              rightIcon: function (e) {
                this.setIcon("_rightIcon", e, "");
              },
              arrow: function (e) {
                this.setIcon("_arrow", e, "chevron-right");
              },
            }),
            e
          );
        }
        return (
          r(o, [
            {
              key: "setIcon",
              value: function (t, r, c) {
                this.setData(e({}, t, (0, l.calcIcon)(r, c)));
              },
            },
            {
              key: "onClick",
              value: function (e) {
                this.triggerEvent("click", e.detail), this.jumpLink();
              },
            },
            {
              key: "jumpLink",
              value: function () {
                var e =
                    arguments.length > 0 && void 0 !== arguments[0]
                      ? arguments[0]
                      : "url",
                  t =
                    arguments.length > 1 && void 0 !== arguments[1]
                      ? arguments[1]
                      : "jumpType",
                  r = this.data[e],
                  c = this.data[t];
                r && wx[c]({ url: r });
              },
            },
          ]),
          o
        );
      })(s.SuperComponent),
      d = (f = (0, i.__decorate)([(0, s.wxComponent)()], f));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/cell/cell.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/cell/cell.js");
