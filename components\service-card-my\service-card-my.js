var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/asyncToGenerator");
Component({
  properties: {
    MyserviceData: {
      type: Object,
      value: {
        id: 0,
        name: "默认标题",
        description: "默认描述",
        thumbnail: "/assets/img/汉堡包.png",
        price: 0,
        order_count: 0,
        ongoing_order_count: 0,
        positive_rate: 0,
        publish_status: "online",
        audit_status: "approved",
        category: { id: 1, name: "默认分类" },
        createdAt: "",
        updatedAt: "",
      },
    },
  },
  data: { isOfflineLoading: !1, isOnlineLoading: !1 },
  methods: {
    onServiceClick: function () {
      var e = this;
      this.navigating ||
        ((this.navigating = !0),
        wx.navigateTo({
          url: "/pages/service-info/service-info?id=".concat(
            this.data.MyserviceData.id
          ),
          complete: function () {
            e.navigating = !1;
          },
        }));
    },
    onOrderClick: function () {
      var e = this;
      this.navigating ||
        ((this.navigating = !0),
        wx.navigateTo({
          url: "/pages/service-order-list/service-order-list?serviceId=".concat(
            this.data.MyserviceData.id
          ),
          complete: function () {
            e.navigating = !1;
          },
        }));
    },
    onEditClick: function () {
      var e = this;
      this.navigating ||
        ((this.navigating = !0),
        wx.navigateTo({
          url: "/pages/edit-service/edit-service?id=".concat(
            this.data.MyserviceData.id
          ),
          complete: function () {
            e.navigating = !1;
          },
        }));
    },
    onPublishStatusClick: function () {
      var e = this.data.MyserviceData;
      "approved" === e.audit_status
        ? "online" === e.publish_status
          ? this.offlineService()
          : this.onlineService()
        : wx.showToast({ title: "服务未通过审核，无法操作", icon: "none" });
    },
    onlineService: function () {
      var a = this;
      return t(
        e().mark(function t() {
          var i, n, r;
          return e().wrap(
            function (e) {
              for (;;)
                switch ((e.prev = e.next)) {
                  case 0:
                    if (((i = a.data.MyserviceData), !a.data.isOnlineLoading)) {
                      e.next = 3;
                      break;
                    }
                    return e.abrupt("return");
                  case 3:
                    return (
                      (e.next = 5),
                      new Promise(function (e) {
                        wx.showModal({
                          title: "确认上架",
                          content:
                            "确定要上架这个服务吗？上架后用户将可以看到并预订此服务。",
                          success: e,
                        });
                      })
                    );
                  case 5:
                    if (e.sent.confirm) {
                      e.next = 8;
                      break;
                    }
                    return e.abrupt("return");
                  case 8:
                    if (
                      (a.setData({ isOnlineLoading: !0 }),
                      (e.prev = 9),
                      (n = getApp()).globalData.openid)
                    ) {
                      e.next = 13;
                      break;
                    }
                    throw new Error("用户未登录，请重新登录");
                  case 13:
                    return (
                      (e.next = 15),
                      n.call({
                        path: "/api/mp/services/".concat(i.id, "/online"),
                        method: "PUT",
                        data: { openid: n.globalData.openid },
                      })
                    );
                  case 15:
                    if (0 !== (r = e.sent).code) {
                      e.next = 22;
                      break;
                    }
                    a.setData({
                      "MyserviceData.publish_status": "online",
                      "MyserviceData.updatedAt": r.data.updatedAt,
                    }),
                      wx.showToast({ title: "上架成功", icon: "success" }),
                      a.triggerEvent("statusChanged", {
                        serviceId: i.id,
                        newStatus: "online",
                        serviceData: r.data,
                      }),
                      (e.next = 23);
                    break;
                  case 22:
                    throw new Error(r.message || "上架失败");
                  case 23:
                    e.next = 29;
                    break;
                  case 25:
                    (e.prev = 25),
                      (e.t0 = e.catch(9)),
                      console.error("上架服务失败：", e.t0),
                      wx.showToast({
                        title: e.t0.message || "上架失败，请重试",
                        icon: "error",
                      });
                  case 29:
                    return (
                      (e.prev = 29),
                      a.setData({ isOnlineLoading: !1 }),
                      e.finish(29)
                    );
                  case 32:
                  case "end":
                    return e.stop();
                }
            },
            t,
            null,
            [[9, 25, 29, 32]]
          );
        })
      )();
    },
    offlineService: function () {
      var a = this;
      return t(
        e().mark(function t() {
          var i, n, r;
          return e().wrap(
            function (e) {
              for (;;)
                switch ((e.prev = e.next)) {
                  case 0:
                    if (
                      ((i = a.data.MyserviceData), !a.data.isOfflineLoading)
                    ) {
                      e.next = 3;
                      break;
                    }
                    return e.abrupt("return");
                  case 3:
                    return (
                      (e.next = 5),
                      new Promise(function (e) {
                        wx.showModal({
                          title: "确认下架",
                          content:
                            "确定要下架这个服务吗？下架后用户将无法看到此服务。",
                          success: e,
                        });
                      })
                    );
                  case 5:
                    if (e.sent.confirm) {
                      e.next = 8;
                      break;
                    }
                    return e.abrupt("return");
                  case 8:
                    if (
                      (a.setData({ isOfflineLoading: !0 }),
                      (e.prev = 9),
                      (n = getApp()).globalData.openid)
                    ) {
                      e.next = 13;
                      break;
                    }
                    throw new Error("用户未登录，请重新登录");
                  case 13:
                    return (
                      (e.next = 15),
                      n.call({
                        path: "/api/mp/services/".concat(i.id, "/offline"),
                        method: "DELETE",
                        data: { openid: n.globalData.openid },
                      })
                    );
                  case 15:
                    if (0 !== (r = e.sent).code) {
                      e.next = 22;
                      break;
                    }
                    a.setData({
                      "MyserviceData.publish_status": "offline",
                      "MyserviceData.updatedAt": r.data.updatedAt,
                    }),
                      wx.showToast({ title: "下架成功", icon: "success" }),
                      a.triggerEvent("statusChanged", {
                        serviceId: i.id,
                        newStatus: "offline",
                        serviceData: r.data,
                      }),
                      (e.next = 23);
                    break;
                  case 22:
                    throw new Error(r.message || "下架失败");
                  case 23:
                    e.next = 29;
                    break;
                  case 25:
                    (e.prev = 25),
                      (e.t0 = e.catch(9)),
                      console.error("下架服务失败：", e.t0),
                      wx.showToast({
                        title: e.t0.message || "下架失败，请重试",
                        icon: "error",
                      });
                  case 29:
                    return (
                      (e.prev = 29),
                      a.setData({ isOfflineLoading: !1 }),
                      e.finish(29)
                    );
                  case 32:
                  case "end":
                    return e.stop();
                }
            },
            t,
            null,
            [[9, 25, 29, 32]]
          );
        })
      )();
    },
  },
});
