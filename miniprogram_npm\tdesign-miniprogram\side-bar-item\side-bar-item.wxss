@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-side-bar-item {
  align-items: center;
  background: var(
    --td-side-bar-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  box-sizing: border-box;
  color: var(
    --td-side-bar-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-flex;
  display: flex;
  font-size: var(--td-side-bar-font-size, 32rpx);
  line-height: var(--td-side-bar-item-line-height, 48rpx);
  min-height: var(--td-side-bar-item-height, 112rpx);
  padding: 32rpx;
  position: relative;
  white-space: wrap;
}
.t-side-bar-item--active {
  --td-badge-content-text-color: var(
    --td-side-bar-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  background: var(--td-bg-color-container, var(--td-font-white-1, #fff));
  color: var(
    --td-side-bar-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  font-weight: 600;
}
.t-side-bar-item__icon {
  font-size: var(--td-side-bar-icon-size, 40rpx);
  margin-right: 4rpx;
}
.t-side-bar-item__prefix,
.t-side-bar-item__suffix {
  background: var(--td-bg-color-container, var(--td-font-white-1, #fff));
  height: calc(var(--td-side-bar-border-radius, 18rpx) * 2);
  pointer-events: none;
  position: absolute;
  right: 0;
  width: 100%;
  z-index: 1;
}
.t-side-bar-item__prefix::after,
.t-side-bar-item__suffix::after {
  background-color: var(
    --td-side-bar-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  content: "";
  display: block;
  height: 100%;
  width: 100%;
}
.t-side-bar-item__prefix {
  top: calc(var(--td-side-bar-border-radius, 18rpx) * -2);
}
.t-side-bar-item__prefix::after {
  border-bottom-right-radius: var(--td-side-bar-border-radius, 18rpx);
}
.t-side-bar-item__suffix {
  bottom: calc(var(--td-side-bar-border-radius, 18rpx) * -2);
}
.t-side-bar-item__suffix::after {
  border-top-right-radius: var(--td-side-bar-border-radius, 18rpx);
}
.t-side-bar-item--disabled {
  color: var(
    --td-side-bar-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-side-bar-item__line {
  background: var(
    --td-side-bar-active-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-radius: 8rpx;
  height: 28rpx;
  left: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
}
