__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/link/link": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            disabled: new Array(3),
            classPrefix: new Array(4),
            style: new Array(1),
            customStyle: new Array(1),
            hover: new Array(1),
            className: new Array(1),
            navigatorProps: new Array(12),
          },
          K = U === true,
          e,
          g,
          h = (C, T, E, B, F, S, J) => {
            var $A = I(g);
            if (g && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-prefix-icon", ariaHidden: true },
                  X(D._prefixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._prefixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._prefixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              g = "icon";
              B(g, h);
            }
          },
          d = (C, T, E, B, F, S) => {
            S("prefix-icon");
            e = D._prefixIcon ? 1 : 0;
            B(e, f);
          },
          j,
          k = (C, T) => {
            if (j === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          i = (C, T, E, B, F, S) => {
            j = D.content ? 1 : 0;
            B(j, k);
            S("content");
            S("");
          },
          m,
          o,
          p = (C, T, E, B, F, S, J) => {
            var $A = I(o);
            if (o && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-suffix-icon", ariaHidden: true },
                  X(D._suffixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._suffixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._suffixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          n = (C, T, E, B, F, S, J) => {
            if (m === 1) {
              o = "icon";
              B(o, p);
            }
          },
          l = (C, T, E, B, F, S) => {
            S("suffix-icon");
            m = D._suffixIcon ? 1 : 0;
            B(m, n);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__prefix-icon " +
                      Y(D.prefix) +
                      "-class-prefix-icon"
                  );
                A["classPrefix"][1] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__prefix-icon " +
                      Y(D.prefix) +
                      "-class-prefix-icon"
                  );
                };
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                A["classPrefix"][2] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                };
              },
              i
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__suffix-icon " +
                      Y(D.prefix) +
                      "-class-suffix-icon"
                  );
                A["classPrefix"][3] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__suffix-icon " +
                      Y(D.prefix) +
                      "-class-suffix-icon"
                  );
                };
              },
              l
            );
          },
          b = (C, T, E) => {
            E(
              "navigator",
              {},
              (N, C) => {
                if (C || K || !!(U.className || U.prefix) || undefined)
                  L(N, Y(D.className) + " class " + Y(D.prefix) + "-class");
                A["className"][0] = (D, E, T) => {
                  L(N, Y(D.className) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C || K || Z(U.navigatorProps, "target"))
                  O(N, "target", X(D.navigatorProps).target);
                A["navigatorProps"][0] = (D, E, T) => {
                  O(N, "target", X(D.navigatorProps).target);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(U.disabled || Z(U.navigatorProps, "url")) ||
                  undefined
                )
                  O(N, "url", !D.disabled && X(D.navigatorProps).url);
                A["disabled"][0] = A["navigatorProps"][1] = (D, E, T) => {
                  O(N, "url", !D.disabled && X(D.navigatorProps).url);
                  E(N);
                };
                if (C || K || !!Z(U.navigatorProps, "openType") || undefined)
                  O(N, "open-type", X(D.navigatorProps).openType || "navigate");
                A["navigatorProps"][2] = (D, E, T) => {
                  O(N, "open-type", X(D.navigatorProps).openType || "navigate");
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "delta"))
                  O(N, "delta", X(D.navigatorProps).delta);
                A["navigatorProps"][3] = (D, E, T) => {
                  O(N, "delta", X(D.navigatorProps).delta);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "appId"))
                  O(N, "app-id", X(D.navigatorProps).appId);
                A["navigatorProps"][4] = (D, E, T) => {
                  O(N, "app-id", X(D.navigatorProps).appId);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "path"))
                  O(N, "path", X(D.navigatorProps).path);
                A["navigatorProps"][5] = (D, E, T) => {
                  O(N, "path", X(D.navigatorProps).path);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "extraData"))
                  O(N, "extra-data", X(D.navigatorProps).extraData);
                A["navigatorProps"][6] = (D, E, T) => {
                  O(N, "extra-data", X(D.navigatorProps).extraData);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "version"))
                  O(N, "version", X(D.navigatorProps).version);
                A["navigatorProps"][7] = (D, E, T) => {
                  O(N, "version", X(D.navigatorProps).version);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "shortLink"))
                  O(N, "short-link", X(D.navigatorProps).shortLink);
                A["navigatorProps"][8] = (D, E, T) => {
                  O(N, "short-link", X(D.navigatorProps).shortLink);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(
                    U.hover ||
                    U.disabled ||
                    U.classPrefix ||
                    U.prefix ||
                    Z(U.navigatorProps, "hoverClass")
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "hover-class",
                    Y(D.hover && !D.disabled && D.classPrefix + "--hover") +
                      " " +
                      Y(D.prefix) +
                      "-class-hover " +
                      Y(X(D.navigatorProps).hoverClass)
                  );
                A["hover"][0] =
                  A["disabled"][1] =
                  A["classPrefix"][0] =
                  A["navigatorProps"][9] =
                    (D, E, T) => {
                      O(
                        N,
                        "hover-class",
                        Y(D.hover && !D.disabled && D.classPrefix + "--hover") +
                          " " +
                          Y(D.prefix) +
                          "-class-hover " +
                          Y(X(D.navigatorProps).hoverClass)
                      );
                      E(N);
                    };
                if (C)
                  O(
                    N,
                    "hover-stop-propagation",
                    "navigatorProps.hoverStopPropagation"
                  );
                if (C || K || Z(U.navigatorProps, "hoverStartTime"))
                  O(N, "hover-start-time", X(D.navigatorProps).hoverStartTime);
                A["navigatorProps"][10] = (D, E, T) => {
                  O(N, "hover-start-time", X(D.navigatorProps).hoverStartTime);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "hoverStayTime"))
                  O(N, "hover-stay-time", X(D.navigatorProps).hoverStayTime);
                A["navigatorProps"][11] = (D, E, T) => {
                  O(N, "hover-stay-time", X(D.navigatorProps).hoverStayTime);
                  E(N);
                };
                if (C) O(N, "bindsuccess", "onSuccess");
                if (C) O(N, "bindfail", "onFail");
                if (C) O(N, "bindcomplete", "onComplete");
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                A["disabled"][2] = (D, E, T) => {
                  O(N, "aria-disabled", D.disabled);
                  E(N);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/message-item/message-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a =
        R["miniprogram_npm/tdesign-miniprogram/message-item/message-item"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          d,
          h,
          j,
          k = (C, T, E, B, F, S, J) => {
            var $A = I(j);
            if (j && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-icon", ariaHidden: true },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          i = (C, T, E, B, F, S, J) => {
            if (h === 1) {
              j = "icon";
              B(j, k);
            }
          },
          g = (C, T, E, B, F, S) => {
            S("icon");
            h = D._icon ? 1 : 0;
            B(h, i);
          },
          n,
          o = (C, T) => {
            if (n === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          m = (C, T, E, B, F, S) => {
            n = D.content ? 1 : 0;
            B(n, o);
            S("content");
            S("");
          },
          l = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__text " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C || K || U.animation) O(N, "animation", D.animation);
                if (C || K || !!U.classPrefix || undefined)
                  R.i(N, Y(D.classPrefix) + "__text");
              },
              m
            );
          },
          p,
          r = (C) => {},
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "t-link",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) + "__link " + Y(D.prefix) + "-class-link"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([Z(U._link, "style"), Z(U._link, "customStyle")])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(b)._style)([X(D._link).style, X(D._link).customStyle])
                    );
                  if (C || K || !!Z(U._link, "disabled") || undefined)
                    O(N, "disabled", X(D._link).disabled || false);
                  if (C || K || !!Z(U._link, "hover") || undefined)
                    O(N, "hover", X(D._link).hover || true);
                  if (C || K || !!Z(U._link, "theme") || undefined)
                    O(N, "theme", X(D._link).theme || "primary");
                  if (C || K || !!Z(U._link, "size") || undefined)
                    O(N, "size", X(D._link).size || "medium");
                  if (C || K || !!Z(U._link, "prefixIcon") || undefined)
                    O(N, "prefixIcon", X(D._link).prefixIcon || false);
                  if (C || K || !!Z(U._link, "suffixIcon") || undefined)
                    O(N, "suffixIcon", X(D._link).suffixIcon || false);
                  if (C || K || !!Z(U._link, "underline") || undefined)
                    O(N, "underline", X(D._link).underline || false);
                  if (C || K || !!Z(U._link, "content") || undefined)
                    O(N, "content", X(D._link).content || "");
                  if (C || K || !!Z(U._link, "navigatorProps") || undefined)
                    O(N, "navigatorProps", X(D._link).navigatorProps || null);
                  if (C) R.v(N, "complete", "handleLinkClick", !1, !1, !1, !1);
                },
                r
              );
            }
          },
          t,
          v,
          w = (C, T, E, B, F, S, J) => {
            var $A = I(v);
            if (v && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass: D.prefix + "-class-close-btn",
                    ariaRole: "button",
                    ariaLabel: "关闭",
                  },
                  X(D._closeBtn),
                  {}
                ),
                K ||
                  (U
                    ? U._closeBtn === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._closeBtn),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          u = (C, T, E, B, F, S, J) => {
            if (t === 1) {
              v = "icon";
              B(v, w);
            }
          },
          s = (C, T, E, B, F, S) => {
            S("close-btn");
            t = D._closeBtn ? 1 : 0;
            B(t, u);
          },
          f = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon--left");
              },
              g
            );
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.marquee;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.marquee ||
                    ($A ? !!U.classPrefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__text-wrap " +
                      Y($A ? D.classPrefix + "__text-nowrap" : "")
                  );
                if (C || K || !!U.align || undefined)
                  R.y(N, "text-align:" + Y(D.align));
                if (C || K || !!U.classPrefix || undefined)
                  R.i(N, Y(D.classPrefix) + "__text-wrap");
              },
              l
            );
            p = D._link && X(D._link).content ? 1 : 0;
            B(p, q);
            S("link");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon--right");
                if (C) R.v(N, "tap", "handleClose", !1, !1, !1, !1);
              },
              s
            );
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.prefix ||
                      U.classPrefix ||
                      U.theme ||
                      U.fadeClass
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        " class " +
                        Y(D.prefix) +
                        "-class " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.theme) +
                        " " +
                        Y(D.fadeClass)
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!(
                          Z(undefined, "getMessageStyles") ||
                          U.zIndex ||
                          U.offset ||
                          U.wrapTop
                        ) || undefined,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(b)._style)([
                        P(X(a).getMessageStyles)(D.zIndex, D.offset, D.wrapTop),
                        D.style,
                        D.customStyle,
                      ])
                    );
                  if (C || K || U.showAnimation)
                    O(N, "animation", D.showAnimation);
                  if (C) O(N, "aria-role", "alert");
                  if (C || K || !!(U.id || U.classPrefix) || undefined)
                    R.i(N, D.id || D.classPrefix);
                },
                f
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.visible ? 1 : 0;
            B(d, e);
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/message/message": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b = (C, c, d, e, f, g, T, E) => {
            var h = (C, T, E, B, F, S) => {
              S("icon", (N) => {}, "icon");
              S("content", (N) => {}, "content");
              S("");
              S("link", (N) => {}, "link");
              S("close-btn", (N) => {}, "close-btn");
            };
            E(
              "t-message-item",
              {},
              (N, C) => {
                if (C) R.v(N, "close-btn-click", "handleClose", !1, !1, !1, !1);
                if (C) R.v(N, "link-click", "handleLinkClick", !1, !1, !1, !1);
                if (C)
                  R.v(N, "duration-end", "handleDurationEnd", !1, !1, !1, !1);
                if (C || K || Z(e, "id")) R.i(N, X(c).id);
              },
              h
            );
          },
          a = (C, T, E, B, F) => {
            F(
              D.messageList,
              "id",
              U ? U.messageList : undefined,
              [0, "messageList"],
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/link/link.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-link--small .",
        [1],
        "t-link__content{font-size:",
        [0, 24],
        ";line-height:",
        [0, 40],
        "}\n.",
        [1],
        "t-link--small .",
        [1],
        "t-link__prefix-icon,.",
        [1],
        "t-link--small .",
        [1],
        "t-link__suffix-icon{font-size:",
        [0, 28],
        "}\n.",
        [1],
        "t-link--medium .",
        [1],
        "t-link__content{font-size:",
        [0, 28],
        ";line-height:",
        [0, 44],
        "}\n.",
        [1],
        "t-link--medium .",
        [1],
        "t-link__prefix-icon,.",
        [1],
        "t-link--medium .",
        [1],
        "t-link__suffix-icon{font-size:",
        [0, 32],
        "}\n.",
        [1],
        "t-link--large .",
        [1],
        "t-link__content{font-size:",
        [0, 32],
        ";line-height:",
        [0, 48],
        "}\n.",
        [1],
        "t-link--large .",
        [1],
        "t-link__prefix-icon,.",
        [1],
        "t-link--large .",
        [1],
        "t-link__suffix-icon{font-size:",
        [0, 36],
        "}\n.",
        [1],
        "t-link--primary{color:var(--td-link-primary-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-link--primary.",
        [1],
        "t-link--underline::after{border-color:var(--td-link-primary-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-link--primary.",
        [1],
        "t-link--disabled{color:var(--td-link-primary-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-link--primary.",
        [1],
        "t-link--hover{color:var(--td-link-primary-active-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-link--primary.",
        [1],
        "t-link--hover.",
        [1],
        "t-link--underline::after{border-color:var(--td-link-primary-active-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-link--success{color:var(--td-link-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
        [1],
        "t-link--success.",
        [1],
        "t-link--underline::after{border-color:var(--td-link-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
        [1],
        "t-link--success.",
        [1],
        "t-link--disabled{color:var(--td-link-success-disabled-color,var(--td-success-color-disabled,var(--td-success-color-3,#92dab2)))}\n.",
        [1],
        "t-link--success.",
        [1],
        "t-link--hover{color:var(--td-link-success-active-color,var(--td-success-color-active,var(--td-success-color-6,#008858)))}\n.",
        [1],
        "t-link--success.",
        [1],
        "t-link--hover.",
        [1],
        "t-link--underline::after{border-color:var(--td-link-success-active-color,var(--td-success-color-active,var(--td-success-color-6,#008858)))}\n.",
        [1],
        "t-link--warning{color:var(--td-link-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
        [1],
        "t-link--warning.",
        [1],
        "t-link--underline::after{border-color:var(--td-link-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
        [1],
        "t-link--warning.",
        [1],
        "t-link--disabled{color:var(--td-link-warning-disabled-color,var(--td-warning-color-disabled,var(--td-warning-color-3,#ffb98c)))}\n.",
        [1],
        "t-link--warning.",
        [1],
        "t-link--hover{color:var(--td-link-warning-active-color,var(--td-warning-color-active,var(--td-warning-color-6,#be5a00)))}\n.",
        [1],
        "t-link--warning.",
        [1],
        "t-link--hover.",
        [1],
        "t-link--underline::after{border-color:var(--td-link-warning-active-color,var(--td-warning-color-active,var(--td-warning-color-6,#be5a00)))}\n.",
        [1],
        "t-link--default{color:var(--td-link-default-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-link--default.",
        [1],
        "t-link--underline::after{border-color:var(--td-link-default-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-link--default.",
        [1],
        "t-link--disabled{color:var(--td-link-default-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-link--default.",
        [1],
        "t-link--hover{color:var(--td-link-default-active-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-link--default.",
        [1],
        "t-link--hover.",
        [1],
        "t-link--underline::after{border-color:var(--td-link-default-active-color,var(--td-brand-color-active,var(--td-primary-color-8,#003cab)))}\n.",
        [1],
        "t-link--danger{color:var(--td-link-danger-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-link--danger.",
        [1],
        "t-link--underline::after{border-color:var(--td-link-danger-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
        [1],
        "t-link--danger.",
        [1],
        "t-link--disabled{color:var(--td-link-danger-disabled-color,var(--td-error-color-disabled,var(--td-error-color-3,#ffb9b0)))}\n.",
        [1],
        "t-link--danger.",
        [1],
        "t-link--hover{color:var(--td-link-danger-active-color,var(--td-error-color-active,var(--td-error-color-7,#ad352f)))}\n.",
        [1],
        "t-link--danger.",
        [1],
        "t-link--hover.",
        [1],
        "t-link--underline::after{border-color:var(--td-link-danger-active-color,var(--td-error-color-active,var(--td-error-color-7,#ad352f)))}\n.",
        [1],
        "t-link{-webkit-align-items:center;align-items:center;box-sizing:initial;display:-webkit-flex;display:flex;position:relative}\n.",
        [1],
        "t-link--underline::after{border-bottom:",
        [0, 2],
        " solid #cd0be7;bottom:0;content:\x22\x22;height:0;left:0;opacity:1;position:absolute;right:0}\n.",
        [1],
        "t-link__content:not(:empty)+.",
        [1],
        "t-link__suffix-icon:not(:empty),.",
        [1],
        "t-link__prefix-icon:not(:empty)+.",
        [1],
        "t-link__content:not(:empty){padding-left:",
        [0, 8],
        "}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/link/link.wxss" }
    );
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/message-item/message-item.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-message{-webkit-align-items:center;align-items:center;background-color:var(--td-message-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));border-radius:var(--td-message-border-radius,var(--td-radius-default,",
      [0, 12],
      "));box-shadow:var(--td-message-box-shadow,var(--td-shadow-1,0 1px 10px rgba(0,0,0,.05),0 4px 5px rgba(0,0,0,.08),0 2px 4px -1px rgba(0,0,0,.12)));box-sizing:border-box;display:-webkit-flex;display:flex;height:",
      [0, 96],
      ";-webkit-justify-content:flex-start;justify-content:flex-start;left:0;line-height:1;padding:0 ",
      [0, 32],
      ";position:fixed;right:0;top:0;transition:opacity .3s,top .4s,-webkit-transform .4s;transition:opacity .3s,transform .4s,top .4s;transition:opacity .3s,transform .4s,top .4s,-webkit-transform .4s;z-index:15000}\n.",
      [1],
      "t-message__text{color:var(--td-message-content-font-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:inline-block;font-size:var(--td-font-size-base,",
      [0, 28],
      ");line-height:",
      [0, 44],
      "}\n.",
      [1],
      "t-message__text-wrap{-webkit-flex:1 1 auto;flex:1 1 auto;overflow-x:hidden;text-overflow:ellipsis}\n.",
      [1],
      "t-message__text-nowrap{white-space:nowrap;word-break:keep-all}\n.",
      [1],
      "t-message--info{color:var(--td-message-info-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
      [1],
      "t-message--success{color:var(--td-message-success-color,var(--td-success-color,var(--td-success-color-5,#2ba471)))}\n.",
      [1],
      "t-message--warning{color:var(--td-message-warning-color,var(--td-warning-color,var(--td-warning-color-5,#e37318)))}\n.",
      [1],
      "t-message--error{color:var(--td-message-error-color,var(--td-error-color,var(--td-error-color-6,#d54941)))}\n.",
      [1],
      "t-message__icon--left,.",
      [1],
      "t-message__icon--right{font-size:",
      [0, 44],
      "}\n.",
      [1],
      "t-message__icon--left:not(:empty){margin-right:var(--td-spacer,",
      [0, 16],
      ")}\n.",
      [1],
      "t-message__icon--right{color:var(--td-message-close-icon-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))))}\n.",
      [1],
      "t-message__icon--right:not(:empty),.",
      [1],
      "t-message__link{-webkit-flex:0 0 auto;flex:0 0 auto;margin-left:var(--td-spacer,",
      [0, 16],
      ")}\n.",
      [1],
      "t-message__fade{opacity:0;-webkit-transform:translateY(-100%);transform:translateY(-100%)}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/message-item/message-item.wxss",
    }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/message/message.wxss"] =
    setCssToHead(
      [[2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"]],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/message/message.wxss" }
    );
}
