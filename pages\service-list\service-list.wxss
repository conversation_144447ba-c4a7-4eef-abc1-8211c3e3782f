page {
  background-color: #f5f5f5;
  box-sizing: border-box;
  padding-bottom: 180rpx;
}
.container,
page {
  min-height: 100vh;
}
.container {
  width: 100%;
}
.nr_box {
  margin: 20rpx auto;
  width: 95%;
}
.loading-container {
  padding: 100rpx 0;
}
.load-more,
.loading-container {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.load-more {
  margin-top: 20rpx;
  padding: 40rpx 0;
}
.loading-more {
  align-items: center;
  color: #999;
  display: -webkit-flex;
  display: flex;
}
.load-more-text {
  background-color: #fff;
  border: 1rpx solid #195abf;
  border-radius: 40rpx;
  color: #195abf;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
}
.no-more {
  color: #999;
  font-size: 24rpx;
  padding: 40rpx 0;
  text-align: center;
}
.empty-state {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  padding: 100rpx 0;
}
