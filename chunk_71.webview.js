__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/my-order-list/my-order-list": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["pages/my-order-list/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { serviceInfo: new Array(1) },
          K = U === true,
          d = (C) => {},
          c = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || !!Z(U.serviceInfo, "service_name") || undefined)
                  O(N, "title", X(D.serviceInfo).service_name || "预约记录");
                A["serviceInfo"][0] = (D, E, T) => {
                  O(N, "title", X(D.serviceInfo).service_name || "预约记录");
                  E(N);
                };
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              d
            );
          },
          h = (C) => {},
          i = (C) => {},
          j = (C) => {},
          k = (C) => {},
          g = (C, T, E) => {
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已下单");
                if (C) O(N, "value", "0");
              },
              h
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已接单");
                if (C) O(N, "value", "1");
              },
              i
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已完成");
                if (C) O(N, "value", "2");
              },
              j
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "已取消");
                if (C) O(N, "value", "3");
              },
              k
            );
          },
          f = (C, T, E) => {
            E(
              "t-tabs",
              {},
              (N, C) => {
                if (C || K || undefined) O(N, "defaultValue", 0);
                if (C) O(N, "t-class", "custom-tabs");
                if (C) O(N, "t-class-track", "custom-track");
                if (C) R.v(N, "change", "onTabsChange", !1, !1, !1, !1);
                if (C) R.v(N, "click", "onTabsClick", !1, !1, !1, !1);
              },
              g
            );
          },
          l,
          o = (C) => {},
          n = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "40rpx");
                if (C) O(N, "text", "加载中...");
                if (C) O(N, "layout", "vertical");
              },
              o
            );
          },
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_container");
                },
                n
              );
            }
          },
          p,
          s = (C, t, u, v, w, x, T, E) => {
            var B0 = (C) => {},
              D0 = (C, T) => {
                C || K || Z(Z(v, "service_provider"), "nickname")
                  ? T(Y(X(X(t).service_provider).nickname))
                  : T();
              },
              C0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_name");
                  },
                  D0
                );
              },
              A0 = (C, T, E) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_avatar");
                    if (
                      C ||
                      K ||
                      !!Z(Z(v, "service_provider"), "avatar") ||
                      undefined
                    )
                      O(
                        N,
                        "src",
                        X(X(t).service_provider).avatar ||
                          "/assets/tx/短发职业女.png"
                      );
                  },
                  B0
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_details");
                  },
                  C0
                );
              },
              F0 = (C, T) => {
                var $A = X(t).order_status;
                C ||
                K ||
                !!Z(v, "order_status") ||
                Z(Z(U.statusMap, $A), "text")
                  ? T(Y(X(X(D.statusMap)[$A]).text))
                  : T();
              },
              E0 = (C, T, E) => {
                E(
                  "t-tag",
                  {},
                  (N, C) => {
                    if (C) O(N, "variant", "light");
                    var $A = X(t).order_status;
                    if (
                      C ||
                      K ||
                      !!Z(v, "order_status") ||
                      Z(Z(U.statusMap, $A), "theme")
                    )
                      O(N, "theme", X(X(D.statusMap)[$A]).theme);
                  },
                  F0
                );
              },
              z = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "user_info");
                  },
                  A0
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_status");
                  },
                  E0
                );
              },
              I0 = (C, T) => {
                C ? T("下单时间：") : T();
              },
              J0 = (C, T) => {
                C ||
                K ||
                !!(Z(undefined, "formatTime") || Z(v, "order_time")) ||
                undefined
                  ? T(Y(P(X(a).formatTime)(X(t).order_time)))
                  : T();
              },
              H0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  I0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  J0
                );
              },
              K0,
              N0 = (C, T) => {
                C ? T("地址：") : T();
              },
              O0 = (C, T) => {
                C ||
                K ||
                !!(
                  Z(Z(Z(v, "customer"), "address"), "building") ||
                  Z(Z(Z(v, "customer"), "address"), "unit") ||
                  Z(Z(Z(v, "customer"), "address"), "room")
                ) ||
                undefined
                  ? T(
                      Y(
                        Y(X(X(X(t).customer).address).building) +
                          "-" +
                          Y(X(X(X(t).customer).address).unit) +
                          "-" +
                          Y(X(X(X(t).customer).address).room)
                      )
                    )
                  : T();
              },
              M0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  N0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  O0
                );
              },
              L0 = (C, T, E) => {
                if (K0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    M0
                  );
                }
              },
              P0,
              S0 = (C, T) => {
                C ? T("接单时间：") : T();
              },
              T0 = (C, T) => {
                C || K || !!(U.formatTime || Z(v, "accept_time")) || undefined
                  ? T(Y(P(D.formatTime)(X(t).accept_time)))
                  : T();
              },
              R0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  S0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  T0
                );
              },
              Q0 = (C, T, E) => {
                if (P0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    R0
                  );
                }
              },
              U0,
              X0 = (C, T) => {
                C ? T("完成时间：") : T();
              },
              Y0 = (C, T) => {
                C || K || !!(U.formatTime || Z(v, "complete_time")) || undefined
                  ? T(Y(P(D.formatTime)(X(t).complete_time)))
                  : T();
              },
              W0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "label");
                  },
                  X0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "value");
                  },
                  Y0
                );
              },
              V0 = (C, T, E) => {
                if (U0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "detail_row");
                    },
                    W0
                  );
                }
              },
              G0 = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "detail_row");
                  },
                  H0
                );
                K0 = X(X(t).customer).address ? 1 : 0;
                B(K0, L0);
                P0 = X(t).accept_time ? 1 : 0;
                B(P0, Q0);
                U0 = X(t).complete_time ? 1 : 0;
                B(U0, V0);
              },
              Z0,
              d0 = (C, T) => {
                C || K || Z(v, "remark") ? T(Y(X(t).remark)) : T();
              },
              c0 = (C, T, E) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "content_text");
                  },
                  d0
                );
              },
              b0 = (C, T, E) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "content_box");
                  },
                  c0
                );
              },
              a0 = (C, T, E) => {
                if (Z0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "order_content");
                    },
                    b0
                  );
                }
              },
              e0,
              i0 = (C, T) => {
                C ? T("联系客户") : T();
              },
              h0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, i0);
              },
              j0,
              m0 = (C, T) => {
                C ? T("取消订单") : T();
              },
              l0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, m0);
              },
              k0 = (C, T, E) => {
                if (j0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "quxiao-btn");
                      if (C || K || Z(v, "order_id"))
                        R.d(N, "orderId", X(t).order_id);
                      if (C) R.v(N, "tap", "onCancelOrder", !1, !1, !1, !1);
                    },
                    l0
                  );
                }
              },
              n0,
              q0 = (C, T) => {
                C ? T("确认完成") : T();
              },
              p0 = (C, T, E) => {
                E("text", {}, (N, C) => {}, q0);
              },
              o0 = (C, T, E) => {
                if (n0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "confirm-btn");
                      if (C || K || Z(v, "order_id"))
                        R.d(N, "orderId", X(t).order_id);
                      if (C) R.v(N, "tap", "onCompleteOrder", !1, !1, !1, !1);
                    },
                    p0
                  );
                }
              },
              g0 = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "contact-btn");
                    if (C || K || Z(v, "order_id"))
                      R.d(N, "orderId", X(t).order_id);
                    if (C || K || Z(Z(v, "customer"), "contact_info"))
                      R.d(N, "phone", X(X(t).customer).contact_info);
                    if (C) R.v(N, "tap", "onContactCustomer", !1, !1, !1, !1);
                  },
                  h0
                );
                j0 = X(t).order_status === "ordered" ? 1 : 0;
                B(j0, k0);
                n0 = X(t).order_status === "accepted" ? 1 : 0;
                B(n0, o0);
              },
              f0 = (C, T, E) => {
                if (e0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "fwdd_list_bottom");
                    },
                    g0
                  );
                }
              },
              y = (C, T, E, B) => {
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_header");
                  },
                  z
                );
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C) L(N, "order_details");
                  },
                  G0
                );
                Z0 = X(t).remark ? 1 : 0;
                B(Z0, a0);
                e0 =
                  X(t).order_status === "ordered" ||
                  X(t).order_status === "accepted"
                    ? 1
                    : 0;
                B(e0, f0);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "order_card");
              },
              y
            );
          },
          t,
          w = (C) => {},
          v = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "40rpx");
                if (C) O(N, "text", "加载更多...");
                if (C) O(N, "layout", "horizontal");
              },
              w
            );
          },
          u = (C, T, E) => {
            if (t === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading_more");
                },
                v
              );
            }
          },
          x,
          A0 = (C, T) => {
            C ? T("没有更多数据了") : T();
          },
          z = (C, T, E) => {
            E("text", {}, (N, C) => {}, A0);
          },
          y = (C, T, E) => {
            if (x === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "no_more");
                },
                z
              );
            }
          },
          B0,
          E0 = (C, T) => {
            C ? T("暂无订单") : T();
          },
          D0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "empty_text");
              },
              E0
            );
          },
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty_container");
                },
                D0
              );
            }
          },
          r = (C, T, E, B, F) => {
            F(
              D.filteredOrderList,
              "order_id",
              U ? U.filteredOrderList : undefined,
              [0, "filteredOrderList"],
              s
            );
            t = D.loadingMore ? 1 : 0;
            B(t, u);
            var $A = D.currentTab;
            x =
              !X(X(D.pagination)[$A]).hasMore &&
              X(D.filteredOrderList).length > 0
                ? 1
                : 0;
            B(x, y);
            B0 = D.isEmpty ? 1 : 0;
            B(B0, C0);
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                r
              );
            }
          },
          e = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "tags_box");
              },
              f
            );
            l = D.loading ? 1 : 0;
            B(l, m);
            p = !D.loading ? 1 : 0;
            B(p, q);
          },
          b = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, c);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              e
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/my-order-list/my-order-list.wxss"] = setCssToHead(
    [
      "body{background-color:#eff2f5}\n.",
      [1],
      "nr_box{margin:",
      [0, 10],
      " auto;width:95%}\n.",
      [1],
      "tags_box{margin:0 auto;width:100%}\n.",
      [1],
      "custom-tabs{background-color:#eff2f5!important;-webkit-justify-content:flex-start!important;justify-content:flex-start!important}\n.",
      [1],
      "custom-tabs .",
      [1],
      "t-tabs__item{color:#1e1e1e!important}\n.",
      [1],
      "custom-tabs .",
      [1],
      "t-tabs__item--active{color:#ff4d4f!important}\n.",
      [1],
      "custom-track{background-color:#ff4d4f!important}\n.",
      [1],
      "order_card{background-color:#fff;border:",
      [0, 2],
      " solid #f0f0f0;border-radius:",
      [0, 20],
      ";box-shadow:0 ",
      [0, 2],
      " ",
      [0, 10],
      " rgba(0,0,0,.05);margin-bottom:",
      [0, 20],
      ";padding:",
      [0, 30],
      "}\n.",
      [1],
      "order_header{border-bottom:",
      [0, 2],
      " solid #f5f5f5;-webkit-justify-content:space-between;justify-content:space-between;margin-bottom:",
      [0, 30],
      ";padding-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "order_header,.",
      [1],
      "user_info{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
      [1],
      "user_avatar{border-radius:50%;height:",
      [0, 80],
      ";margin-right:",
      [0, 20],
      ";width:",
      [0, 80],
      "}\n.",
      [1],
      "user_name{color:#333;font-size:",
      [0, 32],
      ";font-weight:500}\n.",
      [1],
      "order_status{padding:",
      [0, 10],
      " ",
      [0, 20],
      "}\n.",
      [1],
      "status_text{color:#666;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "order_details{margin-bottom:",
      [0, 30],
      "}\n.",
      [1],
      "detail_row{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;margin-bottom:",
      [0, 15],
      "}\n.",
      [1],
      "detail_row:last-child{margin-bottom:0}\n.",
      [1],
      "label{color:#666;-webkit-flex-shrink:0;flex-shrink:0;font-size:",
      [0, 28],
      ";width:",
      [0, 160],
      "}\n.",
      [1],
      "value{color:#333}\n.",
      [1],
      "price,.",
      [1],
      "value{-webkit-flex:1;flex:1;font-size:",
      [0, 28],
      "}\n.",
      [1],
      "price{color:#ff4d4f;font-weight:500}\n.",
      [1],
      "order_content{margin-bottom:",
      [0, 30],
      "}\n.",
      [1],
      "content_box{background-color:#f8f8f8;border:",
      [0, 2],
      " solid #f0f0f0;border-radius:",
      [0, 15],
      ";padding:",
      [0, 25],
      "}\n.",
      [1],
      "content_text{color:#333;font-size:",
      [0, 26],
      ";line-height:1.5}\n.",
      [1],
      "order_actions{display:-webkit-flex;display:flex;gap:",
      [0, 20],
      ";-webkit-justify-content:space-between;justify-content:space-between}\n.",
      [1],
      "action_btn{border:",
      [0, 5],
      " solid;border-radius:",
      [0, 20],
      ";font-size:",
      [0, 28],
      ";padding:",
      [0, 15],
      " 0;text-align:center;transition:all .3s ease}\n.",
      [1],
      "contact_btn{background-color:#fff;border:none;color:#195abf;-webkit-flex:1;flex:1}\n.",
      [1],
      "contact_btn:active{background-color:#f0f7ff}\n.",
      [1],
      "confirm_btn{background-color:#fff;border-color:#e03131;color:#e03131;-webkit-flex:0.5;flex:0.5}\n.",
      [1],
      "confirm_btn:active{background-color:#fff5f5}\n.",
      [1],
      "action_btn wx-text{font-weight:500}\n.",
      [1],
      "fwdd_list_bottom{-webkit-align-items:center;align-items:center;border-top:",
      [0, 2],
      " solid #f5f5f5;display:-webkit-flex;display:flex;gap:",
      [0, 10],
      ";-webkit-justify-content:space-between;justify-content:space-between;padding-top:",
      [0, 30],
      "}\n.",
      [1],
      "quxiao-btn{border:",
      [0, 5],
      " solid #ff4d4f;border-radius:",
      [0, 20],
      ";color:#ff4d4f;font-size:",
      [0, 24],
      ";margin-left:",
      [0, 10],
      ";padding:",
      [0, 8],
      " ",
      [0, 32],
      "}\n.",
      [1],
      "contact-btn{color:#2b5ee3;font-size:",
      [0, 28],
      ";margin-left:0}\n.",
      [1],
      "accept-btn{margin-left:",
      [0, 10],
      "}\n.",
      [1],
      "accept-btn,.",
      [1],
      "confirm-btn{background-color:#ff4d4f;border:",
      [0, 5],
      " solid #ff4d4f;border-radius:",
      [0, 20],
      ";color:#f0f7ff;font-size:",
      [0, 24],
      ";padding:",
      [0, 8],
      " ",
      [0, 32],
      "}\n.",
      [1],
      "loading_container{padding:",
      [0, 40],
      " 0}\n.",
      [1],
      "loading_container,.",
      [1],
      "loading_more{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "loading_more,.",
      [1],
      "no_more{padding:",
      [0, 20],
      " 0}\n.",
      [1],
      "no_more{color:#999;font-size:",
      [0, 24],
      ";text-align:center}\n.",
      [1],
      "empty_container{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:center;justify-content:center;padding:",
      [0, 100],
      " 0}\n.",
      [1],
      "empty_text{color:#999;font-size:",
      [0, 28],
      "}\n",
    ],
    "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/my-order-list/my-order-list.wxss:1:2107)",
    { path: "./pages/my-order-list/my-order-list.wxss" }
  );
}
