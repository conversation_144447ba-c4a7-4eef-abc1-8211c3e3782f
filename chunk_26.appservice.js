__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/fab/draggable/draggable": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            prefix: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            classPrefix: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["classPrefix"][0] = A["prefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) R.v(N, "touchstart", "onTouchStart", !1, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onTouchMove", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/fab/fab": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (G["miniprogram_npm/tdesign-miniprogram/fab/template/view"] || {})
              ._,
            (
              G["miniprogram_npm/tdesign-miniprogram/fab/template/draggable"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b,
          c = (C, T, E, B, F, S, J) => {
            var $A = I(b);
            if (b && $A)
              $A(
                R,
                C,
                {
                  prefix: D.prefix,
                  classPrefix: D.classPrefix,
                  style: D.style,
                  customStyle: D.customStyle,
                  moveStyle: D.moveStyle,
                  draggable: D.draggable,
                  buttonData: D.buttonData,
                },
                K ||
                  (U
                    ? {
                        prefix: U.prefix,
                        classPrefix: U.classPrefix,
                        style: U.style,
                        customStyle: U.customStyle,
                        moveStyle: U.moveStyle,
                        draggable: U.draggable,
                        buttonData: U.buttonData,
                      }
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          a = (C, T, E, B, F, S, J) => {
            var $A = D.draggable;
            b = $A ? "draggable" : "view";
            B(b, c);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/fab/draggable/draggable";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/fab/draggable/draggable.js";
define(
  "miniprogram_npm/tdesign-miniprogram/fab/draggable/draggable.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var t = require("../../../../@babel/runtime/helpers/regeneratorRuntime"),
      e = require("../../../../@babel/runtime/helpers/createClass"),
      r = require("../../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../../@babel/runtime/helpers/inherits"),
      s = require("../../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      o = require("../../common/src/index"),
      c = u(require("../../common/config")),
      a = u(require("./props")),
      h = require("../../common/utils");
    function u(t) {
      return t && t.__esModule ? t : { default: t };
    }
    var p = c.default.prefix,
      d = "".concat(p, "-draggable"),
      l = (function (o) {
        i(u, o);
        var c = s(u);
        function u() {
          var e;
          return (
            r(this, u),
            ((e = c.apply(this, arguments)).properties = a.default),
            (e.externalClasses = ["".concat(p, "-class")]),
            (e.data = { prefix: p, classPrefix: d }),
            (e.lifetimes = {
              ready: function () {
                this.computedRect();
              },
            }),
            (e.methods = {
              onTouchStart: function (t) {
                "none" !== this.properties.direction &&
                  ((this.startX =
                    t.touches[0].clientX +
                    h.systemInfo.windowWidth -
                    this.rect.right),
                  (this.startY =
                    t.touches[0].clientY +
                    h.systemInfo.windowHeight -
                    this.rect.bottom),
                  this.triggerEvent("start", {
                    startX: this.startX,
                    startY: this.startY,
                    rect: this.rect,
                    e: t,
                  }));
              },
              onTouchMove: function (t) {
                if ("none" !== this.properties.direction) {
                  var e = this.startX - t.touches[0].clientX,
                    r = this.startY - t.touches[0].clientY;
                  "vertical" === this.properties.direction &&
                    (e = h.systemInfo.windowWidth - this.rect.right),
                    "horizontal" === this.properties.direction &&
                      (r = h.systemInfo.windowHeight - this.rect.bottom),
                    this.triggerEvent("move", {
                      x: e,
                      y: r,
                      rect: this.rect,
                      e: t,
                    });
                }
              },
              onTouchEnd: function (e) {
                return (0, n.__awaiter)(
                  this,
                  void 0,
                  void 0,
                  t().mark(function r() {
                    return t().wrap(
                      function (t) {
                        for (;;)
                          switch ((t.prev = t.next)) {
                            case 0:
                              if (
                                ((t.t0 = "none" !== this.properties.direction),
                                !t.t0)
                              ) {
                                t.next = 5;
                                break;
                              }
                              return (t.next = 4), this.computedRect();
                            case 4:
                              this.triggerEvent("end", {
                                rect: this.rect,
                                e: e,
                              });
                            case 5:
                            case "end":
                              return t.stop();
                          }
                      },
                      r,
                      this
                    );
                  })
                );
              },
              computedRect: function () {
                return (0, n.__awaiter)(
                  this,
                  void 0,
                  void 0,
                  t().mark(function e() {
                    return t().wrap(
                      function (t) {
                        for (;;)
                          switch ((t.prev = t.next)) {
                            case 0:
                              return (
                                (this.rect = {
                                  right: 0,
                                  bottom: 0,
                                  width: 0,
                                  height: 0,
                                }),
                                (t.prev = 1),
                                (t.next = 4),
                                (0, h.getRect)(
                                  this,
                                  ".".concat(this.data.classPrefix)
                                )
                              );
                            case 4:
                              (this.rect = t.sent), (t.next = 9);
                              break;
                            case 7:
                              (t.prev = 7), (t.t0 = t.catch(1));
                            case 9:
                            case "end":
                              return t.stop();
                          }
                      },
                      e,
                      this,
                      [[1, 7]]
                    );
                  })
                );
              },
            }),
            e
          );
        }
        return e(u);
      })(o.SuperComponent),
      f = (l = (0, n.__decorate)([(0, o.wxComponent)()], l));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/fab/draggable/draggable.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/fab/draggable/draggable.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/fab/fab";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/fab/fab.js";
define(
  "miniprogram_npm/tdesign-miniprogram/fab/fab.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      o = require("tslib"),
      s = require("../common/src/index"),
      a = l(require("../common/config")),
      n = l(require("./props")),
      u = l(require("../mixins/using-custom-navbar")),
      c = require("../common/utils");
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var p = a.default.prefix,
      d = "".concat(p, "-fab"),
      h = {
        size: "large",
        shape: "circle",
        theme: "primary",
        tClass: "".concat(p, "-fab__button"),
      },
      m = (function (o) {
        i(a, o);
        var s = r(a);
        function a() {
          var e;
          return (
            t(this, a),
            ((e = s.apply(this, arguments)).behaviors = [u.default]),
            (e.properties = n.default),
            (e.externalClasses = [
              "class",
              "".concat(p, "-class"),
              "".concat(p, "-class-button"),
            ]),
            (e.data = {
              prefix: p,
              classPrefix: d,
              buttonData: h,
              moveStyle: null,
            }),
            (e.observers = {
              "buttonProps.**, icon, text, ariaLabel, yBounds": function () {
                var e;
                this.setData(
                  {
                    buttonData: Object.assign(
                      Object.assign(
                        Object.assign(Object.assign({}, h), {
                          shape: this.properties.text ? "round" : "circle",
                          icon: this.properties.icon,
                        }),
                        this.properties.buttonProps
                      ),
                      {
                        content: this.properties.text,
                        ariaLabel: this.properties.ariaLabel,
                      }
                    ),
                  },
                  null === (e = this.computedSize) || void 0 === e
                    ? void 0
                    : e.bind(this)
                );
              },
            }),
            (e.methods = {
              onTplButtonTap: function (e) {
                this.triggerEvent("click", e);
              },
              onStart: function (e) {
                this.triggerEvent("dragstart", e.detail.e);
              },
              onMove: function (e) {
                var t = this.properties.yBounds,
                  i = this.data.distanceTop,
                  r = e.detail,
                  o = r.x,
                  s = r.y,
                  a = r.rect,
                  n = c.systemInfo.windowWidth - a.width,
                  u =
                    c.systemInfo.windowHeight -
                    Math.max(i, (0, c.unitConvert)(t[0])) -
                    a.height,
                  l = Math.max(0, Math.min(o, n)),
                  p = Math.max(0, (0, c.unitConvert)(t[1]), Math.min(s, u));
                this.setData({
                  moveStyle: "right: "
                    .concat(l, "px; bottom: ")
                    .concat(p, "px;"),
                });
              },
              onEnd: function (e) {
                this.triggerEvent("dragend", e.detail.e);
              },
              computedSize: function () {
                var e, t;
                if (this.properties.draggable) {
                  var i = this.selectComponent("#draggable");
                  (
                    null ===
                      (t =
                        null === (e = this.properties) || void 0 === e
                          ? void 0
                          : e.yBounds) || void 0 === t
                      ? void 0
                      : t[1]
                  )
                    ? this.setData(
                        {
                          moveStyle: "bottom: ".concat(
                            (0, c.unitConvert)(this.properties.yBounds[1]),
                            "px"
                          ),
                        },
                        i.computedRect
                      )
                    : i.computedRect();
                }
              },
            }),
            e
          );
        }
        return e(a);
      })(s.SuperComponent),
      b = (m = (0, o.__decorate)([(0, s.wxComponent)()], m));
    exports.default = b;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/fab/fab.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/fab/fab.js");
