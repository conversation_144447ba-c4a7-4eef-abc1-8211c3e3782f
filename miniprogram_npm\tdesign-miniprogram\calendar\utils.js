function e(e, t) {
  var r = new Date(e);
  return r.setMonth(r.getMonth() + t), r.setDate(1), r;
}
function t(e, t) {
  var r = new Date(e);
  return r.setFullYear(r.getFullYear() + t), r.setDate(1), r;
}
Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.getMonthByOffset = e),
  (exports.getPrevYear =
    exports.getPrevMonth =
    exports.getNextYear =
    exports.getNextMonth =
      void 0),
  (exports.getYearByOffset = t);
exports.getPrevMonth = function (t) {
  return e(t, -1);
};
exports.getNextMonth = function (t) {
  return e(t, 1);
};
exports.getPrevYear = function (e) {
  return t(e, -1);
};
exports.getNextYear = function (e) {
  return t(e, 1);
};
