__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/loading/loading": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            show: new Array(1),
            style: new Array(1),
            layout: new Array(2),
            fullscreen: new Array(1),
            customStyle: new Array(1),
          },
          K = U === true,
          d,
          g = (C, h, i, j, k, l, T, E, B) => {
            var m,
              o = (C) => {},
              n = (C, T, E) => {
                if (m === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (
                        C ||
                        K ||
                        !!(U.classPrefix || U.classPrefix || k) ||
                        undefined
                      )
                        L(
                          N,
                          Y(D.classPrefix) +
                            "__dot " +
                            Y(D.classPrefix) +
                            "__dot-" +
                            Y(i)
                        );
                    },
                    o
                  );
                }
              };
            m = D.theme === "spinner" ? 1 : 0;
            B(m, n);
          },
          h,
          j = (C) => {},
          i = (C, T, E) => {
            if (h === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__circular");
                },
                j
              );
            }
          },
          k,
          m = (C) => {},
          n = (C) => {},
          o = (C) => {},
          l = (C, T, E) => {
            if (k === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__dot");
                  var $A = D.duration;
                  var $B = D.pause;
                  if (
                    C ||
                    K ||
                    !!(
                      U.duration ||
                      ($A ? !!U.duration || undefined : undefined) ||
                      U.pause ||
                      ($B ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      Y(
                        $A
                          ? "animation-duration: " +
                              D.duration / 1000 +
                              "s; animation-delay:" +
                              0 +
                              "s;"
                          : ""
                      ) +
                        "animation-play-state:" +
                        Y($B ? "paused" : "running") +
                        ";"
                    );
                },
                m
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__dot");
                  var $A = D.duration;
                  var $B = D.pause;
                  if (
                    C ||
                    K ||
                    !!(
                      U.duration ||
                      ($A
                        ? !!(U.duration || U.duration) || undefined
                        : undefined) ||
                      U.pause ||
                      ($B ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      Y(
                        $A
                          ? "animation-duration: " +
                              D.duration / 1000 +
                              "s; animation-delay:" +
                              (D.duration * 1) / 3000 +
                              "s;"
                          : ""
                      ) +
                        "animation-play-state:" +
                        Y($B ? "paused" : "running") +
                        ";"
                    );
                },
                n
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__dot");
                  var $A = D.duration;
                  var $B = D.pause;
                  if (
                    C ||
                    K ||
                    !!(
                      U.duration ||
                      ($A
                        ? !!(U.duration || U.duration) || undefined
                        : undefined) ||
                      U.pause ||
                      ($B ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      Y(
                        $A
                          ? "animation-duration: " +
                              D.duration / 1000 +
                              "s; animation-delay:" +
                              (D.duration * 2) / 3000 +
                              "s;"
                          : ""
                      ) +
                        "animation-play-state:" +
                        Y($B ? "paused" : "running") +
                        ";"
                    );
                },
                o
              );
            }
          },
          f = (C, T, E, B, F, S) => {
            F(12, "index", U ? undefined : undefined, null, g);
            h = D.theme === "circular" ? 1 : 0;
            B(h, i);
            k = D.theme === "dots" ? 1 : 0;
            B(k, l);
            S("indicator");
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.reverse;
                  if (
                    C ||
                    K ||
                    !!(
                      U.prefix ||
                      U.classPrefix ||
                      U.classPrefix ||
                      U.theme ||
                      U.reverse ||
                      ($A ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.prefix) +
                        "-class-indicator " +
                        Y(D.classPrefix) +
                        "__spinner " +
                        Y(D.classPrefix) +
                        "__spinner--" +
                        Y(D.theme) +
                        " " +
                        Y($A ? "reverse" : "")
                    );
                  var $B = D.inheritColor;
                  var $C = D.indicator;
                  var $D = D.duration;
                  var $E = D.pause;
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "addUnit") ||
                      U.size ||
                      Z(undefined, "addUnit") ||
                      U.size ||
                      U.inheritColor ||
                      ($B ? undefined : undefined) ||
                      U.indicator ||
                      ($C ? undefined : undefined) ||
                      U.duration ||
                      ($D ? !!U.duration || undefined : undefined) ||
                      U.pause ||
                      ($E ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      "width:" +
                        Y(P(X(a).addUnit)(D.size)) +
                        ";height:" +
                        Y(P(X(a).addUnit)(D.size)) +
                        ";" +
                        Y($B ? "color: inherit;" : "") +
                        " " +
                        Y($C ? "" : "display: none;") +
                        " " +
                        Y(
                          $D
                            ? "animation-duration: " + D.duration / 1000 + "s;"
                            : ""
                        ) +
                        " animation-play-state: " +
                        Y($E ? "paused" : "running") +
                        ";"
                    );
                  if (C || K || !!U.ariaRole || undefined)
                    O(N, "aria-role", D.ariaRole || "img");
                  if (C || K || !!(U.ariaLabel || U.text) || undefined)
                    O(N, "aria-label", D.ariaLabel || D.text || "加载中");
                },
                f
              );
            }
          },
          q,
          r = (C, T) => {
            if (q === 1) {
              C || K || U.text ? T(Y(D.text)) : T();
            }
          },
          p = (C, T, E, B, F, S) => {
            q = D.text ? 1 : 0;
            B(q, r);
            S("text");
            S("");
          },
          c = (C, T, E, B) => {
            d = D.indicator ? 1 : 0;
            B(d, e);
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__text", [D.layout])) +
                      " " +
                      Y(D.prefix) +
                      "-class-text"
                  );
                A["layout"][1] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__text", [D.layout])) +
                      " " +
                      Y(D.prefix) +
                      "-class-text"
                  );
                };
                if (C || K || U.indicator) O(N, "aria-hidden", D.indicator);
                if (C || K || !!(U.ariaLabel || U.text) || undefined)
                  O(N, "aria-label", D.ariaLabel || D.text);
              },
              p
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.fullscreen;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    U.classPrefix ||
                    U.classPrefix ||
                    U.layout ||
                    U.fullscreen ||
                    ($A ? !!U.classPrefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix + "--" + D.layout) +
                      " " +
                      Y($A ? D.classPrefix + "--fullscreen" : "")
                  );
                A["layout"][0] = A["fullscreen"][0] = (D, E, T) => {
                  var $B = D.fullscreen;
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix + "--" + D.layout) +
                      " " +
                      Y($B ? D.classPrefix + "--fullscreen" : "")
                  );
                };
                var $B = D.show;
                var $C = D.inheritColor;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([
                      U.style,
                      U.customStyle,
                      !!U.show || ($B ? undefined : undefined),
                      !!U.inheritColor || ($C ? undefined : undefined),
                    ])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([
                      D.style,
                      D.customStyle,
                      $B ? "" : "display: none",
                      $C ? "color: inherit" : "",
                    ])
                  );
                A["style"][0] =
                  A["customStyle"][0] =
                  A["show"][0] =
                    (D, E, T) => {
                      var $D = D.show;
                      var $E = D.inheritColor;
                      R.y(
                        N,
                        P(X(a)._style)([
                          D.style,
                          D.customStyle,
                          $D ? "" : "display: none",
                          $E ? "color: inherit" : "",
                        ])
                      );
                    };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/loading/loading";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/loading/loading.js";
define(
  "miniprogram_npm/tdesign-miniprogram/loading/loading.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      a = require("../common/src/index"),
      o = l(require("../common/config")),
      n = l(require("./props"));
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = o.default.prefix,
      c = "".concat(u, "-loading"),
      p = (function (s) {
        r(o, s);
        var a = i(o);
        function o() {
          var t;
          return (
            e(this, o),
            ((t = a.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-text"),
              "".concat(u, "-class-indicator"),
            ]),
            (t.data = { prefix: u, classPrefix: c, show: !0 }),
            (t.options = { multipleSlots: !0 }),
            (t.properties = Object.assign({}, n.default)),
            (t.timer = null),
            (t.observers = {
              loading: function (e) {
                var t = this,
                  r = this.properties.delay;
                this.timer && clearTimeout(this.timer),
                  e && r
                    ? (this.timer = setTimeout(function () {
                        t.setData({ show: e }), (t.timer = null);
                      }, r))
                    : this.setData({ show: e });
              },
            }),
            (t.lifetimes = {
              detached: function () {
                clearTimeout(this.timer);
              },
            }),
            t
          );
        }
        return (
          t(o, [
            {
              key: "refreshPage",
              value: function () {
                this.triggerEvent("reload");
              },
            },
          ]),
          o
        );
      })(a.SuperComponent),
      h = (p = (0, s.__decorate)([(0, a.wxComponent)()], p));
    exports.default = h;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/loading/loading.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/loading/loading.js");
