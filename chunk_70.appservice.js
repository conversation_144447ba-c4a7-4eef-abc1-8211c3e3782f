__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/login/login": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b = (C, T) => {
            C || K || U.appName ? T(Y(D.appName)) : T();
          },
          d = (C) => {},
          c = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) O(N, "src", "/assets/icon/login.png");
              },
              d
            );
          },
          e = (C, T) => {
            C ? T("邻居自己的信息发布平台") : T();
          },
          f,
          j = (C, T) => {
            C ? T("隐私保护指引") : T();
          },
          k = (C, T) => {
            C || K || !!U.appName || undefined
              ? T(
                  Y(
                    "感谢您使用" +
                      Y(D.appName) +
                      "小程序。我们非常重视您的隐私保护和个人信息保护。"
                  )
                )
              : T();
          },
          m = (C, T) => {
            C || K || U.privacyContractName ? T(Y(D.privacyContractName)) : T();
          },
          l = (C, T, E) => {
            C ? T("请您阅读并同意我们的") : T();
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "privacy-link");
                if (C) O(N, "bindtap", "openPrivacyContract");
              },
              m
            );
            C ? T("后继续使用。") : T();
          },
          n = (C, T) => {
            C
              ? T("我们将严格按照相关法律法规和本指引的规定使用您的个人信息。")
              : T();
          },
          p = (C, T) => {
            C ? T("不同意") : T();
          },
          q = (C, T) => {
            C ? T("同意并继续") : T();
          },
          o = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "disagree-btn");
                if (C) O(N, "bindtap", "exitMiniProgram");
              },
              p
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "agree-btn");
                if (C) O(N, "open-type", "agreePrivacyAuthorization");
                if (C)
                  O(N, "bindagreeprivacyauthorization", "handleAgreePrivacy");
              },
              q
            );
          },
          i = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "privacy-title");
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "privacy-desc");
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "privacy-desc");
              },
              l
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "privacy-desc");
              },
              n
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "button-group");
              },
              o
            );
          },
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "privacy-content");
              },
              i
            );
          },
          g = (C, T, E) => {
            if (f === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "privacy-modal");
                },
                h
              );
            }
          },
          r,
          u = (C, T) => {
            C ? T("正在加载...") : T();
          },
          t = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loading-text");
              },
              u
            );
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                t
              );
            }
          },
          a = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "title");
              },
              b
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "loing_img");
              },
              c
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "miaoshu");
              },
              e
            );
            f = D.showPrivacy ? 1 : 0;
            B(f, g);
            r = D.isLoading && !D.showPrivacy ? 1 : 0;
            B(r, s);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/login/login";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/login/login.js";
define(
  "pages/login/login.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var o,
      e =
        (o = require("../../env/index.js")) && o.__esModule
          ? o
          : { default: o };
    Page({
      data: {
        isLoading: !0,
        showPrivacy: !1,
        privacyChecked: !1,
        privacyContractName: "《隐私保护指引》",
        appName: e.default.appName,
      },
      onLoad: function (o) {
        console.log("登录页面加载"), this.checkPrivacyAuthorization();
      },
      checkPrivacyAuthorization: function () {
        var o = this;
        wx.getPrivacySetting({
          success: function (e) {
            console.log("隐私授权状态:", e),
              o.setData({
                privacyChecked: !0,
                privacyContractName:
                  e.privacyContractName || "《隐私保护指引》",
              }),
              e.needAuthorization
                ? o.setData({ showPrivacy: !0, isLoading: !1 })
                : o.checkLoginStatus();
          },
          fail: function (e) {
            console.error("获取隐私设置失败:", e),
              o.setData({ privacyChecked: !0 }),
              o.checkLoginStatus();
          },
        });
      },
      openPrivacyContract: function () {
        wx.openPrivacyContract({
          success: function () {
            console.log("打开隐私协议成功");
          },
          fail: function (o) {
            console.error("打开隐私协议失败:", o),
              wx.showToast({ title: "无法打开隐私协议", icon: "error" });
          },
        });
      },
      handleAgreePrivacy: function () {
        console.log("用户同意隐私协议"),
          this.setData({ showPrivacy: !1, privacyChecked: !0 });
        var o = getApp();
        if (o.isUserLoggedIn()) {
          var e = o.getUserData();
          this.performUserRedirect(e);
        } else this.checkLoginStatus();
      },
      exitMiniProgram: function () {
        wx.showModal({
          title: "提示",
          content: "需要您同意隐私协议才能继续使用小程序",
          showCancel: !1,
          confirmText: "我知道了",
        });
      },
      checkLoginStatus: function () {
        var o = getApp();
        o.isUserLoggedIn()
          ? (console.log("用户已登录，直接处理跳转"),
            this.handleUserRedirect(o.getUserData()))
          : console.log("用户未登录，等待登录完成");
      },
      onUserLoginSuccess: function () {
        console.log("收到登录成功通知");
        var o = getApp().getUserData();
        o
          ? this.handleUserRedirect(o)
          : (console.error("登录成功但未获取到用户信息"),
            wx.showToast({ title: "获取用户信息失败", icon: "error" }));
      },
      handleUserRedirect: function (o) {
        var e = this;
        console.log("处理用户跳转逻辑:", o),
          this.setData({ isLoading: !1 }),
          wx.getPrivacySetting({
            success: function (t) {
              if (
                (console.log("跳转前检查隐私授权状态:", t), t.needAuthorization)
              )
                return (
                  console.log("用户未签署隐私协议，显示隐私协议弹窗"),
                  void e.setData({ showPrivacy: !0, privacyChecked: !0 })
                );
              e.performUserRedirect(o);
            },
            fail: function (t) {
              console.error("检查隐私设置失败:", t), e.performUserRedirect(o);
            },
          });
      },
      performUserRedirect: function (o) {
        console.log("执行用户跳转逻辑:", o),
          o.data && "frozen" === o.data.account_status.value
            ? (console.log("用户账号已冻结，跳转到冻结页面"),
              wx.redirectTo({
                url: "/pages/dongjie/dongjie",
                fail: function (o) {
                  console.error("跳转冻结页面失败:", o),
                    wx.showToast({ title: "页面跳转失败", icon: "error" });
                },
              }))
            : (console.log("用户账号正常，跳转到首页"),
              wx.switchTab({
                url: "/pages/index/index",
                fail: function (o) {
                  console.error("跳转首页失败:", o),
                    wx.showToast({ title: "页面跳转失败", icon: "error" });
                },
              }));
      },
      onShow: function () {
        this.data.privacyChecked ||
          this.data.showPrivacy ||
          this.checkPrivacyAuthorization();
      },
      onReady: function () {},
      onHide: function () {},
      onUnload: function () {},
      onPullDownRefresh: function () {},
      onReachBottom: function () {},
      onShareAppMessage: function () {},
    });
  },
  { isPage: true, isComponent: true, currentFile: "pages/login/login.js" }
);
require("pages/login/login.js");
