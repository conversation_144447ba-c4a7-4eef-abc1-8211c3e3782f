Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.trimValue = exports.trimSingleValue = void 0);
var r = function (r, e, t) {
  return r < e ? e : r > t ? t : r;
};
exports.trimSingleValue = r;
exports.trimValue = function (e, t) {
  var i = t.min,
    a = t.max,
    n = t.range;
  return n && Array.isArray(e)
    ? ((e[0] = r(e[0], i, a)),
      (e[1] = r(e[1], i, a)),
      e[0] <= e[1] ? e : [e[1], e[0]])
    : n
    ? [i, a]
    : n
    ? void 0
    : r(e, i, a);
};
