__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/result/result": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            customStyle: new Array(1),
            theme: new Array(1),
          },
          K = U === true,
          e,
          g = (C) => {},
          h,
          i = (C, T, E, B, F, S, J) => {
            var $A = I(h);
            if (h && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.classPrefix + "__icon" },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.classPrefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              E(
                "t-image",
                {},
                (N, C) => {
                  if (C || K || !!U.prefix || undefined)
                    O(N, "t-class", Y(D.prefix) + "-class-image");
                  if (C || K || U.image) O(N, "src", D.image);
                  if (C) O(N, "mode", "aspectFit");
                },
                g
              );
            } else if (e === 2) {
              h = "icon";
              B(h, i);
            }
          },
          d = (C, T, E, B, F, S) => {
            e = D.image ? 1 : D._icon ? 2 : 0;
            B(e, f);
            S("image");
          },
          k,
          l = (C, T) => {
            if (k === 1) {
              C || K || U.title ? T(Y(D.title)) : T();
            }
          },
          j = (C, T, E, B, F, S) => {
            k = D.title ? 1 : 0;
            B(k, l);
            S("title");
          },
          n,
          o = (C, T) => {
            if (n === 1) {
              C || K || U.description ? T(Y(D.description)) : T();
            }
          },
          m = (C, T, E, B, F, S) => {
            n = D.description ? 1 : 0;
            B(n, o);
            S("description");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__thumb");
                if (C) O(N, "aria-hidden", "true");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__title " + Y(D.prefix) + "-class-title"
                  );
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__description " +
                      Y(D.prefix) +
                      "-class-description"
                  );
              },
              m
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.theme || U.prefix) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix) +
                      "--theme-" +
                      Y(D.theme) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["theme"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix) +
                      "--theme-" +
                      Y(D.theme) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/result/result";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/result/result.js";
define(
  "miniprogram_npm/tdesign-miniprogram/result/result.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      c = require("tslib"),
      s = require("../common/src/index"),
      n = l(require("./props")),
      o = l(require("../common/config")),
      a = require("../common/utils");
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = o.default.prefix,
      p = "".concat(u, "-result"),
      f = {
        default: "error-circle",
        success: "check-circle",
        warning: "error-circle",
        error: "close-circle",
      },
      m = (function (c) {
        t(o, c);
        var s = i(o);
        function o() {
          var e;
          return (
            r(this, o),
            ((e = s.apply(this, arguments)).options = { multipleSlots: !0 }),
            (e.externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-image"),
              "".concat(u, "-class-title"),
              "".concat(u, "-class-description"),
            ]),
            (e.properties = n.default),
            (e.data = { prefix: u, classPrefix: p }),
            (e.lifetimes = {
              ready: function () {
                this.initIcon();
              },
            }),
            (e.observers = {
              "icon, theme": function () {
                this.initIcon();
              },
            }),
            (e.methods = {
              initIcon: function () {
                var e = this.properties,
                  r = e.icon,
                  t = e.theme;
                this.setData({ _icon: (0, a.calcIcon)(r, f[t]) });
              },
            }),
            e
          );
        }
        return e(o);
      })(s.SuperComponent),
      d = (m = (0, c.__decorate)([(0, s.wxComponent)()], m));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/result/result.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/result/result.js");
