__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/image/image": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { style: new Array(1), customStyle: new Array(1) },
          K = U === true,
          d,
          g,
          i = (C) => {},
          j = (C, T) => {
            C || K || U.loading ? T(Y(D.loading)) : T();
          },
          h = (C, T, E, B, F, S) => {
            if (g === 1) {
              E(
                "t-loading",
                {},
                (N, C) => {
                  if (C) O(N, "theme", "dots");
                  if (C) O(N, "size", "44rpx");
                  if (C) O(N, "loading", true);
                  if (C) O(N, "inherit-color", true);
                  if (C) O(N, "t-class", "t-class-load");
                  if (C || K || !!U.classPrefix || undefined)
                    O(N, "t-class-text", Y(D.classPrefix) + "--loading-text");
                },
                i
              );
            } else if (g === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__common " +
                        Y(D.prefix) +
                        "-class-load"
                    );
                },
                j
              );
            } else {
              S("loading");
            }
          },
          f = (C, T, E, B) => {
            g =
              D.loading === "default"
                ? 1
                : D.loading !== "" && D.loading !== "slot"
                ? 2
                : 0;
            B(g, h);
          },
          l,
          o = (C) => {},
          n = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C) O(N, "name", "close");
                if (C) O(N, "aria-role", "img");
                if (C) O(N, "aria-label", "加载失败");
              },
              o
            );
          },
          p = (C, T) => {
            C || K || U.error ? T(Y(D.error)) : T();
          },
          m = (C, T, E, B, F, S) => {
            if (l === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.prefix || undefined)
                    L(N, Y(D.prefix) + "-class-load");
                  if (C) R.y(N, "font-size:44rpx");
                },
                n
              );
            } else if (l === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__common " +
                        Y(D.prefix) +
                        "-class-load"
                    );
                },
                p
              );
            } else {
              S("error");
            }
          },
          k = (C, T, E, B) => {
            l =
              D.error === "default" ? 1 : D.error && D.error !== "slot" ? 2 : 0;
            B(l, m);
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.classPrefix ||
                      U.shape
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__mask " +
                        Y(D.classPrefix) +
                        "--loading " +
                        Y(D.classPrefix) +
                        "--shape-" +
                        Y(D.shape)
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "_style") || Q.a([U.innerStyle])) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.innerStyle]));
                  if (C || K || U.ariaHidden) O(N, "aria-hidden", D.ariaHidden);
                },
                f
              );
            } else if (d === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.classPrefix ||
                      U.shape ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__mask " +
                        Y(D.classPrefix) +
                        "--failed " +
                        Y(D.classPrefix) +
                        "--shape-" +
                        Y(D.shape) +
                        " " +
                        Y(D.prefix) +
                        "-class-error"
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "_style") || Q.a([U.innerStyle])) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.innerStyle]));
                  if (C || K || U.ariaHidden) O(N, "aria-hidden", D.ariaHidden);
                },
                k
              );
            }
          },
          q,
          s = (C) => {},
          r = (C, T, E) => {
            if (q === 1) {
              E(
                "image",
                {},
                (N, C) => {
                  var $A = D.isLoading;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.shape ||
                      U.isLoading ||
                      ($A ? !!U.classPrefix || undefined : undefined) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__img " +
                        Y(D.classPrefix) +
                        "--shape-" +
                        Y(D.shape) +
                        " " +
                        Y($A ? D.classPrefix + "--lazy" : "") +
                        " " +
                        Y(D.prefix) +
                        "-class-image"
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "_style") || Q.a([U.innerStyle])) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.innerStyle]));
                  if (C || K || U.src) O(N, "src", D.src);
                  if (C || K || U.mode) O(N, "mode", D.mode);
                  if (C || K || U.webp) O(N, "webp", D.webp);
                  if (C || K || U.lazy) O(N, "lazy-load", D.lazy);
                  if (C || K || U.showMenuByLongpress)
                    O(N, "show-menu-by-longpress", D.showMenuByLongpress);
                  if (
                    C ||
                    K ||
                    !!(U.ariaHidden || U.isLoading || U.isFailed) ||
                    undefined
                  )
                    O(
                      N,
                      "aria-hidden",
                      D.ariaHidden || D.isLoading || D.isFailed
                    );
                  if (C || K || U.ariaLabel) O(N, "aria-label", D.ariaLabel);
                  if (C) R.v(N, "load", "onLoaded", !1, !1, !1, !1);
                  if (C) R.v(N, "error", "onLoadError", !1, !1, !1, !1);
                  if (C || K || !!U.tId || undefined) R.i(N, D.tId || "image");
                },
                s
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.isLoading ? 1 : D.isFailed ? 2 : 0;
            B(d, e);
            q = !D.isFailed ? 1 : 0;
            B(q, r);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/image/image";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/image/image.js";
define(
  "miniprogram_npm/tdesign-miniprogram/image/image.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      s = require("../common/src/index"),
      o = d(require("./props")),
      n = d(require("../common/config")),
      c = require("../common/utils"),
      l = require("../common/version");
    function d(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var h = n.default.prefix,
      u = "".concat(h, "-image"),
      p = (function (a) {
        i(n, a);
        var s = r(n);
        function n() {
          var e;
          return (
            t(this, n),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
              "".concat(h, "-class-load"),
              "".concat(h, "-class-image"),
              "".concat(h, "-class-error"),
            ]),
            (e.options = { multipleSlots: !0 }),
            (e.properties = o.default),
            (e.data = {
              prefix: h,
              isLoading: !0,
              isFailed: !1,
              innerStyle: "",
              classPrefix: u,
            }),
            (e.preSrc = void 0),
            (e.observers = {
              src: function () {
                this.preSrc !== this.properties.src && this.update();
              },
              "width, height": function (e, t) {
                this.calcSize(e, t);
              },
            }),
            (e.methods = {
              onLoaded: function (e) {
                var t = this,
                  i = c.appBaseInfo.SDKVersion,
                  r = this.properties,
                  a = r.mode,
                  s = r.tId,
                  o = (0, l.compareVersion)(i, "2.10.3") < 0;
                if ("heightFix" === a && o) {
                  var n = e.detail,
                    d = n.height,
                    h = n.width;
                  (0, c.getRect)(this, "#".concat(s || "image")).then(function (
                    e
                  ) {
                    var i = e.height,
                      r = ((i / d) * h).toFixed(2);
                    t.setData({
                      innerStyle: "height: "
                        .concat((0, c.addUnit)(i), "; width: ")
                        .concat(r, "px;"),
                    });
                  });
                }
                this.setData({ isLoading: !1, isFailed: !1 }),
                  this.triggerEvent("load", e.detail);
              },
              onLoadError: function (e) {
                this.setData({ isLoading: !1, isFailed: !0 }),
                  this.triggerEvent("error", e.detail);
              },
              calcSize: function (e, t) {
                var i = "";
                e && (i += "width: ".concat((0, c.addUnit)(e), ";")),
                  t && (i += "height: ".concat((0, c.addUnit)(t), ";")),
                  this.setData({ innerStyle: i });
              },
              update: function () {
                var e = this.properties.src;
                (this.preSrc = e),
                  e
                    ? this.setData({ isLoading: !0, isFailed: !1 })
                    : this.onLoadError({ errMsg: "图片链接为空" });
              },
            }),
            e
          );
        }
        return e(n);
      })(s.SuperComponent),
      f = (p = (0, a.__decorate)([(0, s.wxComponent)()], p));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/image/image.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/image/image.js");
