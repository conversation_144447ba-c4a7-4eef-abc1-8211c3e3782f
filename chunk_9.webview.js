__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/calendar/calendar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/calendar/calendar"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          d,
          g = (C, T, E, B, F, S, J) => {
            var $A = G["miniprogram_npm/tdesign-miniprogram/calendar/template"];
            if ($A) $A("")(R, C, D, U).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            J(g);
          },
          h = (C, T, E, B, F, S, J) => {
            var $A = G["miniprogram_npm/tdesign-miniprogram/calendar/template"];
            if ($A) $A("")(R, C, D, U).C(C, T, E, B, F, S, J);
          },
          e = (C, T, E, B, F, S, J) => {
            if (d === 1) {
              E(
                "t-popup",
                {},
                (N, C) => {
                  if (C) L(N, "class");
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C || K || U.usingCustomNavbar)
                    O(N, "usingCustomNavbar", D.usingCustomNavbar);
                  if (C) O(N, "placement", "bottom");
                  if (C)
                    R.v(N, "visible-change", "onVisibleChange", !1, !1, !1, !1);
                },
                f
              );
            } else {
              J(h);
            }
          },
          c = (C, T, E, B) => {
            d = D.usePopup ? 1 : 0;
            B(d, e);
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/calendar/calendar.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-calendar{background:var(--td-calendar-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));overflow-x:hidden;position:relative;width:inherit;z-index:9999}\n.",
        [1],
        "t-calendar--popup{border-top-left-radius:var(--td-calendar-radius,",
        [0, 24],
        ");border-top-right-radius:var(--td-calendar-radius,",
        [0, 24],
        ")}\n.",
        [1],
        "t-calendar__title{-webkit-align-items:center;align-items:center;color:var(--td-calendar-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex;font-size:var(--td-calendar-title-font-size,18px);font-weight:600;height:",
        [0, 52],
        ";-webkit-justify-content:center;justify-content:center;padding:",
        [0, 32],
        "}\n.",
        [1],
        "t-calendar__title:focus{outline:0}\n.",
        [1],
        "t-calendar__close-btn{color:var(--td-calendar-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));margin:",
        [0, -24],
        ";padding:",
        [0, 24],
        ";position:absolute;right:",
        [0, 32],
        ";top:",
        [0, 32],
        "}\n.",
        [1],
        "t-calendar__days{grid-column-gap:",
        [0, 8],
        ";display:grid;grid-template-columns:repeat(7,1fr);line-height:",
        [0, 92],
        ";padding:0 ",
        [0, 32],
        ";text-align:center}\n.",
        [1],
        "t-calendar__days-item{color:var(--td-calendar-days-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))));font-size:",
        [0, 28],
        ";height:",
        [0, 92],
        "}\n.",
        [1],
        "t-calendar__content{display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;min-height:",
        [0, 400],
        "}\n.",
        [1],
        "t-calendar__month{color:var(--td-calendar-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:",
        [0, 28],
        ";font-weight:600;padding:",
        [0, 32],
        " 0 0}\n.",
        [1],
        "t-calendar__months{box-sizing:border-box;height:",
        [0, 712],
        ";padding:0 ",
        [0, 32],
        " ",
        [0, 32],
        "}\n.",
        [1],
        "t-calendar__months::-webkit-scrollbar{display:none}\n.",
        [1],
        "t-calendar__dates{grid-column-gap:",
        [0, 8],
        ";display:grid;-webkit-flex:1;flex:1;grid-template-columns:repeat(7,1fr)}\n.",
        [1],
        "t-calendar__dates-item{-webkit-tap-highlight-color:transparent;-webkit-align-items:center;align-items:center;border-radius:var(--td-calendar-selected-border-radius,var(--td-radius-default,",
        [0, 12],
        "));color:var(--td-calendar-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));cursor:pointer;display:-webkit-flex;display:flex;font-size:",
        [0, 32],
        ";font-weight:600;height:",
        [0, 120],
        ";-webkit-justify-content:center;justify-content:center;line-height:",
        [0, 48],
        ";margin-top:",
        [0, 16],
        ";position:relative;-webkit-user-select:none;user-select:none}\n.",
        [1],
        "t-calendar__dates-item-prefix,.",
        [1],
        "t-calendar__dates-item-suffix{font-size:",
        [0, 20],
        ";font-weight:400;line-height:",
        [0, 32],
        ";position:absolute;text-align:center;width:100%}\n.",
        [1],
        "t-calendar__dates-item-prefix{top:",
        [0, 8],
        "}\n.",
        [1],
        "t-calendar__dates-item-suffix{bottom:",
        [0, 8],
        ";color:var(--td-calendar-item-suffix-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))))}\n.",
        [1],
        "t-calendar__dates-item-suffix--end,.",
        [1],
        "t-calendar__dates-item-suffix--selected,.",
        [1],
        "t-calendar__dates-item-suffix--start{color:var(--td-calendar-selected-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-calendar__dates-item-suffix--disabled{color:var(--td-calendar-item-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-calendar__dates-item--end,.",
        [1],
        "t-calendar__dates-item--selected,.",
        [1],
        "t-calendar__dates-item--start{background:var(--td-calendar-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:var(--td-calendar-selected-border-radius,var(--td-radius-default,",
        [0, 12],
        "));color:var(--td-calendar-selected-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-calendar__dates-item--start{border-radius:var(--td-calendar-selected-border-radius,var(--td-radius-default,",
        [0, 12],
        ")) 0 0 var(--td-calendar-selected-border-radius,var(--td-radius-default,",
        [0, 12],
        "))}\n.",
        [1],
        "t-calendar__dates-item--end{border-radius:0 var(--td-calendar-selected-border-radius,var(--td-radius-default,",
        [0, 12],
        ")) var(--td-calendar-selected-border-radius,var(--td-radius-default,",
        [0, 12],
        ")) 0}\n.",
        [1],
        "t-calendar__dates-item--start+.",
        [1],
        "t-calendar__dates-item--end::before{background:var(--td-calendar-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));content:\x22\x22;display:block;height:100%;position:absolute;top:0;width:",
        [0, 8],
        "}\n.",
        [1],
        "t-calendar__dates-item--start+.",
        [1],
        "t-calendar__dates-item--end:before{left:",
        [0, -8],
        "}\n.",
        [1],
        "t-calendar__dates-item--centre{border-radius:0}\n.",
        [1],
        "t-calendar__dates-item--centre,.",
        [1],
        "t-calendar__dates-item--centre::after,.",
        [1],
        "t-calendar__dates-item--centre::before{background-color:var(--td-calendar-item-centre-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)))}\n.",
        [1],
        "t-calendar__dates-item--centre::after,.",
        [1],
        "t-calendar__dates-item--centre::before{content:\x22\x22;display:block;height:100%;position:absolute;top:0;width:",
        [0, 8],
        "}\n.",
        [1],
        "t-calendar__dates-item--centre:before{left:",
        [0, -8],
        "}\n.",
        [1],
        "t-calendar__dates-item--centre:after{right:",
        [0, -8],
        "}\n.",
        [1],
        "t-calendar__dates-item--disabled{color:var(--td-calendar-item-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))));cursor:default}\n.",
        [1],
        "t-calendar__footer{padding:",
        [0, 32],
        "}\n.",
        [1],
        "t-calendar-switch-mode--none\x3e.",
        [1],
        "t-calendar__months{height:60vh}\n.",
        [1],
        "t-calendar-header{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between;line-height:",
        [0, 44],
        "}\n.",
        [1],
        "t-calendar-header__with-action{box-sizing:border-box;padding:",
        [0, 0],
        " ",
        [0, 32],
        " ",
        [0, 16],
        ";position:relative}\n.",
        [1],
        "t-calendar-header__with-action::after{background-color:var(--td-border-color,var(--td-gray-color-3,#e7e7e7));bottom:0;content:\x22\x22;display:block;height:1px;left:unset;left:0;position:absolute;right:unset;right:0;top:unset;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
        [1],
        "t-calendar-header__with-action .",
        [1],
        "t-calendar-header__title{-webkit-flex:1;flex:1;font-size:",
        [0, 28],
        ";font-weight:600;text-align:center}\n.",
        [1],
        "t-calendar-header__action{color:var(--td-calendar-switch-mode-icon-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))));display:-webkit-flex;display:flex;font-size:",
        [0, 40],
        "}\n.",
        [1],
        "t-calendar-header__icon{padding:",
        [0, 16],
        "}\n.",
        [1],
        "t-calendar-header__icon--disabled{color:var(--td-calendar-switch-mode-icon-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-calendar-header__title{text-align:left}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/calendar/calendar.wxss" }
    );
}
