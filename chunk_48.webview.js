__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/skeleton/skeleton": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          f,
          i = (C, j, k, l, m, n, T, E) => {
            var p = (C, q, r, s, t, u, T, E) => {
                var v = (C) => {};
                E(
                  "view",
                  {},
                  (N, C) => {
                    if (C || K || !!(Z(s, "class") || U.prefix) || undefined)
                      L(N, Y(X(q).class) + " " + Y(D.prefix) + "-class-col");
                    if (
                      C ||
                      K ||
                      !!(Z(undefined, "_style") || Z(s, "style")) ||
                      undefined
                    )
                      R.y(N, P(X(a)._style)(X(q).style));
                  },
                  v
                );
              },
              o = (C, T, E, B, F) => {
                F(j, "index", U ? l : undefined, [...n], p);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__row " + Y(D.prefix) + "-class-row"
                  );
              },
              o
            );
          },
          h = (C, T, E, B, F) => {
            F(
              D.parsedRowcols,
              "index",
              U ? U.parsedRowcols : undefined,
              [0, "parsedRowcols"],
              i
            );
          },
          g = (C, T, E) => {
            if (f === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__content");
                },
                h
              );
            }
          },
          e = (C, T, E, B) => {
            f = X(D.parsedRowcols).length ? 1 : 0;
            B(f, g);
          },
          j = (C, T, E, B, F, S) => {
            S("");
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                },
                e
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, "class " + Y(D.classPrefix) + "__content");
                },
                j
              );
            }
          },
          b = (C, T, E, B) => {
            c = D.isShow ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/skeleton/skeleton.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-skeleton{box-sizing:border-box}\n.",
        [1],
        "t-skeleton__row{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between;margin-bottom:var(--td-skeleton-row-spacing,var(--td-spacer-2,",
        [0, 32],
        "))}\n.",
        [1],
        "t-skeleton__row:last-child,.",
        [1],
        "t-skeleton__row:only-child{margin-bottom:0}\n.",
        [1],
        "t-skeleton__col{-webkit-align-items:center;align-items:center;background-color:var(--td-skeleton-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
        [1],
        "t-skeleton__col:first-child:last-child,.",
        [1],
        "t-skeleton__col:last-child{margin-right:0}\n.",
        [1],
        "t-skeleton--type-text{border-radius:var(--td-skeleton-text-border-radius,var(--td-radius-small,",
        [0, 6],
        "));height:var(--td-skeleton-text-height,",
        [0, 32],
        ");width:100%}\n.",
        [1],
        "t-skeleton--type-rect{border-radius:var(--td-skeleton-rect-border-radius,var(--td-radius-default,",
        [0, 12],
        "));height:var(--td-skeleton-rect-height,",
        [0, 32],
        ");width:100%}\n.",
        [1],
        "t-skeleton--type-circle{border-radius:var(--td-skeleton-circle-border-radius,var(--td-skeleton-circle-border-radius,var(--td-radius-circle,50%)));-webkit-flex-shrink:0;flex-shrink:0;height:var(--td-skeleton-circle-height,",
        [0, 96],
        ");width:var(--td-skeleton-circle-height,",
        [0, 96],
        ")}\n.",
        [1],
        "t-skeleton--animation-gradient{overflow-x:hidden;position:relative}\n.",
        [1],
        "t-skeleton--animation-gradient::after{-webkit-animation:t-skeleton--gradient 1.5s linear 2s infinite;animation:t-skeleton--gradient 1.5s linear 2s infinite;background:linear-gradient(90deg,hsla(0,0%,100%,0),var(--td-skeleton-animation-gradient,rgba(0,0,0,.04)),hsla(0,0%,100%,0));bottom:0;content:\x22 \x22;left:0;position:absolute;right:0;top:0}\n.",
        [1],
        "t-skeleton--animation-flashed{-webkit-animation:t-skeleton--flashed 2s linear 2s infinite;animation:t-skeleton--flashed 2s linear 2s infinite}\n@-webkit-keyframes t-skeleton--gradient{0%{-webkit-transform:translateX(-100%) skewX(-15deg);transform:translateX(-100%) skewX(-15deg)}\n100%{-webkit-transform:translateX(100%) skewX(-15deg);transform:translateX(100%) skewX(-15deg)}\n}@keyframes t-skeleton--gradient{0%{-webkit-transform:translateX(-100%) skewX(-15deg);transform:translateX(-100%) skewX(-15deg)}\n100%{-webkit-transform:translateX(100%) skewX(-15deg);transform:translateX(100%) skewX(-15deg)}\n}@-webkit-keyframes t-skeleton--flashed{0%{opacity:1}\n50%{background-color:var(--td-skeleton-animation-flashed,hsla(0,0%,90%,.3));opacity:.3}\n100%{opacity:1}\n}@keyframes t-skeleton--flashed{0%{opacity:1}\n50%{background-color:var(--td-skeleton-animation-flashed,hsla(0,0%,90%,.3));opacity:.3}\n100%{opacity:1}\n}",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/skeleton/skeleton.wxss" }
    );
}
