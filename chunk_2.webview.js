__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "components/service-card-my/service-card-my": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          g = (C) => {},
          f = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || Z(U.MyserviceData, "thumbnail"))
                  O(N, "src", X(D.MyserviceData).thumbnail);
              },
              g
            );
          },
          j = (C, T) => {
            C || K || Z(U.MyserviceData, "name")
              ? T(Y(X(D.MyserviceData).name))
              : T();
          },
          i = (C, T, E) => {
            E("text", {}, (N, C) => {}, j);
          },
          l = (C, T) => {
            C || K || Z(U.MyserviceData, "description")
              ? T(Y(X(D.MyserviceData).description))
              : T();
          },
          k = (C, T, E) => {
            E("text", {}, (N, C) => {}, l);
          },
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_title");
              },
              i
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info_ms");
              },
              k
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_img");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top_info");
                if (C) R.v(N, "tap", "onServiceClick", !1, !1, !1, !1);
              },
              h
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left_top");
              },
              e
            );
          },
          o,
          q = (C, T) => {
            var $A = X(D.MyserviceData).audit_status === "pending";
            C ||
            K ||
            !!Z(U.MyserviceData, "audit_status") ||
            ($A ? undefined : undefined)
              ? T(Y($A ? "待审核" : "审核不通过"))
              : T();
          },
          r = (C, T) => {
            var $A = X(D.MyserviceData).publish_status === "online";
            C ||
            K ||
            !!Z(U.MyserviceData, "publish_status") ||
            ($A ? undefined : undefined)
              ? T(Y($A ? "已上线" : "已下架"))
              : T();
          },
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  var $A = X(D.MyserviceData).audit_status === "pending";
                  if (
                    C ||
                    K ||
                    !!Z(U.MyserviceData, "audit_status") ||
                    ($A ? undefined : undefined)
                  )
                    O(N, "theme", $A ? "warning" : "danger");
                },
                q
              );
            } else {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  var $A = X(D.MyserviceData).publish_status === "online";
                  if (
                    C ||
                    K ||
                    !!Z(U.MyserviceData, "publish_status") ||
                    ($A ? undefined : undefined)
                  )
                    O(N, "theme", $A ? "success" : "warning");
                },
                r
              );
            }
          },
          n = (C, T, E, B) => {
            o = X(D.MyserviceData).audit_status !== "approved" ? 1 : 0;
            B(o, p);
          },
          t = (C, T) => {
            var $A = X(D.MyserviceData).price;
            C ||
            K ||
            !!Z(U.MyserviceData, "price") ||
            ($A ? Z(U.MyserviceData, "price") : undefined)
              ? T(Y($A ? X(D.MyserviceData).price : "面议"))
              : T();
          },
          s = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_middle_price");
              },
              t
            );
          },
          m = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_bottom");
              },
              n
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right_middle");
              },
              s
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_left");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "info_right");
              },
              m
            );
          },
          w = (C, T) => {
            C || K || !!Z(U.MyserviceData, "ongoing_order_count") || undefined
              ? T(Y("进行中：" + Y(X(D.MyserviceData).ongoing_order_count)))
              : T();
          },
          v = (C, T, E) => {
            E("text", {}, (N, C) => {}, w);
          },
          y = (C, T) => {
            C ? T("编辑") : T();
          },
          x = (C, T, E) => {
            E("text", {}, (N, C) => {}, y);
          },
          z,
          C0 = (C, T) => {
            var $A = D.isOfflineLoading;
            var $B = D.isOnlineLoading;
            var $C = X(D.MyserviceData).publish_status === "online";
            C ||
            K ||
            !!U.isOfflineLoading ||
            ($A
              ? undefined
              : !!U.isOnlineLoading ||
                ($B
                  ? undefined
                  : !!Z(U.MyserviceData, "publish_status") ||
                    ($C ? undefined : undefined)))
              ? T(Y($A ? "下架中..." : $B ? "上架中..." : $C ? "下架" : "上架"))
              : T();
          },
          B0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                var $A = D.isOfflineLoading || D.isOnlineLoading;
                if (
                  C ||
                  K ||
                  !!(U.isOfflineLoading || U.isOnlineLoading) ||
                  ($A ? undefined : undefined)
                )
                  L(N, $A ? "loading" : "");
              },
              C0
            );
          },
          E0 = (C, T) => {
            C ? T("下架") : T();
          },
          D0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "disabled");
              },
              E0
            );
          },
          A0 = (C, T, E) => {
            if (z === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "caozuo_item");
                  if (C) R.v(N, "tap", "onPublishStatusClick", !1, !1, !1, !1);
                },
                B0
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "caozuo_item");
                },
                D0
              );
            }
          },
          u = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "caozuo_item");
                if (C) R.v(N, "tap", "onOrderClick", !1, !1, !1, !1);
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "caozuo_item");
                if (C) R.v(N, "tap", "onEditClick", !1, !1, !1, !1);
              },
              x
            );
            z = X(D.MyserviceData).audit_status === "approved" ? 1 : 0;
            B(z, A0);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fuwu_list");
              },
              c
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fuwu_caozuo");
              },
              u
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "my_service");
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "components/user-order-card/user-order-card": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          e = (C) => {},
          f = (C, T) => {
            C || K || Z(Z(U.orderData, "service_provider"), "nickname")
              ? T(Y(X(X(D.orderData).service_provider).nickname))
              : T();
          },
          g = (C, T) => {
            C ||
            K ||
            !!(
              Z(Z(Z(U.orderData, "service_provider"), "address"), "building") ||
              Z(Z(Z(U.orderData, "service_provider"), "address"), "unit") ||
              Z(Z(Z(U.orderData, "service_provider"), "address"), "room")
            ) ||
            undefined
              ? T(
                  Y(
                    Y(X(X(X(D.orderData).service_provider).address).building) +
                      "-" +
                      Y(X(X(X(D.orderData).service_provider).address).unit) +
                      "-" +
                      Y(X(X(X(D.orderData).service_provider).address).room)
                  )
                )
              : T();
          },
          d = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) L(N, "avatar");
                if (C || K || Z(Z(U.orderData, "service_provider"), "avatar"))
                  O(N, "src", X(X(D.orderData).service_provider).avatar);
              },
              e
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "name");
              },
              f
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "id");
              },
              g
            );
          },
          i = (C, T) => {
            var $A =
              X(X(D.orderData).service_provider).auth_status === "verified";
            var $B =
              X(X(D.orderData).service_provider).neighbor_type === "owner";
            var $C =
              X(X(D.orderData).service_provider).neighbor_type === "tenant";
            var $D =
              X(X(D.orderData).service_provider).neighbor_type === "resident";
            var $E =
              X(X(D.orderData).service_provider).auth_status === "unverified";
            C ||
            K ||
            !!Z(Z(U.orderData, "service_provider"), "auth_status") ||
            ($A
              ? !!Z(Z(U.orderData, "service_provider"), "neighbor_type") ||
                ($B
                  ? undefined
                  : !!Z(Z(U.orderData, "service_provider"), "neighbor_type") ||
                    ($C
                      ? undefined
                      : !!Z(
                          Z(U.orderData, "service_provider"),
                          "neighbor_type"
                        ) || ($D ? undefined : undefined)))
              : !!Z(Z(U.orderData, "service_provider"), "auth_status") ||
                ($E ? undefined : undefined))
              ? T(
                  Y(
                    $A
                      ? $B
                        ? "业主"
                        : $C
                        ? "租客"
                        : $D
                        ? "住户"
                        : "其他"
                      : $E
                      ? "未认证"
                      : "信息不全"
                  )
                )
              : T();
          },
          h = (C, T, E) => {
            E(
              "t-tag",
              {},
              (N, C) => {
                if (C) L(N, "margin-16");
                if (C) O(N, "variant", "light");
                var $A =
                  X(X(D.orderData).service_provider).auth_status === "verified";
                var $B =
                  X(X(D.orderData).service_provider).auth_status ===
                  "unverified";
                if (
                  C ||
                  K ||
                  !!Z(Z(U.orderData, "service_provider"), "auth_status") ||
                  ($A
                    ? undefined
                    : !!Z(Z(U.orderData, "service_provider"), "auth_status") ||
                      ($B ? undefined : undefined))
                )
                  O(N, "theme", $A ? "success" : $B ? "warning" : "default");
              },
              i
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top_left");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top_right");
              },
              h
            );
          },
          l = (C) => {},
          k = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C) L(N, "service-icon");
                if (C || K || Z(Z(U.orderData, "service"), "thumbnail"))
                  O(N, "src", X(X(D.orderData).service).thumbnail);
              },
              l
            );
          },
          o = (C, T) => {
            C || K || Z(Z(U.orderData, "service"), "name")
              ? T(Y(X(X(D.orderData).service).name))
              : T();
          },
          n = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service-title");
              },
              o
            );
          },
          p,
          s = (C, T) => {
            C || K || Z(Z(U.orderData, "service"), "description")
              ? T(Y(X(X(D.orderData).service).description))
              : T();
          },
          r = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "service-description");
              },
              s
            );
          },
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "attachment-box");
                },
                r
              );
            }
          },
          m = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service-title-box");
              },
              n
            );
            p = X(X(D.orderData).service).description ? 1 : 0;
            B(p, q);
          },
          v,
          x = (C) => {},
          w = (C, T, E) => {
            if (v === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (C) L(N, "price-symbol");
                },
                x
              );
            }
          },
          y = (C, T) => {
            var $A = X(X(D.orderData).service).price !== null;
            C ||
            K ||
            !!Z(Z(U.orderData, "service"), "price") ||
            ($A ? Z(Z(U.orderData, "service"), "price") : undefined)
              ? T(Y($A ? X(X(D.orderData).service).price : "面议"))
              : T();
          },
          u = (C, T, E, B) => {
            v = X(X(D.orderData).service).price !== null ? 1 : 0;
            B(v, w);
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "price-value");
              },
              y
            );
          },
          t = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "price-box");
              },
              u
            );
          },
          j = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_left");
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_midle");
              },
              m
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info_right");
              },
              t
            );
          },
          B0 = (C, T) => {
            C ? T("联系商家") : T();
          },
          A0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, B0);
          },
          D0 = (C, T) => {
            C ||
            K ||
            !!Z(Z(U.orderData, "order_statistics"), "in_progress_count") ||
            undefined
              ? T(
                  Y(
                    "进行中：" +
                      Y(X(X(D.orderData).order_statistics).in_progress_count)
                  )
                )
              : T();
          },
          C0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, D0);
          },
          F0 = (C, T) => {
            C ||
            K ||
            !!Z(Z(U.orderData, "order_statistics"), "completed_count") ||
            undefined
              ? T(
                  Y(
                    "已完成：" +
                      Y(X(X(D.orderData).order_statistics).completed_count)
                  )
                )
              : T();
          },
          E0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, F0);
          },
          z = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "contact-btn");
                if (C) R.v(N, "tap", "onContactCustomer", !1, !1, !1, !1);
              },
              A0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "contact-btn");
                if (C) R.d(N, "status", "in_progress");
                if (C) R.v(N, "tap", "onNavigateToOrderList", !1, !1, !1, !1);
              },
              C0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "contact-btn");
                if (C) R.d(N, "status", "completed");
                if (C) R.v(N, "tap", "onNavigateToOrderList", !1, !1, !1, !1);
              },
              E0
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_top");
              },
              c
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_info");
                if (C)
                  R.v(N, "tap", "onNavigateToServiceDetail", !1, !1, !1, !1);
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list_bottom");
              },
              z
            );
          },
          a = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "fwdd_list");
              },
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "pages/my/my": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "我的");
                if (C || K || undefined) O(N, "back", false);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          g = (C) => {},
          f = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || !!Z(U.myInfo, "avatar") || undefined)
                  O(
                    N,
                    "src",
                    X(D.myInfo).avatar || "/assets/tx/时尚职业男.png"
                  );
              },
              g
            );
          },
          i = (C, T) => {
            C || K || !!Z(U.myInfo, "nickname") || undefined
              ? T(Y(X(D.myInfo).nickname || "暂无昵称"))
              : T();
          },
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box_midle_username");
              },
              i
            );
          },
          l,
          n = (C, T) => {
            C || K || Z(Z(U.myInfo, "neighbor_type"), "label")
              ? T(Y(X(X(D.myInfo).neighbor_type).label))
              : T();
          },
          o = (C, T) => {
            C || K || Z(Z(U.myInfo, "auth_status"), "label")
              ? T(Y(X(X(D.myInfo).auth_status).label))
              : T();
          },
          p = (C, T) => {
            C || K || Z(Z(U.myInfo, "auth_status"), "label")
              ? T(Y(X(X(D.myInfo).auth_status).label))
              : T();
          },
          m = (C, T, E) => {
            if (l === 1) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  if (C) O(N, "theme", "success");
                },
                n
              );
            } else if (l === 2) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  if (C) O(N, "theme", "warning");
                },
                o
              );
            } else if (l === 3) {
              E(
                "t-tag",
                {},
                (N, C) => {
                  if (C) L(N, "margin-16");
                  if (C) O(N, "variant", "light");
                  if (C) O(N, "theme", "danger");
                },
                p
              );
            }
          },
          k = (C, T, E, B) => {
            l =
              X(X(D.myInfo).auth_status).value === "verified"
                ? 1
                : X(X(D.myInfo).auth_status).value === "incomplete"
                ? 2
                : X(X(D.myInfo).auth_status).value === "unverified"
                ? 3
                : 0;
            B(l, m);
          },
          q = (C, T) => {
            C ? T("编辑") : T();
          },
          j = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box_right_tag");
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "edit-button");
                if (C) O(N, "bindtap", "navigateToEditProfile");
              },
              q
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box_left");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box_midle");
              },
              h
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box_right");
              },
              j
            );
          },
          u = (C) => {},
          v = (C) => {},
          t = (C, T, E) => {
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "我的服务");
                if (C) O(N, "value", "0");
              },
              u
            );
            E(
              "t-tab-panel",
              {},
              (N, C) => {
                if (C) O(N, "label", "预约记录");
                if (C) O(N, "value", "1");
              },
              v
            );
          },
          s = (C, T, E) => {
            E(
              "t-tabs",
              {},
              (N, C) => {
                if (C || K || undefined) O(N, "defaultValue", 0);
                if (C) O(N, "t-class", "custom-tabs");
                if (C) O(N, "t-class-track", "custom-track");
                if (C) R.v(N, "change", "onTabsChange", !1, !1, !1, !1);
                if (C) R.v(N, "click", "onTabsClick", !1, !1, !1, !1);
              },
              t
            );
          },
          r = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "tags_box");
              },
              s
            );
          },
          x,
          A0,
          D0 = (C, T) => {
            C ? T("加载中...") : T();
          },
          C0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, D0);
          },
          F0 = (C, T) => {
            C ? T("暂无发布的服务") : T();
          },
          E0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, F0);
          },
          H0 = (C, I0, J0, K0, L0, M0, T, E) => {
            var N0 = (C) => {};
            E(
              "service-card-my",
              {},
              (N, C) => {
                if (C || K || K0) O(N, "MyserviceData", I0);
              },
              N0
            );
          },
          G0 = (C, T, E, B, F) => {
            F(
              D.userServices,
              "id",
              U ? U.userServices : undefined,
              [0, "userServices"],
              H0
            );
          },
          B0 = (C, T, E) => {
            if (A0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                C0
              );
            } else if (A0 === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty-state");
                },
                E0
              );
            } else {
              E("view", {}, (N, C) => {}, G0);
            }
          },
          z = (C, T, E, B) => {
            A0 = D.loading ? 1 : X(D.userServices).length === 0 ? 2 : 0;
            B(A0, B0);
          },
          y = (C, T, E) => {
            if (x === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                z
              );
            }
          },
          I0,
          L0 = (C, M0, N0, O0, P0, Q0, T, E) => {
            var R0 = (C) => {};
            E(
              "user-order-card",
              {},
              (N, C) => {
                if (C || K || O0) O(N, "orderData", M0);
                if (C) R.v(N, "contact", "handleContact", !1, !1, !1, !1);
                if (C) R.v(N, "confirm", "handleConfirm", !1, !1, !1, !1);
                if (C)
                  R.v(
                    N,
                    "orderStatusChanged",
                    "handleOrderStatusChanged",
                    !1,
                    !1,
                    !1,
                    !1
                  );
              },
              R0
            );
          },
          M0,
          P0 = (C, T) => {
            C ? T("暂无预约记录") : T();
          },
          O0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, P0);
          },
          N0 = (C, T, E) => {
            if (M0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty-state");
                },
                O0
              );
            }
          },
          Q0,
          T0 = (C, T) => {
            C ? T("加载中...") : T();
          },
          S0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, T0);
          },
          R0 = (C, T, E) => {
            if (Q0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-state");
                },
                S0
              );
            }
          },
          K0 = (C, T, E, B, F) => {
            F(
              D.userOrders,
              "order_id",
              U ? U.userOrders : undefined,
              [0, "userOrders"],
              L0
            );
            M0 = X(D.userOrders).length === 0 && !D.loading ? 1 : 0;
            B(M0, N0);
            Q0 = D.loading ? 1 : 0;
            B(Q0, R0);
          },
          J0 = (C, T, E) => {
            if (I0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                K0
              );
            }
          },
          w = (C, T, E, B) => {
            x = D.activeTab === 0 ? 1 : 0;
            B(x, y);
            I0 = D.activeTab === 1 ? 1 : 0;
            B(I0, J0);
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "top_box");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "dt_box");
              },
              r
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "dt_box");
              },
              w
            );
          },
          a = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["components/service-card-my/service-card-my.wxss"] =
    setCssToHead(
      [
        ".",
        [1],
        "tags_box{display:-webkit-flex;display:flex;gap:",
        [0, 20],
        ";margin-bottom:",
        [0, 20],
        "}\n.",
        [1],
        "tags_item{color:#333;cursor:pointer;font-size:",
        [0, 28],
        ";padding:",
        [0, 10],
        " ",
        [0, 30],
        ";position:relative}\n.",
        [1],
        "tags_item.",
        [1],
        "active{color:red}\n.",
        [1],
        "tags_item.",
        [1],
        "active::after{background-color:red;bottom:",
        [0, -4],
        ";content:\x22\x22;height:",
        [0, 4],
        ";left:50%;position:absolute;-webkit-transform:translateX(-50%);transform:translateX(-50%);width:80%}\n.",
        [1],
        "tags_item:first-child{color:#195abf}\n.",
        [1],
        "tags_item:last-child{border-color:#fee;color:#f50}\n.",
        [1],
        "my_service{background-color:#fff;border:",
        [0, 5],
        " solid #f0f0f0;border-radius:",
        [0, 20],
        ";box-shadow:0 ",
        [0, 2],
        " ",
        [0, 10],
        " rgba(0,0,0,.05);height:auto;margin:",
        [0, 10],
        " auto;padding:",
        [0, 20],
        ";width:95%}\n.",
        [1],
        "fuwu_list{display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between}\n.",
        [1],
        "info_left{width:70%}\n.",
        [1],
        "info_left_top,.",
        [1],
        "info_left_top_img{display:-webkit-flex;display:flex}\n.",
        [1],
        "info_left_top_img{-webkit-align-items:center;align-items:center;background-color:#eff2f5;border-radius:",
        [0, 15],
        ";height:",
        [0, 150],
        ";-webkit-justify-content:center;justify-content:center;margin-right:",
        [0, 20],
        ";padding:10prx;width:",
        [0, 150],
        "}\n.",
        [1],
        "info_left_top_img wx-image{border-radius:",
        [0, 10],
        ";height:90%;object-fit:cover;width:90%}\n.",
        [1],
        "info_left_top_info{-webkit-flex:1;flex:1}\n.",
        [1],
        "info_left_top_info_title{color:#333;font-size:",
        [0, 30],
        ";font-weight:500}\n.",
        [1],
        "info_left_top_info_ms{color:#666;font-size:",
        [0, 22],
        ";line-height:1.8;margin-bottom:",
        [0, 8],
        ";margin-top:",
        [0, 8],
        "}\n.",
        [1],
        "info_left_top_info_fw,.",
        [1],
        "info_left_top_info_time{color:#999;font-size:",
        [0, 22],
        ";margin-bottom:",
        [0, 6],
        "}\n.",
        [1],
        "info_left_bottom{display:-webkit-flex;display:flex;margin-top:",
        [0, 20],
        "}\n.",
        [1],
        "info_left_bottom_num{color:#999;font-size:",
        [0, 22],
        ";margin-right:",
        [0, 40],
        "}\n.",
        [1],
        "info_right{-webkit-align-items:flex-end;align-items:flex-end;-webkit-flex-direction:column;flex-direction:column;width:25%}\n.",
        [1],
        "info_right,.",
        [1],
        "info_right_top{display:-webkit-flex;display:flex}\n.",
        [1],
        "info_right_top{-webkit-align-items:center;align-items:center}\n.",
        [1],
        "info_right_top_img{height:",
        [0, 50],
        ";margin-right:",
        [0, 10],
        ";width:",
        [0, 50],
        "}\n.",
        [1],
        "info_right_top_img wx-image{border-radius:50%;height:100%;width:100%}\n.",
        [1],
        "info_right_top_username{color:#f90;font-size:",
        [0, 26],
        "}\n.",
        [1],
        "info_right_middle{margin:",
        [0, 30],
        " 0}\n.",
        [1],
        "info_right_middle_price{color:#f50;font-size:",
        [0, 32],
        ";font-weight:700}\n.",
        [1],
        "info_right_bottom{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-flex-direction:row;flex-direction:row;-webkit-justify-content:flex-end;justify-content:flex-end;margin-top:",
        [0, 20],
        "}\n.",
        [1],
        "margin-16{margin-bottom:0;margin-right:",
        [0, 16],
        "}\n.",
        [1],
        "margin-16:last-child{margin-right:0}\n.",
        [1],
        "fuwu_caozuo{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:space-around;justify-content:space-around;padding-top:",
        [0, 20],
        "}\n.",
        [1],
        "caozuo_item{color:#666;-webkit-flex:1;flex:1;font-size:",
        [0, 24],
        ";position:relative;text-align:center}\n.",
        [1],
        "caozuo_item:not(:last-child)::after{background-color:#e0e0e0;content:\x22\x22;height:",
        [0, 24],
        ";position:absolute;right:0;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:",
        [0, 2],
        "}\n.",
        [1],
        "caozuo_item:last-child wx-text,.",
        [1],
        "caozuo_item:nth-last-child(2) wx-text,.",
        [1],
        "caozuo_item:nth-last-child(3) wx-text{color:#195abf}\n.",
        [1],
        "myhelp_box{background-color:#fff;border:",
        [0, 5],
        " solid #f0f0f0;border-radius:",
        [0, 20],
        ";box-shadow:0 ",
        [0, 2],
        " ",
        [0, 10],
        " rgba(0,0,0,.05);height:auto;margin:",
        [0, 10],
        " auto;padding:",
        [0, 20],
        ";width:95%}\n.",
        [1],
        "help_info{-webkit-align-items:flex-start;align-items:flex-start;border-bottom:",
        [0, 2],
        " solid #f5f5f5;-webkit-justify-content:space-between;justify-content:space-between;padding-bottom:",
        [0, 20],
        "}\n.",
        [1],
        "help_info,.",
        [1],
        "help_info_left{display:-webkit-flex;display:flex}\n.",
        [1],
        "help_info_left{-webkit-align-items:center;align-items:center;background:#fee;border-radius:",
        [0, 16],
        ";height:",
        [0, 150],
        ";-webkit-justify-content:center;justify-content:center;width:",
        [0, 150],
        "}\n.",
        [1],
        "help_info_left wx-image{border-radius:",
        [0, 16],
        ";height:100%;width:100%}\n.",
        [1],
        "help_info_midle{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-flex-direction:column;flex-direction:column;padding:0 ",
        [0, 20],
        "}\n.",
        [1],
        "help_title{color:#333;font-size:",
        [0, 32],
        ";font-weight:500;margin-bottom:",
        [0, 10],
        "}\n.",
        [1],
        "help_shuoming{color:#666;font-size:",
        [0, 26],
        ";margin-bottom:",
        [0, 8],
        "}\n.",
        [1],
        "help_overtime{color:#999;font-size:",
        [0, 24],
        "}\n.",
        [1],
        "help_info_right{-webkit-align-items:flex-end;align-items:flex-end;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;padding:",
        [0, 30],
        " 0}\n.",
        [1],
        "help_price{color:#f50;font-size:",
        [0, 28],
        ";font-weight:500;margin-bottom:",
        [0, 10],
        "}\n.",
        [1],
        "help_type{color:#666;font-size:",
        [0, 24],
        ";margin-top:",
        [0, 30],
        "}\n.",
        [1],
        "help_caozuo{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:space-around;justify-content:space-around;padding-top:",
        [0, 20],
        "}\n.",
        [1],
        "help_caozuo_item{color:#666;-webkit-flex:1;flex:1;font-size:",
        [0, 24],
        ";position:relative;text-align:center}\n.",
        [1],
        "help_caozuo_item:not(:last-child)::after{background-color:#e0e0e0;content:\x22\x22;height:",
        [0, 24],
        ";position:absolute;right:0;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:",
        [0, 2],
        "}\n.",
        [1],
        "help_caozuo_item:last-child wx-text,.",
        [1],
        "help_caozuo_item:nth-last-child(2) wx-text{color:#195abf}\n.",
        [1],
        "caozuo_item .",
        [1],
        "loading{color:#999!important;opacity:.6}\n.",
        [1],
        "caozuo_item .",
        [1],
        "disabled{color:#ccc!important;opacity:.5}\n.",
        [1],
        "caozuo_item:last-child wx-text{color:#195abf}\n.",
        [1],
        "caozuo_item:last-child .",
        [1],
        "disabled,.",
        [1],
        "caozuo_item:last-child .",
        [1],
        "loading{color:#999!important}\n",
      ],
      "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./components/service-card-my/service-card-my.wxss:1:4992)",
      { path: "./components/service-card-my/service-card-my.wxss" }
    );
  __wxAppCode__["components/user-order-card/user-order-card.wxss"] =
    setCssToHead(
      [
        ".",
        [1],
        "fwdd_list{background:#fff;border-radius:",
        [0, 24],
        ";margin:",
        [0, 20],
        " auto;padding:",
        [0, 24],
        "}\n.",
        [1],
        "fwdd_list_top{-webkit-justify-content:space-between;justify-content:space-between;margin-bottom:",
        [0, 20],
        "}\n.",
        [1],
        "fwdd_list_top,.",
        [1],
        "fwdd_list_top_left{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
        [1],
        "fwdd_list_top_left{gap:",
        [0, 12],
        "}\n.",
        [1],
        "avatar{border-radius:50%;height:",
        [0, 36],
        ";width:",
        [0, 36],
        "}\n.",
        [1],
        "name{color:#333;font-size:",
        [0, 28],
        "}\n.",
        [1],
        "id{color:#999;font-size:",
        [0, 24],
        "}\n.",
        [1],
        "fwdd_list_top_right{display:-webkit-flex;display:flex;gap:",
        [0, 12],
        "}\n.",
        [1],
        "btn-delivery,.",
        [1],
        "btn-service{border-radius:",
        [0, 8],
        ";font-size:",
        [0, 24],
        ";padding:",
        [0, 4],
        " ",
        [0, 16],
        "}\n.",
        [1],
        "btn-service{border:",
        [0, 2],
        " solid #ff4d4f;color:#ff4d4f}\n.",
        [1],
        "btn-delivery{border:",
        [0, 2],
        " solid #ddd;color:#666}\n.",
        [1],
        "fwdd_list_info{border-bottom:",
        [0, 2],
        " solid #f5f5f5;border-top:",
        [0, 2],
        " solid #f5f5f5;display:-webkit-flex;display:flex;padding:",
        [0, 20],
        " 0}\n.",
        [1],
        "service-icon{border-radius:",
        [0, 16],
        ";height:",
        [0, 120],
        ";width:",
        [0, 120],
        "}\n.",
        [1],
        "fwdd_list_info_midle{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:flex-start;justify-content:flex-start;margin-left:",
        [0, 20],
        "}\n.",
        [1],
        "service-title{color:#333;font-size:",
        [0, 30],
        ";font-weight:500}\n.",
        [1],
        "attachment-box{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;margin:",
        [0, 8],
        " 0}\n.",
        [1],
        "attachment-label{color:#2b5ee3;font-size:",
        [0, 26],
        "}\n.",
        [1],
        "attachment-count{color:#666;font-size:",
        [0, 26],
        "}\n.",
        [1],
        "download-hint{color:#999;font-size:",
        [0, 24],
        "}\n.",
        [1],
        "fwdd_list_info_right{-webkit-align-items:flex-end;align-items:flex-end;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:space-between;justify-content:space-between;min-width:",
        [0, 120],
        ";text-align:right}\n.",
        [1],
        "fwdd_list_info_right,.",
        [1],
        "price-box{display:-webkit-flex;display:flex}\n.",
        [1],
        "price-box{-webkit-align-items:baseline;align-items:baseline}\n.",
        [1],
        "price-box,.",
        [1],
        "price-box wx-text{color:#ff4d4f}\n.",
        [1],
        "price-symbol{color:#333;font-size:",
        [0, 24],
        "}\n.",
        [1],
        "price-value{color:#333;font-size:",
        [0, 32],
        ";font-weight:500}\n.",
        [1],
        "quantity{color:#999;font-size:",
        [0, 24],
        ";margin:",
        [0, 8],
        " 0}\n.",
        [1],
        "total-price{color:#666;font-size:",
        [0, 24],
        "}\n.",
        [1],
        "amount{color:#ff4d4f}\n.",
        [1],
        "fwdd_list_bottom{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between;margin-top:",
        [0, 20],
        "}\n.",
        [1],
        "contact-btn{color:#2b5ee3;font-size:",
        [0, 24],
        ";margin-left:",
        [0, 20],
        "}\n.",
        [1],
        "quxiao-btn{color:#ff4d4f}\n.",
        [1],
        "confirm-btn,.",
        [1],
        "quxiao-btn{border:",
        [0, 5],
        " solid #ff4d4f;border-radius:",
        [0, 20],
        ";font-size:",
        [0, 24],
        ";padding:",
        [0, 8],
        " ",
        [0, 32],
        "}\n.",
        [1],
        "confirm-btn{background-color:#ff4d4f;color:#fff}\n.",
        [1],
        "service-description{color:#999;font-size:",
        [0, 24],
        ";line-height:1.4}\n.",
        [1],
        "fwdd_list_info_left{-webkit-align-items:center;align-items:center;background-color:#eff2f5;border-radius:",
        [0, 15],
        ";display:-webkit-flex;display:flex;height:",
        [0, 150],
        ";-webkit-justify-content:center;justify-content:center;margin-right:",
        [0, 20],
        ";padding:10prx;width:",
        [0, 150],
        "}\n",
      ],
      "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./components/user-order-card/user-order-card.wxss:1:1769)",
      { path: "./components/user-order-card/user-order-card.wxss" }
    );
  __wxAppCode__["pages/my/my.wxss"] = setCssToHead(
    [
      "body{background-color:#f5f5f5;box-sizing:border-box;min-height:100vh;padding-bottom:",
      [0, 180],
      "}\n.",
      [1],
      "dt_box{width:100%}\n.",
      [1],
      "top_box{-webkit-align-items:center;align-items:center;background-color:#fff;display:-webkit-flex;display:flex;padding:",
      [0, 30],
      "}\n.",
      [1],
      "top_box_left{margin-right:",
      [0, 30],
      "}\n.",
      [1],
      "top_box_left wx-image{border-radius:50%;height:",
      [0, 120],
      ";width:",
      [0, 120],
      "}\n.",
      [1],
      "edit-button{-webkit-align-self:flex-end;align-self:flex-end;background-color:#e9ecef;border:3px solid #e9ecef;border-radius:",
      [0, 10],
      ";color:#1e1e1e;font-size:",
      [0, 26],
      ";margin-top:",
      [0, 30],
      ";text-align:right;text-align:center}\n.",
      [1],
      "top_box_midle{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-flex-direction:column;flex-direction:column}\n.",
      [1],
      "top_box_midle_username{font-size:",
      [0, 36],
      ";font-weight:500;margin-bottom:",
      [0, 30],
      "}\n.",
      [1],
      "top_box_midle_shuoming{color:#888;font-size:",
      [0, 28],
      "}\n.",
      [1],
      "button_box{background-color:#fff;display:-webkit-flex;display:flex;-webkit-justify-content:flex-start;justify-content:flex-start;padding:",
      [0, 20],
      " ",
      [0, 30],
      "}\n.",
      [1],
      "button_box_item{background-color:#f0f0f0;border-radius:",
      [0, 20],
      ";color:#666;font-size:",
      [0, 28],
      ";height:",
      [0, 70],
      ";line-height:",
      [0, 70],
      ";margin-left:",
      [0, 20],
      ";text-align:center;transition:all .3s ease;width:30%}\n.",
      [1],
      "button_box_item.",
      [1],
      "active{background-color:#fdd;color:#f66}\n.",
      [1],
      "nr_box{margin:0 auto;width:95%}\n.",
      [1],
      "scroll-container{height:100vh;width:100%}\n.",
      [1],
      "container{min-height:100%;padding-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "tags_box{margin:",
      [0, 20],
      " auto;width:95%}\n.",
      [1],
      "custom-tabs{background-color:initial!important;-webkit-justify-content:flex-start!important;justify-content:flex-start!important}\n.",
      [1],
      "custom-tabs .",
      [1],
      "t-tabs__item{color:#1e1e1e!important}\n.",
      [1],
      "custom-tabs .",
      [1],
      "t-tabs__item--active{color:#ff4d4f!important}\n.",
      [1],
      "custom-track{background-color:#ff4d4f!important}\n.",
      [1],
      "fwdd_list{background:#fff;border-radius:",
      [0, 24],
      ";margin:",
      [0, 20],
      ";padding:",
      [0, 24],
      "}\n.",
      [1],
      "fwdd_list_top{-webkit-justify-content:space-between;justify-content:space-between;margin-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "fwdd_list_top,.",
      [1],
      "fwdd_list_top_left{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex}\n.",
      [1],
      "fwdd_list_top_left{gap:",
      [0, 12],
      "}\n.",
      [1],
      "avatar{border-radius:50%;height:",
      [0, 36],
      ";width:",
      [0, 36],
      "}\n.",
      [1],
      "name{color:#333;font-size:",
      [0, 28],
      "}\n.",
      [1],
      "id{color:#999;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "fwdd_list_top_right{display:-webkit-flex;display:flex;gap:",
      [0, 12],
      "}\n.",
      [1],
      "btn-delivery,.",
      [1],
      "btn-service{border-radius:",
      [0, 8],
      ";font-size:",
      [0, 24],
      ";padding:",
      [0, 4],
      " ",
      [0, 16],
      "}\n.",
      [1],
      "btn-service{border:",
      [0, 2],
      " solid #ff4d4f;color:#ff4d4f}\n.",
      [1],
      "btn-delivery{border:",
      [0, 2],
      " solid #ddd;color:#666}\n.",
      [1],
      "fwdd_list_info{border-bottom:",
      [0, 2],
      " solid #f5f5f5;border-top:",
      [0, 2],
      " solid #f5f5f5;display:-webkit-flex;display:flex;padding:",
      [0, 20],
      " 0}\n.",
      [1],
      "service-icon{border-radius:",
      [0, 16],
      ";height:",
      [0, 120],
      ";width:",
      [0, 120],
      "}\n.",
      [1],
      "fwdd_list_info_midle{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:space-between;justify-content:space-between;margin-left:",
      [0, 20],
      "}\n.",
      [1],
      "service-title{color:#333;font-size:",
      [0, 30],
      ";font-weight:500}\n.",
      [1],
      "attachment-box{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;margin:",
      [0, 8],
      " 0}\n.",
      [1],
      "attachment-label{color:#2b5ee3;font-size:",
      [0, 26],
      "}\n.",
      [1],
      "attachment-count{color:#666;font-size:",
      [0, 26],
      "}\n.",
      [1],
      "download-hint{color:#999;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "fwdd_list_info_right{-webkit-align-items:flex-end;align-items:flex-end;-webkit-flex-direction:column;flex-direction:column;-webkit-justify-content:space-between;justify-content:space-between;min-width:",
      [0, 120],
      ";text-align:right}\n.",
      [1],
      "fwdd_list_info_right,.",
      [1],
      "price-box{display:-webkit-flex;display:flex}\n.",
      [1],
      "price-box{-webkit-align-items:baseline;align-items:baseline}\n.",
      [1],
      "price-symbol{color:#333;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "price-value{color:#333;font-size:",
      [0, 32],
      ";font-weight:500}\n.",
      [1],
      "quantity{color:#999;font-size:",
      [0, 24],
      ";margin:",
      [0, 8],
      " 0}\n.",
      [1],
      "total-price{color:#666;font-size:",
      [0, 24],
      "}\n.",
      [1],
      "amount{color:#ff4d4f}\n.",
      [1],
      "fwdd_list_bottom{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between;margin-top:",
      [0, 20],
      "}\n.",
      [1],
      "contact-btn{color:#2b5ee3;font-size:",
      [0, 28],
      "}\n.",
      [1],
      "confirm-btn{border:",
      [0, 2],
      " solid #ff4d4f;border-radius:",
      [0, 32],
      ";color:#ff4d4f;font-size:",
      [0, 28],
      ";padding:",
      [0, 8],
      " ",
      [0, 32],
      "}\n.",
      [1],
      "empty-state{color:#999;padding:",
      [0, 60],
      " ",
      [0, 30],
      ";text-align:center}\n.",
      [1],
      "empty-state wx-text{color:#999;font-size:",
      [0, 28],
      "}\n",
    ],
    "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/my/my.wxss:1:4009)",
    { path: "./pages/my/my.wxss" }
  );
}
