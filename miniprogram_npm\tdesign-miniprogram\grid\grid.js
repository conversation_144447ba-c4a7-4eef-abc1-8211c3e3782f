Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  r = require("../../../@babel/runtime/helpers/inherits"),
  n = require("../../../@babel/runtime/helpers/createSuper"),
  i = require("tslib"),
  o = require("../common/src/index"),
  a = l(require("../common/config")),
  s = require("../common/validator"),
  u = l(require("./props"));
function l(e) {
  return e && e.__esModule ? e : { default: e };
}
var c = a.default.prefix,
  p = "".concat(c, "-grid"),
  d = (function (i) {
    r(a, i);
    var o = n(a);
    function a() {
      var e;
      return (
        t(this, a),
        ((e = o.apply(this, arguments)).externalClasses = ["t-class"]),
        (e.relations = { "../grid-item/grid-item": { type: "descendant" } }),
        (e.properties = u.default),
        (e.data = { prefix: c, classPrefix: p, contentStyle: "" }),
        (e.observers = {
          "column,hover,align,gutter,border": function () {
            this.updateContentStyle(),
              this.doForChild(function (e) {
                return e.updateStyle();
              });
          },
        }),
        (e.lifetimes = {
          attached: function () {
            this.updateContentStyle();
          },
        }),
        (e.methods = {
          doForChild: function (e) {
            this.$children.forEach(e);
          },
          updateContentStyle: function () {
            var e = [],
              t = this.getContentMargin();
            t && e.push(t), this.setData({ contentStyle: e.join(";") });
          },
          getContentMargin: function () {
            var e = this.properties.gutter,
              t = this.properties.border;
            if (!t)
              return "margin-left:-"
                .concat(e, "rpx; margin-top:-")
                .concat(e, "rpx");
            (0, s.isObject)(t) || (t = {});
            var r = t.width,
              n = void 0 === r ? 2 : r;
            return "margin-left:-"
              .concat(n, "rpx; margin-top:-")
              .concat(n, "rpx");
          },
        }),
        e
      );
    }
    return e(a);
  })(o.SuperComponent),
  h = (d = (0, i.__decorate)([(0, o.wxComponent)()], d));
exports.default = h;
