var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  t = require("../../@babel/runtime/helpers/toConsumableArray"),
  r = require("../../@babel/runtime/helpers/asyncToGenerator");
Page({
  data: {
    serviceId: "",
    currentTab: "ordered",
    orderList: [],
    filteredOrderList: [],
    loading: !0,
    isEmpty: !1,
    pagination: {
      current_page: 1,
      page_size: 10,
      total_count: 0,
      total_pages: 0,
      has_next_page: !1,
      has_prev_page: !1,
    },
    tabPagination: {
      ordered: { current_page: 1, has_more: !0 },
      accepted: { current_page: 1, has_more: !0 },
      completed: { current_page: 1, has_more: !0 },
      cancelled: { current_page: 1, has_more: !0 },
    },
    statusMap: {
      ordered: {
        text: "已下单",
        theme: "primary",
        showActions: ["contact", "accept"],
      },
      accepted: {
        text: "已接单",
        theme: "warning",
        showActions: ["contact", "complete"],
      },
      completed: { text: "已完成", theme: "success", showActions: ["contact"] },
      cancelled: { text: "已取消", theme: "default", showActions: [] },
    },
  },
  onLoad: function (e) {
    console.log("Service Order List Page Loaded", e),
      e.serviceId
        ? (this.setData({ serviceId: e.serviceId }), this.loadOrderList())
        : (wx.showToast({ title: "服务ID不存在", icon: "error" }),
          setTimeout(function () {
            wx.navigateBack();
          }, 1500));
  },
  loadOrderList: function () {
    var a = arguments,
      n = this;
    return r(
      e().mark(function r() {
        var o, s, c, i, d, u, l, p, h, g;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  if (
                    ((o = a.length > 0 && void 0 !== a[0] && a[0]),
                    (s = getApp()),
                    o || n.setData({ loading: !0 }),
                    (e.prev = 3),
                    (c = s.globalData.userInfo.data.id))
                  ) {
                    e.next = 7;
                    break;
                  }
                  throw new Error("用户未登录");
                case 7:
                  return (
                    (i = n.data.currentTab),
                    (d = o ? n.data.tabPagination[i].current_page + 1 : 1),
                    (u = i),
                    (e.next = 12),
                    s.call({
                      path: "/api/mp/serviceOrder/service/".concat(
                        n.data.serviceId
                      ),
                      method: "GET",
                      data: {
                        user_id: c,
                        order_status: u,
                        page: d,
                        page_size: 10,
                      },
                    })
                  );
                case 12:
                  if (!(l = e.sent) || 0 !== l.code) {
                    e.next = 22;
                    break;
                  }
                  (p = l.data.orders || []),
                    (h = l.data.pagination || {
                      current_page: 1,
                      page_size: 10,
                      total_count: 0,
                      total_pages: 0,
                      has_next_page: !1,
                      has_prev_page: !1,
                    }),
                    console.log("订单列表获取成功:", p),
                    ((g = n.data.tabPagination)[i] = {
                      current_page: h.current_page,
                      has_more: h.has_next_page,
                    }),
                    o
                      ? n.setData({
                          orderList: [].concat(t(n.data.orderList), t(p)),
                          filteredOrderList: [].concat(
                            t(n.data.filteredOrderList),
                            t(p)
                          ),
                          pagination: h,
                          tabPagination: g,
                          loading: !1,
                        })
                      : n.setData({
                          orderList: p,
                          filteredOrderList: p,
                          pagination: h,
                          tabPagination: g,
                          loading: !1,
                          isEmpty: 0 === p.length,
                        }),
                    (e.next = 23);
                  break;
                case 22:
                  throw new Error(l.message || "获取订单列表失败");
                case 23:
                  e.next = 30;
                  break;
                case 25:
                  (e.prev = 25),
                    (e.t0 = e.catch(3)),
                    console.error("获取订单列表失败：", e.t0),
                    o || n.setData({ loading: !1, isEmpty: !0 }),
                    wx.showToast({
                      title: e.t0.message || "加载失败",
                      icon: "error",
                    });
                case 30:
                case "end":
                  return e.stop();
              }
          },
          r,
          null,
          [[3, 25]]
        );
      })
    )();
  },
  onTabsChange: function (e) {
    var t = "ordered";
    switch (e.detail.value) {
      case "0":
        t = "ordered";
        break;
      case "1":
        t = "accepted";
        break;
      case "2":
        t = "completed";
        break;
      case "3":
        t = "cancelled";
    }
    this.setData({ currentTab: t }),
      this.loadOrderList(),
      console.log("Change tab, current tab is ".concat(t));
  },
  onTabsClick: function (e) {
    console.log("Click tab, tab-panel value is ".concat(e.detail.value));
  },
  filterOrdersByTab: function (e) {
    var t = this.data.orderList,
      r = [];
    switch (e) {
      case "all":
        r = t;
        break;
      case "ongoing":
        r = t.filter(function (e) {
          return "ordered" === e.order_status || "accepted" === e.order_status;
        });
        break;
      case "completed":
        r = t.filter(function (e) {
          return (
            "completed" === e.order_status || "cancelled" === e.order_status
          );
        });
    }
    this.setData({ filteredOrderList: r, isEmpty: 0 === r.length });
  },
  getOrderStatusInfo: function (e) {
    return (
      this.data.statusMap[e] || {
        text: "未知状态",
        theme: "default",
        showActions: [],
      }
    );
  },
  onContactCustomer: function (e) {
    var t = e.currentTarget.dataset.phone;
    t
      ? wx.makePhoneCall({
          phoneNumber: t,
          fail: function (e) {
            console.error("拨打电话失败：", e),
              wx.showToast({ title: "拨打失败", icon: "error" });
          },
        })
      : wx.showToast({ title: "客户电话不存在", icon: "none" });
  },
  onAcceptOrder: function (t) {
    var a = this;
    return r(
      e().mark(function r() {
        var n, o, s, c;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  if (
                    ((n = t.currentTarget.dataset.orderId),
                    (o = getApp()),
                    (e.prev = 2),
                    wx.showLoading({ title: "处理中..." }),
                    (s = o.getUserData()),
                    console.log("当前用户ID：", s),
                    s && s.data.id)
                  ) {
                    e.next = 8;
                    break;
                  }
                  throw new Error("无法获取用户信息");
                case 8:
                  return (
                    (e.next = 10),
                    o.call({
                      path: "/api/mp/serviceOrder/".concat(n, "/accept"),
                      method: "PUT",
                      data: { user_id: s.data.id },
                    })
                  );
                case 10:
                  if (!(c = e.sent) || 0 !== c.code) {
                    e.next = 17;
                    break;
                  }
                  return (
                    wx.showToast({ title: "接单成功", icon: "success" }),
                    (e.next = 15),
                    a.loadOrderList()
                  );
                case 15:
                  e.next = 18;
                  break;
                case 17:
                  throw new Error(c.message || "接单失败");
                case 18:
                  e.next = 24;
                  break;
                case 20:
                  (e.prev = 20),
                    (e.t0 = e.catch(2)),
                    console.error("接单失败：", e.t0),
                    wx.showToast({
                      title: e.t0.message || "接单失败",
                      icon: "error",
                    });
                case 24:
                  return (e.prev = 24), wx.hideLoading(), e.finish(24);
                case 27:
                case "end":
                  return e.stop();
              }
          },
          r,
          null,
          [[2, 20, 24, 27]]
        );
      })
    )();
  },
  onCompleteOrder: function (t) {
    var a = this;
    return r(
      e().mark(function n() {
        var o, s;
        return e().wrap(function (n) {
          for (;;)
            switch ((n.prev = n.next)) {
              case 0:
                (o = t.currentTarget.dataset.orderId),
                  (s = getApp()),
                  wx.showModal({
                    title: "确认完成",
                    content: "",
                    editable: !0,
                    placeholderText: "请填写备注信息",
                    success: (function () {
                      var t = r(
                        e().mark(function t(r) {
                          var n, c;
                          return e().wrap(
                            function (e) {
                              for (;;)
                                switch ((e.prev = e.next)) {
                                  case 0:
                                    if (!r.confirm) {
                                      e.next = 25;
                                      break;
                                    }
                                    if (
                                      ((e.prev = 1),
                                      wx.showLoading({ title: "处理中..." }),
                                      (n = s.getUserData()) && n.data.id)
                                    ) {
                                      e.next = 6;
                                      break;
                                    }
                                    throw new Error("无法获取用户信息");
                                  case 6:
                                    return (
                                      (e.next = 8),
                                      s.call({
                                        path: "/api/mp/serviceOrder/".concat(
                                          o,
                                          "/complete"
                                        ),
                                        method: "PUT",
                                        data: {
                                          user_id: n.data.id,
                                          completion_note: r.content || "",
                                        },
                                      })
                                    );
                                  case 8:
                                    if (!(c = e.sent) || 0 !== c.code) {
                                      e.next = 15;
                                      break;
                                    }
                                    return (
                                      wx.showToast({
                                        title: "订单已完成",
                                        icon: "success",
                                      }),
                                      (e.next = 13),
                                      a.loadOrderList()
                                    );
                                  case 13:
                                    e.next = 16;
                                    break;
                                  case 15:
                                    throw new Error(c.message || "操作失败");
                                  case 16:
                                    e.next = 22;
                                    break;
                                  case 18:
                                    (e.prev = 18),
                                      (e.t0 = e.catch(1)),
                                      console.error("完成订单失败：", e.t0),
                                      wx.showToast({
                                        title: e.t0.message || "操作失败",
                                        icon: "error",
                                      });
                                  case 22:
                                    return (
                                      (e.prev = 22),
                                      wx.hideLoading(),
                                      e.finish(22)
                                    );
                                  case 25:
                                  case "end":
                                    return e.stop();
                                }
                            },
                            t,
                            null,
                            [[1, 18, 22, 25]]
                          );
                        })
                      );
                      return function (e) {
                        return t.apply(this, arguments);
                      };
                    })(),
                  });
              case 3:
              case "end":
                return n.stop();
            }
        }, n);
      })
    )();
  },
  onCancelOrder: function (t) {
    var a = this;
    return r(
      e().mark(function n() {
        var o, s;
        return e().wrap(function (n) {
          for (;;)
            switch ((n.prev = n.next)) {
              case 0:
                (o = t.currentTarget.dataset.orderId),
                  (s = getApp()),
                  wx.showModal({
                    title: "确认取消",
                    content: "",
                    editable: !0,
                    placeholderText: "请输入取消原因（选填）",
                    success: (function () {
                      var t = r(
                        e().mark(function t(r) {
                          var n, c;
                          return e().wrap(
                            function (e) {
                              for (;;)
                                switch ((e.prev = e.next)) {
                                  case 0:
                                    if (!r.confirm) {
                                      e.next = 25;
                                      break;
                                    }
                                    if (
                                      ((e.prev = 1),
                                      wx.showLoading({ title: "处理中..." }),
                                      (n = s.getUserData()) && n.data.id)
                                    ) {
                                      e.next = 6;
                                      break;
                                    }
                                    throw new Error("无法获取用户信息");
                                  case 6:
                                    return (
                                      (e.next = 8),
                                      s.call({
                                        path: "/api/mp/serviceOrder/".concat(
                                          o,
                                          "/cancel"
                                        ),
                                        method: "PUT",
                                        data: {
                                          user_id: n.data.id,
                                          cancel_reason: r.content || "",
                                        },
                                      })
                                    );
                                  case 8:
                                    if (!(c = e.sent) || 0 !== c.code) {
                                      e.next = 15;
                                      break;
                                    }
                                    return (
                                      wx.showToast({
                                        title: "订单已取消",
                                        icon: "success",
                                      }),
                                      (e.next = 13),
                                      a.loadOrderList()
                                    );
                                  case 13:
                                    e.next = 16;
                                    break;
                                  case 15:
                                    throw new Error(c.message || "取消失败");
                                  case 16:
                                    e.next = 22;
                                    break;
                                  case 18:
                                    (e.prev = 18),
                                      (e.t0 = e.catch(1)),
                                      console.error("取消订单失败：", e.t0),
                                      wx.showToast({
                                        title: e.t0.message || "取消失败",
                                        icon: "error",
                                      });
                                  case 22:
                                    return (
                                      (e.prev = 22),
                                      wx.hideLoading(),
                                      e.finish(22)
                                    );
                                  case 25:
                                  case "end":
                                    return e.stop();
                                }
                            },
                            t,
                            null,
                            [[1, 18, 22, 25]]
                          );
                        })
                      );
                      return function (e) {
                        return t.apply(this, arguments);
                      };
                    })(),
                  });
              case 3:
              case "end":
                return n.stop();
            }
        }, n);
      })
    )();
  },
  updateOrderStatus: function (t, a, n) {
    var o = this;
    return r(
      e().mark(function r() {
        var s, c;
        return e().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  return (
                    (s = getApp()),
                    (e.prev = 1),
                    wx.showLoading({ title: "处理中..." }),
                    (e.next = 5),
                    s.call({
                      path: "/api/mp/serviceOrder/".concat(t, "/status"),
                      method: "PUT",
                      data: { order_status: a },
                    })
                  );
                case 5:
                  if (!(c = e.sent) || 0 !== c.code) {
                    e.next = 12;
                    break;
                  }
                  return (
                    wx.showToast({ title: n, icon: "success" }),
                    (e.next = 10),
                    o.loadOrderList()
                  );
                case 10:
                  e.next = 13;
                  break;
                case 12:
                  throw new Error(c.message || "操作失败");
                case 13:
                  e.next = 19;
                  break;
                case 15:
                  (e.prev = 15),
                    (e.t0 = e.catch(1)),
                    console.error("更新订单状态失败：", e.t0),
                    wx.showToast({
                      title: e.t0.message || "操作失败",
                      icon: "error",
                    });
                case 19:
                  return (e.prev = 19), wx.hideLoading(), e.finish(19);
                case 22:
                case "end":
                  return e.stop();
              }
          },
          r,
          null,
          [[1, 15, 19, 22]]
        );
      })
    )();
  },
  formatTime: function (e) {
    if (!e) return "";
    var t = new Date(e),
      r = t.getFullYear(),
      a = String(t.getMonth() + 1).padStart(2, "0"),
      n = String(t.getDate()).padStart(2, "0"),
      o = String(t.getHours()).padStart(2, "0"),
      s = String(t.getMinutes()).padStart(2, "0");
    return ""
      .concat(r, "-")
      .concat(a, "-")
      .concat(n, " ")
      .concat(o, ":")
      .concat(s);
  },
  onPullDownRefresh: function () {
    this.loadOrderList().finally(function () {
      wx.stopPullDownRefresh();
    });
  },
  onReachBottom: function () {
    var e = this.data.currentTab,
      t = this.data.tabPagination[e];
    t && t.has_more
      ? this.loadOrderList(!0)
      : wx.showToast({ title: "没有更多数据了", icon: "none" });
  },
  onShow: function () {
    this.data.serviceId && this.loadOrderList();
  },
});
