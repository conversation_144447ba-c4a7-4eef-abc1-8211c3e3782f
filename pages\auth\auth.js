var t,
  e = require("../../@babel/runtime/helpers/defineProperty"),
  a = require("../../@babel/runtime/helpers/regeneratorRuntime"),
  n = require("../../@babel/runtime/helpers/asyncToGenerator");
Page(
  (e(
    (t = {
      data: {
        formData: { phone: "", building: "", unit: "", roomNumber: "" },
        isSubmitting: !1,
        showBackButton: !0,
        showCancelButton: !0,
        showSkipButton: !1,
        fromPage: "",
      },
      onBuildingInput: function (t) {
        var e = t.detail.value.replace(/\D/g, "");
        this.setData({ "formData.building": e });
      },
      onUnitInput: function (t) {
        var e = t.detail.value.replace(/\D/g, "");
        this.setData({ "formData.unit": e });
      },
      onRoomNumberInput: function (t) {
        var e = t.detail.value.replace(/\D/g, "");
        this.setData({ "formData.roomNumber": e });
      },
      onLoad: function (t) {
        var e = t.from || "";
        console.log("认证页面来源:", e),
          this.setData({ fromPage: e }),
          "login" === e &&
            this.setData({
              showBackButton: !1,
              showCancelButton: !1,
              showSkipButton: !0,
            });
      },
      onPhoneInput: function (t) {
        this.setData({ "formData.phone": t.detail.value });
      },
      onBuildingChange: function (t) {
        this.setData({ "formData.buildingIndex": t.detail.value });
      },
      onUnitChange: function (t) {
        this.setData({ "formData.unitIndex": t.detail.value });
      },
    }),
    "onRoomNumberInput",
    function (t) {
      this.setData({ "formData.roomNumber": t.detail.value });
    }
  ),
  e(t, "validateForm", function () {
    var t = this.data.formData,
      e = t.phone,
      a = t.building,
      n = t.unit,
      o = t.roomNumber;
    return e
      ? /^1\d{10}$/.test(e)
        ? a
          ? n
            ? !!o ||
              (wx.showToast({ title: "请填写门牌号", icon: "error" }), !1)
            : (wx.showToast({ title: "请输入单元号", icon: "error" }), !1)
          : (wx.showToast({ title: "请输入楼栋号", icon: "error" }), !1)
        : (wx.showToast({ title: "手机号格式不正确", icon: "error" }), !1)
      : (wx.showToast({ title: "请填写手机号", icon: "error" }), !1);
  }),
  e(t, "onSubmit", function () {
    var t = this;
    return n(
      a().mark(function e() {
        var n, o, r, i, s, u, l;
        return a().wrap(
          function (e) {
            for (;;)
              switch ((e.prev = e.next)) {
                case 0:
                  if (!t.data.isSubmitting) {
                    e.next = 2;
                    break;
                  }
                  return e.abrupt("return");
                case 2:
                  if (t.validateForm()) {
                    e.next = 4;
                    break;
                  }
                  return e.abrupt("return");
                case 4:
                  if (
                    (t.setData({ isSubmitting: !0 }),
                    wx.showLoading({ title: "提交中..." }),
                    (e.prev = 6),
                    (n = getApp()),
                    (o = t.data.formData),
                    (r = o.phone),
                    (i = o.building),
                    (s = o.unit),
                    (u = o.roomNumber),
                    n.globalData.openid)
                  ) {
                    e.next = 11;
                    break;
                  }
                  throw new Error("未获取到用户openid");
                case 11:
                  return (
                    (e.next = 13),
                    n.call({
                      path: "/api/mp/auth/bind-address",
                      method: "POST",
                      data: {
                        openid: n.globalData.openid,
                        phone: r,
                        building: i + "栋",
                        unit: s + "单元",
                        room: u,
                      },
                    })
                  );
                case 13:
                  if (0 !== (l = e.sent).code) {
                    e.next = 21;
                    break;
                  }
                  return (
                    wx.showToast({ title: "提交成功", icon: "success" }),
                    (e.next = 18),
                    n.refreshUserInfo()
                  );
                case 18:
                  setTimeout(function () {
                    "login" === t.data.fromPage
                      ? wx.switchTab({ url: "/pages/index/index" })
                      : wx.navigateBack();
                  }, 1500),
                    (e.next = 22);
                  break;
                case 21:
                  throw new Error(l.message || "提交失败");
                case 22:
                  e.next = 28;
                  break;
                case 24:
                  (e.prev = 24),
                    (e.t0 = e.catch(6)),
                    console.error("绑定地址失败:", e.t0),
                    wx.showToast({
                      title: e.t0.message || "提交失败",
                      icon: "error",
                    });
                case 28:
                  return (
                    (e.prev = 28),
                    t.setData({ isSubmitting: !1 }),
                    wx.hideLoading(),
                    e.finish(28)
                  );
                case 32:
                case "end":
                  return e.stop();
              }
          },
          e,
          null,
          [[6, 24, 28, 32]]
        );
      })
    )();
  }),
  e(t, "onCancel", function () {
    wx.navigateBack();
  }),
  e(t, "onSkip", function () {
    wx.switchTab({ url: "/pages/index/index" });
  }),
  t)
);
