@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-stepper {
  align-items: center;
  color: var(
    --td-stepper-input-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-flex;
  display: flex;
}
.t-stepper__input {
  height: inherit;
  margin: 0 8rpx;
  min-height: inherit;
  text-align: center;
  vertical-align: top;
}
.t-stepper__minus,
.t-stepper__plus {
  box-sizing: border-box;
  padding: 8rpx;
}
.t-stepper__input,
.t-stepper__minus-icon,
.t-stepper__plus-icon {
  color: inherit;
}
.t-stepper__input--filled,
.t-stepper__input--normal,
.t-stepper__input--outline {
  box-sizing: border-box;
  height: inherit;
}
.t-stepper--small {
  font-size: 20rpx;
  height: 40rpx;
}
.t-stepper--medium {
  font-size: 24rpx;
  height: 48rpx;
}
.t-stepper--large {
  font-size: 32rpx;
  height: 56rpx;
}
.t-stepper__input--small {
  width: 68rpx;
}
.t-stepper__input--medium {
  height: 48rpx;
  width: 76rpx;
}
.t-stepper__input--large {
  width: 90rpx;
}
.t-stepper__icon--small {
  font-size: 24rpx;
  height: 40rpx;
  width: 40rpx;
}
.t-stepper__icon--medium {
  font-size: 32rpx;
  height: 48rpx;
  width: 48rpx;
}
.t-stepper__icon--large {
  font-size: 40rpx;
  height: 56rpx;
  width: 56rpx;
}
.t-stepper__minus--outline,
.t-stepper__plus--outline {
  border: 2rpx solid
    var(
      --td-stepper-border-color,
      var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
    );
}
.t-stepper__input--outline {
  border: none;
  border-bottom: 2rpx solid
    var(
      --td-stepper-border-color,
      var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
    );
  border-top: 2rpx solid
    var(
      --td-stepper-border-color,
      var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
    );
}
.t-stepper__minus--filled,
.t-stepper__minus--outline {
  border-radius: 0;
  border-bottom-left-radius: var(
    --td-stepper-border-radius,
    var(--td-radius-small, 6rpx)
  );
  border-top-left-radius: var(
    --td-stepper-border-radius,
    var(--td-radius-small, 6rpx)
  );
}
.t-stepper__plus--filled,
.t-stepper__plus--outline {
  border-radius: 0;
  border-bottom-right-radius: var(
    --td-stepper-border-radius,
    var(--td-radius-small, 6rpx)
  );
  border-top-right-radius: var(
    --td-stepper-border-radius,
    var(--td-radius-small, 6rpx)
  );
}
.t-stepper__input--filled,
.t-stepper__minus--filled,
.t-stepper__plus--filled {
  background-color: var(
    --td-bg-color-secondarycontainer,
    var(--td-gray-color-1, #f3f3f3)
  );
}
.t-stepper__input--filled {
  margin: 0 8rpx;
}
.t-stepper__input--filled .t-stepper__input {
  margin: 0;
}
.t-stepper--filled-disabled,
.t-stepper--normal-disabled,
.t-stepper--outline-disabled {
  color: var(
    --td-stepper-input-disabled-color,
    var(--td-text-color-disabled, var(--td-font-gray-4, rgba(0, 0, 0, 0.26)))
  );
}
.t-stepper--filled-disabled,
.t-stepper--outline-disabled {
  background-color: var(
    --td-stepper-input-disabled-bg,
    var(--td-bg-color-component-disabled, var(--td-gray-color-2, #eee))
  );
}
