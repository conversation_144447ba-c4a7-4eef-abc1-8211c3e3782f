@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-steps-item {
  display: -webkit-flex;
  display: flex;
  flex: 1;
  position: relative;
  vertical-align: top;
}
.t-steps-item__circle--default {
  background-color: var(
    --td-step-item-default-circle-bg,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  color: var(
    --td-step-item-default-circle-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
}
.t-steps-item__title--default {
  color: var(
    --td-step-item-default-title-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
}
.t-steps-item__icon--default {
  color: var(
    --td-step-item-default-icon-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
}
.t-steps-item__dot--default {
  background-color: var(
    --td-step-item-default-dot-border-color,
    var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
  );
  border-color: var(
    --td-step-item-default-dot-border-color,
    var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
  );
}
.t-steps-item__circle--process {
  background-color: var(
    --td-step-item-process-circle-bg,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  color: var(
    --td-step-item-process-circle-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
}
.t-steps-item__title--process {
  color: var(
    --td-step-item-process-title-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-steps-item__icon--process {
  color: var(
    --td-step-item-process-icon-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-steps-item__dot--process {
  background-color: var(
    --td-step-item-process-dot-border-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-color: var(
    --td-step-item-process-dot-border-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-steps-item__circle--finish {
  background-color: var(
    --td-step-item-finish-circle-bg,
    var(--td-brand-color-light, var(--td-primary-color-1, #f2f3ff))
  );
  color: var(
    --td-step-item-finish-circle-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-steps-item__title--finish {
  color: var(
    --td-step-item-finish-title-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
}
.t-steps-item__icon--finish {
  color: var(
    --td-step-item-finish-icon-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-steps-item__dot--finish {
  background-color: var(
    --td-step-item-finish-dot-border-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-color: var(
    --td-step-item-finish-dot-border-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-steps-item__circle--error {
  background-color: var(
    --td-step-item-error-circle-bg,
    var(--td-error-color-1, #fff0ed)
  );
  color: var(
    --td-step-item-error-circle-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
}
.t-steps-item__title--error {
  color: var(
    --td-step-item-error-title-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
}
.t-steps-item__icon--error {
  color: var(
    --td-step-item-error-icon-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
}
.t-steps-item__dot--error {
  background-color: var(
    --td-step-item-error-dot-border-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
  border-color: var(
    --td-step-item-error-dot-border-color,
    var(--td-error-color, var(--td-error-color-6, #d54941))
  );
}
.t-steps-item--horizontal {
  flex-direction: column;
}
.t-steps-item--horizontal,
.t-steps-item__anchor {
  align-items: center;
  justify-content: center;
}
.t-steps-item__anchor {
  display: -webkit-flex;
  display: flex;
}
.t-steps-item__anchor--vertical,
.t-steps-item__circle {
  height: var(--td-step-item-circle-size, 44rpx);
  width: var(--td-step-item-circle-size, 44rpx);
}
.t-steps-item__circle {
  align-items: center;
  border-radius: 50%;
  display: -webkit-flex;
  display: flex;
  font-size: var(--td-step-item-circle-font-size, 28rpx);
  justify-content: center;
  text-align: center;
}
.t-steps-item__icon {
  font-size: var(--td-font-size-base, 28rpx);
  position: relative;
  vertical-align: top;
  z-index: 1;
}
.t-steps-item__dot {
  border-radius: 50%;
  border-style: solid;
  border-width: 1px;
  box-sizing: border-box;
  height: var(--td-step-item-dot-size, 16rpx);
  width: var(--td-step-item-dot-size, 16rpx);
}
.t-steps-item__content {
  text-align: center;
}
.t-steps-item__content--horizontal {
  margin-top: 16rpx;
  max-width: 80px;
}
.t-steps-item__content--vertical {
  flex: 1;
  margin-left: 16rpx;
  padding-bottom: 32rpx;
}
.t-steps-item__content--vertical.t-steps-item__content--last {
  padding-bottom: 0;
}
.t-steps-item__title {
  font-size: var(--td-font-size-base, 28rpx);
  line-height: var(--td-step-item-circle-size, 44rpx);
  position: relative;
}
.t-steps-item__title--process {
  font-weight: 600;
}
.t-steps-item__title--vertical {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
  text-align: left;
}
.t-steps-item__description {
  color: var(
    --td-step-item-description-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  font-size: var(--td-font-size-s, 24rpx);
  line-height: 40rpx;
}
.t-steps-item__description--vertical {
  text-align: left;
}
.t-steps-item__extra:not(:empty) {
  margin-top: 16rpx;
}
.t-steps-item__line {
  background-color: var(
    --td-step-item-line-color,
    var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
  );
  content: "";
  display: block;
  position: absolute;
}
.t-steps-item__line--horizontal {
  height: 1px;
  left: calc(50% + var(--td-step-item-circle-size, 44rpx) / 2 + 16rpx);
  top: calc(var(--td-step-item-circle-size, 44rpx) / 2 + 1px);
  transform: translateY(-50%);
  width: calc(100% - 32rpx - var(--td-step-item-circle-size, 44rpx));
}
.t-steps-item__line--horizontal.t-steps-item__line--dot {
  top: calc(var(--td-step-item-dot-size, 16rpx) / 2);
}
.t-steps-item__line--finish,
.t-steps-item__line--reverse.t-steps-item__line--process {
  background-color: var(
    --td-step-item-finish-line-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-steps-item__line--vertical {
  height: calc(100% - 32rpx - var(--td-step-item-circle-size, 44rpx));
  left: calc(var(--td-step-item-circle-size, 44rpx) / 2);
  top: calc(var(--td-step-item-circle-size, 44rpx) + 16rpx);
  transform: translateX(-50%);
  width: 1px;
}
.t-steps-item__line--vertical.t-steps-item__line--dot {
  height: calc(100% - var(--td-step-item-circle-size, 44rpx));
  top: var(--td-step-item-circle-size, 44rpx);
}
