__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/loading/loading": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            show: new Array(1),
            style: new Array(1),
            layout: new Array(2),
            fullscreen: new Array(1),
            customStyle: new Array(1),
          },
          K = U === true,
          d,
          g = (C, h, i, j, k, l, T, E, B) => {
            var m,
              o = (C) => {},
              n = (C, T, E) => {
                if (m === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (
                        C ||
                        K ||
                        !!(U.classPrefix || U.classPrefix || k) ||
                        undefined
                      )
                        L(
                          N,
                          Y(D.classPrefix) +
                            "__dot " +
                            Y(D.classPrefix) +
                            "__dot-" +
                            Y(i)
                        );
                    },
                    o
                  );
                }
              };
            m = D.theme === "spinner" ? 1 : 0;
            B(m, n);
          },
          h,
          j = (C) => {},
          i = (C, T, E) => {
            if (h === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__circular");
                },
                j
              );
            }
          },
          k,
          m = (C) => {},
          n = (C) => {},
          o = (C) => {},
          l = (C, T, E) => {
            if (k === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__dot");
                  var $A = D.duration;
                  var $B = D.pause;
                  if (
                    C ||
                    K ||
                    !!(
                      U.duration ||
                      ($A ? !!U.duration || undefined : undefined) ||
                      U.pause ||
                      ($B ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      Y(
                        $A
                          ? "animation-duration: " +
                              D.duration / 1000 +
                              "s; animation-delay:" +
                              0 +
                              "s;"
                          : ""
                      ) +
                        "animation-play-state:" +
                        Y($B ? "paused" : "running") +
                        ";"
                    );
                },
                m
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__dot");
                  var $A = D.duration;
                  var $B = D.pause;
                  if (
                    C ||
                    K ||
                    !!(
                      U.duration ||
                      ($A
                        ? !!(U.duration || U.duration) || undefined
                        : undefined) ||
                      U.pause ||
                      ($B ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      Y(
                        $A
                          ? "animation-duration: " +
                              D.duration / 1000 +
                              "s; animation-delay:" +
                              (D.duration * 1) / 3000 +
                              "s;"
                          : ""
                      ) +
                        "animation-play-state:" +
                        Y($B ? "paused" : "running") +
                        ";"
                    );
                },
                n
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__dot");
                  var $A = D.duration;
                  var $B = D.pause;
                  if (
                    C ||
                    K ||
                    !!(
                      U.duration ||
                      ($A
                        ? !!(U.duration || U.duration) || undefined
                        : undefined) ||
                      U.pause ||
                      ($B ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      Y(
                        $A
                          ? "animation-duration: " +
                              D.duration / 1000 +
                              "s; animation-delay:" +
                              (D.duration * 2) / 3000 +
                              "s;"
                          : ""
                      ) +
                        "animation-play-state:" +
                        Y($B ? "paused" : "running") +
                        ";"
                    );
                },
                o
              );
            }
          },
          f = (C, T, E, B, F, S) => {
            F(12, "index", U ? undefined : undefined, null, g);
            h = D.theme === "circular" ? 1 : 0;
            B(h, i);
            k = D.theme === "dots" ? 1 : 0;
            B(k, l);
            S("indicator");
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.reverse;
                  if (
                    C ||
                    K ||
                    !!(
                      U.prefix ||
                      U.classPrefix ||
                      U.classPrefix ||
                      U.theme ||
                      U.reverse ||
                      ($A ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.prefix) +
                        "-class-indicator " +
                        Y(D.classPrefix) +
                        "__spinner " +
                        Y(D.classPrefix) +
                        "__spinner--" +
                        Y(D.theme) +
                        " " +
                        Y($A ? "reverse" : "")
                    );
                  var $B = D.inheritColor;
                  var $C = D.indicator;
                  var $D = D.duration;
                  var $E = D.pause;
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "addUnit") ||
                      U.size ||
                      Z(undefined, "addUnit") ||
                      U.size ||
                      U.inheritColor ||
                      ($B ? undefined : undefined) ||
                      U.indicator ||
                      ($C ? undefined : undefined) ||
                      U.duration ||
                      ($D ? !!U.duration || undefined : undefined) ||
                      U.pause ||
                      ($E ? undefined : undefined)
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      "width:" +
                        Y(P(X(a).addUnit)(D.size)) +
                        ";height:" +
                        Y(P(X(a).addUnit)(D.size)) +
                        ";" +
                        Y($B ? "color: inherit;" : "") +
                        " " +
                        Y($C ? "" : "display: none;") +
                        " " +
                        Y(
                          $D
                            ? "animation-duration: " + D.duration / 1000 + "s;"
                            : ""
                        ) +
                        " animation-play-state: " +
                        Y($E ? "paused" : "running") +
                        ";"
                    );
                  if (C || K || !!U.ariaRole || undefined)
                    O(N, "aria-role", D.ariaRole || "img");
                  if (C || K || !!(U.ariaLabel || U.text) || undefined)
                    O(N, "aria-label", D.ariaLabel || D.text || "加载中");
                },
                f
              );
            }
          },
          q,
          r = (C, T) => {
            if (q === 1) {
              C || K || U.text ? T(Y(D.text)) : T();
            }
          },
          p = (C, T, E, B, F, S) => {
            q = D.text ? 1 : 0;
            B(q, r);
            S("text");
            S("");
          },
          c = (C, T, E, B) => {
            d = D.indicator ? 1 : 0;
            B(d, e);
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__text", [D.layout])) +
                      " " +
                      Y(D.prefix) +
                      "-class-text"
                  );
                A["layout"][1] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__text", [D.layout])) +
                      " " +
                      Y(D.prefix) +
                      "-class-text"
                  );
                };
                if (C || K || U.indicator) O(N, "aria-hidden", D.indicator);
                if (C || K || !!(U.ariaLabel || U.text) || undefined)
                  O(N, "aria-label", D.ariaLabel || D.text);
              },
              p
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.fullscreen;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    U.classPrefix ||
                    U.classPrefix ||
                    U.layout ||
                    U.fullscreen ||
                    ($A ? !!U.classPrefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix + "--" + D.layout) +
                      " " +
                      Y($A ? D.classPrefix + "--fullscreen" : "")
                  );
                A["layout"][0] = A["fullscreen"][0] = (D, E, T) => {
                  var $B = D.fullscreen;
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix + "--" + D.layout) +
                      " " +
                      Y($B ? D.classPrefix + "--fullscreen" : "")
                  );
                };
                var $B = D.show;
                var $C = D.inheritColor;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([
                      U.style,
                      U.customStyle,
                      !!U.show || ($B ? undefined : undefined),
                      !!U.inheritColor || ($C ? undefined : undefined),
                    ])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([
                      D.style,
                      D.customStyle,
                      $B ? "" : "display: none",
                      $C ? "color: inherit" : "",
                    ])
                  );
                A["style"][0] =
                  A["customStyle"][0] =
                  A["show"][0] =
                    (D, E, T) => {
                      var $D = D.show;
                      var $E = D.inheritColor;
                      R.y(
                        N,
                        P(X(a)._style)([
                          D.style,
                          D.customStyle,
                          $D ? "" : "display: none",
                          $E ? "color: inherit" : "",
                        ])
                      );
                    };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/loading/loading.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-loading{display:-webkit-inline-flex;display:inline-flex;font-size:",
        [0, 24],
        "}\n.",
        [1],
        "t-loading,.",
        [1],
        "t-loading--fullscreen{-webkit-align-items:center;align-items:center;-webkit-justify-content:center;justify-content:center}\n.",
        [1],
        "t-loading--fullscreen{background-color:var(--td-loading-full-bg-color,hsla(0,0%,100%,.6));display:-webkit-flex;display:flex;height:100%;left:0;position:fixed;top:0;vertical-align:middle;width:100%;z-index:var(--td-loading-z-index,3500)}\n.",
        [1],
        "t-loading__spinner{-webkit-animation:rotate .8s linear infinite;animation:rotate .8s linear infinite;box-sizing:border-box;color:var(--td-loading-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));height:100%;max-height:100%;max-width:100%;position:relative;width:100%}\n.",
        [1],
        "t-loading__spinner.",
        [1],
        "reverse{-webkit-animation-name:rotateReverse;animation-name:rotateReverse}\n.",
        [1],
        "t-loading__spinner--spinner{-webkit-animation-timing-function:steps(12);animation-timing-function:steps(12);color:var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9)))}\n.",
        [1],
        "t-loading__spinner--spinner .",
        [1],
        "t-loading__dot{height:100%;left:0;position:absolute;top:0;width:100%}\n.",
        [1],
        "t-loading__spinner--spinner .",
        [1],
        "t-loading__dot::before{background-color:var(--td-loading-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:40%;content:\x22 \x22;display:block;height:25%;margin:0 auto;width:",
        [0, 5],
        "}\n.",
        [1],
        "t-loading__spinner--circular .",
        [1],
        "t-loading__circular{background:conic-gradient(from 180deg at 50% 50%,hsla(0,0%,100%,0) 0deg,hsla(0,0%,100%,0) 60deg,currentColor 330deg,hsla(0,0%,100%,0) 1turn);border-radius:100%;height:100%;mask:radial-gradient(transparent calc(50% - ",
        [0, 1],
        "),#fff 50%);-webkit-mask:radial-gradient(transparent calc(50% - ",
        [0, 1],
        "),#fff 50%);width:100%}\n.",
        [1],
        "t-loading__spinner--dots{-webkit-align-items:center;align-items:center;-webkit-animation:none;animation:none;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between}\n.",
        [1],
        "t-loading__spinner--dots .",
        [1],
        "t-loading__dot{-webkit-animation-duration:1.8s;animation-duration:1.8s;-webkit-animation-fill-mode:both;animation-fill-mode:both;-webkit-animation-iteration-count:infinite;animation-iteration-count:infinite;-webkit-animation-name:dotting;animation-name:dotting;-webkit-animation-timing-function:linear;animation-timing-function:linear;background-color:var(--td-loading-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:50%;height:20%;width:20%}\n.",
        [1],
        "t-loading__text{color:var(--td-loading-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-size:var(--td-loading-text-font-size,",
        [0, 24],
        ");line-height:var(--td-loading-text-line-height,",
        [0, 40],
        ")}\n.",
        [1],
        "t-loading__text--vertical:not(:first-child):not(:empty){margin-top:",
        [0, 12],
        "}\n.",
        [1],
        "t-loading__text--horizontal:not(:first-child):not(:empty){margin-left:",
        [0, 16],
        "}\n.",
        [1],
        "t-loading--vertical{-webkit-flex-direction:column;flex-direction:column}\n.",
        [1],
        "t-loading--horizontal{-webkit-flex-direction:row;flex-direction:row;vertical-align:top}\n@-webkit-keyframes t-bar{0%{width:0}\n50%{width:70%}\n100%{width:80%}\n}@keyframes t-bar{0%{width:0}\n50%{width:70%}\n100%{width:80%}\n}@-webkit-keyframes t-bar-loaded{0%{height:",
        [0, 6],
        ";opacity:1;width:90%}\n50%{height:",
        [0, 6],
        ";opacity:1;width:100%}\n100%{height:0;opacity:0;width:100%}\n}@keyframes t-bar-loaded{0%{height:",
        [0, 6],
        ";opacity:1;width:90%}\n50%{height:",
        [0, 6],
        ";opacity:1;width:100%}\n100%{height:0;opacity:0;width:100%}\n}.",
        [1],
        "t-loading__dot-1{opacity:0;-webkit-transform:rotate(30deg);transform:rotate(30deg)}\n.",
        [1],
        "t-loading__dot-2{opacity:.08333333;-webkit-transform:rotate(60deg);transform:rotate(60deg)}\n.",
        [1],
        "t-loading__dot-3{opacity:.16666667;-webkit-transform:rotate(90deg);transform:rotate(90deg)}\n.",
        [1],
        "t-loading__dot-4{opacity:.25;-webkit-transform:rotate(120deg);transform:rotate(120deg)}\n.",
        [1],
        "t-loading__dot-5{opacity:.33333333;-webkit-transform:rotate(150deg);transform:rotate(150deg)}\n.",
        [1],
        "t-loading__dot-6{opacity:.41666667;-webkit-transform:rotate(180deg);transform:rotate(180deg)}\n.",
        [1],
        "t-loading__dot-7{opacity:.5;-webkit-transform:rotate(210deg);transform:rotate(210deg)}\n.",
        [1],
        "t-loading__dot-8{opacity:.58333333;-webkit-transform:rotate(240deg);transform:rotate(240deg)}\n.",
        [1],
        "t-loading__dot-9{opacity:.66666667;-webkit-transform:rotate(270deg);transform:rotate(270deg)}\n.",
        [1],
        "t-loading__dot-10{opacity:.75;-webkit-transform:rotate(300deg);transform:rotate(300deg)}\n.",
        [1],
        "t-loading__dot-11{opacity:.83333333;-webkit-transform:rotate(330deg);transform:rotate(330deg)}\n.",
        [1],
        "t-loading__dot-12{opacity:.91666667;-webkit-transform:rotate(1turn);transform:rotate(1turn)}\n@-webkit-keyframes rotate{from{-webkit-transform:rotate(0);transform:rotate(0)}\nto{-webkit-transform:rotate(1turn);transform:rotate(1turn)}\n}@keyframes rotate{from{-webkit-transform:rotate(0);transform:rotate(0)}\nto{-webkit-transform:rotate(1turn);transform:rotate(1turn)}\n}@-webkit-keyframes rotateReverse{from{-webkit-transform:rotate(1turn);transform:rotate(1turn)}\nto{-webkit-transform:rotate(0);transform:rotate(0)}\n}@keyframes rotateReverse{from{-webkit-transform:rotate(1turn);transform:rotate(1turn)}\nto{-webkit-transform:rotate(0);transform:rotate(0)}\n}@-webkit-keyframes dotting{0%{opacity:.15}\n1%{opacity:.8}\n33%{opacity:.8}\n34%{opacity:.15}\n100%{opacity:.15}\n}@keyframes dotting{0%{opacity:.15}\n1%{opacity:.8}\n33%{opacity:.8}\n34%{opacity:.15}\n100%{opacity:.15}\n}",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/loading/loading.wxss" }
    );
}
