Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  r = require("../../../@babel/runtime/helpers/classCallCheck"),
  t = require("../../../@babel/runtime/helpers/inherits"),
  s = require("../../../@babel/runtime/helpers/createSuper"),
  i = require("tslib"),
  a = require("../common/src/index"),
  n = u(require("./props")),
  o = u(require("../common/config")),
  c = require("../common/utils");
function u(e) {
  return e && e.__esModule ? e : { default: e };
}
var l = o.default.prefix,
  p = "".concat(l, "-empty"),
  m = (function (i) {
    t(o, i);
    var a = s(o);
    function o() {
      var e;
      return (
        r(this, o),
        ((e = a.apply(this, arguments)).options = { multipleSlots: !0 }),
        (e.externalClasses = [
          "".concat(l, "-class"),
          "".concat(l, "-class-description"),
          "".concat(l, "-class-image"),
        ]),
        (e.properties = n.default),
        (e.data = { prefix: l, classPrefix: p }),
        (e.observers = {
          icon: function (e) {
            var r = (0, c.setIcon)("icon", e, "");
            this.setData(Object.assign({}, r));
          },
        }),
        e
      );
    }
    return e(o);
  })(a.SuperComponent),
  f = (m = (0, i.__decorate)([(0, a.wxComponent)()], m));
exports.default = f;
