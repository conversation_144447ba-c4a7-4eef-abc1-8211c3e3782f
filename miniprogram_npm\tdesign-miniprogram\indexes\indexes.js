Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var t = require("../../../@babel/runtime/helpers/createClass"),
  e = require("../../../@babel/runtime/helpers/classCallCheck"),
  i = require("../../../@babel/runtime/helpers/inherits"),
  n = require("../../../@babel/runtime/helpers/createSuper"),
  r = require("tslib"),
  o = require("../common/src/index"),
  s = l(require("../common/config")),
  h = l(require("./props")),
  a = require("../common/utils"),
  c = l(require("../mixins/page-scroll"));
function l(t) {
  return t && t.__esModule ? t : { default: t };
}
var u = s.default.prefix,
  d = "".concat(u, "-indexes"),
  p = (function (r) {
    i(s, r);
    var o = n(s);
    function s() {
      var t;
      return (
        e(this, s),
        ((t = o.apply(this, arguments)).externalClasses = [
          "".concat(u, "-class"),
          "".concat(u, "-class-sidebar"),
          "".concat(u, "-class-sidebar-item"),
        ]),
        (t.properties = h.default),
        (t.data = {
          prefix: u,
          classPrefix: d,
          _height: 0,
          _indexList: [],
          scrollTop: 0,
          activeAnchor: null,
          showTips: !1,
        }),
        (t.relations = {
          "../indexes-anchor/indexes-anchor": { type: "child" },
        }),
        (t.behaviors = [(0, c.default)()]),
        (t.timer = null),
        (t.groupTop = []),
        (t.sidebar = null),
        (t.currentTouchAnchor = null),
        (t.observers = {
          indexList: function (t) {
            this.setIndexList(t), this.setHeight(this.data._height);
          },
          height: function (t) {
            this.setHeight(t);
          },
        }),
        (t.lifetimes = {
          ready: function () {
            (this.timer = null),
              (this.groupTop = []),
              (this.sidebar = null),
              0 === this.data._height && this.setHeight(),
              null === this.data.indexList && this.setIndexList();
          },
        }),
        (t.methods = {
          setHeight: function (t) {
            var e = this;
            t || (t = a.systemInfo.windowHeight);
            this.setData({ _height: t }, function () {
              e.getAllRect();
            });
          },
          setIndexList: function (t) {
            if (t) this.setData({ _indexList: t });
            else {
              for (
                var e = "A".charCodeAt(0), i = [], n = e, r = e + 26;
                n < r;
                n += 1
              )
                i.push(String.fromCharCode(n));
              this.setData({ _indexList: i });
            }
          },
          getAllRect: function () {
            var t = this;
            this.getAnchorsRect().then(function () {
              t.groupTop.forEach(function (e, i) {
                var n = t.groupTop[i + 1];
                e.totalHeight = ((null == n ? void 0 : n.top) || 1 / 0) - e.top;
              }),
                t.setAnchorOnScroll(0);
            }),
              this.getSidebarRect();
          },
          getAnchorsRect: function () {
            var t = this;
            return Promise.all(
              this.$children.map(function (e) {
                return (0, a.getRect)(e, ".".concat(d, "-anchor")).then(
                  function (i) {
                    t.groupTop.push({
                      height: i.height,
                      top: i.top,
                      anchor: e.data.index,
                    });
                  }
                );
              })
            );
          },
          getSidebarRect: function () {
            var t = this;
            (0, a.getRect)(this, "#id-".concat(d, "__bar")).then(function (e) {
              var i = e.top,
                n = e.height,
                r = t.data._indexList.length;
              t.sidebar = {
                top: i,
                height: n,
                itemHeight: (n - 2 * (r - 1)) / r,
              };
            });
          },
          toggleTips: function (t) {
            var e = this;
            t
              ? this.setData({ showTips: !0 })
              : (clearInterval(this.timer),
                (this.timer = setTimeout(function () {
                  e.setData({ showTips: !1 });
                }, 300)));
          },
          setAnchorByIndex: function (t) {
            var e = this.data,
              i = e._indexList,
              n = e.stickyOffset,
              r = i[t];
            if (
              null === this.data.activeAnchor ||
              this.data.activeAnchor !== r
            ) {
              var o = this.groupTop.find(function (t) {
                return t.anchor === r;
              });
              if (o) {
                this.currentTouchAnchor = r;
                var s = o.top - n;
                wx.pageScrollTo({ scrollTop: s, duration: 0 }),
                  this.toggleTips(!0),
                  this.triggerEvent("select", { index: r }),
                  this.setData({ activeAnchor: r });
              }
            }
          },
          onClick: function (t) {
            var e = t.currentTarget.dataset.index;
            this.setAnchorByIndex(e);
          },
          onTouchMove: function (t) {
            this.onAnchorTouch(t);
          },
          onTouchCancel: function () {
            this.toggleTips(!1);
          },
          onTouchEnd: function (t) {
            this.toggleTips(!1), this.onAnchorTouch(t);
          },
          onAnchorTouch: (0, a.throttle)(function (t) {
            var e = this,
              i = (function (t) {
                var i = t - e.sidebar.top;
                return i <= 0
                  ? 0
                  : i > e.sidebar.height
                  ? e.data._indexList.length - 1
                  : Math.floor(i / e.sidebar.itemHeight);
              })(t.changedTouches[0].clientY);
            this.setAnchorByIndex(i);
          }, 1e3 / 30),
          setAnchorOnScroll: function (t) {
            if (this.groupTop) {
              var e = this.data,
                i = e.sticky,
                n = e.stickyOffset,
                r = e.activeAnchor;
              t += n;
              var o = this.groupTop.findIndex(function (e) {
                return (
                  t >= e.top - e.height && t <= e.top + e.totalHeight - e.height
                );
              });
              if (-1 !== o) {
                var s = this.groupTop[o];
                if (
                  (null !== this.currentTouchAnchor
                    ? (this.triggerEvent("change", { index: s.anchor }),
                      (this.currentTouchAnchor = null))
                    : r !== s.anchor &&
                      (this.triggerEvent("change", { index: s.anchor }),
                      this.setData({ activeAnchor: s.anchor })),
                  i)
                ) {
                  var h = s.top - t,
                    a = h < s.height && h > 0 && t > n;
                  this.$children.forEach(function (e, i) {
                    if (i === o) {
                      var r = t > n,
                        c = "transform: translate3d(0, "
                          .concat(a ? h : 0, "px, 0); top: ")
                          .concat(n, "px");
                      (c === e.data.anchorStyle && r === e.data.sticky) ||
                        e.setData({
                          sticky: r,
                          active: !0,
                          style: "height: ".concat(s.height, "px"),
                          anchorStyle: c,
                        });
                    } else if (i + 1 === o) {
                      var l = "transform: translate3d(0, "
                        .concat(a ? h - s.height : 0, "px, 0); top: ")
                        .concat(n, "px");
                      l !== e.data.anchorStyle &&
                        e.setData({
                          sticky: !0,
                          active: !0,
                          style: "height: ".concat(s.height, "px"),
                          anchorStyle: l,
                        });
                    } else
                      e.setData({ active: !1, sticky: !1, anchorStyle: "" });
                  });
                }
              }
            }
          },
          onScroll: function (t) {
            var e = t.scrollTop;
            this.setAnchorOnScroll(e);
          },
        }),
        t
      );
    }
    return t(s);
  })(o.SuperComponent),
  g = (p = (0, r.__decorate)([(0, o.wxComponent)()], p));
exports.default = g;
