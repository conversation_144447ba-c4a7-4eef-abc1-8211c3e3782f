@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-cell-group {
  position: relative;
}
.t-cell-group__title {
  background-color: var(
    --td-cell-group-title-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  color: var(
    --td-cell-group-title-color,
    var(--td-text-color-placeholder, var(--td-font-gray-3, rgba(0, 0, 0, 0.4)))
  );
  font-family: PingFangSC-Regular;
  font-size: var(--td-cell-group-title-font-size, 28rpx);
  line-height: var(--td-cell-group-title-line-height, 90rpx);
  padding-left: var(--td-cell-group-title-padding-left, 32rpx);
  text-align: left;
}
.t-cell-group--bordered::before {
  border-top: 1px solid
    var(
      --td-cell-group-border-color,
      var(--td-component-stroke, var(--td-gray-color-3, #e7e7e7))
    );
  top: 0;
  transform: scaleY(0.5);
  transform-origin: 0 0;
  -webkit-transform-origin: top;
  transform-origin: top;
}
.t-cell-group--bordered::after,
.t-cell-group--bordered::before {
  box-sizing: border-box;
  content: " ";
  left: 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  z-index: 1;
}
.t-cell-group--bordered::after {
  border-bottom: 1px solid
    var(
      --td-cell-group-border-color,
      var(--td-component-stroke, var(--td-gray-color-3, #e7e7e7))
    );
  bottom: 0;
  transform: scaleY(0.5);
  transform-origin: bottom;
}
.t-cell-group--card {
  border-radius: var(--td-radius-large, 18rpx);
  margin: 0 32rpx;
  overflow: hidden;
}
