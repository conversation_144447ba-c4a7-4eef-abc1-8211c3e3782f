__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/switch/switch": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { customStyle: new Array(1), style: new Array(1) },
          K = U === true,
          f,
          i,
          k = (C) => {},
          l = (C, T) => {
            var $A = D.checked;
            var $B = 0;
            var $C = 1;
            C || K || !!U.checked || ($A ? Z(U.label, $B) : Z(U.label, $C))
              ? T(Y($A ? X(D.label)[$B] : X(D.label)[$C]))
              : T();
          },
          m = (C) => {},
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "t-loading",
                {},
                (N, C) => {
                  if (C) O(N, "inherit-color", true);
                  if (C) O(N, "size", "32rpx");
                },
                k
              );
            } else if (i === 2) {
              E("text", {}, (N, C) => {}, l);
            } else if (i === 3) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  var $A = D.checked;
                  var $B = 0;
                  var $C = 1;
                  if (
                    C ||
                    K ||
                    !!U.checked ||
                    ($A ? Z(U.icon, $B) : Z(U.icon, $C))
                  )
                    O(N, "name", $A ? X(D.icon)[$B] : X(D.icon)[$C]);
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([U.checked]), U.size])
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "t-class",
                      P(X(a).cls)(D.classPrefix + "__icon", [
                        ["checked", D.checked],
                        D.size,
                      ])
                    );
                },
                m
              );
            }
          },
          h = (C, T, E, B) => {
            i = D.loading
              ? 1
              : X(D.label).length == 2
              ? 2
              : X(D.icon).length == 2
              ? 3
              : 0;
            B(i, j);
          },
          g = (C, T, E) => {
            if (f === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([U.checked]), Q.a([U.disabled]), U.size]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__label", [
                          ["checked", D.checked],
                          ["disabled", D.disabled],
                          D.size,
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-label"
                    );
                },
                h
              );
            }
          },
          e = (C, T, E, B) => {
            f = D.label ? 1 : 0;
            B(f, g);
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([U.checked]),
                      Q.a([U.disabled]),
                      Q.a([
                        !!(
                          Z(U.label, "length") ||
                          Z(U.icon, "length") ||
                          U.loading
                        ) || undefined,
                      ]),
                      U.size,
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__dot", [
                        ["checked", D.checked],
                        ["disabled", D.disabled],
                        [
                          "plain",
                          X(D.label).length != 2 &&
                            X(D.icon).length != 2 &&
                            !D.loading,
                        ],
                        D.size,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-dot"
                  );
                if (C || K || undefined) O(N, "aria-hidden", true);
              },
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([U.checked]),
                      Q.a([!!(U.disabled || U.loading) || undefined]),
                      U.size,
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__body", [
                        ["checked", D.checked],
                        ["disabled", D.disabled || D.loading],
                        D.size,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-body"
                  );
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                  L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C || K || U.checked) O(N, "aria-checked", D.checked);
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                if (C) O(N, "aria-role", "switch");
                if (C) R.v(N, "tap", "handleSwitch", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/switch/switch";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/switch/switch.js";
define(
  "miniprogram_npm/tdesign-miniprogram/switch/switch.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/slicedToArray"),
      r = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      s = require("../../../@babel/runtime/helpers/createSuper"),
      l = require("tslib"),
      i = require("../common/src/index"),
      u = n(require("../common/config")),
      o = n(require("./props"));
    function n(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var c = u.default.prefix,
      d = "".concat(c, "-switch"),
      h = (function (l) {
        a(u, l);
        var i = s(u);
        function u() {
          var r;
          return (
            t(this, u),
            ((r = i.apply(this, arguments)).externalClasses = [
              "t-class",
              "t-class-label",
              "t-class-body",
              "t-class-dot",
            ]),
            (r.behaviors = ["wx://form-field"]),
            (r.properties = o.default),
            (r.data = { prefix: c, classPrefix: d, checked: !1 }),
            (r.controlledProps = [{ key: "value", event: "change" }]),
            (r.observers = {
              value: function (r) {
                var t = e(this.data.customValue, 1)[0];
                this.setData({ checked: r === t });
              },
            }),
            (r.methods = {
              handleSwitch: function () {
                var r = this.data,
                  t = r.loading,
                  a = r.disabled,
                  s = r.value,
                  l = r.customValue,
                  i = e(l, 2),
                  u = i[0],
                  o = i[1];
                t || a || this._trigger("change", { value: s === u ? o : u });
              },
            }),
            r
          );
        }
        return r(u);
      })(i.SuperComponent),
      p = (h = (0, l.__decorate)([(0, i.wxComponent)()], h));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/switch/switch.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/switch/switch.js");
