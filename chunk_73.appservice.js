__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/service-list/service-list": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { categoryName: new Array(1) },
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || U.categoryName) O(N, "title", D.categoryName);
                A["categoryName"][0] = (D, E, T) => {
                  O(N, "title", D.categoryName);
                  E(N);
                };
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          e,
          h = (C) => {},
          g = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "40rpx");
                if (C) O(N, "text", "加载中...");
              },
              h
            );
          },
          j = (C, k, l, m, n, o, T, E) => {
            var p = (C) => {};
            E(
              "service-card",
              {},
              (N, C) => {
                if (C || K || m) O(N, "service", k);
                if (C) R.v(N, "contact", "handleContact", !1, !1, !1, !1);
                if (C) R.v(N, "confirm", "handleConfirm", !1, !1, !1, !1);
              },
              p
            );
          },
          k,
          n,
          q = (C) => {},
          p = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "32rpx");
                if (C) O(N, "text", "加载更多...");
              },
              q
            );
          },
          r = (C, T) => {
            C ? T("点击加载更多") : T();
          },
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-more");
                },
                p
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "load-more-text");
                },
                r
              );
            }
          },
          m = (C, T, E, B) => {
            n = D.serviceLoading ? 1 : 0;
            B(n, o);
          },
          l = (C, T, E) => {
            if (k === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "load-more");
                  if (C) O(N, "bindtap", "loadMoreServices");
                },
                m
              );
            }
          },
          s,
          u = (C, T) => {
            C ? T("没有更多服务了") : T();
          },
          t = (C, T, E) => {
            if (s === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "no-more");
                },
                u
              );
            }
          },
          v,
          y = (C) => {},
          x = (C, T, E) => {
            E(
              "t-empty",
              {},
              (N, C) => {
                if (C) O(N, "description", "暂无相关服务");
              },
              y
            );
          },
          w = (C, T, E) => {
            if (v === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty-state");
                },
                x
              );
            }
          },
          i = (C, T, E, B, F) => {
            F(
              D.serviceList,
              "id",
              U ? U.serviceList : undefined,
              [0, "serviceList"],
              j
            );
            k = D.serviceHasMore ? 1 : 0;
            B(k, l);
            s = !D.serviceHasMore && X(D.serviceList).length > 0 ? 1 : 0;
            B(s, t);
            v = X(D.serviceList).length === 0 && !D.serviceLoading ? 1 : 0;
            B(v, w);
          },
          f = (C, T, E) => {
            if (e === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                g
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                i
              );
            }
          },
          d = (C, T, E, B) => {
            e = D.serviceLoading && X(D.serviceList).length === 0 ? 1 : 0;
            B(e, f);
          },
          a = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/service-list/service-list";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/service-list/service-list.js";
define(
  "pages/service-list/service-list.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var e = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      t = require("../../@babel/runtime/helpers/toConsumableArray"),
      a = require("../../@babel/runtime/helpers/asyncToGenerator"),
      r = require("../../env/index.js");
    Page({
      data: {
        categoryId: "",
        categoryName: "",
        serviceList: [],
        serviceLoading: !1,
        servicePage: 1,
        serviceLimit: 10,
        serviceHasMore: !0,
      },
      onLoad: function (e) {
        var t = e.categoryId,
          a = e.categoryName;
        this.setData({ categoryId: t || "", categoryName: a || "服务列表" }),
          wx.setNavigationBarTitle({ title: a || "服务列表" }),
          this.fetchServiceList(!0);
      },
      fetchServiceList: function () {
        var r = arguments,
          i = this;
        return a(
          e().mark(function a() {
            var o, n, c, s, d, v, l;
            return e().wrap(
              function (e) {
                for (;;)
                  switch ((e.prev = e.next)) {
                    case 0:
                      if (
                        ((o = r.length > 0 && void 0 !== r[0] && r[0]),
                        !i.data.serviceLoading)
                      ) {
                        e.next = 3;
                        break;
                      }
                      return e.abrupt("return");
                    case 3:
                      return (
                        (n = getApp()),
                        i.setData({ serviceLoading: !0 }),
                        (e.prev = 5),
                        (c = o ? 1 : i.data.servicePage),
                        (s = { page: c, limit: i.data.serviceLimit }),
                        i.data.categoryId &&
                          (s.category_id = i.data.categoryId),
                        (e.next = 11),
                        n.call({
                          path: "/api/mp/services",
                          method: "GET",
                          data: s,
                        })
                      );
                    case 11:
                      if (0 !== (d = e.sent).code) {
                        e.next = 19;
                        break;
                      }
                      (v = d.data.services || []),
                        (l = o ? v : [].concat(t(i.data.serviceList), t(v))),
                        i.setData({
                          serviceList: l,
                          servicePage: c + 1,
                          serviceHasMore: d.data.pagination.has_next,
                          serviceLoading: !1,
                        }),
                        console.log("获取服务列表成功:", l),
                        (e.next = 20);
                      break;
                    case 19:
                      throw new Error(d.message || "获取服务列表失败");
                    case 20:
                      e.next = 27;
                      break;
                    case 22:
                      (e.prev = 22),
                        (e.t0 = e.catch(5)),
                        console.error("获取服务列表失败:", e.t0),
                        i.setData({ serviceLoading: !1 }),
                        wx.showToast({ title: "加载失败", icon: "error" });
                    case 27:
                    case "end":
                      return e.stop();
                  }
              },
              a,
              null,
              [[5, 22]]
            );
          })
        )();
      },
      loadMoreServices: function () {
        this.data.serviceHasMore &&
          !this.data.serviceLoading &&
          this.fetchServiceList(!1);
      },
      handleContact: function (e) {
        var t = e.detail;
        console.log("联系服务提供者:", t);
      },
      handleConfirm: function (e) {
        var t = e.detail;
        console.log("确认服务:", t);
      },
      onReady: function () {},
      onShow: function () {},
      onHide: function () {},
      onUnload: function () {},
      onPullDownRefresh: function () {
        this.fetchServiceList(!0).then(function () {
          wx.stopPullDownRefresh();
        });
      },
      onReachBottom: function () {
        this.loadMoreServices();
      },
      onShareAppMessage: function () {
        return {
          title: "".concat(this.data.categoryName, " - ").concat(r.appName),
          path: "/pages/service-list/service-list?categoryId="
            .concat(this.data.categoryId, "&categoryName=")
            .concat(this.data.categoryName),
        };
      },
    });
  },
  {
    isPage: true,
    isComponent: true,
    currentFile: "pages/service-list/service-list.js",
  }
);
require("pages/service-list/service-list.js");
