.weui-navigation-bar {
  --weui-FG-0: rgba(0, 0, 0, 0.9);
  --height: 44px;
  --left: 16px;
}
.weui-navigation-bar .android {
  --height: 48px;
}
.weui-navigation-bar {
  color: var(--weui-FG-0);
  flex: none;
  overflow: hidden;
}
.weui-navigation-bar__inner {
  height: calc(var(--height) + env(safe-area-inset-top));
  justify-content: center;
  left: 0;
  padding-top: env(safe-area-inset-top);
  top: 0;
  width: 100%;
}
.weui-navigation-bar__inner,
.weui-navigation-bar__left {
  align-items: center;
  box-sizing: border-box;
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
  position: relative;
}
.weui-navigation-bar__left {
  height: 100%;
  padding-left: var(--left);
}
.weui-navigation-bar__btn_goback_wrapper {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  margin: -11px -18px -11px -16px;
  padding: 11px 18px 11px 16px;
}
.weui-navigation-bar__btn_goback_wrapper.weui-active {
  opacity: 0.5;
}
.weui-navigation-bar__btn_goback {
  background-color: var(--weui-FG-0);
  font-size: 12px;
  height: 24px;
  mask: url(data:image/svg+xml;charset=utf8,%3Csvg\ xmlns=\'http://www.w3.org/2000/svg\'\ width=\'12\'\ height=\'24\'\ viewBox=\'0\ 0\ 12\ 24\'%3E\ \ %3Cpath\ fill-opacity=\'.9\'\ fill-rule=\'evenodd\'\ d=\'M10\ 19.438L8.955\ 20.5l-7.666-7.79a1.02\ 1.02\ 0\ 0\ 1\ 0-1.42L8.955\ 3.5\ 10\ 4.563\ 2.682\ 12\ 10\ 19.438z\'/%3E%3C/svg%3E)no-repeat 50% 50%;
  mask-size: cover;
  width: 12px;
}
.weui-navigation-bar__center {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  flex: 1;
  flex-direction: row;
  font-size: 17px;
  font-weight: 700;
  height: 100%;
  justify-content: center;
  position: relative;
  text-align: center;
}
.weui-navigation-bar__loading {
  align-items: center;
  margin-right: 4px;
}
.weui-loading {
  animation: loading 1s linear infinite;
  background: transparent url(data:image/svg+xml;charset=utf-8,%3Csvg\ width=\'80\'\ height=\'80\'\ xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cdefs%3E%3ClinearGradient\ x1=\'94.087%25\'\ y1=\'0%25\'\ x2=\'94.087%25\'\ y2=\'90.559%25\'\ id=\'a\'%3E%3Cstop\ stop-color=\'%23606060\'\ stop-opacity=\'0\'\ offset=\'0%25\'/%3E%3Cstop\ stop-color=\'%23606060\'\ stop-opacity=\'.3\'\ offset=\'100%25\'/%3E%3C/linearGradient%3E%3ClinearGradient\ x1=\'100%25\'\ y1=\'8.674%25\'\ x2=\'100%25\'\ y2=\'90.629%25\'\ id=\'b\'%3E%3Cstop\ stop-color=\'%23606060\'\ offset=\'0%25\'/%3E%3Cstop\ stop-color=\'%23606060\'\ stop-opacity=\'.3\'\ offset=\'100%25\'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cg\ fill=\'none\'\ fill-rule=\'evenodd\'\ opacity=\'.9\'%3E%3Cpath\ d=\'M40\ 0c22.091\ 0\ 40\ 17.909\ 40\ 40S62.091\ 80\ 40\ 80v-7c18.225\ 0\ 33-14.775\ 33-33S58.225\ 7\ 40\ 7V0Z\'\ fill=\'url\(%23a\)\'/%3E%3Cpath\ d=\'M40\ 0v7C21.775\ 7\ 7\ 21.775\ 7\ 40s14.775\ 33\ 33\ 33v7C17.909\ 80\ 0\ 62.091\ 0\ 40S17.909\ 0\ 40\ 0Z\'\ fill=\'url\(%23b\)\'/%3E%3Ccircle\ fill=\'%23606060\'\ cx=\'40.5\'\ cy=\'3.5\'\ r=\'3.5\'/%3E%3C/g%3E%3C/svg%3E)no-repeat;
  background-size: 100%;
  display: block;
  font-size: 16px;
  height: 16px;
  margin-left: 0;
  width: 16px;
}
@-webkit-keyframes loading {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(1turn);
  }
}
@keyframes loading {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(1turn);
  }
}
