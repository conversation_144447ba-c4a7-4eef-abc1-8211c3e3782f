@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-swipe-cell {
  overflow: hidden;
  position: relative;
}
.t-swipe-cell__left,
.t-swipe-cell__right {
  height: 100%;
  position: absolute;
  top: 0;
}
.t-swipe-cell__left {
  left: 0;
  transform: translate3d(-100%, 0, 0);
}
.t-swipe-cell__right {
  right: 0;
  transform: translate3d(100%, 0, 0);
}
.t-swipe-cell__content {
  align-items: center;
  display: -webkit-inline-flex;
  display: inline-flex;
  justify-content: center;
  padding: 0 var(--td-spacer-2, 32rpx);
}
.t-swipe-cell__icon {
  font-size: var(--td-font-size-xl, 40rpx);
}
.t-swipe-cell__icon + .t-swipe-cell__text:not(:empty) {
  font-size: var(--td-font-size-base, 28rpx);
  line-height: 44rpx;
  margin-left: var(--td-spacer, 16rpx);
}
