Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.parseGradientString =
    exports.isGradientColor =
    exports.default =
      void 0);
var r,
  t =
    (r = require("tinycolor2/esm/tinycolor")) && r.__esModule
      ? r
      : { default: r },
  e = require("../../validator");
var o,
  i,
  a,
  s,
  n,
  l,
  d = function (r, t) {
    for (var o = "", i = 0; i < r.length; i += 1)
      (0, e.isString)(r[i]) ? (o += r[i]) : (o += r[i].source);
    return new RegExp(o, t);
  },
  c =
    ((o = /\s*,\s*/),
    (i = /(?:[+-]?\d*\.?\d+)(?:%|[a-z]+)?/),
    (a = d(
      [
        "(?:",
        /#(?:[a-f0-9]{6}|[a-f0-9]{3})/,
        "|",
        "(?:rgb|hsl)",
        /\(\s*(?:\d{1,3}\s*,\s*){2}\d{1,3}\s*\)/,
        "|",
        "(?:rgba|hsla)",
        /\(\s*(?:\d{1,3}\s*,\s*){2}\d{1,3}\s*,\s*\d*\.?\d+\)/,
        "|",
        /[_a-z-][_a-z0-9-]*/,
        ")",
      ],
      ""
    )),
    (s = d([a, "(?:\\s+", i, "(?:\\s+", i, ")?)?"], "")),
    (n = d(["(?:", s, o, ")*", s], "")),
    (l = d(
      [
        "(?:(",
        /(?:[+-]?\d*\.?\d+)(?:deg|grad|rad|turn)/,
        ")|",
        /to\s+((?:(?:left|right|top|bottom)(?:\s+(?:top|bottom|left|right))?))/,
        ")",
      ],
      ""
    )),
    {
      gradientSearch: d(["(?:(", l, ")", o, ")?(", n, ")"], "gi"),
      colorStopSearch: d(
        ["\\s*(", a, ")", "(?:\\s+", "(", i, "))?", "(?:", o, "\\s*)?"],
        "gi"
      ),
    }),
  p = /.*gradient\s*\(((?:\([^)]*\)|[^)(]*)*)\)/gim,
  g = function (r) {
    return (p.lastIndex = 0), p.exec(r);
  };
exports.isGradientColor = g;
var u = {
    top: 0,
    right: 90,
    bottom: 180,
    left: 270,
    "top left": 225,
    "left top": 225,
    "top right": 135,
    "right top": 135,
    "bottom left": 315,
    "left bottom": 315,
    "bottom right": 45,
    "right bottom": 45,
  },
  f = function (r) {
    var o = g(r);
    if (!o) return !1;
    var i = { points: [], degree: 0 },
      a = (function (r, t) {
        var o, i, a;
        r.gradientSearch.lastIndex = 0;
        var s = r.gradientSearch.exec(t);
        if (!(0, e.isNull)(s))
          for (
            o = { original: s[0], colorStopList: [] },
              s[1] && (o.line = s[1]),
              s[2] && (o.angle = s[2]),
              s[3] && (o.sideCorner = s[3]),
              r.colorStopSearch.lastIndex = 0,
              i = r.colorStopSearch.exec(s[4]);
            !(0, e.isNull)(i);

          )
            (a = { color: i[1] }),
              i[2] && (a.position = i[2]),
              o.colorStopList.push(a),
              (i = r.colorStopSearch.exec(s[4]));
        return o;
      })(c, o[1]);
    if (a.original.trim() !== o[1].trim()) return !1;
    var s = a.colorStopList.map(function (r) {
      var e = r.color,
        o = r.position,
        i = Object.create(null);
      return (
        (i.color = (0, t.default)(e).toRgbString()), (i.left = parseFloat(o)), i
      );
    });
    i.points = s;
    var n = parseInt(a.angle, 10);
    return Number.isNaN(n) && (n = u[a.sideCorner] || 90), (i.degree = n), i;
  };
exports.parseGradientString = f;
var h = f;
exports.default = h;
