Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = {
  allowHalf: { type: Boolean, value: !1 },
  color: { type: null, value: "#ED7B2F" },
  count: { type: Number, value: 5 },
  disabled: { type: null, value: void 0 },
  gap: { type: null, value: 8 },
  icon: { type: null },
  iconPrefix: { type: String, value: void 0 },
  placement: { type: String, value: "top" },
  showText: { type: Boolean, value: !1 },
  size: { type: String, value: "24px" },
  texts: { type: Array, value: [] },
  value: { type: Number, value: null },
  defaultValue: { type: Number, value: 0 },
  variant: { type: String, value: "outline" },
};
exports.default = e;
