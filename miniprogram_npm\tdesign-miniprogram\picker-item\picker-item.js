Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  i = require("../../../@babel/runtime/helpers/inherits"),
  n = require("../../../@babel/runtime/helpers/createSuper"),
  a = require("tslib"),
  s = require("../common/src/index"),
  r = l(require("../common/config")),
  o = l(require("./props"));
function l(e) {
  return e && e.__esModule ? e : { default: e };
}
var u = r.default.prefix,
  d = "".concat(u, "-picker-item"),
  c = function (e, t, i) {
    return Math.min(Math.max(e, t), i);
  },
  f = function (e, t) {
    var i = e;
    return (i = (Math.abs(i / t) / 0.005) * (i < 0 ? -1 : 1));
  },
  h = (function (a) {
    i(r, a);
    var s = n(r);
    function r() {
      var e;
      return (
        t(this, r),
        ((e = s.apply(this, arguments)).relations = {
          "../picker/picker": {
            type: "parent",
            linked: function (e) {
              if ("keys" in e.data) {
                var t = e.data.keys;
                if (
                  null === t ||
                  JSON.stringify(this.data.pickerKeys) === JSON.stringify(t)
                )
                  return;
                this.setData({ pickerKeys: t });
              }
            },
          },
        }),
        (e.options = { multipleSlots: !0 }),
        (e.externalClasses = ["".concat(u, "-class")]),
        (e.properties = o.default),
        (e.observers = {
          "options, pickerKeys": function () {
            this.update();
          },
        }),
        (e.data = {
          prefix: u,
          classPrefix: d,
          offset: 0,
          duration: 0,
          value: "",
          curIndex: 0,
          columnIndex: 0,
          pickerKeys: { value: "value", label: "label" },
          formatOptions: o.default.options.value,
        }),
        (e.lifetimes = {
          created: function () {
            (this.StartY = 0), (this.StartOffset = 0), (this.startTime = 0);
          },
        }),
        (e.methods = {
          onTouchStart: function (e) {
            (this.StartY = e.touches[0].clientY),
              (this.StartOffset = this.data.offset),
              (this.startTime = Date.now()),
              this.setData({ duration: 0 });
          },
          onTouchMove: function (e) {
            var t = this.StartY,
              i = this.StartOffset,
              n = this.data.pickItemHeight,
              a = e.touches[0].clientY - t,
              s = c(i + a, -this.getCount() * n, 0);
            this.setData({ offset: s });
          },
          onTouchEnd: function (e) {
            var t = this,
              i = this.data,
              n = i.offset,
              a = i.pickerKeys,
              s = i.columnIndex,
              r = i.pickItemHeight,
              o = i.formatOptions,
              l = this.startTime;
            if (n !== this.StartOffset) {
              var u = 0,
                d = e.changedTouches[0].clientY - this.StartY,
                h = Date.now() - l;
              h < 300 && Math.abs(d) > 15 && (u = f(d, h));
              var p = c(n + u, -this.getCount() * r, 0),
                v = c(Math.round(-p / r), 0, this.getCount() - 1);
              this.setData({ offset: -v * r, duration: 1e3, curIndex: v }),
                v !== this._selectedIndex &&
                  ((this._selectedIndex = v),
                  wx.nextTick(function () {
                    var e, i, n;
                    (t._selectedIndex = v),
                      (t._selectedValue =
                        null === (e = o[v]) || void 0 === e
                          ? void 0
                          : e[null == a ? void 0 : a.value]),
                      (t._selectedLabel =
                        null === (i = o[v]) || void 0 === i
                          ? void 0
                          : i[null == a ? void 0 : a.label]),
                      null === (n = t.$parent) ||
                        void 0 === n ||
                        n.triggerColumnChange({ index: v, column: s });
                  }));
            }
          },
          formatOption: function (e, t, i) {
            return "function" != typeof i
              ? e
              : e.map(function (e) {
                  return i(e, t);
                });
          },
          update: function () {
            var e,
              t,
              i,
              n,
              a = this.data,
              s = a.options,
              r = a.value,
              o = a.pickerKeys,
              l = a.pickItemHeight,
              u = a.format,
              d = a.columnIndex,
              c = this.formatOption(s, d, u),
              f = c.findIndex(function (e) {
                return e[null == o ? void 0 : o.value] === r;
              }),
              h = f > 0 ? f : 0;
            (this._selectedIndex = h),
              (this._selectedValue =
                null === (e = c[h]) || void 0 === e
                  ? void 0
                  : e[null == o ? void 0 : o.value]),
              (this._selectedLabel =
                null === (t = c[h]) || void 0 === t
                  ? void 0
                  : t[null == o ? void 0 : o.label]),
              this.setData({ formatOptions: c, offset: -h * l, curIndex: h }),
              (this._selectedIndex = h),
              (this._selectedValue =
                null === (i = s[h]) || void 0 === i
                  ? void 0
                  : i[null == o ? void 0 : o.value]),
              (this._selectedLabel =
                null === (n = s[h]) || void 0 === n
                  ? void 0
                  : n[null == o ? void 0 : o.label]);
          },
          getCount: function () {
            var e, t;
            return null ===
              (t =
                null === (e = this.data) || void 0 === e
                  ? void 0
                  : e.options) || void 0 === t
              ? void 0
              : t.length;
          },
        }),
        e
      );
    }
    return e(r);
  })(s.SuperComponent),
  p = (h = (0, a.__decorate)([(0, s.wxComponent)()], h));
exports.default = p;
