@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-picker {
  background-color: var(
    --td-picker-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  border-top-left-radius: var(--td-picker-border-radius, 24rpx);
  border-top-right-radius: var(--td-picker-border-radius, 24rpx);
  position: relative;
}
.t-picker__toolbar {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  height: var(--td-picker-toolbar-height, 116rpx);
  justify-content: space-between;
  overflow: hidden;
}
.t-picker__title {
  color: var(
    --td-picker-title-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  flex: 1;
  font-size: var(--td-picker-title-font-size, 36rpx);
  font-weight: var(--td-picker-title-font-weight, 600);
  line-height: var(--td-picker-title-line-height, 52rpx);
  overflow: hidden;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.t-picker__cancel,
.t-picker__confirm {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  font-size: var(--td-picker-button-font-size, 32rpx);
  height: 100%;
  justify-content: center;
  padding: 0 32rpx;
  user-select: none;
}
.t-picker__cancel {
  color: var(
    --td-picker-cancel-color,
    var(--td-text-color-secondary, var(--td-font-gray-2, rgba(0, 0, 0, 0.6)))
  );
}
.t-picker__confirm {
  color: var(
    --td-picker-confirm-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-picker__main {
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  padding-left: 64rpx;
  padding-right: 64rpx;
  position: relative;
}
.t-picker__mask {
  backface-visibility: hidden;
  height: 96rpx;
  left: 0;
  pointer-events: none;
  position: absolute;
  right: 0;
  z-index: 3;
}
.t-picker__mask--top {
  top: 0;
}
.t-picker__mask--bottom,
.t-picker__mask--top {
  background: linear-gradient(
    180deg,
    var(
        --td-picker-bg-color,
        var(--td-bg-color-container, var(--td-font-white-1, #fff))
      )
      0,
    var(--td-picker-transparent-color) 100%
  );
}
.t-picker__mask--bottom {
  bottom: 0;
  transform: matrix(1, 0, 0, -1, 0, 0);
}
.t-picker__indicator {
  background-color: var(
    --td-picker-indicator-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  border-radius: var(--td-picker-indicator-border-radius, 12rpx);
  left: 32rpx;
  pointer-events: none;
  position: absolute;
  right: 32rpx;
  top: 144rpx;
}
