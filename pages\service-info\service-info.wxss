.service_description_section {
  margin-bottom: 40rpx;
}
.section_title {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.description_box {
  background-color: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  border-radius: 15rpx;
  padding: 25rpx;
}
.description_text {
  color: #666;
  display: block;
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 10rpx;
}
.description_text:last-child {
  margin-bottom: 0;
}
.image_section {
  flex: 1;
}
.image_item {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  margin: 0 auto 20rpx;
  width: 100%;
}
.service_image {
  border-radius: 15rpx;
  display: block;
  margin: 0 auto;
  width: 100%;
}
.image_placeholder {
  align-items: center;
  background-color: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  border-radius: 15rpx;
  display: -webkit-flex;
  display: flex;
  height: 300rpx;
  justify-content: center;
  margin-bottom: 20rpx;
  width: 100%;
}
.image_placeholder:last-child {
  margin-bottom: 0;
}
.placeholder_text {
  color: #999;
  font-size: 28rpx;
}
.bottom_actions {
  border-top: 2rpx solid #f0f0f0;
  bottom: 0;
  box-shadow: 0-2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: 60rpx;
  left: 0;
  padding: 30rpx;
  position: fixed;
  right: 0;
  z-index: 999;
}
.bottom_actions,
.order_button_text {
  align-items: center;
  background-color: #ff4d4f;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.order_button_text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  width: 100%;
}
.service_info_container {
  background-color: #fff;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 190rpx 30rpx 150rpx;
}
.contact_section {
  flex: 1;
  margin-right: 20rpx;
}
.contact_text {
  color: #195abf;
  font-size: 28rpx;
}
.order_button_section {
  display: -webkit-flex;
  display: flex;
  flex: 1;
  justify-content: flex-end;
}
.custom_order_button {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  height: 80rpx;
  justify-content: center;
  padding: 0 40rpx;
}
.custom_order_button:active {
  background-color: rgba(255, 77, 79, 0.1);
}
.service_info_container {
  padding-bottom: 120rpx;
}
.modal_overlay {
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  display: -webkit-flex;
  display: flex;
  height: 100%;
  justify-content: center;
  left: 0;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
}
.modal_container {
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  max-width: 600rpx;
  overflow: hidden;
  width: 90%;
}
.modal_header {
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
}
.modal_title {
  color: #333;
  font-size: 32rpx;
  font-weight: 700;
}
.modal_close {
  align-items: center;
  color: #999;
  display: -webkit-flex;
  display: flex;
  font-size: 40rpx;
  height: 40rpx;
  justify-content: center;
  width: 40rpx;
}
.modal_content {
  padding: 30rpx;
}
.service_info_summary {
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 10rpx;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 20rpx;
}
.service_name {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}
.service_price {
  color: #195abf;
  font-size: 32rpx;
  font-weight: 700;
}
.remark_section {
  margin-top: 20rpx;
}
.remark_label {
  color: #333;
  display: block;
  font-size: 28rpx;
  margin-bottom: 15rpx;
}
.remark_input {
  background-color: #fafafa;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  box-sizing: border-box;
  color: #333;
  font-size: 26rpx;
  min-height: 120rpx;
  padding: 20rpx;
  width: 100%;
}
.remark_count {
  color: #999;
  display: block;
  font-size: 24rpx;
  margin-top: 10rpx;
  text-align: right;
}
.modal_footer {
  border-top: 1rpx solid #f0f0f0;
  display: -webkit-flex;
  display: flex;
}
.cancel_button,
.confirm_button {
  border: none;
  border-radius: 0;
  flex: 1;
  font-size: 30rpx;
  height: 100rpx;
}
.cancel_button {
  background-color: #f5f5f5;
  color: #666;
}
.confirm_button {
  background-color: #ff4d4f;
  color: #fff;
}
.cancel_button::after,
.confirm_button::after {
  border: none;
}
