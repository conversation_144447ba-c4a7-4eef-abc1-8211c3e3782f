@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-skeleton {
  box-sizing: border-box;
}
.t-skeleton__row {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--td-skeleton-row-spacing, var(--td-spacer-2, 32rpx));
}
.t-skeleton__row:last-child,
.t-skeleton__row:only-child {
  margin-bottom: 0;
}
.t-skeleton__col {
  align-items: center;
  background-color: var(
    --td-skeleton-bg-color,
    var(--td-bg-color-secondarycontainer, var(--td-gray-color-1, #f3f3f3))
  );
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.t-skeleton__col:first-child:last-child,
.t-skeleton__col:last-child {
  margin-right: 0;
}
.t-skeleton--type-text {
  border-radius: var(
    --td-skeleton-text-border-radius,
    var(--td-radius-small, 6rpx)
  );
  height: var(--td-skeleton-text-height, 32rpx);
  width: 100%;
}
.t-skeleton--type-rect {
  border-radius: var(
    --td-skeleton-rect-border-radius,
    var(--td-radius-default, 12rpx)
  );
  height: var(--td-skeleton-rect-height, 32rpx);
  width: 100%;
}
.t-skeleton--type-circle {
  border-radius: var(
    --td-skeleton-circle-border-radius,
    var(--td-skeleton-circle-border-radius, var(--td-radius-circle, 50%))
  );
  flex-shrink: 0;
  height: var(--td-skeleton-circle-height, 96rpx);
  width: var(--td-skeleton-circle-height, 96rpx);
}
.t-skeleton--animation-gradient {
  overflow-x: hidden;
  position: relative;
}
.t-skeleton--animation-gradient::after {
  animation: t-skeleton--gradient 1.5s linear 2s infinite;
  background: linear-gradient(
    90deg,
    hsla(0, 0%, 100%, 0),
    var(--td-skeleton-animation-gradient, rgba(0, 0, 0, 0.04)),
    hsla(0, 0%, 100%, 0)
  );
  bottom: 0;
  content: " ";
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}
.t-skeleton--animation-flashed {
  animation: t-skeleton--flashed 2s linear 2s infinite;
}
@-webkit-keyframes t-skeleton--gradient {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
  }
}
@keyframes t-skeleton--gradient {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  100% {
    transform: translateX(100%) skewX(-15deg);
  }
}
@-webkit-keyframes t-skeleton--flashed {
  0% {
    opacity: 1;
  }
  50% {
    background-color: var(
      --td-skeleton-animation-flashed,
      hsla(0, 0%, 90%, 0.3)
    );
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}
@keyframes t-skeleton--flashed {
  0% {
    opacity: 1;
  }
  50% {
    background-color: var(
      --td-skeleton-animation-flashed,
      hsla(0, 0%, 90%, 0.3)
    );
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}
