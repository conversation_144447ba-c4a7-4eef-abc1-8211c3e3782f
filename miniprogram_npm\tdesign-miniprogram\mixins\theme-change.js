Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../common/utils"),
  t = Behavior({
    data: { theme: "light" },
    attached: function () {
      this._initTheme();
    },
    methods: {
      _initTheme: function () {
        var t = this;
        t.setData({ theme: e.appBaseInfo.theme }),
          wx.onThemeChange(function (e) {
            t.setData({ theme: e.theme });
          });
      },
    },
  });
exports.default = t;
