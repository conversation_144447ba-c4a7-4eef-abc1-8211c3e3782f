__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/transition/transition": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            transitionClass: new Array(1),
            prefix: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            classPrefix: new Array(1),
            visible: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.prefix || U.classPrefix || U.transitionClass) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(D.classPrefix) +
                      " " +
                      Y(D.transitionClass)
                  );
                A["prefix"][0] =
                  A["classPrefix"][0] =
                  A["transitionClass"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        "class " +
                          Y(D.prefix) +
                          "-class " +
                          Y(D.classPrefix) +
                          " " +
                          Y(D.transitionClass)
                      );
                    };
                var $A = D.visible;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([
                      !!U.visible || ($A ? undefined : undefined),
                      U.style,
                      U.customStyle,
                    ])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(a)._style)([
                      $A ? "" : "display: none",
                      D.style,
                      D.customStyle,
                    ])
                  );
                A["visible"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      var $B = D.visible;
                      R.y(
                        N,
                        P(X(a)._style)([
                          $B ? "" : "display: none",
                          D.style,
                          D.customStyle,
                        ])
                      );
                    };
                if (C)
                  R.v(N, "transitionend", "onTransitionEnd", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/transition/transition";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/transition/transition.js";
define(
  "miniprogram_npm/tdesign-miniprogram/transition/transition.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      n = require("../common/src/index"),
      s = u(require("../mixins/transition"));
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var l = u(require("../common/config")).default.prefix,
      o = "".concat(l, "-transition"),
      c = (function (a) {
        t(u, a);
        var n = i(u);
        function u() {
          var e;
          return (
            r(this, u),
            ((e = n.apply(this, arguments)).externalClasses = [
              "".concat(l, "-class"),
            ]),
            (e.behaviors = [(0, s.default)()]),
            (e.data = { classPrefix: o }),
            e
          );
        }
        return e(u);
      })(n.SuperComponent),
      p = (c = (0, a.__decorate)([(0, n.wxComponent)()], c));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/transition/transition.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/transition/transition.js");
