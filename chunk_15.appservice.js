__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/col/col": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/col/col"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            prefix: new Array(1),
            gutter: new Array(1),
            style: new Array(1),
            offset: new Array(2),
            customStyle: new Array(1),
            classPrefix: new Array(2),
            span: new Array(1),
          },
          K = U === true,
          d = (C, T, E, B, F, S) => {
            S("");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.offset;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.span]) ||
                    U.offset ||
                    ($A
                      ? !!(U.classPrefix || U.offset) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(P(X(a).cls)(D.classPrefix, [D.span])) +
                      " " +
                      Y($A ? D.classPrefix + "--offset-" + D.offset : "")
                  );
                A["prefix"][0] =
                  A["classPrefix"][0] =
                  A["span"][0] =
                  A["offset"][0] =
                  A["classPrefix"][1] =
                  A["offset"][1] =
                    (D, E, T) => {
                      var $B = D.offset;
                      L(
                        N,
                        "class " +
                          Y(D.prefix) +
                          "-class " +
                          Y(P(X(a).cls)(D.classPrefix, [D.span])) +
                          " " +
                          Y($B ? D.classPrefix + "--offset-" + D.offset : "")
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getColStyles") ||
                    U.gutter ||
                    U.style ||
                    U.customStyle
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(b).getColStyles)(D.gutter, D.style, D.customStyle)
                  );
                A["gutter"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(b).getColStyles)(D.gutter, D.style, D.customStyle)
                      );
                    };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/col/col";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/col/col.js";
define(
  "miniprogram_npm/tdesign-miniprogram/col/col.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      s = require("../common/src/index"),
      u = n(require("../common/config")),
      l = n(require("./props"));
    function n(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = u.default.prefix,
      p = "".concat(o, "-col"),
      c = (function (i) {
        t(u, i);
        var s = a(u);
        function u() {
          var e;
          return (
            r(this, u),
            ((e = s.apply(this, arguments)).externalClasses = [
              "".concat(o, "-class"),
            ]),
            (e.properties = l.default),
            (e.data = { prefix: o, classPrefix: p }),
            (e.relations = { "../row/row": { type: "parent" } }),
            e
          );
        }
        return e(u);
      })(s.SuperComponent),
      f = (c = (0, i.__decorate)([(0, s.wxComponent)()], c));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/col/col.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/col/col.js");
