__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/rate/rate": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/rate/rate"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { style: new Array(1), customStyle: new Array(1) },
          K = U === true,
          f = (C, g, h, i, j, k, T, E) => {
            var l = (C) => {};
            E(
              "t-icon",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    Z(undefined, "getIconClass") ||
                    U.classPrefix ||
                    U.defaultValue ||
                    U.value ||
                    j ||
                    U.allowHalf ||
                    U.disabled ||
                    U.scaleIndex
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__icon " +
                      Y(
                        P(X(b).getIconClass)(
                          D.classPrefix + "__icon",
                          D.defaultValue,
                          D.value,
                          h,
                          D.allowHalf,
                          D.disabled,
                          D.scaleIndex
                        )
                      )
                  );
                var $A = D.count - h > 1;
                if (
                  C ||
                  K ||
                  !!(
                    U.count ||
                    j ||
                    ($A
                      ? !!(Z(undefined, "addUnit") || U.gap) || undefined
                      : undefined) ||
                    Z(undefined, "getColor") ||
                    U.color
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    "margin-right:" +
                      Y($A ? P(X(a).addUnit)(D.gap) : 0) +
                      ";" +
                      Y(P(X(b).getColor)(D.color))
                  );
                if (C || K || !!U.prefix || undefined)
                  O(N, "t-class", Y(D.prefix) + "-class-icon");
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getIconName") ||
                    U.defaultValue ||
                    U.value ||
                    j ||
                    U.icon
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "name",
                    P(X(b).getIconName)(D.defaultValue, D.value, h, D.icon)
                  );
                if (C || K || U.size) O(N, "size", D.size);
                if (C || K || U.iconPrefix) O(N, "prefix", D.iconPrefix);
              },
              l
            );
          },
          e = (C, T, E, B, F) => {
            F(D.count, "*this", U ? U.count : undefined, [0, "count"], f);
          },
          g,
          i = (C, T) => {
            C ||
            K ||
            !!(
              Z(undefined, "getText") ||
              U.texts ||
              U.value ||
              U.defaultTexts
            ) ||
            undefined
              ? T(Y(P(X(b).getText)(D.texts, D.value, D.defaultTexts)))
              : T();
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([!!U.value || undefined])]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__text", [
                          ["active", D.value > 0],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-text"
                    );
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                i
              );
            }
          },
          j,
          l = (C, T) => {
            C ||
            K ||
            !!(
              U.value ||
              Z(undefined, "getText") ||
              U.texts ||
              U.value ||
              U.defaultTexts
            ) ||
            undefined
              ? T(
                  Y(
                    Y(D.value + "星 ") +
                      Y(P(X(b).getText)(D.texts, D.value, D.defaultTexts))
                  )
                )
              : T();
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "text",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([!!U.value || undefined]),
                        Q.a([U.isVisibleToScreenReader]),
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__text", [
                          ["active", D.value > 0],
                          ["sr-only", D.isVisibleToScreenReader],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-text"
                    );
                  if (C) O(N, "aria-role", "alert");
                  if (C) O(N, "aria-live", "assertive");
                },
                l
              );
            }
          },
          m,
          p,
          r,
          u = (C) => {},
          v = (C, T) => {
            C || K || !!(Z(undefined, "ceil") || U.value) || undefined
              ? T(Y(P(X(b).ceil)(D.value) - 0.5))
              : T();
          },
          t = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__icon " +
                      Y(D.classPrefix) +
                      "__icon--selected-half"
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getColor") || U.color) ||
                  undefined
                )
                  R.y(N, P(X(b).getColor)(D.color));
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getIconName") ||
                    U.defaultValue ||
                    U.value ||
                    U.index ||
                    U.icon
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "name",
                    P(X(b).getIconName)(
                      D.defaultValue,
                      D.value,
                      D.index,
                      D.icon
                    )
                  );
                if (C || K || U.size) O(N, "size", D.size);
              },
              u
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__tips-text");
              },
              v
            );
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([
                          !!(Z(undefined, "ceil") || U.value || U.value) ||
                            undefined,
                        ]),
                      ])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__tips-item", [
                        ["active", P(X(b).ceil)(D.value) - 0.5 == D.value],
                      ])
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "ceil") || U.value) ||
                    undefined
                  )
                    R.d(N, "value", P(X(b).ceil)(D.value) - 0.5);
                  if (C) R.v(N, "tap", "onSelect", !1, !1, !1, !1);
                },
                t
              );
            }
          },
          x = (C) => {},
          y = (C, T) => {
            C || K || !!(Z(undefined, "ceil") || U.value) || undefined
              ? T(Y(P(X(b).ceil)(D.value)))
              : T();
          },
          w = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "cls") || U.classPrefix || Q.a([])) ||
                  undefined
                )
                  L(N, P(X(a).cls)(D.classPrefix + "__icon", ["selected"]));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getColor") || U.color) ||
                  undefined
                )
                  R.y(N, P(X(b).getColor)(D.color));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getIconName") || U.defaultValue || U.icon) ||
                  undefined
                )
                  O(
                    N,
                    "name",
                    P(X(b).getIconName)(D.defaultValue, 0, 0, D.icon)
                  );
                if (C || K || U.size) O(N, "size", D.size);
              },
              x
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__tips-text");
              },
              y
            );
          },
          A0 = (C) => {},
          B0 = (C, T) => {
            C || K || U.value ? T(Y(D.value)) : T();
          },
          z = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([
                        !!(Z(undefined, "ceil") || U.value || U.value) ||
                          undefined,
                      ]),
                      Q.a([
                        !!(Z(undefined, "ceil") || U.value || U.value) ||
                          undefined,
                      ]),
                    ])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__icon", [
                      ["selected", P(X(b).ceil)(D.value) == D.value],
                      ["selected-half", P(X(b).ceil)(D.value) != D.value],
                    ])
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getColor") || U.color) ||
                  undefined
                )
                  R.y(N, P(X(b).getColor)(D.color));
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "getIconName") || U.defaultValue || U.icon) ||
                  undefined
                )
                  O(
                    N,
                    "name",
                    P(X(b).getIconName)(D.defaultValue, 0, 0, D.icon)
                  );
                if (C || K || U.size) O(N, "size", D.size);
              },
              A0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__tips-text");
              },
              B0
            );
          },
          q = (C, T, E, B) => {
            if (p === 1) {
              r = D.allowHalf ? 1 : 0;
              B(r, s);
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([
                          !!(Z(undefined, "ceil") || U.value || U.value) ||
                            undefined,
                        ]),
                      ])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__tips-item", [
                        ["active", P(X(b).ceil)(D.value) == D.value],
                      ])
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "ceil") || U.value) ||
                    undefined
                  )
                    R.d(N, "value", P(X(b).ceil)(D.value));
                  if (C) R.v(N, "tap", "onSelect", !1, !1, !1, !1);
                },
                w
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        Q.a([
                          !!(
                            Z(undefined, "ceil") ||
                            U.value ||
                            U.value ||
                            U.actionType
                          ) || undefined,
                        ]),
                      ])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__tips-item", [
                        [
                          "active",
                          P(X(b).ceil)(D.value) == D.value &&
                            D.actionType == "tap",
                        ],
                      ])
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "ceil") || U.value) ||
                    undefined
                  )
                    R.d(N, "value", P(X(b).ceil)(D.value));
                  if (C) R.v(N, "tap", "onSelect", !1, !1, !1, !1);
                },
                z
              );
            }
          },
          o = (C, T, E, B) => {
            p = D.actionType == "tap" ? 1 : 0;
            B(p, q);
          },
          n = (C, T, E) => {
            if (m === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.placement])
                    ) ||
                    undefined
                  )
                    L(N, P(X(a).cls)(D.classPrefix + "__tips", [D.placement]));
                  if (C || K || !!U.tipsLeft || undefined)
                    R.y(N, "left:" + Y(D.tipsLeft) + "px");
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                o
              );
            }
          },
          d = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__wrapper " + Y(D.prefix) + "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "regSize") || U.size) ||
                  undefined
                )
                  R.y(N, "font-size:" + Y(P(X(b).regSize)(D.size)));
                if (C) O(N, "aria-role", "slider");
                if (C || K || U.count) O(N, "aria-valuemax", D.count);
                if (C || K || undefined) O(N, "aria-valuemin", 0);
                if (C || K || U.value) O(N, "aria-valuenow", D.value);
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getText") ||
                    U.texts ||
                    U.value ||
                    U.defaultTexts
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-valuetext",
                    P(X(b).getText)(D.texts, D.value, D.defaultTexts)
                  );
                var $A = !D.disabled;
                if (C || K || !!U.disabled || ($A ? undefined : undefined))
                  R.v(
                    N,
                    "touchstart",
                    $A ? "onTouchStart" : "",
                    !1,
                    !1,
                    !1,
                    !0
                  );
                var $B = !D.disabled;
                if (C || K || !!U.disabled || ($B ? undefined : undefined))
                  R.v(N, "touchmove", $B ? "onTouchMove" : "", !1, !1, !1, !0);
                if (C) R.v(N, "tap", "onTap", !1, !1, !1, !1);
                var $C = !D.disabled;
                if (C || K || !!U.disabled || ($C ? undefined : undefined))
                  R.v(N, "touchend", $C ? "onTouchEnd" : "", !1, !1, !1, !0);
                var $D = !D.disabled;
                if (C || K || !!U.disabled || ($D ? undefined : undefined))
                  R.v(N, "touchcancel", $D ? "onTouchEnd" : "", !1, !1, !1, !0);
              },
              e
            );
            g = D.showText ? 1 : 0;
            B(g, h);
            j = D.isVisibleToScreenReader ? 1 : 0;
            B(j, k);
            m = D.tipsVisible && D.placement ? 1 : 0;
            B(m, n);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + " class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/rate/rate";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/rate/rate.js";
define(
  "miniprogram_npm/tdesign-miniprogram/rate/rate.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/slicedToArray"),
      t = require("../../../@babel/runtime/helpers/createClass"),
      i = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      n = require("../common/src/index"),
      o = l(require("../common/config")),
      c = l(require("./props")),
      u = require("../common/utils");
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var h = o.default.prefix,
      p = "".concat(h, "-rate"),
      d = (function (s) {
        r(o, s);
        var n = a(o);
        function o() {
          var t;
          return (
            i(this, o),
            ((t = n.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
              "".concat(h, "-class-icon"),
              "".concat(h, "-class-text"),
            ]),
            (t.properties = c.default),
            (t.controlledProps = [{ key: "value", event: "change" }]),
            (t.data = {
              prefix: h,
              classPrefix: p,
              defaultTexts: ["极差", "失望", "一般", "满意", "惊喜"],
              tipsVisible: !1,
              tipsLeft: 0,
              actionType: "",
              scaleIndex: -1,
              isVisibleToScreenReader: !1,
            }),
            (t.methods = {
              onTouch: function (t, i) {
                var r = this,
                  a = this.properties,
                  s = a.count,
                  n = a.allowHalf,
                  o = a.gap,
                  c = a.value,
                  l = a.size,
                  h = e(t.changedTouches, 1)[0],
                  d = (0, u.unitConvert)(o);
                (0, u.getRect)(this, ".".concat(p, "__wrapper")).then(function (
                  e
                ) {
                  var t = e.width,
                    a = e.left,
                    p = (t - (s - 1) * d) / s,
                    f = (h.pageX - a + d) / (p + d),
                    v = f % 1,
                    T = f - v,
                    b = v <= 0.5 && n ? T + 0.5 : T + 1;
                  if (
                    (b > s ? (b = s) : b < 0 && (b = 0),
                    "move" === i || ("tap" === i && n))
                  ) {
                    var m =
                      Math.ceil(b - 1) *
                        ((0, u.unitConvert)(o) + (0, u.unitConvert)(l)) +
                      0.5 * (0, u.unitConvert)(l);
                    r.setData({
                      tipsVisible: !0,
                      actionType: i,
                      scaleIndex: Math.ceil(b),
                      tipsLeft: Math.max(m, 0),
                    });
                  }
                  b !== c && r._trigger("change", { value: b }),
                    r.touchEnd && r.hideTips();
                });
              },
              onTap: function (e) {
                this.properties.disabled || this.onTouch(e, "tap");
              },
              onTouchStart: function () {
                this.touchEnd = !1;
              },
              onTouchMove: function (e) {
                this.onTouch(e, "move"), this.showAlertText();
              },
              onTouchEnd: function () {
                (this.touchEnd = !0), this.hideTips();
              },
              hideTips: function () {
                "move" === this.data.actionType &&
                  this.setData({ tipsVisible: !1, scaleIndex: -1 });
              },
              onSelect: function (e) {
                var t = this,
                  i = e.currentTarget.dataset.value;
                "move" !== this.data.actionType &&
                  (this._trigger("change", { value: i }),
                  setTimeout(function () {
                    return t.setData({ tipsVisible: !1, scaleIndex: -1 });
                  }, 300));
              },
              showAlertText: function () {
                var e = this;
                !0 !== this.data.isVisibleToScreenReader &&
                  (this.setData({ isVisibleToScreenReader: !0 }),
                  setTimeout(function () {
                    e.setData({ isVisibleToScreenReader: !1 });
                  }, 2e3));
              },
            }),
            t
          );
        }
        return t(o);
      })(n.SuperComponent),
      f = (d = (0, s.__decorate)([(0, n.wxComponent)()], d));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/rate/rate.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/rate/rate.js");
