__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/count-down/count-down": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/count-down/count-down"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            size: new Array(1),
            customStyle: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          e,
          f = (C, T, E, B, F, S) => {
            if (e === 1) {
              S("content");
            }
          },
          g,
          i = (C, j, k, l, m, n, T, E, B) => {
            var o = (C, T) => {
                var $B = k;
                var $A = X(D.timeRange)[$B];
                C ||
                K ||
                !!(
                  Z(undefined, "format") ||
                  m ||
                  Z(U.timeRange, $B) ||
                  Z(U.timeData, $A)
                ) ||
                undefined
                  ? T(Y(P(X(b).format)(X(D.timeData)[$A])))
                  : T();
              },
              p,
              r = (C, T) => {
                var $A = D.splitWithUnit;
                var $C = k;
                var $B = X(D.timeRange)[$C];
                C ||
                K ||
                !!U.splitWithUnit ||
                ($A
                  ? !!(m || Z(U.timeRange, $C)) || Z(U.timeDataUnit, $B)
                  : undefined)
                  ? T(Y($A ? X(D.timeDataUnit)[$B] : ":"))
                  : T();
              },
              q = (C, T, E) => {
                if (p === 1) {
                  E(
                    "text",
                    {},
                    (N, C) => {
                      var $A = D.splitWithUnit;
                      if (
                        C ||
                        K ||
                        !!(
                          U.classPrefix ||
                          U.classPrefix ||
                          U.splitWithUnit ||
                          ($A ? undefined : undefined) ||
                          U.prefix
                        ) ||
                        undefined
                      )
                        L(
                          N,
                          Y(D.classPrefix) +
                            "__split " +
                            Y(D.classPrefix) +
                            "__split--" +
                            Y($A ? "text" : "dot") +
                            " " +
                            Y(D.prefix) +
                            "-class-split"
                        );
                    },
                    r
                  );
                }
              };
            E(
              "text",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__item " + Y(D.prefix) + "-class-count"
                  );
              },
              o
            );
            p = D.splitWithUnit || X(D.timeRange).length - 1 !== k ? 1 : 0;
            B(p, q);
          },
          h = (C, T, E, B, F, S) => {
            if (g === 1) {
              S("");
            } else if (g === 2) {
              C || K || U.formattedTime ? T(Y(D.formattedTime)) : T();
            } else {
              F(
                D.timeRange,
                "index",
                U ? U.timeRange : undefined,
                [0, "timeRange"],
                i
              );
            }
          },
          d = (C, T, E, B) => {
            e = D.content !== "default" ? 1 : 0;
            B(e, f);
            g =
              D.content !== "default"
                ? 1
                : D.theme == "default" && !D.splitWithUnit
                ? 2
                : 0;
            B(g, h);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.classPrefix ||
                    U.theme ||
                    U.classPrefix ||
                    U.size ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.theme) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.size) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["size"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.theme) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.size) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-role", "option");
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/count-down/count-down";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/count-down/count-down.js";
define(
  "miniprogram_npm/tdesign-miniprogram/count-down/count-down.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      a = require("../common/src/index"),
      n = m(require("../common/config")),
      o = m(require("./props")),
      u = require("./utils");
    function m(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var c = n.default.prefix,
      h = "".concat(c, "-count-down"),
      l = (function (s) {
        i(n, s);
        var a = r(n);
        function n() {
          var e;
          return (
            t(this, n),
            ((e = a.apply(this, arguments)).externalClasses = [
              "".concat(c, "-class"),
              "".concat(c, "-class-count"),
              "".concat(c, "-class-split"),
            ]),
            (e.properties = o.default),
            (e.observers = {
              time: function () {
                this.reset();
              },
            }),
            (e.data = {
              prefix: c,
              classPrefix: h,
              timeDataUnit: u.TimeDataUnit,
              timeData: (0, u.parseTimeData)(0),
              formattedTime: "0",
            }),
            (e.timeoutId = null),
            (e.isInitialTime = !1),
            (e.lifetimes = {
              detached: function () {
                this.timeoutId &&
                  (clearTimeout(this.timeoutId), (this.timeoutId = null));
              },
            }),
            (e.methods = {
              start: function () {
                this.counting ||
                  ((this.counting = !0),
                  (this.endTime = Date.now() + this.remain),
                  this.doCount());
              },
              pause: function () {
                (this.counting = !1),
                  this.timeoutId && clearTimeout(this.timeoutId);
              },
              reset: function () {
                this.pause(),
                  (this.remain = this.properties.time),
                  this.updateTime(this.remain),
                  this.properties.autoStart && this.remain > 0 && this.start(),
                  (this.isInitialTime = !0);
              },
              getTime: function () {
                return Math.max(this.endTime - Date.now(), 0);
              },
              updateTime: function (e) {
                var t = this.properties.format;
                this.remain = e;
                var i = (0, u.parseTimeData)(e);
                this.triggerEvent("change", i);
                var r = (0, u.parseFormat)(e, t).timeText,
                  s = t.split(":");
                this.setData({
                  timeRange: s,
                  timeData: i,
                  formattedTime: r.replace(/:/g, " : "),
                }),
                  0 === e &&
                    (this.counting || this.isInitialTime) &&
                    (this.pause(),
                    this.triggerEvent("finish"),
                    (this.counting = !1));
              },
              doCount: function () {
                var e = this;
                this.timeoutId = setTimeout(function () {
                  var t = e.getTime();
                  e.properties.millisecond
                    ? e.updateTime(t)
                    : ((0, u.isSameSecond)(t, e.remain) && 0 !== t) ||
                      e.updateTime(t),
                    0 !== t && e.doCount();
                }, 33);
              },
            }),
            e
          );
        }
        return e(n);
      })(a.SuperComponent),
      p = (l = (0, s.__decorate)([(0, a.wxComponent)()], l));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/count-down/count-down.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/count-down/count-down.js");
