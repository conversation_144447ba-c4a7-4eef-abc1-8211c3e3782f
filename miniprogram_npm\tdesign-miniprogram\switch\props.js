Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = {
  customValue: { type: Array, value: [!0, !1] },
  disabled: { type: null, value: void 0 },
  icon: { type: Array, value: [] },
  label: { type: Array, value: [] },
  loading: { type: Boolean, value: !1 },
  size: { type: String, value: "medium" },
  value: { type: null, value: null },
  defaultValue: { type: null, value: null },
};
exports.default = e;
