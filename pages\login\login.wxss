page {
  align-items: center;
  background-color: #fff;
  box-sizing: border-box;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: center;
  padding: 0 60rpx;
}
.title {
  color: #000;
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 80rpx;
  text-align: center;
}
.loing_img {
  margin-bottom: 60rpx;
  text-align: center;
}
.loing_img image {
  border-radius: 20rpx;
  height: 300rpx;
  width: 300rpx;
}
.miaoshu {
  color: #b0abab;
  font-size: 28rpx;
  line-height: 1.5;
  text-align: center;
}
.privacy-modal {
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  display: -webkit-flex;
  display: flex;
  height: 100%;
  justify-content: center;
  left: 0;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 9999;
}
.privacy-content {
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  max-width: 600rpx;
  padding: 40rpx;
  width: 85%;
}
.privacy-title {
  color: #333;
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 30rpx;
  text-align: center;
}
.privacy-desc {
  color: #666;
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 20rpx;
  text-align: justify;
}
.privacy-link {
  color: #195abf;
  font-weight: 700;
  text-decoration: underline;
}
.button-group {
  display: -webkit-flex;
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}
.disagree-btn {
  background: #f5f5f5;
  color: #666;
}
.agree-btn,
.disagree-btn {
  border: none;
  border-radius: 10rpx;
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
}
.agree-btn {
  background: #195abf;
  color: #fff;
}
.agree-btn::after,
.disagree-btn::after {
  border: none;
}
.loading-container {
  left: 50%;
  position: fixed;
  text-align: center;
  top: 50%;
  transform: translate(-50%, -50%);
}
.loading-text {
  color: #999;
  font-size: 28rpx;
}
