__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/articles-info/articles-info": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b = (C) => {},
          d,
          g = (C, T) => {
            C ? T("加载中...") : T();
          },
          f = (C, T, E) => {
            E("text", {}, (N, C) => {}, g);
          },
          i,
          l = (C, T) => {
            C || K || Z(U.articlesData, "content")
              ? T(Y(X(D.articlesData).content))
              : T();
          },
          k = (C, T, E) => {
            E("text", {}, (N, C) => {}, l);
          },
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "article-content");
                },
                k
              );
            }
          },
          m,
          p = (C, q, r, s, t, u, T, E) => {
            var w = (C) => {},
              v = (C, T, E) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "detail-image");
                    if (C || K || s) O(N, "src", q);
                    if (C) O(N, "mode", "widthFix");
                    if (C) O(N, "bindtap", "previewImage");
                    if (C || K || s) R.d(N, "src", q);
                  },
                  w
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_item");
              },
              v
            );
          },
          o = (C, T, E, B, F) => {
            F(
              X(D.articlesData).detail_images,
              "index",
              U ? Z(U.articlesData, "detail_images") : undefined,
              [0, "articlesData", "detail_images"],
              p
            );
          },
          n = (C, T, E) => {
            if (m === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "detail_images");
                },
                o
              );
            }
          },
          h = (C, T, E, B) => {
            i = X(D.articlesData).content ? 1 : 0;
            B(i, j);
            m =
              X(D.articlesData).detail_images &&
              X(X(D.articlesData).detail_images).length > 0
                ? 1
                : 0;
            B(m, n);
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                f
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "article-wrapper");
                },
                h
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.loading ? 1 : 0;
            B(d, e);
          },
          a = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || !!Z(U.articlesData, "title") || undefined)
                  O(N, "title", X(D.articlesData).title || "文章详情");
                if (C) O(N, "background", "#FFF");
              },
              b
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              c
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/articles-info/articles-info.wxss"] = setCssToHead(
    [
      ".",
      [1],
      "loading-container{-webkit-align-items:center;align-items:center;color:#999;display:-webkit-flex;display:flex;height:",
      [0, 400],
      ";-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "article-wrapper{background-color:#fff;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;min-height:100vh;padding:",
      [0, 40],
      " ",
      [0, 30],
      " ",
      [0, 150],
      "}\n.",
      [1],
      "article-title{color:#333;font-size:",
      [0, 36],
      ";font-weight:700;line-height:1.4;margin-bottom:",
      [0, 30],
      "}\n.",
      [1],
      "article-content{background-color:#f5f5f5;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 15],
      ";padding:",
      [0, 25],
      "}\n.",
      [1],
      "detail_images{margin-top:",
      [0, 20],
      "}\n.",
      [1],
      "detail-image{border-radius:",
      [0, 10],
      ";height:",
      [0, 400],
      ";width:100%}\n",
    ],
    undefined,
    { path: "./pages/articles-info/articles-info.wxss" }
  );
}
