__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/date-time-picker/date-time-picker":
      (() => {
        var H = {};
        var S;
        var I = (P) => {
          if (!S) S = Object.assign({}, H);
          return S[P];
        };
        var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
        H[""] = (R, C, D, U) => {
          R.setFnFilter(Q.A, Q.B);
          if (typeof R.setEventListenerWrapper === "function")
            R.setEventListenerWrapper(Q.C);
          var L = R.c,
            M = R.m,
            O = R.r,
            A = {
              locale: new Array(2),
              columnsValue: new Array(1),
              customStyle: new Array(1),
              visible: new Array(1),
              title: new Array(1),
              popupProps: new Array(1),
              header: new Array(1),
              autoClose: new Array(1),
              style: new Array(1),
              cancelBtn: new Array(1),
              usePopup: new Array(1),
              prefix: new Array(1),
              confirmBtn: new Array(1),
            },
            K = U === true,
            d = (C, e, f, g, h, i, T, E) => {
              var j = (C) => {};
              E(
                "t-picker-item",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([Q.a([!!(Z(U.columns, "length") || h) || undefined])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__item", [
                        ["roomly", X(D.columns).length >= 5 && f == 0],
                      ])
                    );
                  if (C || K || g) O(N, "options", e);
                  if (C) O(N, "index", "index");
                  if (C || K || U.formatter) O(N, "format", D.formatter);
                },
                j
              );
            },
            c = (C, T, E, B, F, S) => {
              S("header", (N) => {}, "header");
              F(
                D.columns,
                "index",
                U ? U.columns : undefined,
                [0, "columns"],
                d
              );
              S("footer", (N) => {}, "footer");
            },
            b = (C, T, E) => {
              E(
                "t-picker",
                {},
                (N, C) => {
                  if (C || K || !!(U.prefix || U.classPrefix) || undefined)
                    L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                  A["prefix"][0] = (D, E, T) => {
                    L(N, "class " + Y(D.prefix) + "-class " + Y(D.classPrefix));
                  };
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  };
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  A["visible"][0] = (D, E, T) => {
                    O(N, "visible", D.visible);
                    E(N);
                  };
                  if (C || K || U.columnsValue) O(N, "value", D.columnsValue);
                  A["columnsValue"][0] = (D, E, T) => {
                    O(N, "value", D.columnsValue);
                    E(N);
                  };
                  if (C || K || U.header) O(N, "header", D.header);
                  A["header"][0] = (D, E, T) => {
                    O(N, "header", D.header);
                    E(N);
                  };
                  if (C || K || U.title) O(N, "title", D.title);
                  A["title"][0] = (D, E, T) => {
                    O(N, "title", D.title);
                    E(N);
                  };
                  if (C || K || U.autoClose) O(N, "auto-close", D.autoClose);
                  A["autoClose"][0] = (D, E, T) => {
                    O(N, "auto-close", D.autoClose);
                    E(N);
                  };
                  if (
                    C ||
                    K ||
                    !!(U.confirmBtn || Z(U.locale, "confirm")) ||
                    undefined
                  )
                    O(N, "confirm-btn", D.confirmBtn || X(D.locale).confirm);
                  A["confirmBtn"][0] = A["locale"][0] = (D, E, T) => {
                    O(N, "confirm-btn", D.confirmBtn || X(D.locale).confirm);
                    E(N);
                  };
                  if (
                    C ||
                    K ||
                    !!(U.cancelBtn || Z(U.locale, "cancel")) ||
                    undefined
                  )
                    O(N, "cancel-btn", D.cancelBtn || X(D.locale).cancel);
                  A["cancelBtn"][0] = A["locale"][1] = (D, E, T) => {
                    O(N, "cancel-btn", D.cancelBtn || X(D.locale).cancel);
                    E(N);
                  };
                  if (C || K || U.usePopup) O(N, "use-popup", D.usePopup);
                  A["usePopup"][0] = (D, E, T) => {
                    O(N, "use-popup", D.usePopup);
                    E(N);
                  };
                  if (C || K || U.popupProps) O(N, "popup-props", D.popupProps);
                  A["popupProps"][0] = (D, E, T) => {
                    O(N, "popup-props", D.popupProps);
                    E(N);
                  };
                  if (C) R.v(N, "pick", "onColumnChange", !1, !1, !1, !1);
                  if (C) R.v(N, "change", "onConfirm", !1, !1, !1, !1);
                  if (C) R.v(N, "cancel", "onCancel", !1, !1, !1, !1);
                  if (C)
                    R.v(N, "visible-change", "onVisibleChange", !1, !1, !1, !1);
                  if (C) R.v(N, "close", "onClose", !1, !1, !1, !1);
                },
                c
              );
            };
          return { C: b, B: A };
        };
        return Object.assign(
          function (R) {
            return H[R];
          },
          { _: H }
        );
      })(),
    "miniprogram_npm/tdesign-miniprogram/picker-item/picker-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            style: new Array(1),
            duration: new Array(1),
            prefix: new Array(1),
            offset: new Array(1),
          },
          K = U === true,
          e = (C, f, g, h, i, j, T, E) => {
            var l = (C, T) => {
                var $A = X(D.pickerKeys).label;
                C || K || !!Z(U.pickerKeys, "label") || Z(h, $A)
                  ? T(Y(X(f)[$A]))
                  : T();
              },
              k = (C, T, E, B, F, S) => {
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C || K || !!U.classPrefix || undefined)
                      L(N, Y(D.classPrefix) + "__item-label");
                  },
                  l
                );
                S(
                  C || K || !!i || undefined
                    ? Y("label-suffix--" + Y(g))
                    : undefined
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!(U.curIndex || i) || undefined])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__item", [
                      ["active", D.curIndex == g],
                    ])
                  );
                if (C || K || !!U.pickItemHeight || undefined)
                  R.y(N, "height:" + Y(D.pickItemHeight) + "px");
                if (C || K || i) R.d(N, "index", g);
              },
              k
            );
          },
          d = (C, T, E, B, F) => {
            F(
              D.formatOptions,
              "index",
              U ? U.formatOptions : undefined,
              [0, "formatOptions"],
              e
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrapper");
                if (C || K || !!(U.duration || U.offset) || undefined)
                  R.y(
                    N,
                    "transition:transform " +
                      Y(D.duration) +
                      "ms cubic-bezier(0.215, 0.61, 0.355, 1);transform:translate3d(0, " +
                      Y(D.offset) +
                      "px, 0)"
                  );
                A["duration"][0] = A["offset"][0] = (D, E, T) => {
                  R.y(
                    N,
                    "transition:transform " +
                      Y(D.duration) +
                      "ms cubic-bezier(0.215, 0.61, 0.355, 1);transform:translate3d(0, " +
                      Y(D.offset) +
                      "px, 0)"
                  );
                };
              },
              d
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__group", [])) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["prefix"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__group", [])) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) R.v(N, "touchstart", "onTouchStart", !1, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onTouchMove", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !1, !1, !1, !1);
                if (C) R.v(N, "touchcancel", "onTouchEnd", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/picker/picker": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          f = (C, T, E, B, F, S, J) => {
            var $A = G["miniprogram_npm/tdesign-miniprogram/picker/template"];
            if ($A) $A("")(R, C, D, U).C(C, T, E, B, F, S, J);
          },
          e = (C, T, E, B, F, S, J) => {
            J(f);
          },
          g = (C, T, E, B, F, S, J) => {
            var $A = G["miniprogram_npm/tdesign-miniprogram/picker/template"];
            if ($A) $A("")(R, C, D, U).C(C, T, E, B, F, S, J);
          },
          d = (C, T, E, B, F, S, J) => {
            if (c === 1) {
              E(
                "t-popup",
                {},
                (N, C) => {
                  if (C) L(N, "class");
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C) O(N, "placement", "bottom");
                  if (
                    C ||
                    K ||
                    !!(
                      U.usingCustomNavbar ||
                      Z(U.popupProps, "usingCustomNavbar")
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "using-custom-navbar",
                      D.usingCustomNavbar || X(D.popupProps).usingCustomNavbar
                    );
                  if (
                    C ||
                    K ||
                    !!(Z(U.popupProps, "zIndex") || U.defaultPopUpzIndex) ||
                    undefined
                  )
                    O(
                      N,
                      "z-index",
                      X(D.popupProps).zIndex || D.defaultPopUpzIndex
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(U.popupProps, "overlayProps") || U.defaultPopUpProps
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "overlay-props",
                      X(D.popupProps).overlayProps || D.defaultPopUpProps
                    );
                  if (C)
                    R.v(N, "visible-change", "onPopupChange", !1, !1, !1, !1);
                },
                e
              );
            } else {
              J(g);
            }
          },
          b = (C, T, E, B) => {
            c = D.usePopup ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/date-time-picker/date-time-picker.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-date-time-picker__item--roomly{-webkit-flex:0 0 var(--td-data-time-picker-year-width,",
      [0, 128],
      ");flex:0 0 var(--td-data-time-picker-year-width,",
      [0, 128],
      ");width:var(--td-data-time-picker-year-width,",
      [0, 128],
      ")}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/date-time-picker/date-time-picker.wxss",
    }
  );
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/picker-item/picker-item.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-picker-item__group{-webkit-flex:1;flex:1;height:var(--td-picker-group-height,",
      [0, 400],
      ");overflow:hidden;z-index:1}\n.",
      [1],
      "t-picker-item__wrapper{padding:",
      [0, 144],
      " 0}\n.",
      [1],
      "t-picker-item__item{-webkit-align-items:center;align-items:center;color:var(--td-picker-item-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))));display:-webkit-flex;display:flex;font-size:var(--td-picker-item-font-size,var(--td-font-size-m,",
      [0, 32],
      "));-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "t-picker-item__item-label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\n.",
      [1],
      "t-picker-item__item--active{color:var(--td-picker-item-active-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));font-weight:600}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/picker-item/picker-item.wxss",
    }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/picker/picker.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-picker{background-color:var(--td-picker-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));border-top-left-radius:var(--td-picker-border-radius,",
        [0, 24],
        ");border-top-right-radius:var(--td-picker-border-radius,",
        [0, 24],
        ");position:relative}\n.",
        [1],
        "t-picker__toolbar{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;height:var(--td-picker-toolbar-height,",
        [0, 116],
        ");-webkit-justify-content:space-between;justify-content:space-between;overflow:hidden}\n.",
        [1],
        "t-picker__title{color:var(--td-picker-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));-webkit-flex:1;flex:1;font-size:var(--td-picker-title-font-size,",
        [0, 36],
        ");font-weight:var(--td-picker-title-font-weight,600);line-height:var(--td-picker-title-line-height,",
        [0, 52],
        ");overflow:hidden;text-align:center;text-overflow:ellipsis;white-space:nowrap}\n.",
        [1],
        "t-picker__cancel,.",
        [1],
        "t-picker__confirm{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;font-size:var(--td-picker-button-font-size,",
        [0, 32],
        ");height:100%;-webkit-justify-content:center;justify-content:center;padding:0 ",
        [0, 32],
        ";-webkit-user-select:none;user-select:none}\n.",
        [1],
        "t-picker__cancel{color:var(--td-picker-cancel-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))))}\n.",
        [1],
        "t-picker__confirm{color:var(--td-picker-confirm-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-picker__main{display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;padding-left:",
        [0, 64],
        ";padding-right:",
        [0, 64],
        ";position:relative}\n.",
        [1],
        "t-picker__mask{-webkit-backface-visibility:hidden;backface-visibility:hidden;height:",
        [0, 96],
        ";left:0;pointer-events:none;position:absolute;right:0;z-index:3}\n.",
        [1],
        "t-picker__mask--top{top:0}\n.",
        [1],
        "t-picker__mask--bottom,.",
        [1],
        "t-picker__mask--top{background:linear-gradient(180deg,var(--td-picker-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff))) 0,var(--td-picker-transparent-color) 100%)}\n.",
        [1],
        "t-picker__mask--bottom{bottom:0;-webkit-transform:matrix(1,0,0,-1,0,0);transform:matrix(1,0,0,-1,0,0)}\n.",
        [1],
        "t-picker__indicator{background-color:var(--td-picker-indicator-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));border-radius:var(--td-picker-indicator-border-radius,",
        [0, 12],
        ");left:",
        [0, 32],
        ";pointer-events:none;position:absolute;right:",
        [0, 32],
        ";top:",
        [0, 144],
        "}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/picker/picker.wxss" }
    );
}
