__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/service-list/service-list": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { categoryName: new Array(1) },
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C || K || U.categoryName) O(N, "title", D.categoryName);
                A["categoryName"][0] = (D, E, T) => {
                  O(N, "title", D.categoryName);
                  E(N);
                };
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          e,
          h = (C) => {},
          g = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "40rpx");
                if (C) O(N, "text", "加载中...");
              },
              h
            );
          },
          j = (C, k, l, m, n, o, T, E) => {
            var p = (C) => {};
            E(
              "service-card",
              {},
              (N, C) => {
                if (C || K || m) O(N, "service", k);
                if (C) R.v(N, "contact", "handleContact", !1, !1, !1, !1);
                if (C) R.v(N, "confirm", "handleConfirm", !1, !1, !1, !1);
              },
              p
            );
          },
          k,
          n,
          q = (C) => {},
          p = (C, T, E) => {
            E(
              "t-loading",
              {},
              (N, C) => {
                if (C) O(N, "theme", "circular");
                if (C) O(N, "size", "32rpx");
                if (C) O(N, "text", "加载更多...");
              },
              q
            );
          },
          r = (C, T) => {
            C ? T("点击加载更多") : T();
          },
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-more");
                },
                p
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "load-more-text");
                },
                r
              );
            }
          },
          m = (C, T, E, B) => {
            n = D.serviceLoading ? 1 : 0;
            B(n, o);
          },
          l = (C, T, E) => {
            if (k === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "load-more");
                  if (C) O(N, "bindtap", "loadMoreServices");
                },
                m
              );
            }
          },
          s,
          u = (C, T) => {
            C ? T("没有更多服务了") : T();
          },
          t = (C, T, E) => {
            if (s === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "no-more");
                },
                u
              );
            }
          },
          v,
          y = (C) => {},
          x = (C, T, E) => {
            E(
              "t-empty",
              {},
              (N, C) => {
                if (C) O(N, "description", "暂无相关服务");
              },
              y
            );
          },
          w = (C, T, E) => {
            if (v === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "empty-state");
                },
                x
              );
            }
          },
          i = (C, T, E, B, F) => {
            F(
              D.serviceList,
              "id",
              U ? U.serviceList : undefined,
              [0, "serviceList"],
              j
            );
            k = D.serviceHasMore ? 1 : 0;
            B(k, l);
            s = !D.serviceHasMore && X(D.serviceList).length > 0 ? 1 : 0;
            B(s, t);
            v = X(D.serviceList).length === 0 && !D.serviceLoading ? 1 : 0;
            B(v, w);
          },
          f = (C, T, E) => {
            if (e === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "loading-container");
                },
                g
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "nr_box");
                },
                i
              );
            }
          },
          d = (C, T, E, B) => {
            e = D.serviceLoading && X(D.serviceList).length === 0 ? 1 : 0;
            B(e, f);
          },
          a = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/service-list/service-list.wxss"] = setCssToHead(
    [
      "body{background-color:#f5f5f5;box-sizing:border-box;padding-bottom:",
      [0, 180],
      "}\n.",
      [1],
      "container,body{min-height:100vh}\n.",
      [1],
      "container{width:100%}\n.",
      [1],
      "nr_box{margin:",
      [0, 20],
      " auto;width:95%}\n.",
      [1],
      "loading-container{padding:",
      [0, 100],
      " 0}\n.",
      [1],
      "load-more,.",
      [1],
      "loading-container{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "load-more{margin-top:",
      [0, 20],
      ";padding:",
      [0, 40],
      " 0}\n.",
      [1],
      "loading-more{-webkit-align-items:center;align-items:center;color:#999;display:-webkit-flex;display:flex}\n.",
      [1],
      "load-more-text{background-color:#fff;border:",
      [0, 1],
      " solid #195abf;border-radius:",
      [0, 40],
      ";color:#195abf;font-size:",
      [0, 28],
      ";padding:",
      [0, 20],
      " ",
      [0, 40],
      "}\n.",
      [1],
      "no-more{color:#999;font-size:",
      [0, 24],
      ";padding:",
      [0, 40],
      " 0;text-align:center}\n.",
      [1],
      "empty-state{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;padding:",
      [0, 100],
      " 0}\n",
    ],
    "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/service-list/service-list.wxss:1:86)",
    { path: "./pages/service-list/service-list.wxss" }
  );
}
