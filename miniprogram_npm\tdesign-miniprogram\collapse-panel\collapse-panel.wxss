@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-collapse-panel {
  background-color: var(
    --td-collapse-panel-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
}
.t-collapse-panel--disabled {
  pointer-events: none;
}
.t-collapse-panel--disabled .t-collapse-panel__content,
.t-collapse-panel--disabled .t-collapse-panel__header {
  opacity: 0.3;
}
.t-collapse-panel--top {
  display: -webkit-flex;
  display: flex;
  flex-direction: column-reverse;
}
.t-collapse-panel__header {
  align-items: center;
  color: var(
    --td-collapse-header-text-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  display: -webkit-flex;
  display: flex;
  height: var(--td-collapse-header-height, auto);
  justify-content: space-between;
  padding-left: var(--td-collapse-horizontal-padding, 32rpx);
  position: relative;
}
.t-collapse-panel__header--top {
  position: relative;
}
.t-collapse-panel__header--top::after {
  background-color: var(
    --td-collapse-border-color,
    var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: unset;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: 0;
  transform: scaleY(0.5);
}
.t-collapse-panel__header--bottom {
  position: relative;
}
.t-collapse-panel__header--bottom::after {
  background-color: var(
    --td-collapse-border-color,
    var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: unset;
  transform: scaleY(0.5);
}
.t-collapse-panel__header::after {
  left: 32rpx;
}
.t-collapse-panel__header-right {
  align-items: center;
  display: -webkit-inline-flex;
  display: inline-flex;
  height: 100%;
}
.t-collapse-panel__header-icon {
  color: var(
    --td-collapse-icon-color,
    var(--td-font-gray-3, rgba(0, 0, 0, 0.4))
  );
  height: 100%;
  padding-left: 8px;
  padding-right: 8px;
  width: 44px;
}
.t-collapse-panel__extra {
  font-size: var(--td-collapse-extra-font-size, var(--td-font-size-m, 32rpx));
}
.t-collapse-panel__body {
  position: relative;
}
.t-collapse-panel__body::after {
  background-color: var(
    --td-collapse-border-color,
    var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: unset;
  transform: scaleY(0.5);
}
.t-collapse-panel__wrapper {
  height: 0;
  overflow: hidden;
}
.t-collapse-panel__content {
  color: var(
    --td-collapse-content-text-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  font-size: var(
    --td-collapse-content-font-size,
    var(--td-font-size-base, 28rpx)
  );
  line-height: var(--td-collapse-content-line-height, 1.5);
  padding: var(--td-collapse-content-padding, 32rpx);
}
.t-collapse-panel__content--disabled {
  color: var(
    --td-text-color-disabled,
    var(--td-font-gray-4, rgba(0, 0, 0, 0.26))
  );
}
.t-collapse-panel__content--expanded.t-collapse-panel__content--bottom {
  position: relative;
}
.t-collapse-panel__content--expanded.t-collapse-panel__content--bottom::after {
  background-color: var(
    --td-collapse-border-color,
    var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: 0;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: unset;
  transform: scaleY(0.5);
}
.t-collapse-panel__content--expanded.t-collapse-panel__content--top {
  position: relative;
}
.t-collapse-panel__content--expanded.t-collapse-panel__content--top::after {
  background-color: var(
    --td-collapse-border-color,
    var(--td-border-level-1-color, var(--td-gray-color-3, #e7e7e7))
  );
  bottom: unset;
  content: "";
  display: block;
  height: 1px;
  left: unset;
  left: 0;
  position: absolute;
  right: unset;
  right: 0;
  top: 0;
  transform: scaleY(0.5);
}
.t-collapse-panel__arrow--top {
  transform: rotate(180deg);
}
.class-title {
  font-size: var(--td-collapse-title-font-size, var(--td-font-size-m, 32rpx));
}
.class-note--disabled,
.class-right-icon--disabled,
.class-title--disabled {
  color: var(
    --td-text-color-disabled,
    var(--td-font-gray-4, rgba(0, 0, 0, 0.26))
  );
}
