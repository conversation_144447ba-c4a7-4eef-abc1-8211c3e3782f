@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-back-top {
  align-items: center;
  background-color: initial;
  box-sizing: border-box;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  height: auto;
  justify-content: center;
  overflow: hidden;
  transition: height 0.2s;
}
.t-back-top--fixed {
  bottom: calc(var(--td-spacer-2, 32rpx) + env(safe-area-inset-bottom));
  position: fixed;
  right: var(--td-spacer, 16rpx);
}
.t-back-top--round,
.t-back-top--round-dark {
  border-radius: var(
    --td-back-top-round-border-radius,
    var(--td-radius-circle, 50%)
  );
  height: 96rpx;
  width: 96rpx;
}
.t-back-top--half-round,
.t-back-top--round {
  background-color: var(
    --td-back-top-round-bg-color,
    var(--td-bg-color-container, var(--td-font-white-1, #fff))
  );
  border: 1rpx solid
    var(
      --td-back-top-round-border-color,
      var(--td-component-border, var(--td-gray-color-4, #dcdcdc))
    );
  color: var(
    --td-back-top-round-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
}
.t-back-top--half-round-dark,
.t-back-top--round-dark {
  background-color: var(
    --td-back-top-round-dark-bg-color,
    var(--td-gray-color-13, #242424)
  );
  color: var(
    --td-back-top-round-dark-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
}
.t-back-top--half-round,
.t-back-top--half-round-dark {
  border-radius: 0;
  border-bottom-left-radius: var(
    --td-back-top-half-round-border-radius,
    var(--td-radius-round, 999px)
  );
  border-top-left-radius: var(
    --td-back-top-half-round-border-radius,
    var(--td-radius-round, 999px)
  );
  flex-direction: row;
  height: 80rpx;
  right: 0;
  width: 120rpx;
}
.t-back-top__text--half-round,
.t-back-top__text--half-round-dark,
.t-back-top__text--round,
.t-back-top__text--round-dark {
  font-size: var(--td-font-size, 20rpx);
  line-height: 24rpx;
}
.t-back-top__text--half-round,
.t-back-top__text--half-round-dark {
  width: 48rpx;
}
.t-back-top__icon:not(:empty) + .t-back-top__text--half-round,
.t-back-top__icon:not(:empty) + .t-back-top__text--half-round-dark {
  margin-left: 8rpx;
}
.t-back-top__icon {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  font-size: 44rpx;
  justify-content: center;
}
