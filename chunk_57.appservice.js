__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/tag/tag": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            className: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            tagStyle: new Array(1),
          },
          K = U === true,
          e,
          g,
          h = (C, T, E, B, F, S, J) => {
            var $A = I(g);
            if (g && $A)
              $A(
                R,
                C,
                Object.assign({ tClass: D.prefix + "-icon" }, X(D._icon), {}),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              g = "icon";
              B(g, h);
            }
          },
          d = (C, T, E, B, F, S) => {
            e = D._icon ? 1 : 0;
            B(e, f);
            S("icon");
          },
          i = (C, T, E, B, F, S) => {
            S("");
          },
          j,
          l,
          m = (C, T, E, B, F, S, J) => {
            var $A = I(l);
            if (l && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass:
                      D.classPrefix + "__icon-close " + D.prefix + "-icon",
                    bindclick: "handleClose",
                    ariaRole: "button",
                    ariaLabel: "关闭",
                  },
                  X(D._closable),
                  {}
                ),
                K ||
                  (U
                    ? U._closable === true ||
                      Object.assign(
                        { tClass: !!(U.classPrefix || U.prefix) || undefined },
                        X(U._closable),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          k = (C, T, E, B, F, S, J) => {
            if (j === 1) {
              l = "icon";
              B(l, m);
            } else {
              S("closable");
            }
          },
          c = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon");
                if (C || K || undefined) O(N, "aria-hidden", true);
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__text");
              },
              i
            );
            j = D._closable ? 1 : 0;
            B(j, k);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.className || U.prefix) || undefined)
                  L(N, Y(D.className) + " class " + Y(D.prefix) + "-class");
                A["className"][0] = (D, E, T) => {
                  L(N, Y(D.className) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([U.tagStyle, U.style, U.customStyle])
                  ) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.tagStyle, D.style, D.customStyle]));
                A["tagStyle"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(a)._style)([D.tagStyle, D.style, D.customStyle])
                      );
                    };
                if (C) R.v(N, "tap", "handleClick", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/tag/tag";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/tag/tag.js";
define(
  "miniprogram_npm/tdesign-miniprogram/tag/tag.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      s = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      c = require("../common/src/index"),
      r = u(require("../common/config")),
      l = u(require("./props")),
      n = require("../common/utils"),
      o = require("../common/validator");
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var d = r.default.prefix,
      h = "".concat(d, "-tag"),
      p = (function (i) {
        a(r, i);
        var c = s(r);
        function r() {
          var e;
          return (
            t(this, r),
            ((e = c.apply(this, arguments)).data = {
              prefix: d,
              classPrefix: h,
              className: "",
              tagStyle: "",
            }),
            (e.properties = l.default),
            (e.externalClasses = ["".concat(d, "-class")]),
            (e.options = { multipleSlots: !0 }),
            (e.lifetimes = {
              attached: function () {
                this.setClass(), this.setTagStyle();
              },
            }),
            (e.observers = {
              "size, shape, theme, variant, closable, disabled": function () {
                this.setClass();
              },
              maxWidth: function () {
                this.setTagStyle();
              },
              icon: function (e) {
                this.setData({ _icon: (0, n.calcIcon)(e) });
              },
              closable: function (e) {
                this.setData({ _closable: (0, n.calcIcon)(e, "close") });
              },
            }),
            (e.methods = {
              setClass: function () {
                var e = this.data,
                  t = e.prefix,
                  a = e.classPrefix,
                  s = this.properties,
                  i = s.size,
                  c = s.shape,
                  r = s.theme,
                  l = s.variant,
                  o = s.closable,
                  u = s.disabled,
                  d = (0, n.classNames)([
                    a,
                    "".concat(a, "--").concat(r || "default"),
                    "".concat(a, "--").concat(l),
                    o
                      ? "".concat(a, "--closable ").concat(t, "-is-closable")
                      : "",
                    u
                      ? "".concat(a, "--disabled ").concat(t, "-is-disabled")
                      : "",
                    "".concat(a, "--").concat(i),
                    "".concat(a, "--").concat(c),
                  ]);
                this.setData({ className: d });
              },
              setTagStyle: function () {
                var e = this.properties.maxWidth;
                if (!e) return "";
                var t = (0, o.isNumber)(e) ? "".concat(e, "px") : e;
                this.setData({ tagStyle: "max-width:".concat(t, ";") });
              },
              handleClick: function (e) {
                this.data.disabled || this.triggerEvent("click", e);
              },
              handleClose: function (e) {
                this.data.disabled || this.triggerEvent("close", e);
              },
            }),
            e
          );
        }
        return e(r);
      })(c.SuperComponent),
      f = (p = (0, i.__decorate)([(0, c.wxComponent)()], p));
    exports.default = f;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/tag/tag.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/tag/tag.js");
