.loading-container {
  align-items: center;
  color: #999;
  display: -webkit-flex;
  display: flex;
  height: 400rpx;
  justify-content: center;
}
.article-wrapper {
  background-color: #fff;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 40rpx 30rpx 150rpx;
}
.article-title {
  color: #333;
  font-size: 36rpx;
  font-weight: 700;
  line-height: 1.4;
  margin-bottom: 30rpx;
}
.article-content {
  background-color: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  border-radius: 15rpx;
  padding: 25rpx;
}
.detail_images {
  margin-top: 20rpx;
}
.detail-image {
  border-radius: 10rpx;
  height: 400rpx;
  width: 100%;
}
