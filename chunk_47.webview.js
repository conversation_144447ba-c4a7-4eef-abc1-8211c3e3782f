__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/search/search": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/search/search"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            holdKeyboard: new Array(1),
            placeholder: new Array(1),
            selectionStart: new Array(1),
            center: new Array(2),
            cursorSpacing: new Array(1),
            confirmType: new Array(1),
            selectionEnd: new Array(1),
            shape: new Array(1),
            adjustPosition: new Array(1),
            placeholderClass: new Array(1),
            customStyle: new Array(1),
            confirmHold: new Array(1),
            placeholderStyle: new Array(1),
            cursor: new Array(1),
            readonly: new Array(1),
            type: new Array(1),
            maxlength: new Array(1),
            disabled: new Array(2),
            alwaysEmbed: new Array(1),
            style: new Array(1),
            focus: new Array(2),
          },
          K = U === true,
          f,
          h = (C) => {},
          g = (C, T, E, B, F, S) => {
            if (f === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C || K || !!(U.prefix || U.prefix) || undefined)
                    L(N, Y(D.prefix) + "-icon " + Y(D.prefix) + "-class-left");
                  if (C || K || U.leftIcon) O(N, "name", D.leftIcon);
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                h
              );
            } else {
              S("left-icon");
            }
          },
          i = (C) => {},
          j,
          m = (C) => {},
          l = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C) O(N, "name", "close-circle-filled");
                if (C) O(N, "size", "inherit");
                if (C) O(N, "color", "inherit");
              },
              m
            );
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__clear hotspot-expanded " +
                        Y(D.prefix) +
                        "-class-clear"
                    );
                  if (C) O(N, "aria-role", "button");
                  if (C) O(N, "aria-label", "清除");
                  if (C) R.v(N, "tap", "handleClear", !0, !1, !1, !1);
                },
                l
              );
            }
          },
          e = (C, T, E, B) => {
            f = D.leftIcon ? 1 : 0;
            B(f, g);
            E(
              "input",
              {},
              (N, C) => {
                var $A = D.disabled;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    U.prefix ||
                    U.disabled ||
                    ($A ? !!U.prefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.prefix) +
                      "-input__keyword " +
                      Y(D.prefix) +
                      "-class-input " +
                      Y($A ? D.prefix + "-input--disabled" : "")
                  );
                A["disabled"][1] = (D, E, T) => {
                  var $B = D.disabled;
                  L(
                    N,
                    Y(D.prefix) +
                      "-input__keyword " +
                      Y(D.prefix) +
                      "-class-input " +
                      Y($B ? D.prefix + "-input--disabled" : "")
                  );
                };
                if (C || K || U.type) O(N, "type", D.type);
                A["type"][0] = (D, E, T) => {
                  O(N, "type", D.type);
                  E(N);
                };
                if (C) O(N, "name", "input");
                if (C || K || U.maxlength) O(N, "maxlength", D.maxlength);
                A["maxlength"][0] = (D, E, T) => {
                  O(N, "maxlength", D.maxlength);
                  E(N);
                };
                if (C || K || !!(U.disabled || U.readonly) || undefined)
                  O(N, "disabled", D.disabled || D.readonly);
                A["disabled"][0] = A["readonly"][0] = (D, E, T) => {
                  O(N, "disabled", D.disabled || D.readonly);
                  E(N);
                };
                if (C || K || U.focus) O(N, "focus", D.focus);
                A["focus"][1] = (D, E, T) => {
                  O(N, "focus", D.focus);
                  E(N);
                };
                if (C || K || U.value) O(N, "value", D.value);
                if (C || K || U.confirmType)
                  O(N, "confirm-type", D.confirmType);
                A["confirmType"][0] = (D, E, T) => {
                  O(N, "confirm-type", D.confirmType);
                  E(N);
                };
                if (C || K || U.confirmHold)
                  O(N, "confirm-hold", D.confirmHold);
                A["confirmHold"][0] = (D, E, T) => {
                  O(N, "confirm-hold", D.confirmHold);
                  E(N);
                };
                if (C || K || U.cursor) O(N, "cursor", D.cursor);
                A["cursor"][0] = (D, E, T) => {
                  O(N, "cursor", D.cursor);
                  E(N);
                };
                if (C || K || U.adjustPosition)
                  O(N, "adjust-position", D.adjustPosition);
                A["adjustPosition"][0] = (D, E, T) => {
                  O(N, "adjust-position", D.adjustPosition);
                  E(N);
                };
                if (C || K || U.alwaysEmbed)
                  O(N, "always-embed", D.alwaysEmbed);
                A["alwaysEmbed"][0] = (D, E, T) => {
                  O(N, "always-embed", D.alwaysEmbed);
                  E(N);
                };
                if (C || K || U.selectionStart)
                  O(N, "selection-start", D.selectionStart);
                A["selectionStart"][0] = (D, E, T) => {
                  O(N, "selection-start", D.selectionStart);
                  E(N);
                };
                if (C || K || U.selectionEnd)
                  O(N, "selection-end", D.selectionEnd);
                A["selectionEnd"][0] = (D, E, T) => {
                  O(N, "selection-end", D.selectionEnd);
                  E(N);
                };
                if (C || K || U.holdKeyboard)
                  O(N, "hold-keyboard", D.holdKeyboard);
                A["holdKeyboard"][0] = (D, E, T) => {
                  O(N, "hold-keyboard", D.holdKeyboard);
                  E(N);
                };
                if (C || K || U.cursorSpacing)
                  O(N, "cursor-spacing", D.cursorSpacing);
                A["cursorSpacing"][0] = (D, E, T) => {
                  O(N, "cursor-spacing", D.cursorSpacing);
                  E(N);
                };
                if (C || K || U.placeholder) O(N, "placeholder", D.placeholder);
                A["placeholder"][0] = (D, E, T) => {
                  O(N, "placeholder", D.placeholder);
                  E(N);
                };
                if (C || K || U.placeholderStyle)
                  O(N, "placeholder-style", D.placeholderStyle);
                A["placeholderStyle"][0] = (D, E, T) => {
                  O(N, "placeholder-style", D.placeholderStyle);
                  E(N);
                };
                var $B = D.center;
                if (
                  C ||
                  K ||
                  !!(
                    U.placeholderClass ||
                    U.classPrefix ||
                    U.classPrefix ||
                    U.center ||
                    ($B ? undefined : undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "placeholder-class",
                    Y(D.placeholderClass) +
                      " " +
                      Y(D.classPrefix) +
                      "__placeholder " +
                      Y(D.classPrefix) +
                      "__placeholder--" +
                      Y($B ? "center" : "normal")
                  );
                A["placeholderClass"][0] = A["center"][1] = (D, E, T) => {
                  var $C = D.center;
                  O(
                    N,
                    "placeholder-class",
                    Y(D.placeholderClass) +
                      " " +
                      Y(D.classPrefix) +
                      "__placeholder " +
                      Y(D.classPrefix) +
                      "__placeholder--" +
                      Y($C ? "center" : "normal")
                  );
                  E(N);
                };
                if (C) R.v(N, "input", "onInput", !1, !1, !1, !1);
                if (C) R.v(N, "focus", "onFocus", !1, !1, !1, !1);
                if (C) R.v(N, "blur", "onBlur", !1, !1, !1, !1);
                if (C) R.v(N, "confirm", "onConfirm", !1, !1, !1, !1);
              },
              i
            );
            j = D.value !== "" && D.clearable && D.showClearIcon ? 1 : 0;
            B(j, k);
          },
          n,
          p = (C, T) => {
            C || K || U.action ? T(Y(D.action)) : T();
          },
          o = (C, T, E, B, F, S) => {
            if (n === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__search-action " +
                        Y(D.prefix) +
                        "-class-action"
                    );
                  if (C) O(N, "aria-role", "button");
                  if (C) R.v(N, "tap", "onActionClick", !0, !1, !1, !1);
                },
                p
              );
            } else {
              S("action");
            }
          },
          d = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.focus;
                var $B = D.center;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.prefix ||
                    U.focus ||
                    ($A ? undefined : undefined) ||
                    U.classPrefix ||
                    U.center ||
                    ($B ? undefined : undefined) ||
                    U.classPrefix ||
                    U.shape ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__input-box " +
                      Y(D.prefix) +
                      "-" +
                      Y($A ? "is-focused" : "not-focused") +
                      " " +
                      Y(D.classPrefix) +
                      "__input-box--" +
                      Y($B ? "center" : "") +
                      " " +
                      Y(D.classPrefix) +
                      "__input-box--" +
                      Y(D.shape) +
                      " " +
                      Y(D.prefix) +
                      "-class-input-container"
                  );
                A["focus"][0] =
                  A["center"][0] =
                  A["shape"][0] =
                    (D, E, T) => {
                      var $C = D.focus;
                      var $D = D.center;
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__input-box " +
                          Y(D.prefix) +
                          "-" +
                          Y($C ? "is-focused" : "not-focused") +
                          " " +
                          Y(D.classPrefix) +
                          "__input-box--" +
                          Y($D ? "center" : "") +
                          " " +
                          Y(D.classPrefix) +
                          "__input-box--" +
                          Y(D.shape) +
                          " " +
                          Y(D.prefix) +
                          "-class-input-container"
                      );
                    };
              },
              e
            );
            n = D.action ? 1 : 0;
            B(n, o);
          },
          q,
          t = (C, u, v, w, x, y, T, E) => {
            var A0 = (C) => {},
              z = (C, T, E) => {
                E(
                  "rich-text",
                  {},
                  (N, C) => {
                    if (
                      C ||
                      K ||
                      !!(Z(undefined, "highLight") || w || U.value) ||
                      undefined
                    )
                      O(N, "nodes", P(X(b).highLight)(u, D.value));
                  },
                  A0,
                  "title"
                );
              };
            E(
              "t-cell",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__result-item");
                if (C) O(N, "hover", true);
                if (C) O(N, "aria-role", "option");
                if (C || K || x) R.d(N, "index", v);
                if (C) R.v(N, "tap", "onSelectResultItem", !1, !1, !1, !1);
              },
              z
            );
          },
          s = (C, T, E, B, F) => {
            F(
              D.resultList,
              "index",
              U ? U.resultList : undefined,
              [0, "resultList"],
              t
            );
          },
          r = (C, T, E) => {
            if (q === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__result-list");
                  if (C) O(N, "aria-role", "listbox");
                },
                s
              );
            }
          },
          c = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    "class " + Y(D.classPrefix) + " " + Y(D.prefix) + "-class"
                  );
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
            q = D.isShowResultList && !D.isSelected ? 1 : 0;
            B(q, r);
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/search/search.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-search{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:space-between;justify-content:space-between}\n.",
        [1],
        "t-search__label{color:var(--td-search-label-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));padding:",
        [0, 8],
        "}\n.",
        [1],
        "t-search__input-box{-webkit-align-items:center;align-items:center;background:var(--td-search-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));border:",
        [0, 2],
        " solid var(--td-search-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)));box-sizing:border-box;display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;height:var(--td-search-height,",
        [0, 80],
        ");padding:var(--td-search-padding,",
        [0, 16],
        " ",
        [0, 24],
        ")}\n.",
        [1],
        "t-search__input-box.",
        [1],
        "t-is-focused{border-color:var(--td-search-bg-color,var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3)))}\n.",
        [1],
        "t-search__input-box--round{border-radius:calc(var(--td-search-height,",
        [0, 80],
        ")/ 2)}\n.",
        [1],
        "t-search__input-box--square{border-radius:var(--td-search-square-radius,var(--td-radius-default,",
        [0, 12],
        "))}\n.",
        [1],
        "t-search__input-box--center{text-align:center}\n.",
        [1],
        "t-search__input-box .",
        [1],
        "t-input__keyword{color:var(--td-search-text-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:inline-block;-webkit-flex:1;flex:1;font-size:var(--td-search-font-size,var(--td-font-size-m,",
        [0, 32],
        "));line-height:",
        [0, 48],
        ";min-height:",
        [0, 48],
        ";padding-left:",
        [0, 10],
        "}\n.",
        [1],
        "t-search__input-box .",
        [1],
        "t-input--disabled{-webkit-text-fill-color:currentColor;color:var(--td-search-disabled-text-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))));cursor:not-allowed;opacity:1}\n.",
        [1],
        "t-search__input-box .",
        [1],
        "t-icon{color:var(--td-search-icon-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-search-icon-font-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-search__clear{color:var(--td-search-clear-icon-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))));font-size:var(--td-search-clear-icon-font-size,",
        [0, 48],
        ");margin-left:10px;position:relative}\n.",
        [1],
        "t-search__search-action{color:var(--td-search-action-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));font-size:var(--td-search-font-size,var(--td-font-size-m,",
        [0, 32],
        "));margin-left:",
        [0, 30],
        "}\n.",
        [1],
        "t-search__placeholder{color:var(--td-search-placeholder-color,var(--td-text-color-placeholder,var(--td-font-gray-3,rgba(0,0,0,.4))))}\n.",
        [1],
        "t-search__placeholder--center{text-align:center}\n.",
        [1],
        "t-search__result-item--highLight{color:var(--td-search-result-high-light-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-search__result-list .",
        [1],
        "t-search__result-item{padding-left:0}\n.",
        [1],
        "t-search__result-list .",
        [1],
        "t-search__result-item::after{left:0}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/search/search.wxss" }
    );
}
