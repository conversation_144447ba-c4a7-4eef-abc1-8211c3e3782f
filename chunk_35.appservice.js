__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/link/link": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            disabled: new Array(3),
            classPrefix: new Array(4),
            style: new Array(1),
            customStyle: new Array(1),
            hover: new Array(1),
            className: new Array(1),
            navigatorProps: new Array(12),
          },
          K = U === true,
          e,
          g,
          h = (C, T, E, B, F, S, J) => {
            var $A = I(g);
            if (g && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-prefix-icon", ariaHidden: true },
                  X(D._prefixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._prefixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._prefixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              g = "icon";
              B(g, h);
            }
          },
          d = (C, T, E, B, F, S) => {
            S("prefix-icon");
            e = D._prefixIcon ? 1 : 0;
            B(e, f);
          },
          j,
          k = (C, T) => {
            if (j === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          i = (C, T, E, B, F, S) => {
            j = D.content ? 1 : 0;
            B(j, k);
            S("content");
            S("");
          },
          m,
          o,
          p = (C, T, E, B, F, S, J) => {
            var $A = I(o);
            if (o && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-suffix-icon", ariaHidden: true },
                  X(D._suffixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._suffixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._suffixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          n = (C, T, E, B, F, S, J) => {
            if (m === 1) {
              o = "icon";
              B(o, p);
            }
          },
          l = (C, T, E, B, F, S) => {
            S("suffix-icon");
            m = D._suffixIcon ? 1 : 0;
            B(m, n);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__prefix-icon " +
                      Y(D.prefix) +
                      "-class-prefix-icon"
                  );
                A["classPrefix"][1] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__prefix-icon " +
                      Y(D.prefix) +
                      "-class-prefix-icon"
                  );
                };
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                A["classPrefix"][2] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                };
              },
              i
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__suffix-icon " +
                      Y(D.prefix) +
                      "-class-suffix-icon"
                  );
                A["classPrefix"][3] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__suffix-icon " +
                      Y(D.prefix) +
                      "-class-suffix-icon"
                  );
                };
              },
              l
            );
          },
          b = (C, T, E) => {
            E(
              "navigator",
              {},
              (N, C) => {
                if (C || K || !!(U.className || U.prefix) || undefined)
                  L(N, Y(D.className) + " class " + Y(D.prefix) + "-class");
                A["className"][0] = (D, E, T) => {
                  L(N, Y(D.className) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C || K || Z(U.navigatorProps, "target"))
                  O(N, "target", X(D.navigatorProps).target);
                A["navigatorProps"][0] = (D, E, T) => {
                  O(N, "target", X(D.navigatorProps).target);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(U.disabled || Z(U.navigatorProps, "url")) ||
                  undefined
                )
                  O(N, "url", !D.disabled && X(D.navigatorProps).url);
                A["disabled"][0] = A["navigatorProps"][1] = (D, E, T) => {
                  O(N, "url", !D.disabled && X(D.navigatorProps).url);
                  E(N);
                };
                if (C || K || !!Z(U.navigatorProps, "openType") || undefined)
                  O(N, "open-type", X(D.navigatorProps).openType || "navigate");
                A["navigatorProps"][2] = (D, E, T) => {
                  O(N, "open-type", X(D.navigatorProps).openType || "navigate");
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "delta"))
                  O(N, "delta", X(D.navigatorProps).delta);
                A["navigatorProps"][3] = (D, E, T) => {
                  O(N, "delta", X(D.navigatorProps).delta);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "appId"))
                  O(N, "app-id", X(D.navigatorProps).appId);
                A["navigatorProps"][4] = (D, E, T) => {
                  O(N, "app-id", X(D.navigatorProps).appId);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "path"))
                  O(N, "path", X(D.navigatorProps).path);
                A["navigatorProps"][5] = (D, E, T) => {
                  O(N, "path", X(D.navigatorProps).path);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "extraData"))
                  O(N, "extra-data", X(D.navigatorProps).extraData);
                A["navigatorProps"][6] = (D, E, T) => {
                  O(N, "extra-data", X(D.navigatorProps).extraData);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "version"))
                  O(N, "version", X(D.navigatorProps).version);
                A["navigatorProps"][7] = (D, E, T) => {
                  O(N, "version", X(D.navigatorProps).version);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "shortLink"))
                  O(N, "short-link", X(D.navigatorProps).shortLink);
                A["navigatorProps"][8] = (D, E, T) => {
                  O(N, "short-link", X(D.navigatorProps).shortLink);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(
                    U.hover ||
                    U.disabled ||
                    U.classPrefix ||
                    U.prefix ||
                    Z(U.navigatorProps, "hoverClass")
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "hover-class",
                    Y(D.hover && !D.disabled && D.classPrefix + "--hover") +
                      " " +
                      Y(D.prefix) +
                      "-class-hover " +
                      Y(X(D.navigatorProps).hoverClass)
                  );
                A["hover"][0] =
                  A["disabled"][1] =
                  A["classPrefix"][0] =
                  A["navigatorProps"][9] =
                    (D, E, T) => {
                      O(
                        N,
                        "hover-class",
                        Y(D.hover && !D.disabled && D.classPrefix + "--hover") +
                          " " +
                          Y(D.prefix) +
                          "-class-hover " +
                          Y(X(D.navigatorProps).hoverClass)
                      );
                      E(N);
                    };
                if (C)
                  O(
                    N,
                    "hover-stop-propagation",
                    "navigatorProps.hoverStopPropagation"
                  );
                if (C || K || Z(U.navigatorProps, "hoverStartTime"))
                  O(N, "hover-start-time", X(D.navigatorProps).hoverStartTime);
                A["navigatorProps"][10] = (D, E, T) => {
                  O(N, "hover-start-time", X(D.navigatorProps).hoverStartTime);
                  E(N);
                };
                if (C || K || Z(U.navigatorProps, "hoverStayTime"))
                  O(N, "hover-stay-time", X(D.navigatorProps).hoverStayTime);
                A["navigatorProps"][11] = (D, E, T) => {
                  O(N, "hover-stay-time", X(D.navigatorProps).hoverStayTime);
                  E(N);
                };
                if (C) O(N, "bindsuccess", "onSuccess");
                if (C) O(N, "bindfail", "onFail");
                if (C) O(N, "bindcomplete", "onComplete");
                if (C || K || U.disabled) O(N, "aria-disabled", D.disabled);
                A["disabled"][2] = (D, E, T) => {
                  O(N, "aria-disabled", D.disabled);
                  E(N);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/message-item/message-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a =
        R["miniprogram_npm/tdesign-miniprogram/message-item/message-item"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          d,
          h,
          j,
          k = (C, T, E, B, F, S, J) => {
            var $A = I(j);
            if (j && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-icon", ariaHidden: true },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          i = (C, T, E, B, F, S, J) => {
            if (h === 1) {
              j = "icon";
              B(j, k);
            }
          },
          g = (C, T, E, B, F, S) => {
            S("icon");
            h = D._icon ? 1 : 0;
            B(h, i);
          },
          n,
          o = (C, T) => {
            if (n === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          m = (C, T, E, B, F, S) => {
            n = D.content ? 1 : 0;
            B(n, o);
            S("content");
            S("");
          },
          l = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__text " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C || K || U.animation) O(N, "animation", D.animation);
                if (C || K || !!U.classPrefix || undefined)
                  R.i(N, Y(D.classPrefix) + "__text");
              },
              m
            );
          },
          p,
          r = (C) => {},
          q = (C, T, E) => {
            if (p === 1) {
              E(
                "t-link",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) + "__link " + Y(D.prefix) + "-class-link"
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([Z(U._link, "style"), Z(U._link, "customStyle")])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(b)._style)([X(D._link).style, X(D._link).customStyle])
                    );
                  if (C || K || !!Z(U._link, "disabled") || undefined)
                    O(N, "disabled", X(D._link).disabled || false);
                  if (C || K || !!Z(U._link, "hover") || undefined)
                    O(N, "hover", X(D._link).hover || true);
                  if (C || K || !!Z(U._link, "theme") || undefined)
                    O(N, "theme", X(D._link).theme || "primary");
                  if (C || K || !!Z(U._link, "size") || undefined)
                    O(N, "size", X(D._link).size || "medium");
                  if (C || K || !!Z(U._link, "prefixIcon") || undefined)
                    O(N, "prefixIcon", X(D._link).prefixIcon || false);
                  if (C || K || !!Z(U._link, "suffixIcon") || undefined)
                    O(N, "suffixIcon", X(D._link).suffixIcon || false);
                  if (C || K || !!Z(U._link, "underline") || undefined)
                    O(N, "underline", X(D._link).underline || false);
                  if (C || K || !!Z(U._link, "content") || undefined)
                    O(N, "content", X(D._link).content || "");
                  if (C || K || !!Z(U._link, "navigatorProps") || undefined)
                    O(N, "navigatorProps", X(D._link).navigatorProps || null);
                  if (C) R.v(N, "complete", "handleLinkClick", !1, !1, !1, !1);
                },
                r
              );
            }
          },
          t,
          v,
          w = (C, T, E, B, F, S, J) => {
            var $A = I(v);
            if (v && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass: D.prefix + "-class-close-btn",
                    ariaRole: "button",
                    ariaLabel: "关闭",
                  },
                  X(D._closeBtn),
                  {}
                ),
                K ||
                  (U
                    ? U._closeBtn === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._closeBtn),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          u = (C, T, E, B, F, S, J) => {
            if (t === 1) {
              v = "icon";
              B(v, w);
            }
          },
          s = (C, T, E, B, F, S) => {
            S("close-btn");
            t = D._closeBtn ? 1 : 0;
            B(t, u);
          },
          f = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon--left");
              },
              g
            );
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.marquee;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.marquee ||
                    ($A ? !!U.classPrefix || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__text-wrap " +
                      Y($A ? D.classPrefix + "__text-nowrap" : "")
                  );
                if (C || K || !!U.align || undefined)
                  R.y(N, "text-align:" + Y(D.align));
                if (C || K || !!U.classPrefix || undefined)
                  R.i(N, Y(D.classPrefix) + "__text-wrap");
              },
              l
            );
            p = D._link && X(D._link).content ? 1 : 0;
            B(p, q);
            S("link");
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon--right");
                if (C) R.v(N, "tap", "handleClose", !1, !1, !1, !1);
              },
              s
            );
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.prefix ||
                      U.classPrefix ||
                      U.theme ||
                      U.fadeClass
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        " class " +
                        Y(D.prefix) +
                        "-class " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.theme) +
                        " " +
                        Y(D.fadeClass)
                    );
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!(
                          Z(undefined, "getMessageStyles") ||
                          U.zIndex ||
                          U.offset ||
                          U.wrapTop
                        ) || undefined,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(b)._style)([
                        P(X(a).getMessageStyles)(D.zIndex, D.offset, D.wrapTop),
                        D.style,
                        D.customStyle,
                      ])
                    );
                  if (C || K || U.showAnimation)
                    O(N, "animation", D.showAnimation);
                  if (C) O(N, "aria-role", "alert");
                  if (C || K || !!(U.id || U.classPrefix) || undefined)
                    R.i(N, D.id || D.classPrefix);
                },
                f
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.visible ? 1 : 0;
            B(d, e);
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/message/message": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b = (C, c, d, e, f, g, T, E) => {
            var h = (C, T, E, B, F, S) => {
              S("icon", (N) => {}, "icon");
              S("content", (N) => {}, "content");
              S("");
              S("link", (N) => {}, "link");
              S("close-btn", (N) => {}, "close-btn");
            };
            E(
              "t-message-item",
              {},
              (N, C) => {
                if (C) R.v(N, "close-btn-click", "handleClose", !1, !1, !1, !1);
                if (C) R.v(N, "link-click", "handleLinkClick", !1, !1, !1, !1);
                if (C)
                  R.v(N, "duration-end", "handleDurationEnd", !1, !1, !1, !1);
                if (C || K || Z(e, "id")) R.i(N, X(c).id);
              },
              h
            );
          },
          a = (C, T, E, B, F) => {
            F(
              D.messageList,
              "id",
              U ? U.messageList : undefined,
              [0, "messageList"],
              b
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/link/link";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/link/link.js";
define(
  "miniprogram_npm/tdesign-miniprogram/link/link.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0),
      require("../../../@babel/runtime/helpers/Arrayincludes");
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      s = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      i = require("../common/src/index"),
      a = l(require("../common/config")),
      c = l(require("./props")),
      o = require("../common/utils");
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var u = a.default.prefix,
      p = "".concat(u, "-link"),
      f = (function (n) {
        r(a, n);
        var i = s(a);
        function a() {
          var e;
          return (
            t(this, a),
            ((e = i.apply(this, arguments)).externalClasses = [
              "".concat(u, "-class"),
              "".concat(u, "-class-hover"),
              "".concat(u, "-class-prefix-icon"),
              "".concat(u, "-class-content"),
              "".concat(u, "-class-suffix-icon"),
            ]),
            (e.properties = c.default),
            (e.options = { multipleSlots: !0 }),
            (e.data = { prefix: u, classPrefix: p }),
            (e.observers = {
              "theme, disabled, size, underline, navigatorProps": function () {
                this.setClass();
              },
              prefixIcon: function (e) {
                this.setData({ _prefixIcon: (0, o.calcIcon)(e) });
              },
              suffixIcon: function (e) {
                this.setData({ _suffixIcon: (0, o.calcIcon)(e) });
              },
            }),
            (e.lifetimes = {
              attached: function () {
                this.setClass();
              },
            }),
            (e.methods = {
              setClass: function () {
                var e = this.properties,
                  t = e.theme,
                  r = e.size,
                  s = e.underline,
                  n = e.navigatorProps,
                  i = e.disabled,
                  a = [
                    p,
                    "".concat(p, "--").concat(t),
                    "".concat(p, "--").concat(r),
                  ],
                  c = null != n ? n : {},
                  o = c.url,
                  l = c.appId,
                  u = c.shortLink,
                  f = c.target,
                  d = c.openType,
                  h = !(o || ("miniProgram" === f && (l || u)));
                s && a.push("".concat(p, "--underline")),
                  ((n && h && !["navigateBack", "exit"].includes(d)) || i) &&
                    a.push("".concat(p, "--disabled")),
                  this.setData({ className: a.join(" ") });
              },
              onSuccess: function (e) {
                this.triggerEvent("success", e);
              },
              onFail: function (e) {
                this.triggerEvent("fail", e);
              },
              onComplete: function (e) {
                this.triggerEvent("complete", e);
              },
            }),
            e
          );
        }
        return e(a);
      })(i.SuperComponent),
      d = (f = (0, n.__decorate)([(0, i.wxComponent)()], f));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/link/link.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/link/link.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/message-item/message-item";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/message-item/message-item.js";
define(
  "miniprogram_npm/tdesign-miniprogram/message-item/message-item.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var t = require("../../../@babel/runtime/helpers/slicedToArray"),
      e = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/createClass"),
      n = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      s = require("tslib"),
      o = require("../common/src/index"),
      r = h(require("../common/config")),
      c = h(require("../message/props")),
      l = require("../common/utils"),
      u = require("../common/validator");
    function h(t) {
      return t && t.__esModule ? t : { default: t };
    }
    var m = r.default.prefix,
      f = "".concat(m, "-message"),
      d = {
        info: "info-circle-filled",
        success: "check-circle-filled",
        warning: "info-circle-filled",
        error: "error-circle-filled",
      },
      p = (function (s) {
        n(r, s);
        var o = a(r);
        function r() {
          var t;
          return (
            e(this, r),
            ((t = o.apply(this, arguments)).externalClasses = [
              "".concat(m, "-class"),
              "".concat(m, "-class-content"),
              "".concat(m, "-class-icon"),
              "".concat(m, "-class-link"),
              "".concat(m, "-class-close-btn"),
            ]),
            (t.options = { multipleSlots: !0 }),
            (t.properties = Object.assign({}, c.default)),
            (t.data = {
              prefix: m,
              classPrefix: f,
              loop: -1,
              animation: [],
              showAnimation: [],
              wrapTop: -999,
              fadeClass: "",
            }),
            (t.closeTimeoutContext = 0),
            (t.nextAnimationContext = 0),
            (t.resetAnimation = wx.createAnimation({
              duration: 0,
              timingFunction: "linear",
            })),
            (t.observers = {
              marquee: function (t) {
                ("{}" !== JSON.stringify(t) && "true" !== JSON.stringify(t)) ||
                  this.setData({ marquee: { speed: 50, loop: -1, delay: 0 } });
              },
              "icon, theme": function (t, e) {
                this.setData({ _icon: (0, l.calcIcon)(t, d[e]) });
              },
              link: function (t) {
                var e = (0, u.isObject)(t)
                  ? Object.assign({}, t)
                  : { content: t };
                this.setData({ _link: e });
              },
              closeBtn: function (t) {
                this.setData({ _closeBtn: (0, l.calcIcon)(t, "close") });
              },
            }),
            (t.lifetimes = {
              ready: function () {
                this.memoInitialData();
              },
              detached: function () {
                this.clearMessageAnimation();
              },
            }),
            t
          );
        }
        return (
          i(r, [
            {
              key: "memoInitialData",
              value: function () {
                this.initialData = Object.assign(
                  Object.assign({}, this.properties),
                  this.data
                );
              },
            },
            {
              key: "resetData",
              value: function (t) {
                this.setData(Object.assign({}, this.initialData), t);
              },
            },
            {
              key: "checkAnimation",
              value: function () {
                var e = this,
                  i = this.properties.marquee;
                if (i && 0 !== i.loop) {
                  var n = i.speed;
                  if (this.data.loop > 0) this.data.loop -= 1;
                  else if (0 === this.data.loop)
                    return void this.setData({
                      animation: this.resetAnimation
                        .translateX(0)
                        .step()
                        .export(),
                    });
                  this.nextAnimationContext && this.clearMessageAnimation();
                  var a = "#".concat(f, "__text-wrap"),
                    s = "#".concat(f, "__text");
                  Promise.all([
                    (0, l.getRect)(this, s),
                    (0, l.getRect)(this, a),
                  ]).then(function (i) {
                    var a = t(i, 2),
                      s = a[0],
                      o = a[1];
                    e.setData(
                      {
                        animation: e.resetAnimation
                          .translateX(o.width)
                          .step()
                          .export(),
                      },
                      function () {
                        var t = ((s.width + o.width) / n) * 1e3,
                          i = wx
                            .createAnimation({ duration: t })
                            .translateX(-s.width)
                            .step()
                            .export();
                        setTimeout(function () {
                          (e.nextAnimationContext = setTimeout(
                            e.checkAnimation.bind(e),
                            t
                          )),
                            e.setData({ animation: i });
                        }, 20);
                      }
                    );
                  });
                }
              },
            },
            {
              key: "clearMessageAnimation",
              value: function () {
                clearTimeout(this.nextAnimationContext),
                  (this.nextAnimationContext = 0);
              },
            },
            {
              key: "show",
              value: function () {
                var t = this,
                  e =
                    arguments.length > 0 && void 0 !== arguments[0]
                      ? arguments[0]
                      : 0,
                  i = this.properties,
                  n = i.duration,
                  a = i.marquee,
                  s = i.offset,
                  o = i.id;
                this.setData({
                  visible: !0,
                  loop: a.loop || this.data.loop,
                  fadeClass: "".concat(f, "__fade"),
                  wrapTop: (0, l.unitConvert)(s[0]) + e,
                }),
                  this.reset(),
                  this.checkAnimation(),
                  n &&
                    n > 0 &&
                    (this.closeTimeoutContext = setTimeout(function () {
                      t.hide(), t.triggerEvent("duration-end", { self: t });
                    }, n)),
                  (0, l.getRect)(this, "#".concat(o || f)).then(function (e) {
                    t.setData({ height: e.height }, function () {
                      t.setData({ fadeClass: "" });
                    });
                  });
              },
            },
            {
              key: "hide",
              value: function () {
                var t = this;
                this.reset(),
                  this.setData({ fadeClass: "".concat(f, "__fade") }),
                  setTimeout(function () {
                    t.setData({ visible: !1, animation: [] });
                  }, 500),
                  "function" == typeof this.onHide && this.onHide();
              },
            },
            {
              key: "reset",
              value: function () {
                this.nextAnimationContext && this.clearMessageAnimation(),
                  clearTimeout(this.closeTimeoutContext),
                  (this.closeTimeoutContext = 0);
              },
            },
            {
              key: "handleClose",
              value: function () {
                this.hide(), this.triggerEvent("close-btn-click");
              },
            },
            {
              key: "handleLinkClick",
              value: function () {
                this.triggerEvent("link-click");
              },
            },
          ]),
          r
        );
      })(o.SuperComponent),
      v = (p = (0, s.__decorate)([(0, o.wxComponent)()], p));
    exports.default = v;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/message-item/message-item.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/message-item/message-item.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/message/message";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/message/message.js";
define(
  "miniprogram_npm/tdesign-miniprogram/message/message.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/toConsumableArray"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      i = require("../../../@babel/runtime/helpers/createClass"),
      s = require("../../../@babel/runtime/helpers/inherits"),
      n = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      r = require("../common/src/index"),
      o = l(require("../common/config")),
      c = require("./message.interface"),
      u = l(require("./props")),
      h = require("../common/utils");
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var d = o.default.prefix,
      f = "".concat(d, "-message"),
      g = (function (a) {
        s(o, a);
        var r = n(o);
        function o() {
          var e;
          return (
            t(this, o),
            ((e = r.apply(this, arguments)).options = { multipleSlots: !0 }),
            (e.properties = Object.assign({}, u.default)),
            (e.data = { prefix: d, classPrefix: f, messageList: [] }),
            (e.index = 0),
            (e.instances = []),
            (e.gap = 12),
            (e.observers = {
              visible: function (e) {
                e
                  ? this.setMessage(this.properties, this.properties.theme)
                  : this.setData({ messageList: [] });
              },
            }),
            (e.pageLifetimes = {
              show: function () {
                this.hideAll();
              },
            }),
            (e.lifetimes = {
              ready: function () {
                this.memoInitialData();
              },
            }),
            e
          );
        }
        return (
          i(o, [
            {
              key: "memoInitialData",
              value: function () {
                this.initialData = Object.assign(
                  Object.assign({}, this.properties),
                  this.data
                );
              },
            },
            {
              key: "setMessage",
              value: function (e) {
                var t = this,
                  i =
                    arguments.length > 1 && void 0 !== arguments[1]
                      ? arguments[1]
                      : c.MessageType.info,
                  s = "".concat(f, "_").concat(this.index);
                e.single && (s = f),
                  (this.gap = (0, h.unitConvert)(e.gap || this.gap));
                var n = Object.assign(Object.assign({}, e), {
                    theme: i,
                    id: s,
                    gap: this.gap,
                  }),
                  a = this.instances.findIndex(function (e) {
                    return e.id === s;
                  });
                if (a < 0) this.addMessage(n);
                else {
                  var r = this.instances[a],
                    o = this.getOffsetHeight(a);
                  r.resetData(function () {
                    r.setData(n, r.show.bind(r, o)),
                      (r.onHide = function () {
                        t.close(s);
                      });
                  });
                }
              },
            },
            {
              key: "addMessage",
              value: function (t) {
                var i = this,
                  s = [].concat(e(this.data.messageList), [{ id: t.id }]);
                this.setData({ messageList: s }, function () {
                  var e = i.getOffsetHeight(),
                    s = i.showMessageItem(t, t.id, e);
                  i.instances && (i.instances.push(s), (i.index += 1));
                });
              },
            },
            {
              key: "getOffsetHeight",
              value: function () {
                var e =
                    arguments.length > 0 && void 0 !== arguments[0]
                      ? arguments[0]
                      : -1,
                  t = 0,
                  i = e;
                (-1 === i || i > this.instances.length) &&
                  (i = this.instances.length);
                for (var s = 0; s < i; s += 1) {
                  var n = this.instances[s];
                  t += n.data.height + n.data.gap;
                }
                return t;
              },
            },
            {
              key: "showMessageItem",
              value: function (e, t, i) {
                var s = this,
                  n = this.selectComponent("#".concat(t));
                if (n)
                  return (
                    n.resetData(function () {
                      n.setData(e, n.show.bind(n, i)),
                        (n.onHide = function () {
                          s.close(t);
                        });
                    }),
                    n
                  );
                console.error("未找到组件,请确认 selector && context 是否正确");
              },
            },
            {
              key: "close",
              value: function (e) {
                var t = this;
                setTimeout(function () {
                  t.removeMsg(e);
                }, 400),
                  this.removeInstance(e);
              },
            },
            {
              key: "hide",
              value: function (e) {
                e || this.hideAll();
                var t = this.instances.find(function (t) {
                  return t.id === e;
                });
                t && t.hide();
              },
            },
            {
              key: "hideAll",
              value: function () {
                for (; 0 < this.instances.length; ) this.instances[0].hide();
              },
            },
            {
              key: "removeInstance",
              value: function (e) {
                var t = this.instances.findIndex(function (t) {
                  return t.id === e;
                });
                if (!(t < 0)) {
                  var i = this.instances[t].data.height;
                  this.instances.splice(t, 1);
                  for (var s = t; s < this.instances.length; s += 1) {
                    var n = this.instances[s];
                    n.setData({ wrapTop: n.data.wrapTop - i - n.data.gap });
                  }
                }
              },
            },
            {
              key: "removeMsg",
              value: function (e) {
                var t = this.data.messageList.findIndex(function (t) {
                  return t.id === e;
                });
                t > -1 &&
                  (this.data.messageList.splice(t, 1),
                  this.setData({ messageList: this.data.messageList }));
              },
            },
            {
              key: "handleClose",
              value: function () {
                this.triggerEvent("close-btn-click");
              },
            },
            {
              key: "handleLinkClick",
              value: function () {
                this.triggerEvent("link-click");
              },
            },
            {
              key: "handleDurationEnd",
              value: function () {
                this.triggerEvent("duration-end");
              },
            },
          ]),
          o
        );
      })(r.SuperComponent),
      p = (g = (0, a.__decorate)([(0, r.wxComponent)()], g));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/message/message.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/message/message.js");
