@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-loading {
  display: -webkit-inline-flex;
  display: inline-flex;
  font-size: 24rpx;
}
.t-loading,
.t-loading--fullscreen {
  align-items: center;
  justify-content: center;
}
.t-loading--fullscreen {
  background-color: var(--td-loading-full-bg-color, hsla(0, 0%, 100%, 0.6));
  display: -webkit-flex;
  display: flex;
  height: 100%;
  left: 0;
  position: fixed;
  top: 0;
  vertical-align: middle;
  width: 100%;
  z-index: var(--td-loading-z-index, 3500);
}
.t-loading__spinner {
  animation: rotate 0.8s linear infinite;
  box-sizing: border-box;
  color: var(
    --td-loading-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  height: 100%;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}
.t-loading__spinner.reverse {
  animation-name: rotateReverse;
}
.t-loading__spinner--spinner {
  animation-timing-function: steps(12);
  color: var(
    --td-text-color-primary,
    var(--td-font-gray-1, rgba(0, 0, 0, 0.9))
  );
}
.t-loading__spinner--spinner .t-loading__dot {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}
.t-loading__spinner--spinner .t-loading__dot::before {
  background-color: var(
    --td-loading-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-radius: 40%;
  content: " ";
  display: block;
  height: 25%;
  margin: 0 auto;
  width: 5rpx;
}
.t-loading__spinner--circular .t-loading__circular {
  background: conic-gradient(
    from 180deg at 50% 50%,
    hsla(0, 0%, 100%, 0) 0deg,
    hsla(0, 0%, 100%, 0) 60deg,
    currentColor 330deg,
    hsla(0, 0%, 100%, 0) 1turn
  );
  border-radius: 100%;
  height: 100%;
  mask: radial-gradient(transparent calc(50% - 1rpx), #fff 50%);
  width: 100%;
}
.t-loading__spinner--dots {
  align-items: center;
  animation: none;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
}
.t-loading__spinner--dots .t-loading__dot {
  animation-duration: 1.8s;
  animation-fill-mode: both;
  animation-iteration-count: infinite;
  animation-name: dotting;
  animation-timing-function: linear;
  background-color: var(
    --td-loading-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
  border-radius: 50%;
  height: 20%;
  width: 20%;
}
.t-loading__text {
  color: var(
    --td-loading-text-color,
    var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9)))
  );
  font-size: var(--td-loading-text-font-size, 24rpx);
  line-height: var(--td-loading-text-line-height, 40rpx);
}
.t-loading__text--vertical:not(:first-child):not(:empty) {
  margin-top: 12rpx;
}
.t-loading__text--horizontal:not(:first-child):not(:empty) {
  margin-left: 16rpx;
}
.t-loading--vertical {
  flex-direction: column;
}
.t-loading--horizontal {
  flex-direction: row;
  vertical-align: top;
}
@-webkit-keyframes t-bar {
  0% {
    width: 0;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 80%;
  }
}
@keyframes t-bar {
  0% {
    width: 0;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 80%;
  }
}
@-webkit-keyframes t-bar-loaded {
  0% {
    height: 6rpx;
    opacity: 1;
    width: 90%;
  }
  50% {
    height: 6rpx;
    opacity: 1;
    width: 100%;
  }
  100% {
    height: 0;
    opacity: 0;
    width: 100%;
  }
}
@keyframes t-bar-loaded {
  0% {
    height: 6rpx;
    opacity: 1;
    width: 90%;
  }
  50% {
    height: 6rpx;
    opacity: 1;
    width: 100%;
  }
  100% {
    height: 0;
    opacity: 0;
    width: 100%;
  }
}
.t-loading__dot-1 {
  opacity: 0;
  transform: rotate(30deg);
}
.t-loading__dot-2 {
  opacity: 0.08333333;
  transform: rotate(60deg);
}
.t-loading__dot-3 {
  opacity: 0.16666667;
  transform: rotate(90deg);
}
.t-loading__dot-4 {
  opacity: 0.25;
  transform: rotate(120deg);
}
.t-loading__dot-5 {
  opacity: 0.33333333;
  transform: rotate(150deg);
}
.t-loading__dot-6 {
  opacity: 0.41666667;
  transform: rotate(180deg);
}
.t-loading__dot-7 {
  opacity: 0.5;
  transform: rotate(210deg);
}
.t-loading__dot-8 {
  opacity: 0.58333333;
  transform: rotate(240deg);
}
.t-loading__dot-9 {
  opacity: 0.66666667;
  transform: rotate(270deg);
}
.t-loading__dot-10 {
  opacity: 0.75;
  transform: rotate(300deg);
}
.t-loading__dot-11 {
  opacity: 0.83333333;
  transform: rotate(330deg);
}
.t-loading__dot-12 {
  opacity: 0.91666667;
  transform: rotate(1turn);
}
@-webkit-keyframes rotate {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(1turn);
  }
}
@keyframes rotate {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(1turn);
  }
}
@-webkit-keyframes rotateReverse {
  from {
    transform: rotate(1turn);
  }
  to {
    transform: rotate(0);
  }
}
@keyframes rotateReverse {
  from {
    transform: rotate(1turn);
  }
  to {
    transform: rotate(0);
  }
}
@-webkit-keyframes dotting {
  0% {
    opacity: 0.15;
  }
  1% {
    opacity: 0.8;
  }
  33% {
    opacity: 0.8;
  }
  34% {
    opacity: 0.15;
  }
  100% {
    opacity: 0.15;
  }
}
@keyframes dotting {
  0% {
    opacity: 0.15;
  }
  1% {
    opacity: 0.8;
  }
  33% {
    opacity: 0.8;
  }
  34% {
    opacity: 0.15;
  }
  100% {
    opacity: 0.15;
  }
}
