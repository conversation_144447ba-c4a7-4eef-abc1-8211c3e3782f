__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/calendar/calendar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/calendar/calendar"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          d,
          g = (C, T, E, B, F, S, J) => {
            var $A = G["miniprogram_npm/tdesign-miniprogram/calendar/template"];
            if ($A) $A("")(R, C, D, U).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            J(g);
          },
          h = (C, T, E, B, F, S, J) => {
            var $A = G["miniprogram_npm/tdesign-miniprogram/calendar/template"];
            if ($A) $A("")(R, C, D, U).C(C, T, E, B, F, S, J);
          },
          e = (C, T, E, B, F, S, J) => {
            if (d === 1) {
              E(
                "t-popup",
                {},
                (N, C) => {
                  if (C) L(N, "class");
                  if (C || K || U.visible) O(N, "visible", D.visible);
                  if (C || K || U.usingCustomNavbar)
                    O(N, "usingCustomNavbar", D.usingCustomNavbar);
                  if (C) O(N, "placement", "bottom");
                  if (C)
                    R.v(N, "visible-change", "onVisibleChange", !1, !1, !1, !1);
                },
                f
              );
            } else {
              J(h);
            }
          },
          c = (C, T, E, B) => {
            d = D.usePopup ? 1 : 0;
            B(d, e);
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/calendar/calendar";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/calendar/calendar.js";
define(
  "miniprogram_npm/tdesign-miniprogram/calendar/calendar.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var t = require("../../../@babel/runtime/helpers/typeof"),
      e = require("../../../@babel/runtime/helpers/createClass"),
      n = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      s = require("../common/src/index"),
      o = m(require("../common/config")),
      h = m(require("./props")),
      l = m(require("../common/shared/calendar/index")),
      u = m(require("../mixins/using-custom-navbar")),
      c = require("./utils");
    function m(t) {
      return t && t.__esModule ? t : { default: t };
    }
    var f = o.default.prefix,
      g = "".concat(f, "-calendar"),
      d = {
        title: "请选择日期",
        weekdays: ["日", "一", "二", "三", "四", "五", "六"],
        monthTitle: "{year} 年 {month}",
        months: [
          "1 月",
          "2 月",
          "3 月",
          "4 月",
          "5 月",
          "6 月",
          "7 月",
          "8 月",
          "9 月",
          "10 月",
          "11 月",
          "12 月",
        ],
        confirm: "确认",
      },
      v = (function (i) {
        a(o, i);
        var s = r(o);
        function o() {
          var e;
          return (
            n(this, o),
            ((e = s.apply(this, arguments)).behaviors = [u.default]),
            (e.externalClasses = ["".concat(f, "-class")]),
            (e.options = { multipleSlots: !0 }),
            (e.properties = h.default),
            (e.data = {
              prefix: f,
              classPrefix: g,
              months: [],
              scrollIntoView: "",
              innerConfirmBtn: {},
              realLocalText: {},
              currentMonth: {},
              actionButtons: {
                preYearBtnDisable: !1,
                prevMonthBtnDisable: !1,
                nextMonthBtnDisable: !1,
                nextYearBtnDisable: !1,
              },
            }),
            (e.controlledProps = [
              { key: "value", event: "confirm" },
              { key: "value", event: "change" },
            ]),
            (e.lifetimes = {
              created: function () {
                this.base = new l.default(this.properties);
              },
              ready: function () {
                var t = Object.assign(
                  Object.assign({}, d),
                  this.properties.localeText
                );
                this.initialValue(),
                  this.setData({
                    days: this.base.getDays(t.weekdays),
                    realLocalText: t,
                  }),
                  this.calcMonths(),
                  "none" !== this.data.switchMode && this.calcCurrentMonth(),
                  this.data.usePopup || this.scrollIntoView();
              },
            }),
            (e.observers = {
              type: function (t) {
                this.base.type = t;
              },
              confirmBtn: function (e) {
                "string" == typeof e
                  ? this.setData({
                      innerConfirmBtn: "slot" === e ? "slot" : { content: e },
                    })
                  : "object" == t(e) && this.setData({ innerConfirmBtn: e });
              },
              "firstDayOfWeek,minDate,maxDate": function (t, e, n) {
                t && (this.base.firstDayOfWeek = t),
                  e && (this.base.minDate = e),
                  n && (this.base.maxDate = n),
                  this.calcMonths();
              },
              value: function (t) {
                (this.base.value = t), this.calcMonths();
              },
              visible: function (t) {
                t &&
                  (this.scrollIntoView(),
                  (this.base.value = this.data.value),
                  this.calcMonths());
              },
              format: function (t) {
                var e = this.data,
                  n = e.usePopup,
                  a = e.visible;
                (this.base.format = t), (n && !a) || this.calcMonths();
              },
            }),
            (e.methods = {
              initialValue: function () {
                var t = this.data,
                  e = t.value,
                  n = t.type,
                  a = t.minDate;
                if (!e) {
                  var r = new Date(),
                    i =
                      a ||
                      new Date(
                        r.getFullYear(),
                        r.getMonth(),
                        r.getDate()
                      ).getTime(),
                    s = "single" === n ? i : [i];
                  "range" === n && (s[1] = i + 864e5),
                    this.setData({ value: s }),
                    (this.base.value = s);
                }
              },
              scrollIntoView: function () {
                var t = this.data.value;
                if (t) {
                  var e = new Date(Array.isArray(t) ? t[0] : t);
                  e &&
                    this.setData({
                      scrollIntoView: "year_"
                        .concat(e.getFullYear(), "_month_")
                        .concat(e.getMonth()),
                    });
                }
              },
              getCurrentYearAndMonth: function (t) {
                var e = new Date(t);
                return { year: e.getFullYear(), month: e.getMonth() };
              },
              updateActionButton: function (t) {
                var e = this.getCurrentYearAndMonth(this.base.minDate),
                  n = this.getCurrentYearAndMonth(this.base.maxDate),
                  a = new Date(e.year, e.month, 1).getTime(),
                  r = new Date(n.year, n.month, 1).getTime(),
                  i = (0, c.getPrevYear)(t).getTime(),
                  s = (0, c.getPrevMonth)(t).getTime(),
                  o = (0, c.getNextMonth)(t).getTime(),
                  h = (0, c.getNextYear)(t).getTime(),
                  l = i < a || s < a,
                  u = s < a,
                  m = o > r || h > r,
                  f = o > r;
                this.setData({
                  actionButtons: {
                    preYearBtnDisable: l,
                    prevMonthBtnDisable: u,
                    nextYearBtnDisable: m,
                    nextMonthBtnDisable: f,
                  },
                });
              },
              calcCurrentMonth: function (t) {
                var e = t || this.getCurrentDate(),
                  n = this.getCurrentYearAndMonth(e),
                  a = n.year,
                  r = n.month,
                  i = this.data.months.filter(function (t) {
                    return t.year === a && t.month === r;
                  });
                this.updateActionButton(e),
                  this.setData({
                    currentMonth: i.length > 0 ? i : [this.data.months[0]],
                  });
              },
              calcMonths: function () {
                var t = this.base.getMonths();
                this.setData({ months: t });
              },
              close: function (t) {
                this.data.autoClose && this.setData({ visible: !1 }),
                  this.triggerEvent("close", { trigger: t });
              },
              onVisibleChange: function () {
                this.close("overlay");
              },
              handleClose: function () {
                this.close("close-btn");
              },
              handleSelect: function (t) {
                var e = t.currentTarget.dataset,
                  n = e.date,
                  a = e.year,
                  r = e.month;
                if ("disabled" !== n.type) {
                  var i = this.base.select({
                      cellType: n.type,
                      year: a,
                      month: r,
                      date: n.day,
                    }),
                    s = this.toTime(i);
                  if ((this.calcMonths(), "none" !== this.data.switchMode)) {
                    var o = this.getCurrentDate();
                    this.calcCurrentMonth(o);
                  }
                  null == this.data.confirmBtn &&
                    (("single" !== this.data.type && 2 !== i.length) ||
                      (this.setData({ visible: !1 }),
                      this._trigger("change", { value: s }))),
                    this.triggerEvent("select", { value: s });
                }
              },
              onTplButtonTap: function () {
                var t = this.base.getTrimValue(),
                  e = this.toTime(t);
                this.close("confirm-btn"),
                  this._trigger("confirm", { value: e });
              },
              toTime: function (t) {
                return Array.isArray(t)
                  ? t.map(function (t) {
                      return t.getTime();
                    })
                  : t.getTime();
              },
              onScroll: function (t) {
                this.triggerEvent("scroll", t.detail);
              },
              getCurrentDate: function () {
                var t,
                  e,
                  n = Array.isArray(this.base.value)
                    ? this.base.value[0]
                    : this.base.value;
                if (this.data.currentMonth.length > 0) {
                  var a =
                      null === (t = this.data.currentMonth[0]) || void 0 === t
                        ? void 0
                        : t.year,
                    r =
                      null === (e = this.data.currentMonth[0]) || void 0 === e
                        ? void 0
                        : e.month;
                  n = new Date(a, r, 1).getTime();
                }
                return n;
              },
              handleSwitchModeChange: function (t) {
                var e = t.currentTarget.dataset,
                  n = e.type;
                if (!e.disabled) {
                  var a = this.getCurrentDate(),
                    r = {
                      "pre-year": function () {
                        return (0, c.getPrevYear)(a);
                      },
                      "pre-month": function () {
                        return (0, c.getPrevMonth)(a);
                      },
                      "next-month": function () {
                        return (0, c.getNextMonth)(a);
                      },
                      "next-year": function () {
                        return (0, c.getNextYear)(a);
                      },
                    }[n]();
                  if (r) {
                    var i = this.getCurrentYearAndMonth(r),
                      s = i.year,
                      o = i.month;
                    this.triggerEvent("panel-change", {
                      year: s,
                      month: o + 1,
                    }),
                      this.calcCurrentMonth(r);
                  }
                }
              },
            }),
            e
          );
        }
        return e(o);
      })(s.SuperComponent),
      p = (v = (0, i.__decorate)([(0, s.wxComponent)()], v));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/calendar/calendar.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/calendar/calendar.js");
