__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/back-top/back-top": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            fixed: new Array(1),
            customStyle: new Array(1),
            style: new Array(1),
            hidden: new Array(1),
          },
          K = U === true,
          e,
          g,
          h = (C, T, E, B, F, S, J) => {
            var $A = I(g);
            if (g && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-icon" },
                  X(D._icon),
                  {}
                ),
                K ||
                  (U
                    ? U._icon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._icon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          f = (C, T, E, B, F, S, J) => {
            if (e === 1) {
              g = "icon";
              B(g, h);
            }
          },
          d = (C, T, E, B, F, S) => {
            S("icon");
            e = D._icon ? 1 : 0;
            B(e, f);
          },
          i,
          k = (C, T) => {
            C || K || U.text ? T(Y(D.text)) : T();
          },
          j = (C, T, E) => {
            if (i === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(U.classPrefix || U.theme || U.prefix) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__text--" +
                        Y(D.theme) +
                        " " +
                        Y(D.prefix) +
                        "-class-text"
                    );
                },
                k
              );
            }
          },
          c = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon");
                if (C) O(N, "aria-hidden", true);
              },
              d
            );
            i = !!D.text ? 1 : 0;
            B(i, j);
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.fixed]), U.theme])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          ["fixed", D.fixed],
                          D.theme,
                        ])
                      )
                  );
                A["fixed"][0] = (D, E, T) => {
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(
                        P(X(a).cls)(D.classPrefix, [
                          ["fixed", D.fixed],
                          D.theme,
                        ])
                      )
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "bindtap", "toTop");
                if (C) O(N, "aria-role", "button");
                if (C || K || U.hidden) O(N, "hidden", D.hidden);
                A["hidden"][0] = (D, E, T) => {
                  O(N, "hidden", D.hidden);
                  E(N);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/back-top/back-top.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-back-top{-webkit-align-items:center;align-items:center;background-color:initial;box-sizing:border-box;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;height:auto;-webkit-justify-content:center;justify-content:center;overflow:hidden;transition:height .2s}\n.",
        [1],
        "t-back-top--fixed{bottom:calc(var(--td-spacer-2,",
        [0, 32],
        ") + env(safe-area-inset-bottom));position:fixed;right:var(--td-spacer,",
        [0, 16],
        ")}\n.",
        [1],
        "t-back-top--round,.",
        [1],
        "t-back-top--round-dark{border-radius:var(--td-back-top-round-border-radius,var(--td-radius-circle,50%));height:",
        [0, 96],
        ";width:",
        [0, 96],
        "}\n.",
        [1],
        "t-back-top--half-round,.",
        [1],
        "t-back-top--round{background-color:var(--td-back-top-round-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));border:",
        [0, 1],
        " solid var(--td-back-top-round-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));color:var(--td-back-top-round-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
        [1],
        "t-back-top--half-round-dark,.",
        [1],
        "t-back-top--round-dark{background-color:var(--td-back-top-round-dark-bg-color,var(--td-gray-color-13,#242424));color:var(--td-back-top-round-dark-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
        [1],
        "t-back-top--half-round,.",
        [1],
        "t-back-top--half-round-dark{border-radius:0;border-bottom-left-radius:var(--td-back-top-half-round-border-radius,var(--td-radius-round,999px));border-top-left-radius:var(--td-back-top-half-round-border-radius,var(--td-radius-round,999px));-webkit-flex-direction:row;flex-direction:row;height:",
        [0, 80],
        ";right:0;width:",
        [0, 120],
        "}\n.",
        [1],
        "t-back-top__text--half-round,.",
        [1],
        "t-back-top__text--half-round-dark,.",
        [1],
        "t-back-top__text--round,.",
        [1],
        "t-back-top__text--round-dark{font-size:var(--td-font-size,",
        [0, 20],
        ");line-height:",
        [0, 24],
        "}\n.",
        [1],
        "t-back-top__text--half-round,.",
        [1],
        "t-back-top__text--half-round-dark{width:",
        [0, 48],
        "}\n.",
        [1],
        "t-back-top__icon:not(:empty)+.",
        [1],
        "t-back-top__text--half-round,.",
        [1],
        "t-back-top__icon:not(:empty)+.",
        [1],
        "t-back-top__text--half-round-dark{margin-left:",
        [0, 8],
        "}\n.",
        [1],
        "t-back-top__icon{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;font-size:",
        [0, 44],
        ";-webkit-justify-content:center;justify-content:center}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/back-top/back-top.wxss" }
    );
}
