__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/count-down/count-down": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/count-down/count-down"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            size: new Array(1),
            customStyle: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          e,
          f = (C, T, E, B, F, S) => {
            if (e === 1) {
              S("content");
            }
          },
          g,
          i = (C, j, k, l, m, n, T, E, B) => {
            var o = (C, T) => {
                var $B = k;
                var $A = X(D.timeRange)[$B];
                C ||
                K ||
                !!(
                  Z(undefined, "format") ||
                  m ||
                  Z(U.timeRange, $B) ||
                  Z(U.timeData, $A)
                ) ||
                undefined
                  ? T(Y(P(X(b).format)(X(D.timeData)[$A])))
                  : T();
              },
              p,
              r = (C, T) => {
                var $A = D.splitWithUnit;
                var $C = k;
                var $B = X(D.timeRange)[$C];
                C ||
                K ||
                !!U.splitWithUnit ||
                ($A
                  ? !!(m || Z(U.timeRange, $C)) || Z(U.timeDataUnit, $B)
                  : undefined)
                  ? T(Y($A ? X(D.timeDataUnit)[$B] : ":"))
                  : T();
              },
              q = (C, T, E) => {
                if (p === 1) {
                  E(
                    "text",
                    {},
                    (N, C) => {
                      var $A = D.splitWithUnit;
                      if (
                        C ||
                        K ||
                        !!(
                          U.classPrefix ||
                          U.classPrefix ||
                          U.splitWithUnit ||
                          ($A ? undefined : undefined) ||
                          U.prefix
                        ) ||
                        undefined
                      )
                        L(
                          N,
                          Y(D.classPrefix) +
                            "__split " +
                            Y(D.classPrefix) +
                            "__split--" +
                            Y($A ? "text" : "dot") +
                            " " +
                            Y(D.prefix) +
                            "-class-split"
                        );
                    },
                    r
                  );
                }
              };
            E(
              "text",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__item " + Y(D.prefix) + "-class-count"
                  );
              },
              o
            );
            p = D.splitWithUnit || X(D.timeRange).length - 1 !== k ? 1 : 0;
            B(p, q);
          },
          h = (C, T, E, B, F, S) => {
            if (g === 1) {
              S("");
            } else if (g === 2) {
              C || K || U.formattedTime ? T(Y(D.formattedTime)) : T();
            } else {
              F(
                D.timeRange,
                "index",
                U ? U.timeRange : undefined,
                [0, "timeRange"],
                i
              );
            }
          },
          d = (C, T, E, B) => {
            e = D.content !== "default" ? 1 : 0;
            B(e, f);
            g =
              D.content !== "default"
                ? 1
                : D.theme == "default" && !D.splitWithUnit
                ? 2
                : 0;
            B(g, h);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.classPrefix ||
                    U.theme ||
                    U.classPrefix ||
                    U.size ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.theme) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.size) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["size"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.theme) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.size) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-role", "option");
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/count-down/count-down.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-count-down--small.",
      [1],
      "t-count-down--default{font-size:var(--td-font-size-base,",
      [0, 28],
      ")}\n.",
      [1],
      "t-count-down--small.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__item,.",
      [1],
      "t-count-down--small.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__item{font-size:var(--td-font-size-s,",
      [0, 24],
      ");height:",
      [0, 40],
      ";width:",
      [0, 40],
      "}\n.",
      [1],
      "t-count-down--small.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__split--dot,.",
      [1],
      "t-count-down--small.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__split--dot{font-size:var(--td-font-size-base,",
      [0, 28],
      ");font-weight:700;margin:0 ",
      [0, 4],
      "}\n.",
      [1],
      "t-count-down--small.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__split--text,.",
      [1],
      "t-count-down--small.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__split--text{font-size:var(--td-font-size,",
      [0, 20],
      ");margin:0 ",
      [0, 8],
      "}\n.",
      [1],
      "t-count-down--medium.",
      [1],
      "t-count-down--default{font-size:var(--td-font-size-m,",
      [0, 32],
      ")}\n.",
      [1],
      "t-count-down--medium.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__item,.",
      [1],
      "t-count-down--medium.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__item{font-size:var(--td-font-size-base,",
      [0, 28],
      ");height:",
      [0, 48],
      ";width:",
      [0, 48],
      "}\n.",
      [1],
      "t-count-down--medium.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__split--dot,.",
      [1],
      "t-count-down--medium.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__split--dot{font-size:var(--td-font-size-m,",
      [0, 32],
      ");font-weight:700;margin:0 ",
      [0, 6],
      "}\n.",
      [1],
      "t-count-down--medium.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__split--text,.",
      [1],
      "t-count-down--medium.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__split--text{font-size:var(--td-font-size-s,",
      [0, 24],
      ");margin:0 ",
      [0, 10],
      "}\n.",
      [1],
      "t-count-down--large.",
      [1],
      "t-count-down--default{font-size:",
      [0, 36],
      "}\n.",
      [1],
      "t-count-down--large.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__item,.",
      [1],
      "t-count-down--large.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__item{font-size:var(--td-font-size-m,",
      [0, 32],
      ");height:",
      [0, 56],
      ";width:",
      [0, 56],
      "}\n.",
      [1],
      "t-count-down--large.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__split--dot,.",
      [1],
      "t-count-down--large.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__split--dot{font-size:",
      [0, 36],
      ";font-weight:700;margin:0 ",
      [0, 12],
      "}\n.",
      [1],
      "t-count-down--large.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__split--text,.",
      [1],
      "t-count-down--large.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__split--text{font-size:var(--td-font-size-base,",
      [0, 28],
      ");margin:0 ",
      [0, 12],
      "}\n.",
      [1],
      "t-count-down{display:-webkit-flex;display:flex;font-family:TCloudNumber,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Hiragino Sans GB,Microsoft YaHei UI,Microsoft YaHei,Source Han Sans CN,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol}\n.",
      [1],
      "t-count-down .",
      [1],
      "t-count-down__item,.",
      [1],
      "t-count-down .",
      [1],
      "t-count-down__split{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__split--dot,.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__split--dot{color:var(--td-error-color,var(--td-error-color-6,#d54941))}\n.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__split--text,.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__split--text{color:var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9)))}\n.",
      [1],
      "t-count-down--default{color:var(--td-countdown-default-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))))}\n.",
      [1],
      "t-count-down--square{color:var(--td-countdown-round-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
      [1],
      "t-count-down--square\x3e.",
      [1],
      "t-count-down__item{background:var(--td-countdown-bg-color,var(--td-error-color,var(--td-error-color-6,#d54941)));border-radius:var(--td-countdown-square-border-radius,var(--td-radius-small,",
      [0, 6],
      "))}\n.",
      [1],
      "t-count-down--round{color:var(--td-countdown-round-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)))}\n.",
      [1],
      "t-count-down--round\x3e.",
      [1],
      "t-count-down__item{background:var(--td-countdown-bg-color,var(--td-error-color,var(--td-error-color-6,#d54941)));border-radius:var(--td-countdown-round-border-radius,var(--td-radius-circle,50%))}\n",
    ],
    undefined,
    { path: "./miniprogram_npm/tdesign-miniprogram/count-down/count-down.wxss" }
  );
}
