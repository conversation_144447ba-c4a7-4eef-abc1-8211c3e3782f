__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/step-item/step-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/step-item/step-item"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            style: new Array(1),
            prefix: new Array(5),
            ariaRole: new Array(1),
            customStyle: new Array(1),
            ariaLabel: new Array(1),
            readonly: new Array(2),
          },
          K = U === true,
          f,
          h = (C) => {},
          j,
          l = (C) => {},
          k = (C, T, E, B, F, S) => {
            if (j === 1) {
              S("icon");
            } else {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C || K || U.icon) O(N, "name", D.icon);
                  if (C) O(N, "size", "44rpx");
                },
                l
              );
            }
          },
          i = (C, T, E, B) => {
            j = D.icon == "slot" ? 1 : 0;
            B(j, k);
          },
          n,
          p = (C) => {},
          q = (C) => {},
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "name", "check");
                },
                p
              );
            } else if (n === 2) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (C) O(N, "name", "close");
                },
                q
              );
            } else {
              C || K || !!U.index || undefined ? T(Y(D.index + 1)) : T();
            }
          },
          m = (C, T, E, B) => {
            n = D.curStatus == "finish" ? 1 : D.curStatus == "error" ? 2 : 0;
            B(n, o);
          },
          g = (C, T, E) => {
            if (f === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.curStatus])
                    ) ||
                    undefined
                  )
                    L(N, P(X(a).cls)(D.classPrefix + "__dot", [D.curStatus]));
                },
                h
              );
            } else if (f === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.curStatus])
                    ) ||
                    undefined
                  )
                    L(N, P(X(a).cls)(D.classPrefix + "__icon", [D.curStatus]));
                },
                i
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.curStatus])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__circle", [D.curStatus])
                    );
                },
                m
              );
            }
          },
          e = (C, T, E, B) => {
            f = D.isDot ? 1 : D.icon ? 2 : 0;
            B(f, g);
          },
          t,
          u = (C, T) => {
            if (t === 1) {
              C || K || U.title ? T(Y(D.title)) : T();
            }
          },
          v,
          w = (C, T, E, B, F, S) => {
            if (v === 1) {
              S("title-right");
            }
          },
          s = (C, T, E, B, F, S) => {
            t = D.title ? 1 : 0;
            B(t, u);
            S("title");
            v = D.layout === "vertical" ? 1 : 0;
            B(v, w);
          },
          y,
          z = (C, T) => {
            if (y === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          x = (C, T, E, B, F, S) => {
            y = D.content ? 1 : 0;
            B(y, z);
            S("content");
          },
          A0 = (C, T, E, B, F, S) => {
            S("extra");
          },
          r = (C, T, E, B, F, S) => {
            S("");
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.curStatus, U.layout]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__title", [
                        D.curStatus,
                        D.layout,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-title"
                  );
                A["prefix"][2] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__title", [
                        D.curStatus,
                        D.layout,
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-title"
                  );
                };
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__description", [D.layout])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-description"
                  );
                A["prefix"][3] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__description", [D.layout])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-description"
                  );
                };
              },
              x
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__extra", [D.layout])) +
                      " " +
                      Y(D.prefix) +
                      "-class-extra"
                  );
                A["prefix"][4] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix + "__extra", [D.layout])) +
                      " " +
                      Y(D.prefix) +
                      "-class-extra"
                  );
                };
              },
              A0
            );
          },
          B0,
          D0 = (C) => {},
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.curStatus, U.layout, U.theme, U.sequence])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__line", [
                        D.curStatus,
                        D.layout,
                        D.theme,
                        D.sequence,
                      ])
                    );
                  if (C) O(N, "aria-hidden", "true");
                },
                D0
              );
            }
          },
          d = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "cls") || U.classPrefix || Q.a([U.layout])) ||
                  undefined
                )
                  L(N, P(X(a).cls)(D.classPrefix + "__anchor", [D.layout]));
                if (C) O(N, "aria-hidden", "true");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout, Q.a([U.isLastChild])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__content", [
                        D.layout,
                        ["last", D.isLastChild],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                A["prefix"][1] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__content", [
                        D.layout,
                        ["last", D.isLastChild],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                };
                if (C) O(N, "aria-hidden", "true");
              },
              r
            );
            B0 = !D.isLastChild ? 1 : 0;
            B(B0, C0);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout, Q.a([U.readonly])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D.layout,
                        ["readonly", D.readonly],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["readonly"][1] = A["prefix"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D.layout,
                        ["readonly", D.readonly],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                var $A = D.ariaRole || D.readonly;
                if (
                  C ||
                  K ||
                  !!(U.ariaRole || U.readonly) ||
                  ($A ? undefined : undefined)
                )
                  O(N, "aria-role", $A ? "option" : "button");
                A["ariaRole"][0] = A["readonly"][0] = (D, E, T) => {
                  var $B = D.ariaRole || D.readonly;
                  O(N, "aria-role", $B ? "option" : "button");
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    Z(undefined, "getAriaLabel") ||
                    U.index ||
                    U.title ||
                    U.content
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      P(X(b).getAriaLabel)(D.index, D.title, D.content)
                  );
                A["ariaLabel"][0] = (D, E, T) => {
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      P(X(b).getAriaLabel)(D.index, D.title, D.content)
                  );
                  E(N);
                };
                var $B = D.curStatus == "process";
                if (C || K || !!U.curStatus || ($B ? undefined : undefined))
                  O(N, "aria-current", $B ? "step" : "");
                if (C) R.v(N, "tap", "onTap", !1, !1, !1, !1);
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/steps/steps": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            prefix: new Array(1),
            layout: new Array(1),
            sequence: new Array(1),
            customStyle: new Array(1),
            classPrefix: new Array(1),
            readonly: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.layout, Q.a([U.readonly]), U.sequence]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D.layout,
                        ["readonly", D.readonly],
                        D.sequence,
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["classPrefix"][0] =
                  A["layout"][0] =
                  A["readonly"][0] =
                  A["sequence"][0] =
                  A["prefix"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(
                          P(X(a).cls)(D.classPrefix, [
                            D.layout,
                            ["readonly", D.readonly],
                            D.sequence,
                          ])
                        ) +
                          " class " +
                          Y(D.prefix) +
                          "-class"
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/step-item/step-item";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/step-item/step-item.js";
define(
  "miniprogram_npm/tdesign-miniprogram/step-item/step-item.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      s = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      a = require("tslib"),
      i = require("../common/src/index"),
      u = c(require("../common/config")),
      n = c(require("./props"));
    function c(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = u.default.prefix,
      l = "".concat(o, "-steps-item"),
      p = (function (a) {
        s(u, a);
        var i = r(u);
        function u() {
          var e;
          return (
            t(this, u),
            ((e = i.apply(this, arguments)).options = { multipleSlots: !0 }),
            (e.relations = { "../steps/steps": { type: "parent" } }),
            (e.externalClasses = [
              "".concat(o, "-class"),
              "".concat(o, "-class-content"),
              "".concat(o, "-class-title"),
              "".concat(o, "-class-description"),
              "".concat(o, "-class-extra"),
            ]),
            (e.properties = n.default),
            (e.data = {
              classPrefix: l,
              prefix: o,
              index: 0,
              isDot: !1,
              curStatus: "",
              layout: "vertical",
              isLastChild: !1,
              sequence: "positive",
            }),
            (e.observers = {
              status: function (e) {
                var t = this.data.curStatus;
                "" !== t && e !== t && this.setData({ curStatus: e });
              },
            }),
            (e.methods = {
              updateStatus: function (e) {
                var t = e.current,
                  s = e.currentStatus,
                  r = e.index,
                  a = e.theme,
                  i = e.layout,
                  u = e.items,
                  n = e.sequence,
                  c = this.data.status;
                "default" === c &&
                  (r < Number(t) ? (c = "finish") : r === Number(t) && (c = s)),
                  this.setData({
                    curStatus: c,
                    index: r,
                    isDot: "dot" === a,
                    layout: i,
                    theme: a,
                    sequence: n,
                    isLastChild: r === ("positive" === n ? u.length - 1 : 0),
                  });
              },
              onTap: function () {
                this.$parent.handleClick(this.data.index);
              },
            }),
            e
          );
        }
        return e(u);
      })(i.SuperComponent),
      d = (p = (0, a.__decorate)([(0, i.wxComponent)()], p));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/step-item/step-item.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/step-item/step-item.js");
__wxRoute = "miniprogram_npm/tdesign-miniprogram/steps/steps";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/steps/steps.js";
define(
  "miniprogram_npm/tdesign-miniprogram/steps/steps.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      r = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      n = require("tslib"),
      a = require("../common/src/index"),
      s = l(require("../common/config")),
      u = l(require("./props"));
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = s.default.prefix,
      c = "".concat(o, "-steps"),
      d = (function (n) {
        r(s, n);
        var a = i(s);
        function s() {
          var e;
          return (
            t(this, s),
            ((e = a.apply(this, arguments)).relations = {
              "../step-item/step-item": {
                type: "child",
                linked: function (e) {
                  this.updateChildren();
                  var t = this.data.readonly;
                  e.setData({ readonly: t });
                },
                unlinked: function () {
                  this.updateLastChid();
                },
              },
            }),
            (e.externalClasses = ["".concat(o, "-class")]),
            (e.properties = u.default),
            (e.controlledProps = [{ key: "current", event: "change" }]),
            (e.data = { prefix: o, classPrefix: c }),
            (e.observers = {
              "current, theme, sequence": function () {
                this.updateChildren();
              },
            }),
            (e.methods = {
              updateChildren: function () {
                var e = this,
                  t = this.$children;
                t.forEach(function (r, i) {
                  r.updateStatus(Object.assign({ index: i, items: t }, e.data));
                });
              },
              updateLastChid: function () {
                var e = this.$children;
                e.forEach(function (t, r) {
                  return t.setData({ isLastChild: r === e.length - 1 });
                });
              },
              handleClick: function (e) {
                if (!this.data.readonly) {
                  var t = this.data.current;
                  this._trigger("change", { previous: t, current: e });
                }
              },
            }),
            e
          );
        }
        return e(s);
      })(a.SuperComponent),
      h = (d = (0, n.__decorate)([(0, a.wxComponent)()], d));
    exports.default = h;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/steps/steps.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/steps/steps.js");
