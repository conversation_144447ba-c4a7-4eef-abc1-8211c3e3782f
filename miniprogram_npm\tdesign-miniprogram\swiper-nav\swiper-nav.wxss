@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-swiper-nav__dots,
.t-swiper-nav__dots-bar {
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
}
.t-swiper-nav__dots-bar-item,
.t-swiper-nav__dots-item {
  background: var(
    --td-swiper-nav-dot-color,
    var(--td-font-white-2, hsla(0, 0%, 100%, 0.55))
  );
  border-radius: 50%;
  height: var(--td-swiper-nav-dot-size, 12rpx);
  margin: 0 10rpx;
  transition: all 0.4s ease-in;
  width: var(--td-swiper-nav-dot-size, 12rpx);
}
.t-swiper-nav__dots-bar-item--vertical,
.t-swiper-nav__dots-item--vertical {
  margin: 10rpx 0;
}
.t-swiper-nav__dots-bar-item--active,
.t-swiper-nav__dots-item--active {
  background-color: var(
    --td-swiper-nav-dot-active-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
}
.t-swiper-nav__dots-bar-item--vertical.t-swiper-nav__dots-bar-item--active {
  height: var(--td-swiper-nav-dots-bar-active-width, 40rpx);
  width: var(--td-swiper-nav-dot-size, 12rpx);
}
.t-swiper-nav__dots-bar-item--active {
  background-color: var(
    --td-swiper-nav-dot-active-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
  border-radius: calc(var(--td-swiper-nav-dot-size, 12rpx) / 2);
  width: var(--td-swiper-nav-dots-bar-active-width, 40rpx);
}
.t-swiper-nav--left {
  left: 24rpx;
}
.t-swiper-nav--left,
.t-swiper-nav--right {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.t-swiper-nav--right {
  right: 24rpx;
}
.t-swiper-nav--top-left {
  left: 24rpx;
  position: absolute;
  top: 24rpx;
}
.t-swiper-nav--top {
  left: 50%;
  position: absolute;
  top: 24rpx;
  transform: translateX(-50%);
}
.t-swiper-nav--top-right {
  position: absolute;
  right: 24rpx;
  top: 24rpx;
}
.t-swiper-nav--bottom-left {
  bottom: 24rpx;
  left: 24rpx;
  position: absolute;
}
.t-swiper-nav--bottom {
  bottom: 24rpx;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
}
.t-swiper-nav--bottom-right {
  bottom: 24rpx;
  position: absolute;
  right: 24rpx;
}
.t-swiper-nav--vertical {
  flex-direction: column;
}
.t-swiper-nav__fraction {
  background: var(
    --td-swiper-nav-fraction-bg-color,
    var(--td-font-gray-3, rgba(0, 0, 0, 0.4))
  );
  border-radius: calc(var(--td-swiper-nav-fraction-height, 48rpx) / 2);
  color: var(
    --td-swiper-nav-fraction-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
  font-size: var(--td-swiper-nav-fraction-font-size, 24rpx);
  height: var(--td-swiper-nav-fraction-height, 48rpx);
  line-height: var(--td-swiper-nav-fraction-height, 48rpx);
  padding: 0 16rpx;
}
.t-swiper-nav__btn {
  width: 100%;
}
.t-swiper-nav__btn,
.t-swiper-nav__btn--next,
.t-swiper-nav__btn--prev {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.t-swiper-nav__btn--next,
.t-swiper-nav__btn--prev {
  background: var(
    --td-swiper-nav-btn-bg-color,
    var(--td-font-gray-3, rgba(0, 0, 0, 0.4))
  );
  border-radius: 50%;
  height: var(--td-swiper-nav-btn-size, 48rpx);
  width: var(--td-swiper-nav-btn-size, 48rpx);
}
.t-swiper-nav__btn--next::after,
.t-swiper-nav__btn--prev::after {
  border-color: var(
    --td-swiper-nav-btn-color,
    var(--td-text-color-anti, var(--td-font-white-1, #fff))
  );
  border-style: solid;
  content: "";
  display: block;
  height: 12rpx;
  left: 50%;
  position: absolute;
  top: 50%;
  width: 12rpx;
}
.t-swiper-nav__btn--prev {
  left: 30rpx;
}
.t-swiper-nav__btn--prev::after {
  border-width: 2rpx 0 0 2rpx;
  margin-left: 4rpx;
  transform: translate(-50%, -50%) rotateZ(-45deg);
}
.t-swiper-nav__btn--next {
  right: 30rpx;
}
.t-swiper-nav__btn--next::after {
  border-width: 2rpx 2rpx 0 0;
  margin-left: -4rpx;
  transform: translate(-50%, -50%) rotateZ(45deg);
}
