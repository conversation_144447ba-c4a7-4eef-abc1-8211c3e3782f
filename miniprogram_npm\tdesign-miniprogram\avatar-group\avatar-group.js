Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = require("../../../@babel/runtime/helpers/createClass"),
  t = require("../../../@babel/runtime/helpers/classCallCheck"),
  a = require("../../../@babel/runtime/helpers/inherits"),
  s = require("../../../@babel/runtime/helpers/createSuper"),
  i = require("tslib"),
  r = require("../common/src/index"),
  n = l(require("../common/config")),
  c = l(require("./props"));
function l(e) {
  return e && e.__esModule ? e : { default: e };
}
var o = n.default.prefix,
  u = "".concat(o, "-avatar-group"),
  h = (function (i) {
    a(n, i);
    var r = s(n);
    function n() {
      var e;
      return (
        t(this, n),
        ((e = r.apply(this, arguments)).externalClasses = [
          "".concat(o, "-class"),
          "".concat(o, "-class-content"),
          "".concat(o, "-class-image"),
        ]),
        (e.properties = c.default),
        (e.data = {
          prefix: o,
          classPrefix: u,
          hasChild: !0,
          length: 0,
          className: "",
        }),
        (e.options = { multipleSlots: !0 }),
        (e.relations = { "../avatar/avatar": { type: "descendant" } }),
        (e.lifetimes = {
          attached: function () {
            this.setClass();
          },
          ready: function () {
            this.setData({ length: this.$children.length }), this.handleMax();
          },
        }),
        (e.observers = {
          "cascading, size": function () {
            this.setClass();
          },
        }),
        (e.methods = {
          setClass: function () {
            var e = this.properties,
              t = e.cascading,
              a = e.size,
              s = t.split("-")[0],
              i = [
                u,
                "".concat(o, "-class"),
                "".concat(u, "-offset-").concat(s),
                ""
                  .concat(u, "-offset-")
                  .concat(s, "-")
                  .concat(a.indexOf("px") > -1 ? "medium" : a || "medium"),
              ];
            this.setData({ className: i.join(" ") });
          },
          handleMax: function () {
            var e = this.data.max,
              t = this.$children.length;
            !e ||
              e > t ||
              this.$children.splice(e, t - e).forEach(function (e) {
                e.hide();
              });
          },
          onCollapsedItemClick: function (e) {
            this.triggerEvent("collapsed-item-click", e.detail);
          },
        }),
        e
      );
    }
    return e(n);
  })(r.SuperComponent),
  d = (h = (0, i.__decorate)([(0, r.wxComponent)()], h));
exports.default = d;
