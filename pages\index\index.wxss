page {
  background-color: #f5f5f5;
  box-sizing: border-box;
  min-height: 100vh;
  padding-bottom: 180rpx;
}
.lb_box {
  background-color: #fff;
  margin: 0 auto;
  padding: 10rpx;
  width: 95%;
}
.lb_box .img {
  height: 100%;
  width: 100%;
}
.fl_box {
  --td-grid-item-image-width: 60rpx;
  --td-grid-item-image-middle-width: 60rpx;
  --td-grid-item-image-small-width: 60rpx;
  margin: 10rpx auto;
  width: 95%;
}
.grid-item {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: flex-start;
}
.custom-image {
  flex-shrink: 0;
  height: 60rpx !important;
  width: 60rpx !important;
}
.grid-item .t-grid-item__content {
  height: 100%;
}
.grid-item .t-grid-item__content,
.grid-item .t-grid-item__text {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.grid-item .t-grid-item__text {
  font-size: 32rpx;
  font-weight: 700;
  line-height: 1.2;
}
.fenxiang_box {
  margin: 20rpx auto;
  width: 95%;
}
.fenxiang_box image {
  height: 180rpx;
  width: 100%;
}
.tuijian_fl_box {
  margin: 10rpx auto;
  width: 95%;
}
.nr_box {
  margin: 20rpx auto;
  width: 95%;
}
.nr_box:last-child {
  margin-bottom: 40rpx;
}
.active-tab,
.active-tab .t-grid-item__text {
  color: #fa5252;
}
.active-tab .t-grid-item__description {
  color: #b0abab;
  opacity: 0.8;
}
.loading-container {
  padding: 60rpx 0;
}
.load-more,
.loading-container {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.load-more {
  margin-top: 20rpx;
  padding: 40rpx 0;
}
.loading-more {
  align-items: center;
  color: #999;
  display: -webkit-flex;
  display: flex;
}
.load-more-text {
  background-color: #fff;
  border: 1rpx solid #195abf;
  border-radius: 40rpx;
  color: #195abf;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
}
.no-more {
  color: #999;
  font-size: 24rpx;
  padding: 40rpx 0;
  text-align: center;
}
.empty-state {
  padding: 100rpx 0;
}
.empty-state,
.refresh-container {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
}
.refresh-container {
  color: #666;
  font-size: 28rpx;
  padding: 20rpx 0;
}
.refresh-text {
  margin-left: 10rpx;
}
.category-title {
  color: #333;
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
}
.category-grid {
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
}
.category-item {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20rpx 10rpx;
}
.category-image {
  border-radius: 10rpx;
  height: 60rpx !important;
  margin-bottom: 10rpx;
  width: 60rpx !important;
}
.category-item .t-grid-item__text {
  color: #333;
  font-size: 24rpx;
  line-height: 1.2;
  text-align: center;
}
.empty-category {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  padding: 60rpx 0;
}
.category-item:active {
  background-color: #f5f5f5;
  border-radius: 10rpx;
}
.custom-tabs {
  background-color: #eff2f5 !important;
  justify-content: flex-start !important;
}
.custom-tabs .t-tabs__item {
  color: #1e1e1e !important;
}
.custom-tabs .t-tabs__item--active {
  color: #ff4d4f !important;
}
.custom-track {
  background-color: #ff4d4f !important;
}
