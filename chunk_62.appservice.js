__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/add-help/add-help": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { noteLength: new Array(1), titleLength: new Array(1) },
          K = U === true,
          b = (C) => {},
          f = (C, T) => {
            C ? T("标题") : T();
          },
          h = (C) => {},
          i = (C, T) => {
            C || K || !!U.titleLength || undefined
              ? T(Y(Y(D.titleLength) + "/15"), (N) => {
                  A["titleLength"][0] = (D, E, T) => {
                    T(N, Y(Y(D.titleLength) + "/15"));
                  };
                })
              : T();
          },
          g = (C, T, E) => {
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请输入事情名称");
                if (C || K || Z(U.formData, "title"))
                  O(N, "value", X(D.formData).title);
                if (C) O(N, "bindinput", "onTitleInput");
                if (C) O(N, "maxlength", "15");
              },
              h
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "char-count-inner");
              },
              i
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "input-wrapper");
              },
              g
            );
          },
          k = (C, T) => {
            C ? T("缩略图") : T();
          },
          n,
          p = (C) => {},
          r = (C, T) => {
            C ? T("点击选择缩略图") : T();
          },
          q = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder-text");
              },
              r
            );
          },
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "image",
                {},
                (N, C) => {
                  if (C) L(N, "selected-thumbnail");
                  if (C || K || Z(U.formData, "thumbnail"))
                    O(N, "src", X(D.formData).thumbnail);
                  if (C) O(N, "mode", "aspectFill");
                },
                p
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "thumbnail-placeholder");
                },
                q
              );
            }
          },
          m = (C, T, E, B) => {
            n = X(D.formData).thumbnail ? 1 : 0;
            B(n, o);
          },
          l = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-preview");
                if (C) O(N, "bindtap", "showThumbnailSelector");
              },
              m
            );
          },
          j = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              k
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-section");
              },
              l
            );
          },
          t = (C, T) => {
            C ? T("报酬") : T();
          },
          u = (C) => {},
          s = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              t
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请写简单的内容");
                if (C || K || Z(U.formData, "reward"))
                  O(N, "value", X(D.formData).reward);
                if (C) O(N, "bindinput", "onRewardInput");
              },
              u
            );
          },
          w = (C, T) => {
            C ? T("截止时间") : T();
          },
          y = (C, T) => {
            C || K || !!Z(U.formData, "deadline") || undefined
              ? T(Y(X(D.formData).deadline || "2025-05-22 17:00"))
              : T();
          },
          x = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-input picker-input");
              },
              y
            );
          },
          v = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              w
            );
            E(
              "picker",
              {},
              (N, C) => {
                if (C) O(N, "mode", "date");
                if (C || K || Z(U.formData, "deadline"))
                  O(N, "value", X(D.formData).deadline);
                if (C) O(N, "bindchange", "onDateChange");
              },
              x
            );
          },
          A0 = (C, T) => {
            C ? T("说明") : T();
          },
          C0 = (C) => {},
          D0 = (C, T) => {
            C || K || !!U.noteLength || undefined
              ? T(Y(Y(D.noteLength) + "/200"), (N) => {
                  A["noteLength"][0] = (D, E, T) => {
                    T(N, Y(Y(D.noteLength) + "/200"));
                  };
                })
              : T();
          },
          B0 = (C, T, E) => {
            E(
              "textarea",
              {},
              (N, C) => {
                if (C) L(N, "form-textarea");
                if (C)
                  O(
                    N,
                    "placeholder",
                    "请输入具体的详细说明，需要什么商品或详细内容"
                  );
                if (C || K || Z(U.formData, "note"))
                  O(N, "value", X(D.formData).note);
                if (C) O(N, "bindinput", "onNoteInput");
                if (C) O(N, "maxlength", "200");
              },
              C0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "char-count-inner textarea-count");
              },
              D0
            );
          },
          z = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              A0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "textarea-wrapper");
              },
              B0
            );
          },
          F0 = (C, T) => {
            C ? T("接单人限制") : T();
          },
          J0 = (C) => {},
          K0 = (C, T) => {
            C ? T("不限制") : T();
          },
          I0 = (C, T, E) => {
            E(
              "radio",
              {},
              (N, C) => {
                if (C) O(N, "value", "unlimited");
                if (C || K || !!Z(U.formData, "contactType") || undefined)
                  O(N, "checked", X(D.formData).contactType === "unlimited");
              },
              J0
            );
            E("text", {}, (N, C) => {}, K0);
          },
          M0 = (C) => {},
          N0 = (C, T) => {
            C ? T("未认证用户") : T();
          },
          L0 = (C, T, E) => {
            E(
              "radio",
              {},
              (N, C) => {
                if (C) O(N, "value", "verified");
                if (C || K || !!Z(U.formData, "contactType") || undefined)
                  O(N, "checked", X(D.formData).contactType === "verified");
              },
              M0
            );
            E("text", {}, (N, C) => {}, N0);
          },
          P0 = (C) => {},
          Q0 = (C, T) => {
            C ? T("认证业主") : T();
          },
          O0 = (C, T, E) => {
            E(
              "radio",
              {},
              (N, C) => {
                if (C) O(N, "value", "owner");
                if (C || K || !!Z(U.formData, "contactType") || undefined)
                  O(N, "checked", X(D.formData).contactType === "owner");
              },
              P0
            );
            E("text", {}, (N, C) => {}, Q0);
          },
          S0 = (C) => {},
          T0 = (C, T) => {
            C ? T("认证租客") : T();
          },
          R0 = (C, T, E) => {
            E(
              "radio",
              {},
              (N, C) => {
                if (C) O(N, "value", "tenant");
                if (C || K || !!Z(U.formData, "contactType") || undefined)
                  O(N, "checked", X(D.formData).contactType === "tenant");
              },
              S0
            );
            E("text", {}, (N, C) => {}, T0);
          },
          H0 = (C, T, E) => {
            E(
              "label",
              {},
              (N, C) => {
                if (C) L(N, "radio-item");
              },
              I0
            );
            E(
              "label",
              {},
              (N, C) => {
                if (C) L(N, "radio-item");
              },
              L0
            );
            E(
              "label",
              {},
              (N, C) => {
                if (C) L(N, "radio-item");
              },
              O0
            );
            E(
              "label",
              {},
              (N, C) => {
                if (C) L(N, "radio-item");
              },
              R0
            );
          },
          G0 = (C, T, E) => {
            E(
              "radio-group",
              {},
              (N, C) => {
                if (C) O(N, "bindchange", "onContactChange");
              },
              H0
            );
          },
          E0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              F0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "contact-options");
              },
              G0
            );
          },
          V0 = (C, T) => {
            C ? T("取消") : T();
          },
          W0 = (C, T) => {
            C ? T("保存") : T();
          },
          X0 = (C, T) => {
            C ? T("发布") : T();
          },
          U0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "onCancel");
              },
              V0
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-save");
                if (C) O(N, "bindtap", "onSave");
              },
              W0
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-publish");
                if (C) O(N, "bindtap", "onPublish");
              },
              X0
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              z
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              E0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "button-group");
              },
              U0
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-container");
              },
              d
            );
          },
          Y0,
          b0 = (C) => {},
          e0 = (C, T) => {
            C ? T("选择缩略图") : T();
          },
          d0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "modal-title");
              },
              e0
            );
          },
          g0 = (C, h0, i0, j0, k0, l0, T, E) => {
            var n0 = (C) => {},
              o0 = (C, T) => {
                C || K || Z(j0, "name") ? T(Y(X(h0).name)) : T();
              },
              p0,
              r0 = (C, T) => {
                C ? T("✓") : T();
              },
              q0 = (C, T, E) => {
                if (p0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "selected-mark");
                    },
                    r0
                  );
                }
              },
              m0 = (C, T, E, B) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "thumbnail-img");
                    if (C || K || Z(j0, "src")) O(N, "src", X(h0).src);
                    if (C) O(N, "mode", "aspectFill");
                  },
                  n0
                );
                E(
                  "text",
                  {},
                  (N, C) => {
                    if (C) L(N, "thumbnail-name");
                  },
                  o0
                );
                p0 = X(D.formData).thumbnail === X(h0).src ? 1 : 0;
                B(p0, q0);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-item");
                if (C) O(N, "bindtap", "selectThumbnail");
                if (C || K || Z(j0, "src")) R.d(N, "src", X(h0).src);
              },
              m0
            );
          },
          f0 = (C, T, E, B, F) => {
            F(
              D.thumbnailList,
              "index",
              U ? U.thumbnailList : undefined,
              [0, "thumbnailList"],
              g0
            );
          },
          i0 = (C, T) => {
            C ? T("取消") : T();
          },
          h0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "hideThumbnailSelector");
              },
              i0
            );
          },
          c0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-header");
              },
              d0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "thumbnail-grid");
              },
              f0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-footer");
              },
              h0
            );
          },
          a0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-mask");
                if (C) O(N, "bindtap", "hideThumbnailSelector");
              },
              b0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-content");
              },
              c0
            );
          },
          Z0 = (C, T, E) => {
            if (Y0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "thumbnail-modal");
                },
                a0
              );
            }
          },
          a = (C, T, E, B) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "发布-有偿求助");
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              b
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              c
            );
            Y0 = D.showThumbnailModal ? 1 : 0;
            B(Y0, Z0);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/add-help/add-help";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/add-help/add-help.js";
define(
  "pages/add-help/add-help.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Page({
      data: {
        formData: {
          title: "",
          reward: "",
          deadline: "",
          note: "",
          contactType: "unlimited",
          thumbnail: "",
        },
        titleLength: 0,
        noteLength: 0,
        showThumbnailModal: !1,
        thumbnailList: [
          { src: "/assets/slt/汉堡包.png", name: "汉堡包" },
          { src: "/assets/slt/美食.png", name: "美食" },
          { src: "/assets/slt/鱼生套餐.png", name: "鱼生套餐" },
        ],
      },
      onLoad: function (t) {
        var a = new Date();
        a.setDate(a.getDate() + 1);
        var n = a.toISOString().split("T")[0];
        this.setData({ "formData.deadline": n });
      },
      onTitleInput: function (t) {
        var a = t.detail.value;
        this.setData({ "formData.title": a, titleLength: a.length });
      },
      onRewardInput: function (t) {
        this.setData({ "formData.reward": t.detail.value });
      },
      onNoteInput: function (t) {
        var a = t.detail.value;
        this.setData({ "formData.note": a, noteLength: a.length });
      },
      onDateChange: function (t) {
        this.setData({ "formData.deadline": t.detail.value });
      },
      onContactChange: function (t) {
        this.setData({ "formData.contactType": t.detail.value });
      },
      onCancel: function () {
        wx.showModal({
          title: "提示",
          content: "确定要取消发布吗？",
          success: function (t) {
            t.confirm && wx.navigateBack();
          },
        });
      },
      onSave: function () {
        this.validateForm() &&
          (wx.setStorageSync("draft_help", this.data.formData),
          wx.showToast({ title: "保存成功", icon: "success" }));
      },
      onPublish: function () {
        this.validateForm() &&
          (wx.showLoading({ title: "发布中..." }),
          setTimeout(function () {
            wx.hideLoading(),
              wx.showToast({
                title: "发布成功",
                icon: "success",
                success: function () {
                  setTimeout(function () {
                    wx.navigateBack();
                  }, 1500);
                },
              });
          }, 1e3));
      },
      validateForm: function () {
        var t = this.data.formData,
          a = t.title,
          n = t.reward,
          e = t.deadline;
        return a.trim()
          ? n.trim()
            ? !!e ||
              (wx.showToast({ title: "请选择截止时间", icon: "none" }), !1)
            : (wx.showToast({ title: "请输入报酬", icon: "none" }), !1)
          : (wx.showToast({ title: "请输入标题", icon: "none" }), !1);
      },
      onReady: function () {},
      onShow: function () {},
      onHide: function () {},
      onUnload: function () {},
      onPullDownRefresh: function () {},
      onReachBottom: function () {},
      onShareAppMessage: function () {},
      showThumbnailSelector: function () {
        this.setData({ showThumbnailModal: !0 });
      },
      hideThumbnailSelector: function () {
        this.setData({ showThumbnailModal: !1 });
      },
      selectThumbnail: function (t) {
        var a = t.currentTarget.dataset.src;
        this.setData({ "formData.thumbnail": a, showThumbnailModal: !1 }),
          wx.showToast({
            title: "缩略图已选择",
            icon: "success",
            duration: 1500,
          });
      },
    });
  },
  { isPage: true, isComponent: true, currentFile: "pages/add-help/add-help.js" }
);
require("pages/add-help/add-help.js");
