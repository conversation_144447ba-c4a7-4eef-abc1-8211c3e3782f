__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/input/input": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/input/input"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            safePasswordNonce: new Array(1),
            customStyle: new Array(1),
            layout: new Array(1),
            placeholderClass: new Array(1),
            cursorColor: new Array(1),
            safePasswordTimeStamp: new Array(1),
            type: new Array(3),
            holdKeyboard: new Array(1),
            placeholder: new Array(1),
            confirmHold: new Array(1),
            focus: new Array(1),
            borderless: new Array(1),
            autoFocus: new Array(1),
            disabled: new Array(3),
            status: new Array(1),
            placeholderStyle: new Array(1),
            cursor: new Array(1),
            selectionStart: new Array(1),
            maxlength: new Array(1),
            safePasswordSalt: new Array(1),
            readonly: new Array(1),
            selectionEnd: new Array(1),
            safePasswordCustomHash: new Array(1),
            alwaysEmbed: new Array(1),
            cursorSpacing: new Array(1),
            confirmType: new Array(1),
            allowInputOverMax: new Array(1),
            safePasswordLength: new Array(1),
            style: new Array(1),
            safePasswordCertPath: new Array(1),
            adjustPosition: new Array(1),
          },
          K = U === true,
          g,
          i,
          j = (C, T, E, B, F, S, J) => {
            var $A = I(i);
            if (i && $A)
              $A(
                R,
                C,
                Object.assign(
                  { tClass: D.prefix + "-class-prefix-icon", ariaHidden: true },
                  X(D._prefixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._prefixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._prefixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          h = (C, T, E, B, F, S, J) => {
            if (g === 1) {
              i = "icon";
              B(i, j);
            }
          },
          f = (C, T, E, B, F, S) => {
            S("prefix-icon");
            g = D._prefixIcon ? 1 : 0;
            B(g, h);
          },
          l,
          m = (C, T) => {
            if (l === 1) {
              C || K || U.label ? T(Y(D.label)) : T();
            }
          },
          k = (C, T, E, B, F, S) => {
            S("label");
            l = D.label ? 1 : 0;
            B(l, m);
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon--prefix");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__label " + Y(D.prefix) + "-class-label"
                  );
                if (C) O(N, "aria-hidden", true);
              },
              k
            );
          },
          p = (C) => {},
          q,
          t,
          u = (C, T, E, B, F, S, J) => {
            var $A = I(t);
            if (t && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass: D.prefix + "-class-clearable",
                    ariaRole: "button",
                    ariaLabel: "清除",
                  },
                  X(D._clearIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._clearIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._clearIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          s = (C, T, E, B, F, S, J) => {
            t = "icon";
            B(t, u);
          },
          r = (C, T, E) => {
            if (q === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__wrap--clearable-icon");
                  if (C) R.v(N, "tap", "clearInput", !1, !1, !1, !1);
                },
                s
              );
            }
          },
          w,
          y = (C, T) => {
            C || K || U.suffix ? T(Y(D.suffix)) : T();
          },
          x = (C, T, E) => {
            if (w === 1) {
              E("text", {}, (N, C) => {}, y);
            }
          },
          v = (C, T, E, B, F, S) => {
            w = D.suffix ? 1 : 0;
            B(w, x);
            S("suffix");
          },
          A0,
          C0,
          D0 = (C, T, E, B, F, S, J) => {
            var $A = I(C0);
            if (C0 && $A)
              $A(
                R,
                C,
                Object.assign(
                  {
                    tClass: D.prefix + "-class-suffix-icon",
                    ariaRole: "button",
                  },
                  X(D._suffixIcon),
                  {}
                ),
                K ||
                  (U
                    ? U._suffixIcon === true ||
                      Object.assign(
                        { tClass: !!U.prefix || undefined },
                        X(U._suffixIcon),
                        {}
                      )
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          B0 = (C, T, E, B, F, S, J) => {
            if (A0 === 1) {
              C0 = "icon";
              B(C0, D0);
            }
          },
          z = (C, T, E, B, F, S) => {
            S("suffix-icon");
            A0 = D._suffixIcon ? 1 : 0;
            B(A0, B0);
          },
          o = (C, T, E, B) => {
            E(
              "input",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getInputClass") ||
                    U.classPrefix ||
                    U.suffix ||
                    U.align ||
                    U.disabled ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(b).getInputClass)(
                        D.classPrefix,
                        D.suffix,
                        D.align,
                        D.disabled
                      )
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-input"
                  );
                A["disabled"][2] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(b).getInputClass)(
                        D.classPrefix,
                        D.suffix,
                        D.align,
                        D.disabled
                      )
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-input"
                  );
                };
                var $A = D.allowInputOverMax;
                if (
                  C ||
                  K ||
                  !!U.allowInputOverMax ||
                  ($A ? undefined : U.maxlength)
                )
                  O(N, "maxlength", $A ? -1 : D.maxlength);
                A["allowInputOverMax"][0] = A["maxlength"][0] = (D, E, T) => {
                  var $B = D.allowInputOverMax;
                  O(N, "maxlength", $B ? -1 : D.maxlength);
                  E(N);
                };
                if (C || K || !!(U.disabled || U.readonly) || undefined)
                  O(N, "disabled", D.disabled || D.readonly);
                A["disabled"][0] = A["readonly"][0] = (D, E, T) => {
                  O(N, "disabled", D.disabled || D.readonly);
                  E(N);
                };
                if (C || K || U.placeholder) O(N, "placeholder", D.placeholder);
                A["placeholder"][0] = (D, E, T) => {
                  O(N, "placeholder", D.placeholder);
                  E(N);
                };
                if (C || K || U.placeholderStyle)
                  O(N, "placeholder-style", D.placeholderStyle);
                A["placeholderStyle"][0] = (D, E, T) => {
                  O(N, "placeholder-style", D.placeholderStyle);
                  E(N);
                };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.disabled])]) ||
                    U.placeholderClass
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "placeholder-class",
                    Y(
                      P(X(a).cls)(D.classPrefix + "__placeholder", [
                        ["disabled", D.disabled],
                      ])
                    ) +
                      " " +
                      Y(D.placeholderClass)
                  );
                A["disabled"][1] = A["placeholderClass"][0] = (D, E, T) => {
                  O(
                    N,
                    "placeholder-class",
                    Y(
                      P(X(a).cls)(D.classPrefix + "__placeholder", [
                        ["disabled", D.disabled],
                      ])
                    ) +
                      " " +
                      Y(D.placeholderClass)
                  );
                  E(N);
                };
                if (C || K || U.value) O(N, "value", D.value);
                if (C || K || !!U.type || undefined)
                  O(N, "password", D.type === "password");
                A["type"][0] = (D, E, T) => {
                  O(N, "password", D.type === "password");
                  E(N);
                };
                var $B = D.type === "password";
                if (C || K || !!U.type || ($B ? undefined : U.type))
                  O(N, "type", $B ? "text" : D.type);
                A["type"][1] = A["type"][2] = (D, E, T) => {
                  var $C = D.type === "password";
                  O(N, "type", $C ? "text" : D.type);
                  E(N);
                };
                if (C || K || U.focus) O(N, "focus", D.focus);
                A["focus"][0] = (D, E, T) => {
                  O(N, "focus", D.focus);
                  E(N);
                };
                if (C || K || U.confirmType)
                  O(N, "confirm-type", D.confirmType);
                A["confirmType"][0] = (D, E, T) => {
                  O(N, "confirm-type", D.confirmType);
                  E(N);
                };
                if (C || K || U.confirmHold)
                  O(N, "confirm-hold", D.confirmHold);
                A["confirmHold"][0] = (D, E, T) => {
                  O(N, "confirm-hold", D.confirmHold);
                  E(N);
                };
                if (C || K || U.cursor) O(N, "cursor", D.cursor);
                A["cursor"][0] = (D, E, T) => {
                  O(N, "cursor", D.cursor);
                  E(N);
                };
                if (C || K || U.cursorColor)
                  O(N, "cursor-color", D.cursorColor);
                A["cursorColor"][0] = (D, E, T) => {
                  O(N, "cursor-color", D.cursorColor);
                  E(N);
                };
                if (C || K || U.cursorSpacing)
                  O(N, "cursor-spacing", D.cursorSpacing);
                A["cursorSpacing"][0] = (D, E, T) => {
                  O(N, "cursor-spacing", D.cursorSpacing);
                  E(N);
                };
                if (C || K || U.adjustPosition)
                  O(N, "adjust-position", D.adjustPosition);
                A["adjustPosition"][0] = (D, E, T) => {
                  O(N, "adjust-position", D.adjustPosition);
                  E(N);
                };
                if (C || K || U.autoFocus) O(N, "auto-focus", D.autoFocus);
                A["autoFocus"][0] = (D, E, T) => {
                  O(N, "auto-focus", D.autoFocus);
                  E(N);
                };
                if (C || K || U.alwaysEmbed)
                  O(N, "always-embed", D.alwaysEmbed);
                A["alwaysEmbed"][0] = (D, E, T) => {
                  O(N, "always-embed", D.alwaysEmbed);
                  E(N);
                };
                if (C || K || U.selectionStart)
                  O(N, "selection-start", D.selectionStart);
                A["selectionStart"][0] = (D, E, T) => {
                  O(N, "selection-start", D.selectionStart);
                  E(N);
                };
                if (C || K || U.selectionEnd)
                  O(N, "selection-end", D.selectionEnd);
                A["selectionEnd"][0] = (D, E, T) => {
                  O(N, "selection-end", D.selectionEnd);
                  E(N);
                };
                if (C || K || U.holdKeyboard)
                  O(N, "hold-keyboard", D.holdKeyboard);
                A["holdKeyboard"][0] = (D, E, T) => {
                  O(N, "hold-keyboard", D.holdKeyboard);
                  E(N);
                };
                if (C || K || U.safePasswordCertPath)
                  O(N, "safe-password-cert-path", D.safePasswordCertPath);
                A["safePasswordCertPath"][0] = (D, E, T) => {
                  O(N, "safe-password-cert-path", D.safePasswordCertPath);
                  E(N);
                };
                if (C || K || U.safePasswordLength)
                  O(N, "safe-password-length", D.safePasswordLength);
                A["safePasswordLength"][0] = (D, E, T) => {
                  O(N, "safe-password-length", D.safePasswordLength);
                  E(N);
                };
                if (C || K || U.safePasswordTimeStamp)
                  O(N, "safe-password-time-stamp", D.safePasswordTimeStamp);
                A["safePasswordTimeStamp"][0] = (D, E, T) => {
                  O(N, "safe-password-time-stamp", D.safePasswordTimeStamp);
                  E(N);
                };
                if (C || K || U.safePasswordNonce)
                  O(N, "safe-password-nonce", D.safePasswordNonce);
                A["safePasswordNonce"][0] = (D, E, T) => {
                  O(N, "safe-password-nonce", D.safePasswordNonce);
                  E(N);
                };
                if (C || K || U.safePasswordSalt)
                  O(N, "safe-password-salt", D.safePasswordSalt);
                A["safePasswordSalt"][0] = (D, E, T) => {
                  O(N, "safe-password-salt", D.safePasswordSalt);
                  E(N);
                };
                if (C || K || U.safePasswordCustomHash)
                  O(N, "safe-password-custom-hash", D.safePasswordCustomHash);
                A["safePasswordCustomHash"][0] = (D, E, T) => {
                  O(N, "safe-password-custom-hash", D.safePasswordCustomHash);
                  E(N);
                };
                if (C) O(N, "aria-role", "textbox");
                if (C || K || U.label) O(N, "aria-label", D.label);
                if (C || K || U.label) O(N, "aria-roledescription", D.label);
                if (C) O(N, "bindinput", "onInput");
                if (C) O(N, "bindfocus", "onFocus");
                if (C) O(N, "bindblur", "onBlur");
                if (C) O(N, "bindconfirm", "onConfirm");
                if (C)
                  O(N, "bindkeyboardheightchange", "onKeyboardHeightChange");
                if (C) O(N, "bindnicknamereview", "onNickNameReview");
              },
              p
            );
            q =
              D._clearIcon && X(D.value).length > 0 && D.showClearIcon ? 1 : 0;
            B(q, r);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__wrap--suffix " +
                      Y(D.prefix) +
                      "-class-suffix"
                  );
                if (C) R.v(N, "tap", "onSuffixClick", !1, !1, !1, !1);
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrap--suffix-icon");
                if (C) R.v(N, "tap", "onSuffixIconClick", !1, !1, !1, !1);
              },
              z
            );
          },
          E0,
          G0 = (C, T) => {
            C || K || U.tips ? T(Y(D.tips)) : T();
          },
          F0 = (C, T, E) => {
            if (E0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(U.classPrefix || U.classPrefix || U.align || U.prefix) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__tips " +
                        Y(D.classPrefix) +
                        "--" +
                        Y(D.align) +
                        " " +
                        Y(D.prefix) +
                        "-class-tips"
                    );
                },
                G0
              );
            }
          },
          n = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.status) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.status)
                  );
                A["status"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.status)
                  );
                };
              },
              o
            );
            E0 = D.tips && X(D.tips).length > 0 ? 1 : 0;
            B(E0, F0);
            S("tips");
          },
          d = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrap--prefix");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__wrap");
              },
              n
            );
            S("extra");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([!!U.borderless || undefined])]) ||
                    U.classPrefix ||
                    U.layout ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix, [["border", !D.borderless]])) +
                      " " +
                      Y(D.classPrefix) +
                      "--layout-" +
                      Y(D.layout) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["borderless"][0] = A["layout"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(P(X(a).cls)(D.classPrefix, [["border", !D.borderless]])) +
                      " " +
                      Y(D.classPrefix) +
                      "--layout-" +
                      Y(D.layout) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-describedby", true);
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/input/input";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/input/input.js";
define(
  "miniprogram_npm/tdesign-miniprogram/input/input.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      i = require("../../../@babel/runtime/helpers/createSuper"),
      r = require("tslib"),
      n = require("../common/src/index"),
      s = u(require("../common/config")),
      c = u(require("./props")),
      l = require("../common/utils"),
      o = require("../common/validator");
    function u(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var h = s.default.prefix,
      f = "".concat(h, "-input"),
      d = (function (r) {
        a(s, r);
        var n = i(s);
        function s() {
          var e;
          return (
            t(this, s),
            ((e = n.apply(this, arguments)).options = { multipleSlots: !0 }),
            (e.externalClasses = [
              "".concat(h, "-class"),
              "".concat(h, "-class-prefix-icon"),
              "".concat(h, "-class-label"),
              "".concat(h, "-class-input"),
              "".concat(h, "-class-clearable"),
              "".concat(h, "-class-suffix"),
              "".concat(h, "-class-suffix-icon"),
              "".concat(h, "-class-tips"),
            ]),
            (e.behaviors = ["wx://form-field"]),
            (e.properties = c.default),
            (e.data = {
              prefix: h,
              classPrefix: f,
              classBasePrefix: h,
              showClearIcon: !0,
            }),
            (e.lifetimes = {
              ready: function () {
                var e,
                  t = this.properties,
                  a = t.value,
                  i = t.defaultValue;
                this.updateValue(
                  null !== (e = null != a ? a : i) && void 0 !== e ? e : ""
                );
              },
            }),
            (e.observers = {
              prefixIcon: function (e) {
                this.setData({ _prefixIcon: (0, l.calcIcon)(e) });
              },
              suffixIcon: function (e) {
                this.setData({ _suffixIcon: (0, l.calcIcon)(e) });
              },
              clearable: function (e) {
                this.setData({
                  _clearIcon: (0, l.calcIcon)(e, "close-circle-filled"),
                });
              },
              "clearTrigger, clearable, disabled, readonly": function () {
                this.updateClearIconVisible();
              },
            }),
            (e.methods = {
              updateValue: function (e) {
                var t = this.properties,
                  a = t.allowInputOverMax,
                  i = t.maxcharacter,
                  r = t.maxlength;
                if (!a && i && i > 0 && !Number.isNaN(i)) {
                  var n = (0, l.getCharacterLength)("maxcharacter", e, i),
                    s = n.length,
                    c = n.characters;
                  this.setData({ value: c, count: s });
                } else if (!a && r && r > 0 && !Number.isNaN(r)) {
                  var u = (0, l.getCharacterLength)("maxlength", e, r),
                    h = u.length,
                    f = u.characters;
                  this.setData({ value: f, count: h });
                } else
                  this.setData({
                    value: e,
                    count: (0, o.isDef)(e) ? String(e).length : 0,
                  });
              },
              updateClearIconVisible: function () {
                var e =
                    arguments.length > 0 &&
                    void 0 !== arguments[0] &&
                    arguments[0],
                  t = this.properties,
                  a = t.clearTrigger,
                  i = t.disabled,
                  r = t.readonly;
                i || r
                  ? this.setData({ showClearIcon: !1 })
                  : this.setData({ showClearIcon: e || "always" === a });
              },
              onInput: function (e) {
                var t = e.detail,
                  a = t.value,
                  i = t.cursor,
                  r = t.keyCode;
                this.updateValue(a),
                  this.triggerEvent("change", {
                    value: this.data.value,
                    cursor: i,
                    keyCode: r,
                  });
              },
              onFocus: function (e) {
                this.updateClearIconVisible(!0),
                  this.triggerEvent("focus", e.detail);
              },
              onBlur: function (e) {
                if (
                  (this.updateClearIconVisible(),
                  "function" == typeof this.properties.format)
                ) {
                  var t = this.properties.format(e.detail.value);
                  return (
                    this.updateValue(t),
                    void this.triggerEvent("blur", {
                      value: this.data.value,
                      cursor: this.data.count,
                    })
                  );
                }
                this.triggerEvent("blur", e.detail);
              },
              onConfirm: function (e) {
                this.triggerEvent("enter", e.detail);
              },
              onSuffixClick: function () {
                this.triggerEvent("click", { trigger: "suffix" });
              },
              onSuffixIconClick: function () {
                this.triggerEvent("click", { trigger: "suffix-icon" });
              },
              clearInput: function (e) {
                this.triggerEvent("clear", e.detail),
                  this.setData({ value: "" });
              },
              onKeyboardHeightChange: function (e) {
                this.triggerEvent("keyboardheightchange", e.detail);
              },
              onNickNameReview: function (e) {
                this.triggerEvent("nicknamereview", e.detail);
              },
            }),
            e
          );
        }
        return e(s);
      })(n.SuperComponent),
      p = (d = (0, r.__decorate)([(0, n.wxComponent)()], d));
    exports.default = p;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/input/input.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/input/input.js");
