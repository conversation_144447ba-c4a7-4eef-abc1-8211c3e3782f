__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/stepper/stepper": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            currentValue: new Array(5),
            step: new Array(2),
            size: new Array(4),
            style: new Array(1),
            classPrefix: new Array(14),
            prefix: new Array(4),
            customStyle: new Array(1),
            min: new Array(2),
            disablePlus: new Array(2),
            disableInput: new Array(2),
            inputWidth: new Array(2),
            max: new Array(2),
            disabled: new Array(6),
            theme: new Array(6),
            disableMinus: new Array(2),
            integer: new Array(1),
          },
          K = U === true,
          e = (C) => {},
          d = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C) O(N, "name", "remove");
              },
              e
            );
          },
          g = (C) => {},
          f = (C, T, E) => {
            E(
              "input",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.size || U.prefix) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__input " +
                      Y(D.classPrefix) +
                      "__input--" +
                      Y(D.size) +
                      " " +
                      Y(D.prefix) +
                      "-class-input"
                  );
                A["classPrefix"][8] =
                  A["classPrefix"][9] =
                  A["size"][2] =
                  A["prefix"][2] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__input " +
                          Y(D.classPrefix) +
                          "__input--" +
                          Y(D.size) +
                          " " +
                          Y(D.prefix) +
                          "-class-input"
                      );
                    };
                var $A = D.inputWidth;
                if (
                  C ||
                  K ||
                  !!U.inputWidth ||
                  ($A ? !!U.inputWidth || undefined : undefined)
                )
                  R.y(N, $A ? "width:" + D.inputWidth + "px;" : "");
                A["inputWidth"][0] = A["inputWidth"][1] = (D, E, T) => {
                  var $B = D.inputWidth;
                  R.y(N, $B ? "width:" + D.inputWidth + "px;" : "");
                };
                if (C || K || !!(U.disabled || U.disableInput) || undefined)
                  O(N, "disabled", D.disabled || D.disableInput);
                A["disabled"][3] = A["disableInput"][1] = (D, E, T) => {
                  O(N, "disabled", D.disabled || D.disableInput);
                  E(N);
                };
                var $B = D.integer;
                if (C || K || !!U.integer || ($B ? undefined : undefined))
                  O(N, "type", $B ? "number" : "digit");
                A["integer"][0] = (D, E, T) => {
                  var $C = D.integer;
                  O(N, "type", $C ? "number" : "digit");
                  E(N);
                };
                if (C || K || U.currentValue) O(N, "value", D.currentValue);
                A["currentValue"][2] = (D, E, T) => {
                  O(N, "value", D.currentValue);
                  E(N);
                };
                if (C) O(N, "catchinput", "handleInput");
                if (C) O(N, "catchfocus", "handleFocus");
                if (C) O(N, "catchblur", "handleBlur");
              },
              g
            );
          },
          i = (C) => {},
          h = (C, T, E) => {
            E(
              "t-icon",
              {},
              (N, C) => {
                if (C) O(N, "name", "add");
              },
              i
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A =
                  D.disabled || D.disableMinus || D.currentValue <= D.min;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.classPrefix ||
                    U.theme ||
                    U.classPrefix ||
                    U.size ||
                    U.disabled ||
                    U.disableMinus ||
                    U.currentValue ||
                    U.min ||
                    ($A
                      ? !!(U.classPrefix || U.theme) || undefined
                      : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__minus " +
                      Y(D.classPrefix) +
                      "__minus--" +
                      Y(D.theme) +
                      " " +
                      Y(D.classPrefix) +
                      "__icon--" +
                      Y(D.size) +
                      " " +
                      Y(
                        $A ? D.classPrefix + "--" + D.theme + "-disabled" : ""
                      ) +
                      " " +
                      Y(D.prefix) +
                      "-class-minus"
                  );
                A["classPrefix"][2] =
                  A["classPrefix"][3] =
                  A["theme"][0] =
                  A["classPrefix"][4] =
                  A["size"][1] =
                  A["disabled"][1] =
                  A["disableMinus"][1] =
                  A["currentValue"][1] =
                  A["min"][1] =
                  A["classPrefix"][5] =
                  A["theme"][1] =
                  A["prefix"][1] =
                    (D, E, T) => {
                      var $B =
                        D.disabled || D.disableMinus || D.currentValue <= D.min;
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__minus " +
                          Y(D.classPrefix) +
                          "__minus--" +
                          Y(D.theme) +
                          " " +
                          Y(D.classPrefix) +
                          "__icon--" +
                          Y(D.size) +
                          " " +
                          Y(
                            $B
                              ? D.classPrefix + "--" + D.theme + "-disabled"
                              : ""
                          ) +
                          " " +
                          Y(D.prefix) +
                          "-class-minus"
                      );
                    };
                if (C) O(N, "catchtap", "minusValue");
                if (C || K || !!U.step || undefined)
                  O(N, "aria-label", "减少" + D.step);
                A["step"][0] = (D, E, T) => {
                  O(N, "aria-label", "减少" + D.step);
                  E(N);
                };
                if (C) O(N, "aria-role", "button");
                if (
                  C ||
                  K ||
                  !!(U.disabled || U.disableMinus || U.currentValue || U.min) ||
                  undefined
                )
                  O(
                    N,
                    "aria-disabled",
                    D.disabled || D.disableMinus || D.currentValue <= D.min
                  );
                A["disabled"][0] =
                  A["disableMinus"][0] =
                  A["currentValue"][0] =
                  A["min"][0] =
                    (D, E, T) => {
                      O(
                        N,
                        "aria-disabled",
                        D.disabled || D.disableMinus || D.currentValue <= D.min
                      );
                      E(N);
                    };
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.disabled || D.disableInput;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.theme ||
                    U.disabled ||
                    U.disableInput ||
                    ($A ? !!(U.classPrefix || U.theme) || undefined : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__input--" +
                      Y(D.theme) +
                      " " +
                      Y($A ? D.classPrefix + "--" + D.theme + "-disabled" : "")
                  );
                A["classPrefix"][6] =
                  A["theme"][2] =
                  A["disabled"][2] =
                  A["disableInput"][0] =
                  A["classPrefix"][7] =
                  A["theme"][3] =
                    (D, E, T) => {
                      var $B = D.disabled || D.disableInput;
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__input--" +
                          Y(D.theme) +
                          " " +
                          Y(
                            $B
                              ? D.classPrefix + "--" + D.theme + "-disabled"
                              : ""
                          )
                      );
                    };
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.disabled || D.disablePlus || D.currentValue >= D.max;
                if (
                  C ||
                  K ||
                  !!(
                    U.classPrefix ||
                    U.classPrefix ||
                    U.theme ||
                    U.classPrefix ||
                    U.size ||
                    U.disabled ||
                    U.disablePlus ||
                    U.currentValue ||
                    U.max ||
                    ($A
                      ? !!(U.classPrefix || U.theme) || undefined
                      : undefined) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__plus " +
                      Y(D.classPrefix) +
                      "__plus--" +
                      Y(D.theme) +
                      " " +
                      Y(D.classPrefix) +
                      "__icon--" +
                      Y(D.size) +
                      " " +
                      Y(
                        $A ? D.classPrefix + "--" + D.theme + "-disabled" : ""
                      ) +
                      " " +
                      Y(D.prefix) +
                      "-class-plus"
                  );
                A["classPrefix"][10] =
                  A["classPrefix"][11] =
                  A["theme"][4] =
                  A["classPrefix"][12] =
                  A["size"][3] =
                  A["disabled"][5] =
                  A["disablePlus"][1] =
                  A["currentValue"][4] =
                  A["max"][1] =
                  A["classPrefix"][13] =
                  A["theme"][5] =
                  A["prefix"][3] =
                    (D, E, T) => {
                      var $B =
                        D.disabled || D.disablePlus || D.currentValue >= D.max;
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__plus " +
                          Y(D.classPrefix) +
                          "__plus--" +
                          Y(D.theme) +
                          " " +
                          Y(D.classPrefix) +
                          "__icon--" +
                          Y(D.size) +
                          " " +
                          Y(
                            $B
                              ? D.classPrefix + "--" + D.theme + "-disabled"
                              : ""
                          ) +
                          " " +
                          Y(D.prefix) +
                          "-class-plus"
                      );
                    };
                if (C) O(N, "catchtap", "plusValue");
                if (C || K || !!U.step || undefined)
                  O(N, "aria-label", "增加" + D.step);
                A["step"][1] = (D, E, T) => {
                  O(N, "aria-label", "增加" + D.step);
                  E(N);
                };
                if (C) O(N, "aria-role", "button");
                if (
                  C ||
                  K ||
                  !!(U.disabled || U.disablePlus || U.currentValue || U.max) ||
                  undefined
                )
                  O(
                    N,
                    "aria-disabled",
                    D.disabled || D.disablePlus || D.currentValue >= D.max
                  );
                A["disabled"][4] =
                  A["disablePlus"][0] =
                  A["currentValue"][3] =
                  A["max"][0] =
                    (D, E, T) => {
                      O(
                        N,
                        "aria-disabled",
                        D.disabled || D.disablePlus || D.currentValue >= D.max
                      );
                      E(N);
                    };
              },
              h
            );
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(U.classPrefix || U.classPrefix || U.size || U.prefix) ||
                  undefined
                )
                  L(
                    N,
                    Y(D.classPrefix) +
                      " " +
                      Y(D.classPrefix) +
                      "--" +
                      Y(D.size) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["classPrefix"][0] =
                  A["classPrefix"][1] =
                  A["size"][0] =
                  A["prefix"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(D.classPrefix) +
                          " " +
                          Y(D.classPrefix) +
                          "--" +
                          Y(D.size) +
                          " class " +
                          Y(D.prefix) +
                          "-class"
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/stepper/stepper.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-stepper{-webkit-align-items:center;align-items:center;color:var(--td-stepper-input-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-flex;display:flex}\n.",
        [1],
        "t-stepper__input{height:inherit;margin:0 ",
        [0, 8],
        ";min-height:inherit;text-align:center;vertical-align:top}\n.",
        [1],
        "t-stepper__minus,.",
        [1],
        "t-stepper__plus{box-sizing:border-box;padding:",
        [0, 8],
        "}\n.",
        [1],
        "t-stepper__input,.",
        [1],
        "t-stepper__minus-icon,.",
        [1],
        "t-stepper__plus-icon{color:inherit}\n.",
        [1],
        "t-stepper__input--filled,.",
        [1],
        "t-stepper__input--normal,.",
        [1],
        "t-stepper__input--outline{box-sizing:border-box;height:inherit}\n.",
        [1],
        "t-stepper--small{font-size:",
        [0, 20],
        ";height:",
        [0, 40],
        "}\n.",
        [1],
        "t-stepper--medium{font-size:",
        [0, 24],
        ";height:",
        [0, 48],
        "}\n.",
        [1],
        "t-stepper--large{font-size:",
        [0, 32],
        ";height:",
        [0, 56],
        "}\n.",
        [1],
        "t-stepper__input--small{width:",
        [0, 68],
        "}\n.",
        [1],
        "t-stepper__input--medium{height:",
        [0, 48],
        ";width:",
        [0, 76],
        "}\n.",
        [1],
        "t-stepper__input--large{width:",
        [0, 90],
        "}\n.",
        [1],
        "t-stepper__icon--small{font-size:",
        [0, 24],
        ";height:",
        [0, 40],
        ";width:",
        [0, 40],
        "}\n.",
        [1],
        "t-stepper__icon--medium{font-size:",
        [0, 32],
        ";height:",
        [0, 48],
        ";width:",
        [0, 48],
        "}\n.",
        [1],
        "t-stepper__icon--large{font-size:",
        [0, 40],
        ";height:",
        [0, 56],
        ";width:",
        [0, 56],
        "}\n.",
        [1],
        "t-stepper__minus--outline,.",
        [1],
        "t-stepper__plus--outline{border:",
        [0, 2],
        " solid var(--td-stepper-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)))}\n.",
        [1],
        "t-stepper__input--outline{border:none;border-bottom:",
        [0, 2],
        " solid var(--td-stepper-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));border-top:",
        [0, 2],
        " solid var(--td-stepper-border-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)))}\n.",
        [1],
        "t-stepper__minus--filled,.",
        [1],
        "t-stepper__minus--outline{border-radius:0;border-bottom-left-radius:var(--td-stepper-border-radius,var(--td-radius-small,",
        [0, 6],
        "));border-top-left-radius:var(--td-stepper-border-radius,var(--td-radius-small,",
        [0, 6],
        "))}\n.",
        [1],
        "t-stepper__plus--filled,.",
        [1],
        "t-stepper__plus--outline{border-radius:0;border-bottom-right-radius:var(--td-stepper-border-radius,var(--td-radius-small,",
        [0, 6],
        "));border-top-right-radius:var(--td-stepper-border-radius,var(--td-radius-small,",
        [0, 6],
        "))}\n.",
        [1],
        "t-stepper__input--filled,.",
        [1],
        "t-stepper__minus--filled,.",
        [1],
        "t-stepper__plus--filled{background-color:var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3))}\n.",
        [1],
        "t-stepper__input--filled{margin:0 ",
        [0, 8],
        "}\n.",
        [1],
        "t-stepper__input--filled .",
        [1],
        "t-stepper__input{margin:0}\n.",
        [1],
        "t-stepper--filled-disabled,.",
        [1],
        "t-stepper--normal-disabled,.",
        [1],
        "t-stepper--outline-disabled{color:var(--td-stepper-input-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-stepper--filled-disabled,.",
        [1],
        "t-stepper--outline-disabled{background-color:var(--td-stepper-input-disabled-bg,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)))}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/stepper/stepper.wxss" }
    );
}
