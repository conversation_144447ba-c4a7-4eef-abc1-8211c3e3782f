.tags_box {
  display: -webkit-flex;
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.tags_item {
  color: #333;
  cursor: pointer;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  position: relative;
}
.tags_item.active {
  color: red;
}
.tags_item.active::after {
  background-color: red;
  bottom: -4rpx;
  content: "";
  height: 4rpx;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
  width: 80%;
}
.tags_item:first-child {
  color: #195abf;
}
.tags_item:last-child {
  border-color: #fee;
  color: #f50;
}
.my_service {
  background-color: #fff;
  border: 5rpx solid #f0f0f0;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: auto;
  margin: 10rpx auto;
  padding: 20rpx;
  width: 95%;
}
.fuwu_list {
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
}
.info_left {
  width: 70%;
}
.info_left_top,
.info_left_top_img {
  display: -webkit-flex;
  display: flex;
}
.info_left_top_img {
  align-items: center;
  background-color: #eff2f5;
  border-radius: 15rpx;
  height: 150rpx;
  justify-content: center;
  margin-right: 20rpx;
  padding: 10prx;
  width: 150rpx;
}
.info_left_top_img image {
  border-radius: 10rpx;
  height: 90%;
  object-fit: cover;
  width: 90%;
}
.info_left_top_info {
  flex: 1;
}
.info_left_top_info_title {
  color: #333;
  font-size: 30rpx;
  font-weight: 500;
}
.info_left_top_info_ms {
  color: #666;
  font-size: 22rpx;
  line-height: 1.8;
  margin-bottom: 8rpx;
  margin-top: 8rpx;
}
.info_left_top_info_fw,
.info_left_top_info_time {
  color: #999;
  font-size: 22rpx;
  margin-bottom: 6rpx;
}
.info_left_bottom {
  display: -webkit-flex;
  display: flex;
  margin-top: 20rpx;
}
.info_left_bottom_num {
  color: #999;
  font-size: 22rpx;
  margin-right: 40rpx;
}
.info_right {
  align-items: flex-end;
  flex-direction: column;
  width: 25%;
}
.info_right,
.info_right_top {
  display: -webkit-flex;
  display: flex;
}
.info_right_top {
  align-items: center;
}
.info_right_top_img {
  height: 50rpx;
  margin-right: 10rpx;
  width: 50rpx;
}
.info_right_top_img image {
  border-radius: 50%;
  height: 100%;
  width: 100%;
}
.info_right_top_username {
  color: #f90;
  font-size: 26rpx;
}
.info_right_middle {
  margin: 30rpx 0;
}
.info_right_middle_price {
  color: #f50;
  font-size: 32rpx;
  font-weight: 700;
}
.info_right_bottom {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 20rpx;
}
.margin-16 {
  margin-bottom: 0;
  margin-right: 16rpx;
}
.margin-16:last-child {
  margin-right: 0;
}
.fuwu_caozuo {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: space-around;
  padding-top: 20rpx;
}
.caozuo_item {
  color: #666;
  flex: 1;
  font-size: 24rpx;
  position: relative;
  text-align: center;
}
.caozuo_item:not(:last-child)::after {
  background-color: #e0e0e0;
  content: "";
  height: 24rpx;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2rpx;
}
.caozuo_item:last-child text,
.caozuo_item:nth-last-child(2) text,
.caozuo_item:nth-last-child(3) text {
  color: #195abf;
}
.myhelp_box {
  background-color: #fff;
  border: 5rpx solid #f0f0f0;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  height: auto;
  margin: 10rpx auto;
  padding: 20rpx;
  width: 95%;
}
.help_info {
  align-items: flex-start;
  border-bottom: 2rpx solid #f5f5f5;
  justify-content: space-between;
  padding-bottom: 20rpx;
}
.help_info,
.help_info_left {
  display: -webkit-flex;
  display: flex;
}
.help_info_left {
  align-items: center;
  background: #fee;
  border-radius: 16rpx;
  height: 150rpx;
  justify-content: center;
  width: 150rpx;
}
.help_info_left image {
  border-radius: 16rpx;
  height: 100%;
  width: 100%;
}
.help_info_midle {
  display: -webkit-flex;
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 0 20rpx;
}
.help_title {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.help_shuoming {
  color: #666;
  font-size: 26rpx;
  margin-bottom: 8rpx;
}
.help_overtime {
  color: #999;
  font-size: 24rpx;
}
.help_info_right {
  align-items: flex-end;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  padding: 30rpx 0;
}
.help_price {
  color: #f50;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.help_type {
  color: #666;
  font-size: 24rpx;
  margin-top: 30rpx;
}
.help_caozuo {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  justify-content: space-around;
  padding-top: 20rpx;
}
.help_caozuo_item {
  color: #666;
  flex: 1;
  font-size: 24rpx;
  position: relative;
  text-align: center;
}
.help_caozuo_item:not(:last-child)::after {
  background-color: #e0e0e0;
  content: "";
  height: 24rpx;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2rpx;
}
.help_caozuo_item:last-child text,
.help_caozuo_item:nth-last-child(2) text {
  color: #195abf;
}
.caozuo_item .loading {
  color: #999 !important;
  opacity: 0.6;
}
.caozuo_item .disabled {
  color: #ccc !important;
  opacity: 0.5;
}
.caozuo_item:last-child text {
  color: #195abf;
}
.caozuo_item:last-child .disabled,
.caozuo_item:last-child .loading {
  color: #999 !important;
}
