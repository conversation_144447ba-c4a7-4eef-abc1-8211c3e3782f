@import "/miniprogram_npm/tdesign-miniprogram/common/style/index.wxss";
.t-switch,
.t-switch__label {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  overflow: hidden;
}
.t-switch__label {
  bottom: 0;
  color: var(
    --td-switch-label-color,
    var(
      --td-bg-color-secondarycontainer-active,
      var(--td-gray-color-4, #dcdcdc)
    )
  );
  flex-wrap: nowrap;
  font-size: var(--td-switch-label-font-size, 28rpx);
  justify-content: center;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}
.t-switch__label--checked {
  color: var(
    --td-switch-label-checked-color,
    var(
      --td-switch-checked-color,
      var(--td-brand-color, var(--td-primary-color-7, #0052d9))
    )
  );
}
.t-switch__label--disabled {
  color: var(
    --td-switch-unchecked-disabled-color,
    var(--td-bg-color-component-disabled, var(--td-gray-color-2, #eee))
  );
}
.t-switch__label--checked.t-switch__label--disabled {
  color: var(
    --td-switch-checked-disabled-color,
    var(--td-brand-color-disabled, var(--td-primary-color-3, #b5c7ff))
  );
}
.t-switch__label--large {
  font-size: var(--td-switch-label-large-font-size, 32rpx);
}
.t-switch__label--small {
  font-size: var(--td-switch-label-small-font-size, 24rpx);
}
.t-switch__label:empty {
  display: none;
}
.t-switch__icon {
  font-size: var(--td-switch-icon-size, 40rpx);
}
.t-switch__icon--large {
  font-size: var(--td-switch-icon-large-size, 48rpx);
}
.t-switch__icon--small {
  font-size: var(--td-switch-icon-small-size, 32rpx);
}
.t-switch__loading {
  color: var(
    --td-switch-label-checked-color,
    var(
      --td-switch-checked-color,
      var(--td-brand-color, var(--td-primary-color-7, #0052d9))
    )
  );
}
.t-switch__body {
  background-color: var(
    --td-switch-unchecked-color,
    var(
      --td-bg-color-secondarycontainer-active,
      var(--td-gray-color-4, #dcdcdc)
    )
  );
  border-radius: var(
    --td-switch-radius,
    calc(var(--td-switch-height, 56rpx) / 2)
  );
  height: var(--td-switch-height, 56rpx);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  vertical-align: middle;
  width: var(--td-switch-width, 90rpx);
}
.t-switch__body--checked {
  background-color: var(
    --td-switch-checked-color,
    var(--td-brand-color, var(--td-primary-color-7, #0052d9))
  );
}
.t-switch__body--disabled {
  background-color: var(
    --td-switch-unchecked-disabled-color,
    var(--td-bg-color-component-disabled, var(--td-gray-color-2, #eee))
  );
}
.t-switch__body--checked.t-switch__body--disabled {
  background-color: var(
    --td-switch-checked-disabled-color,
    var(--td-brand-color-disabled, var(--td-primary-color-3, #b5c7ff))
  );
}
.t-switch__body--large {
  border-radius: var(
    --td-switch-large-radius,
    calc(var(--td-switch-large-height, 64rpx) / 2)
  );
  height: var(--td-switch-large-height, 64rpx);
  width: var(--td-switch-large-width, 104rpx);
}
.t-switch__body--small {
  border-radius: var(
    --td-switch-small-radius,
    calc(var(--td-switch-small-height, 48rpx) / 2)
  );
  height: var(--td-switch-small-height, 48rpx);
  width: var(--td-switch-small-width, 78rpx);
}
.t-switch__dot {
  background-color: var(--td-text-color-anti, var(--td-font-white-1, #fff));
  border-radius: 50%;
  box-shadow: var(
    --td-switch-dot-shadow,
    var(
      --td-shadow-1,
      0 1px 10px rgba(0, 0, 0, 0.05),
      0 4px 5px rgba(0, 0, 0, 0.08),
      0 2px 4px -1px rgba(0, 0, 0, 0.12)
    )
  );
  height: var(--td-switch-dot-size, 44rpx);
  left: var(--td-switch-dot-horizontal-margin, 6rpx);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s;
  width: var(--td-switch-dot-size, 44rpx);
}
.t-switch__dot--disabled {
  background-color: var(
    --td-switch-dot-disabled-color,
    var(--td-font-white-1, #fff)
  );
}
.t-switch__dot--large {
  height: var(--td-switch-dot-large-size, 52rpx);
  width: var(--td-switch-dot-large-size, 52rpx);
}
.t-switch__dot--small {
  height: var(--td-switch-dot-small-size, 36rpx);
  width: var(--td-switch-dot-small-size, 36rpx);
}
.t-switch__dot--checked {
  left: calc(
    var(--td-switch-width, 90rpx) - var(--td-switch-dot-size, 44rpx) -
      var(--td-switch-dot-horizontal-margin, 6rpx)
  );
}
.t-switch__dot--large.t-switch__dot--checked {
  left: calc(
    var(--td-switch-large-width, 104rpx) -
      var(--td-switch-dot-large-size, 52rpx) -
      var(--td-switch-dot-horizontal-margin, 6rpx)
  );
}
.t-switch__dot--small.t-switch__dot--checked {
  left: calc(
    var(--td-switch-small-width, 78rpx) - var(--td-switch-dot-small-size, 36rpx) -
      var(--td-switch-dot-horizontal-margin, 6rpx)
  );
}
.t-switch__dot--plain:not(.t-switch__dot--checked) {
  height: var(--td-switch-dot-plain-size, 36rpx);
  left: var(--td-switch-dot-plain-horizontal-margin, 10rpx);
  width: var(--td-switch-dot-plain-size, 36rpx);
}
.t-switch__dot--large.t-switch__dot--plain:not(.t-switch__dot--checked) {
  height: var(--td-switch-dot-plain-large-size, 44rpx);
  width: var(--td-switch-dot-plain-large-size, 44rpx);
}
.t-switch__dot--small.t-switch__dot--plain:not(.t-switch__dot--checked) {
  height: var(--td-switch-dot-plain-small-size, 28rpx);
  width: var(--td-switch-dot-plain-small-size, 28rpx);
}
