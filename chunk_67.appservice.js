__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/edit-profile/edit-profile": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { genderOptions: new Array(2), bioLength: new Array(1) },
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "编辑个人信息");
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          g = (C, T) => {
            C ? T("头像") : T();
          },
          j,
          l = (C) => {},
          n = (C, T) => {
            C ? T("请选择") : T();
          },
          m = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder-text");
              },
              n
            );
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "image",
                {},
                (N, C) => {
                  if (C) L(N, "selected-avatar");
                  if (C || K || Z(U.formData, "avatar"))
                    O(N, "src", X(D.formData).avatar);
                  if (C) O(N, "mode", "aspectFill");
                },
                l
              );
            } else {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "avatar-placeholder");
                },
                m
              );
            }
          },
          i = (C, T, E, B) => {
            j = X(D.formData).avatar ? 1 : 0;
            B(j, k);
          },
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "avatar-preview");
                if (C) O(N, "bindtap", "showAvatarSelector");
              },
              i
            );
          },
          f = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              g
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "avatar-section");
              },
              h
            );
          },
          p = (C, T) => {
            C ? T("性别") : T();
          },
          r = (C, T) => {
            var $A = X(D.formData).genderIndex;
            C ||
            K ||
            !!(Z(U.formData, "genderIndex") || Z(U.genderOptions, $A)) ||
            undefined
              ? T(Y(X(D.genderOptions)[$A] || "请选择"), (N) => {
                  A["genderOptions"][1] = (D, E, T) => {
                    var $B = X(D.formData).genderIndex;
                    T(N, Y(X(D.genderOptions)[$B] || "请选择"));
                  };
                })
              : T();
          },
          q = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-input picker-input");
              },
              r
            );
          },
          o = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              p
            );
            E(
              "picker",
              {},
              (N, C) => {
                if (C) O(N, "bindchange", "onGenderChange");
                if (C || K || Z(U.formData, "genderIndex"))
                  O(N, "value", X(D.formData).genderIndex);
                if (C || K || U.genderOptions) O(N, "range", D.genderOptions);
                A["genderOptions"][0] = (D, E, T) => {
                  O(N, "range", D.genderOptions);
                  E(N);
                };
              },
              q
            );
          },
          t = (C, T) => {
            C ? T("昵称") : T();
          },
          u = (C) => {},
          s = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              t
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "type", "nickname");
                if (C) O(N, "placeholder", "请输入昵称");
                if (C || K || Z(U.formData, "nickname"))
                  O(N, "value", X(D.formData).nickname);
                if (C) O(N, "bindinput", "onNicknameInput");
                if (C) O(N, "bindblur", "onNicknameBlur");
              },
              u
            );
          },
          w = (C, T) => {
            C ? T("个人简介") : T();
          },
          y = (C) => {},
          z = (C, T) => {
            C || K || !!U.bioLength || undefined
              ? T(Y(Y(D.bioLength) + "/200"), (N) => {
                  A["bioLength"][0] = (D, E, T) => {
                    T(N, Y(Y(D.bioLength) + "/200"));
                  };
                })
              : T();
          },
          x = (C, T, E) => {
            E(
              "textarea",
              {},
              (N, C) => {
                if (C) L(N, "form-textarea");
                if (C) O(N, "placeholder", "请输入");
                if (C || K || Z(U.formData, "bio"))
                  O(N, "value", X(D.formData).bio);
                if (C) O(N, "bindinput", "onBioInput");
                if (C) O(N, "maxlength", "200");
              },
              y
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "char-count-inner textarea-count");
              },
              z
            );
          },
          v = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label");
              },
              w
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "textarea-wrapper");
              },
              x
            );
          },
          B0 = (C, T) => {
            C ? T("取消") : T();
          },
          C0 = (C, T) => {
            C ? T("提交") : T();
          },
          A0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "onCancel");
              },
              B0
            );
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-submit");
                if (C) O(N, "bindtap", "onSubmit");
              },
              C0
            );
          },
          e = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              f
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              o
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              s
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "button-group");
              },
              A0
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-container");
              },
              e
            );
          },
          D0,
          G0 = (C) => {},
          J0 = (C, T) => {
            C ? T("选择头像") : T();
          },
          I0 = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "modal-title");
              },
              J0
            );
          },
          L0 = (C, M0, N0, O0, P0, Q0, T, E) => {
            var S0 = (C) => {},
              T0,
              V0 = (C, T) => {
                C ? T("✓") : T();
              },
              U0 = (C, T, E) => {
                if (T0 === 1) {
                  E(
                    "view",
                    {},
                    (N, C) => {
                      if (C) L(N, "selected-mark");
                    },
                    V0
                  );
                }
              },
              R0 = (C, T, E, B) => {
                E(
                  "image",
                  {},
                  (N, C) => {
                    if (C) L(N, "avatar-img");
                    if (C || K || Z(O0, "src")) O(N, "src", X(M0).src);
                    if (C) O(N, "mode", "aspectFill");
                  },
                  S0
                );
                T0 = X(D.formData).avatar === X(M0).src ? 1 : 0;
                B(T0, U0);
              };
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "avatar-item");
                if (C) O(N, "bindtap", "selectAvatar");
                if (C || K || Z(O0, "src")) R.d(N, "src", X(M0).src);
                if (C || K || Z(O0, "fileID")) R.d(N, "fileid", X(M0).fileID);
              },
              R0
            );
          },
          K0 = (C, T, E, B, F) => {
            F(
              D.avatarList,
              "index",
              U ? U.avatarList : undefined,
              [0, "avatarList"],
              L0
            );
          },
          N0 = (C, T) => {
            C ? T("取消") : T();
          },
          M0 = (C, T, E) => {
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-cancel");
                if (C) O(N, "bindtap", "hideAvatarSelector");
              },
              N0
            );
          },
          H0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-header");
              },
              I0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "avatar-grid");
              },
              K0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-footer");
              },
              M0
            );
          },
          F0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-mask");
                if (C) O(N, "bindtap", "hideAvatarSelector");
              },
              G0
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "modal-content");
              },
              H0
            );
          },
          E0 = (C, T, E) => {
            if (D0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "avatar-modal");
                },
                F0
              );
            }
          },
          a = (C, T, E, B) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
            D0 = D.showAvatarModal ? 1 : 0;
            B(D0, E0);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "pages/edit-profile/edit-profile";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "pages/edit-profile/edit-profile.js";
define(
  "pages/edit-profile/edit-profile.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    var a = require("../../@babel/runtime/helpers/regeneratorRuntime"),
      e = require("../../@babel/runtime/helpers/asyncToGenerator");
    Page({
      data: {
        formData: {
          avatar: "",
          avatarFileID: "",
          nickname: "",
          genderIndex: null,
          bio: "",
        },
        genderOptions: ["男", "女"],
        showAvatarModal: !1,
        avatarList: [],
      },
      onLoad: function (a) {
        var e = getApp().globalData.userInfo.data || {};
        console.log("用户信息:", e),
          e &&
            (this.setData({
              "formData.nickname": e.nickname || "",
              "formData.genderIndex":
                e.gender && "male" === e.gender.value
                  ? 0
                  : e.gender && "female" === e.gender.value
                  ? 1
                  : null,
              "formData.bio": e.bio || "",
              "formData.avatarFileID": e.avatar || "",
            }),
            e.avatar && this.loadUserAvatar(e.avatar)),
          this.fetchDefaultAvatars();
      },
      loadUserAvatar: function (t) {
        var n = this;
        return e(
          a().mark(function e() {
            var r, o;
            return a().wrap(
              function (a) {
                for (;;)
                  switch ((a.prev = a.next)) {
                    case 0:
                      return (
                        (a.prev = 0),
                        (r = getApp()),
                        (a.next = 4),
                        r.getTempFile(t)
                      );
                    case 4:
                      (o = a.sent) &&
                        o.fileList &&
                        o.fileList.length > 0 &&
                        n.setData({
                          "formData.avatar": o.fileList[0].tempFileURL,
                        }),
                        (a.next = 11);
                      break;
                    case 8:
                      (a.prev = 8),
                        (a.t0 = a.catch(0)),
                        console.error("获取用户头像失败:", a.t0);
                    case 11:
                    case "end":
                      return a.stop();
                  }
              },
              e,
              null,
              [[0, 8]]
            );
          })
        )();
      },
      fetchDefaultAvatars: function () {
        var t = this;
        return e(
          a().mark(function e() {
            var n, r, o, i, s, c;
            return a().wrap(
              function (a) {
                for (;;)
                  switch ((a.prev = a.next)) {
                    case 0:
                      return (
                        (a.prev = 0),
                        wx.showLoading({ title: "加载头像中...", mask: !0 }),
                        (n = getApp()),
                        (a.next = 5),
                        n.call({
                          path: "/api/mp/images/default-avatars",
                          method: "GET",
                        })
                      );
                    case 5:
                      if ((r = a.sent) && r.data && Array.isArray(r.data)) {
                        a.next = 8;
                        break;
                      }
                      throw new Error("获取头像列表失败");
                    case 8:
                      if (
                        ((o = r.data),
                        console.log("获取到的fileIDs:", o),
                        (i = o.map(function (a) {
                          return a.file_id;
                        })),
                        console.log("提取的file_id列表:", i),
                        !(i.length > 0))
                      ) {
                        a.next = 24;
                        break;
                      }
                      return (a.next = 15), n.getTempFile(i);
                    case 15:
                      if (!(s = a.sent) || !s.fileList) {
                        a.next = 21;
                        break;
                      }
                      (c = s.fileList.map(function (a) {
                        return { src: a.tempFileURL, fileID: a.fileID };
                      })),
                        t.setData({ avatarList: c }),
                        (a.next = 22);
                      break;
                    case 21:
                      throw new Error("获取临时文件链接失败");
                    case 22:
                      a.next = 25;
                      break;
                    case 24:
                      wx.showToast({ title: "暂无可用头像", icon: "none" });
                    case 25:
                      a.next = 31;
                      break;
                    case 27:
                      (a.prev = 27),
                        (a.t0 = a.catch(0)),
                        console.error("获取默认头像失败:", a.t0),
                        wx.showToast({ title: "获取头像失败", icon: "none" });
                    case 31:
                      return (a.prev = 31), wx.hideLoading(), a.finish(31);
                    case 34:
                    case "end":
                      return a.stop();
                  }
              },
              e,
              null,
              [[0, 27, 31, 34]]
            );
          })
        )();
      },
      onNicknameInput: function (a) {
        this.setData({ "formData.nickname": a.detail.value });
      },
      onNicknameBlur: function (a) {
        var e = a.detail.value;
        e &&
          0 === e.trim().length &&
          wx.showToast({ title: "昵称不能为空", icon: "none" });
      },
      onGenderChange: function (a) {
        this.setData({ "formData.genderIndex": parseInt(a.detail.value) });
      },
      onCancel: function () {
        wx.showModal({
          title: "提示",
          content: "确定要取消编辑吗？",
          success: function (a) {
            a.confirm && wx.navigateBack();
          },
        });
      },
      onSubmit: function () {
        if (this.validateForm()) {
          wx.showLoading({ title: "保存中..." });
          var a = {
              avatar: this.data.formData.avatarFileID,
              nickname: this.data.formData.nickname,
              gender: {
                label: this.data.genderOptions[this.data.formData.genderIndex],
                value: 0 === this.data.formData.genderIndex ? "male" : "female",
              },
              bio: this.data.formData.bio,
            },
            e = getApp(),
            t = e.globalData.openid;
          if (!t)
            return (
              wx.hideLoading(),
              void wx.showToast({ title: "用户未登录", icon: "error" })
            );
          e.call({
            path: "/api/mp/auth/user/".concat(t),
            method: "PUT",
            data: {
              avatar: a.avatar,
              nickname: a.nickname,
              gender: a.gender.value,
              bio: a.bio,
            },
          })
            .then(function (a) {
              wx.hideLoading(),
                0 === a.code
                  ? e
                      .refreshUserInfo()
                      .then(function () {
                        wx.showToast({
                          title: "保存成功",
                          icon: "success",
                          success: function () {
                            setTimeout(function () {
                              wx.navigateBack();
                            }, 1500);
                          },
                        });
                      })
                      .catch(function (a) {
                        console.error("刷新用户信息失败:", a),
                          wx.showToast({
                            title: "保存成功，但刷新信息失败",
                            icon: "none",
                          });
                      })
                  : wx.showToast({
                      title: a.message || "保存失败",
                      icon: "error",
                    });
            })
            .catch(function (a) {
              console.error("更新用户信息失败:", a),
                wx.hideLoading(),
                wx.showToast({ title: "保存失败", icon: "error" });
            });
        }
      },
      validateForm: function () {
        var a = this.data.formData,
          e = a.nickname,
          t = a.genderIndex;
        return e && 0 !== e.trim().length
          ? e.trim().length < 2 || e.trim().length > 20
            ? (wx.showToast({
                title: "昵称长度应在2-20个字符之间",
                icon: "none",
              }),
              !1)
            : null != t ||
              (wx.showToast({ title: "请选择性别", icon: "none" }), !1)
          : (wx.showToast({ title: "请输入昵称", icon: "none" }), !1);
      },
      showAvatarSelector: function () {
        this.setData({ showAvatarModal: !0 });
      },
      hideAvatarSelector: function () {
        this.setData({ showAvatarModal: !1 });
      },
      selectAvatar: function (a) {
        var e = a.currentTarget.dataset.src,
          t = a.currentTarget.dataset.fileid;
        this.setData({
          "formData.avatar": e,
          "formData.avatarFileID": t,
          showAvatarModal: !1,
        }),
          wx.showToast({
            title: "头像已选择",
            icon: "success",
            duration: 1500,
          });
      },
    });
  },
  {
    isPage: true,
    isComponent: true,
    currentFile: "pages/edit-profile/edit-profile.js",
  }
);
require("pages/edit-profile/edit-profile.js");
