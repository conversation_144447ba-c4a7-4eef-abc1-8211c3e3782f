Object.defineProperty(exports, "__esModule", { value: !0 }),
  (exports.default = void 0);
var e = y(require("dayjs/locale/en")),
  l = y(require("dayjs/locale/zh-cn")),
  a = y(require("dayjs/locale/zh-tw")),
  u = y(require("dayjs/locale/ko")),
  r = y(require("dayjs/locale/ja")),
  t = y(require("dayjs/locale/ru")),
  d = y(require("./en")),
  o = y(require("./zh")),
  i = y(require("./tc")),
  c = y(require("./ko")),
  f = y(require("./ja")),
  n = y(require("./ru"));
function y(e) {
  return e && e.__esModule ? e : { default: e };
}
var k = {
  default: {
    key: "zh-cn",
    label: "简体中文",
    locale: l.default,
    i18n: o.default,
  },
  en: { key: "en", label: "English", locale: e.default, i18n: d.default },
  "zh-cn": {
    key: "zh-cn",
    label: "简体中文",
    locale: l.default,
    i18n: o.default,
  },
  zh: { key: "zh-cn", label: "简体中文", locale: l.default, i18n: o.default },
  "zh-tw": {
    key: "zh-tw",
    label: "繁体中文",
    locale: a.default,
    i18n: i.default,
  },
  tc: { key: "zh-tw", label: "繁体中文", locale: a.default, i18n: i.default },
  ko: { key: "ko", label: "한국어", locale: u.default, i18n: c.default },
  kr: { key: "ko", label: "한국어", locale: u.default, i18n: c.default },
  ja: { key: "ja", label: "日本語", locale: r.default, i18n: f.default },
  ru: { key: "ru", label: "русский", locale: t.default, i18n: n.default },
};
exports.default = k;
