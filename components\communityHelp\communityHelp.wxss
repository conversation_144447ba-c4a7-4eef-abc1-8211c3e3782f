.fuwu_list {
  background-color: #fff;
  border: 5rpx solid #f0f0f0;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: -webkit-flex;
  display: flex;
  height: auto;
  justify-content: space-between;
  margin: 10rpx auto;
  padding: 20rpx;
  width: 95%;
}
.info_left {
  width: 70%;
}
.info_left_top,
.info_left_top_img {
  display: -webkit-flex;
  display: flex;
}
.info_left_top_img {
  align-items: center;
  height: 150rpx;
  justify-content: center;
  margin-right: 20rpx;
  width: 150rpx;
}
.info_left_top_img image {
  border-radius: 10rpx;
  height: 100%;
  object-fit: cover;
  vertical-align: middle;
  width: 100%;
}
.info_left_top_info {
  flex: 1;
  max-width: calc(100% - 170rpx);
}
.info_left_top_info_title {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
}
.info_left_top_info_ms {
  color: #666;
  font-size: 24rpx;
  margin-bottom: 12rpx;
}
.info_left_top_info_fw,
.info_left_top_info_time {
  color: #999;
  font-size: 22rpx;
  margin-bottom: 10rpx;
}
.info_left_top_info_fw,
.info_left_top_info_ms,
.info_left_top_info_time,
.info_left_top_info_title {
  width: 100%;
}
.info_left_top_info_fw text,
.info_left_top_info_ms text,
.info_left_top_info_time text,
.info_left_top_info_title text {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
.info_right {
  align-items: flex-end;
  flex-direction: column;
  width: 25%;
}
.info_right,
.info_right_top {
  display: -webkit-flex;
  display: flex;
}
.info_right_top {
  align-items: center;
}
.info_right_top_img {
  height: 50rpx;
  margin-right: 10rpx;
  width: 50rpx;
}
.info_right_top_img image {
  border-radius: 50%;
  height: 100%;
  width: 100%;
}
.info_right_top_username {
  color: #f90;
  font-size: 26rpx;
}
.info_right_middle {
  margin: 30rpx 0;
}
.info_right_middle_price {
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.price-label {
  color: #ff4d4f;
  font-size: 24rpx;
}
.price-value {
  color: #f50;
  font-size: 32rpx;
  font-weight: 700;
}
.info_right_bottom {
  align-items: center;
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 20rpx;
}
.margin-16 {
  margin-bottom: 0;
  margin-right: 16rpx;
}
.margin-16:last-child {
  margin-right: 0;
}
