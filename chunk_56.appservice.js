__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/tab-bar/tab-bar": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            customStyle: new Array(1),
            classPrefix: new Array(1),
            style: new Array(1),
            bordered: new Array(1),
            shape: new Array(1),
            prefix: new Array(1),
            safeAreaInsetBottom: new Array(1),
            fixed: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      Q.a([U.bordered]),
                      Q.a([U.fixed]),
                      Q.a([U.safeAreaInsetBottom]),
                      U.shape,
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        ["border", D.bordered],
                        ["fixed", D.fixed],
                        ["safe", D.safeAreaInsetBottom],
                        D.shape,
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["classPrefix"][0] =
                  A["bordered"][0] =
                  A["fixed"][0] =
                  A["safeAreaInsetBottom"][0] =
                  A["shape"][0] =
                  A["prefix"][0] =
                    (D, E, T) => {
                      L(
                        N,
                        Y(
                          P(X(a).cls)(D.classPrefix, [
                            ["border", D.bordered],
                            ["fixed", D.fixed],
                            ["safe", D.safeAreaInsetBottom],
                            D.shape,
                          ])
                        ) +
                          " class " +
                          Y(D.prefix) +
                          "-class"
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-role", "tablist");
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/tab-bar/tab-bar";
__wxRouteBegin = true;
__wxAppCurrentFile__ = "miniprogram_npm/tdesign-miniprogram/tab-bar/tab-bar.js";
define(
  "miniprogram_npm/tdesign-miniprogram/tab-bar/tab-bar.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/createClass"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      n = require("../common/src/index"),
      u = l(require("../common/config")),
      s = l(require("./props"));
    function l(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var o = u.default.prefix,
      c = "".concat(o, "-tab-bar"),
      h = (function (i) {
        t(u, i);
        var n = a(u);
        function u() {
          var e;
          return (
            r(this, u),
            ((e = n.apply(this, arguments)).relations = {
              "../tab-bar-item/tab-bar-item": { type: "descendant" },
            }),
            (e.externalClasses = ["".concat(o, "-class")]),
            (e.backupValue = -1),
            (e.data = { prefix: o, classPrefix: c }),
            (e.properties = s.default),
            (e.controlledProps = [{ key: "value", event: "change" }]),
            (e.observers = {
              value: function () {
                this.updateChildren();
              },
            }),
            (e.lifetimes = {
              ready: function () {
                this.showChildren();
              },
            }),
            (e.methods = {
              showChildren: function () {
                var e = this,
                  r = this.data.value;
                this.$children.forEach(function (t) {
                  t.setData({ crowded: e.$children.length > 3 }),
                    t.properties.value === r && t.showSpread();
                });
              },
              updateChildren: function () {
                var e = this.data.value;
                this.$children.forEach(function (r) {
                  r.checkActive(e);
                });
              },
              updateValue: function (e) {
                this._trigger("change", { value: e });
              },
              changeOtherSpread: function (e) {
                this.$children.forEach(function (r) {
                  r.properties.value !== e && r.closeSpread();
                });
              },
              initName: function () {
                return (this.backupValue += 1);
              },
            }),
            e
          );
        }
        return e(u);
      })(n.SuperComponent),
      d = (h = (0, i.__decorate)([(0, n.wxComponent)()], h));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/tab-bar/tab-bar.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/tab-bar/tab-bar.js");
