Page({
  data: {
    formData: {
      title: "",
      reward: "",
      deadline: "",
      note: "",
      contactType: "unlimited",
      thumbnail: "",
    },
    titleLength: 0,
    noteLength: 0,
    showThumbnailModal: !1,
    thumbnailList: [
      { src: "/assets/slt/汉堡包.png", name: "汉堡包" },
      { src: "/assets/slt/美食.png", name: "美食" },
      { src: "/assets/slt/鱼生套餐.png", name: "鱼生套餐" },
    ],
  },
  onLoad: function (t) {
    var a = new Date();
    a.setDate(a.getDate() + 1);
    var n = a.toISOString().split("T")[0];
    this.setData({ "formData.deadline": n });
  },
  onTitleInput: function (t) {
    var a = t.detail.value;
    this.setData({ "formData.title": a, titleLength: a.length });
  },
  onRewardInput: function (t) {
    this.setData({ "formData.reward": t.detail.value });
  },
  onNoteInput: function (t) {
    var a = t.detail.value;
    this.setData({ "formData.note": a, noteLength: a.length });
  },
  onDateChange: function (t) {
    this.setData({ "formData.deadline": t.detail.value });
  },
  onContactChange: function (t) {
    this.setData({ "formData.contactType": t.detail.value });
  },
  onCancel: function () {
    wx.showModal({
      title: "提示",
      content: "确定要取消发布吗？",
      success: function (t) {
        t.confirm && wx.navigateBack();
      },
    });
  },
  onSave: function () {
    this.validateForm() &&
      (wx.setStorageSync("draft_help", this.data.formData),
      wx.showToast({ title: "保存成功", icon: "success" }));
  },
  onPublish: function () {
    this.validateForm() &&
      (wx.showLoading({ title: "发布中..." }),
      setTimeout(function () {
        wx.hideLoading(),
          wx.showToast({
            title: "发布成功",
            icon: "success",
            success: function () {
              setTimeout(function () {
                wx.navigateBack();
              }, 1500);
            },
          });
      }, 1e3));
  },
  validateForm: function () {
    var t = this.data.formData,
      a = t.title,
      n = t.reward,
      e = t.deadline;
    return a.trim()
      ? n.trim()
        ? !!e || (wx.showToast({ title: "请选择截止时间", icon: "none" }), !1)
        : (wx.showToast({ title: "请输入报酬", icon: "none" }), !1)
      : (wx.showToast({ title: "请输入标题", icon: "none" }), !1);
  },
  onReady: function () {},
  onShow: function () {},
  onHide: function () {},
  onUnload: function () {},
  onPullDownRefresh: function () {},
  onReachBottom: function () {},
  onShareAppMessage: function () {},
  showThumbnailSelector: function () {
    this.setData({ showThumbnailModal: !0 });
  },
  hideThumbnailSelector: function () {
    this.setData({ showThumbnailModal: !1 });
  },
  selectThumbnail: function (t) {
    var a = t.currentTarget.dataset.src;
    this.setData({ "formData.thumbnail": a, showThumbnailModal: !1 }),
      wx.showToast({ title: "缩略图已选择", icon: "success", duration: 1500 });
  },
});
