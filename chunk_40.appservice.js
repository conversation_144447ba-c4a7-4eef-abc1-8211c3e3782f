__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/progress/progress": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/progress/progress"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { customStyle: new Array(1), style: new Array(1) },
          K = U === true,
          e,
          i = (C) => {},
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__inner " + Y(D.prefix) + "-class-bar"
                  );
                if (C || K || !!(U.colorBar || U.computedProgress) || undefined)
                  R.y(
                    N,
                    "background:" +
                      Y(D.colorBar) +
                      ";width:" +
                      Y(D.computedProgress + "%")
                  );
              },
              i
            );
          },
          j,
          m,
          o,
          p = (C, T, E, B, F, S, J) => {
            var $A = I(o);
            var $B = D.status;
            if (o && $A)
              $A(
                R,
                C,
                {
                  tClass: D.classPrefix + "__icon",
                  size: "44rpx",
                  name: X(X(b).LINE_STATUS_ICON)[$B],
                },
                K ||
                  (U
                    ? {
                        tClass: !!U.classPrefix || undefined,
                        name:
                          !!U.status || Z(Z(undefined, "LINE_STATUS_ICON"), $B),
                      }
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          q = (C, T) => {
            var $A = P(X(a).isString)(D.label);
            C ||
            K ||
            !!(Z(undefined, "isString") || U.label) ||
            ($A ? U.label : !!U.computedProgress || undefined)
              ? T(Y($A ? D.label : D.computedProgress + "%"))
              : T();
          },
          n = (C, T, E, B, F, S, J) => {
            if (m === 1) {
              o = "icon";
              B(o, p);
            } else {
              E("text", {}, (N, C) => {}, q);
            }
          },
          l = (C, T, E, B) => {
            m = P(X(a).includes)(X(b).STATUS, D.status) ? 1 : 0;
            B(m, n);
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__info " +
                        Y(D.prefix) +
                        "-class-label"
                    );
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                l
              );
            }
          },
          g = (C, T, E, B, F, S) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__bar");
                if (
                  C ||
                  K ||
                  !!(U.heightBar || U.heightBar || U.bgColorBar) ||
                  undefined
                )
                  R.y(
                    N,
                    "height:" +
                      Y(D.heightBar) +
                      "px;border-radius:" +
                      Y(D.heightBar) +
                      "px;background-color:" +
                      Y(D.bgColorBar)
                  );
                if (C) O(N, "aria-role", "progressbar");
                if (C) O(N, "aria-valuemin", "0");
                if (C) O(N, "aria-valuemax", "100");
                if (C || K || U.computedProgress)
                  O(N, "aria-valuenow", D.computedProgress);
                var $A = D.isIOS;
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    U.isIOS ||
                    ($A
                      ? !!(Z(undefined, "getIOSAriaLabel") || U.status) ||
                        undefined
                      : !!(Z(undefined, "getAndroidAriaLabel") || U.status) ||
                        undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($A
                        ? P(X(b).getIOSAriaLabel)(D.status)
                        : P(X(b).getAndroidAriaLabel)(D.status))
                  );
                if (C) O(N, "aria-live", "polite");
              },
              h
            );
            j = D.label ? 1 : 0;
            B(j, k);
            S("label");
          },
          f = (C, T, E) => {
            if (e === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.status ||
                      U.computedStatus ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "--thin " +
                        Y(D.classPrefix) +
                        "--status--" +
                        Y(D.status || D.computedStatus) +
                        " " +
                        Y(D.prefix) +
                        "-class"
                    );
                },
                g
              );
            }
          },
          r,
          v,
          y = (C, T) => {
            var $A = P(X(a).isString)(D.label);
            C ||
            K ||
            !!(Z(undefined, "isString") || U.label) ||
            ($A ? U.label : !!U.computedProgress || undefined)
              ? T(Y($A ? D.label : D.computedProgress + "%"))
              : T();
          },
          x = (C, T, E) => {
            E("text", {}, (N, C) => {}, y);
          },
          w = (C, T, E) => {
            if (v === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__info " +
                        Y(D.prefix) +
                        "-class-label"
                    );
                },
                x
              );
            }
          },
          z,
          A0 = (C, T, E, B, F, S) => {
            if (z === 1) {
              S("label");
            }
          },
          u = (C, T, E, B) => {
            v = D.label && D.computedProgress > 10 ? 1 : 0;
            B(v, w);
            z = D.computedProgress > 10 ? 1 : 0;
            B(z, A0);
          },
          B0,
          E0 = (C, T) => {
            var $A = P(X(a).isString)(D.label);
            C ||
            K ||
            !!(Z(undefined, "isString") || U.label) ||
            ($A ? U.label : !!U.computedProgress || undefined)
              ? T(Y($A ? D.label : D.computedProgress + "%"))
              : T();
          },
          D0 = (C, T, E) => {
            E("text", {}, (N, C) => {}, E0);
          },
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__info " +
                        Y(D.prefix) +
                        "-class-label"
                    );
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                D0
              );
            }
          },
          F0,
          G0 = (C, T, E, B, F, S) => {
            if (F0 === 1) {
              S("label");
            }
          },
          t = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) + "__inner " + Y(D.prefix) + "-class-bar"
                  );
                if (C || K || !!(U.colorBar || U.computedProgress) || undefined)
                  R.y(
                    N,
                    "background:" +
                      Y(D.colorBar) +
                      ";width:" +
                      Y(D.computedProgress) +
                      "%"
                  );
              },
              u
            );
            B0 = D.label && D.computedProgress <= 10 ? 1 : 0;
            B(B0, C0);
            F0 = D.computedProgress <= 10 ? 1 : 0;
            B(F0, G0);
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  var $A = D.computedProgress > 10;
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.classPrefix ||
                      U.computedProgress ||
                      ($A
                        ? !!U.classPrefix || undefined
                        : !!U.classPrefix || undefined) ||
                      U.classPrefix ||
                      U.status ||
                      U.computedStatus ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__bar " +
                        Y(D.classPrefix) +
                        "--plump " +
                        Y(
                          $A
                            ? D.classPrefix + "--over-ten"
                            : D.classPrefix + "--under-ten"
                        ) +
                        " " +
                        Y(D.classPrefix) +
                        "--status--" +
                        Y(D.status || D.computedStatus) +
                        " " +
                        Y(D.prefix) +
                        "-class"
                    );
                  if (
                    C ||
                    K ||
                    !!(U.heightBar || U.heightBar || U.bgColorBar) ||
                    undefined
                  )
                    R.y(
                      N,
                      "height:" +
                        Y(D.heightBar) +
                        "px;border-radius:" +
                        Y(D.heightBar) +
                        "px;background-color:" +
                        Y(D.bgColorBar)
                    );
                  if (C) O(N, "aria-role", "progressbar");
                  if (C) O(N, "aria-valuemin", "0");
                  if (C) O(N, "aria-valuemax", "100");
                  if (C || K || U.computedProgress)
                    O(N, "aria-valuenow", D.computedProgress);
                  var $B = D.isIOS;
                  if (
                    C ||
                    K ||
                    !!(
                      U.ariaLabel ||
                      U.isIOS ||
                      ($B
                        ? !!(Z(undefined, "getIOSAriaLabel") || U.status) ||
                          undefined
                        : !!(Z(undefined, "getAndroidAriaLabel") || U.status) ||
                          undefined)
                    ) ||
                    undefined
                  )
                    O(
                      N,
                      "aria-label",
                      D.ariaLabel ||
                        ($B
                          ? P(X(b).getIOSAriaLabel)(D.status)
                          : P(X(b).getAndroidAriaLabel)(D.status))
                    );
                  if (C) O(N, "aria-live", "polite");
                },
                t
              );
            }
          },
          H0,
          M0,
          P0,
          R0,
          S0 = (C, T, E, B, F, S, J) => {
            var $A = I(R0);
            var $B = D.status;
            if (R0 && $A)
              $A(
                R,
                C,
                {
                  tClass: D.classPrefix + "__icon",
                  size: "96rpx",
                  name: X(X(b).CIRCLE_STATUS_ICON)[$B],
                },
                K ||
                  (U
                    ? {
                        tClass: !!U.classPrefix || undefined,
                        name:
                          !!U.status ||
                          Z(Z(undefined, "CIRCLE_STATUS_ICON"), $B),
                      }
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          T0 = (C, T) => {
            var $A = P(X(a).isString)(D.label);
            C ||
            K ||
            !!(Z(undefined, "isString") || U.label) ||
            ($A ? U.label : !!U.computedProgress || undefined)
              ? T(Y($A ? D.label : D.computedProgress + "%"))
              : T();
          },
          Q0 = (C, T, E, B, F, S, J) => {
            if (P0 === 1) {
              R0 = "icon";
              B(R0, S0);
            } else {
              E("text", {}, (N, C) => {}, T0);
            }
          },
          O0 = (C, T, E, B) => {
            P0 = P(X(a).includes)(X(b).STATUS, D.status) ? 1 : 0;
            B(P0, Q0);
          },
          N0 = (C, T, E) => {
            if (M0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__info " +
                        Y(D.prefix) +
                        "-class-label"
                    );
                  if (C || K || undefined) O(N, "aria-hidden", true);
                },
                O0
              );
            }
          },
          L0 = (C, T, E, B, F, S) => {
            M0 = D.label ? 1 : 0;
            B(M0, N0);
            S("label");
          },
          K0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__canvas--inner " +
                      Y(D.prefix) +
                      "-class-bar"
                  );
              },
              L0
            );
          },
          J0 = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U.size])])
                  ) ||
                  undefined
                )
                  L(
                    N,
                    P(X(a).cls)(D.classPrefix + "__canvas--circle", [
                      [D.size, true],
                    ])
                  );
                var $A = D.status;
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getCircleStyle") ||
                    U.size ||
                    U.heightBar ||
                    U.colorCircle ||
                    U.status ||
                    Z(Z(undefined, "STATUS_COLOR"), $A) ||
                    U.computedProgress ||
                    U.bgColorBar
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    Y(P(X(b).getCircleStyle)(D.size, D.heightBar)) +
                      ";background-image:conic-gradient(from var(--td-progress-circle-from), " +
                      Y(
                        D.colorCircle ||
                          X(X(b).STATUS_COLOR)[$A] ||
                          "var(--td-progress-inner-bg-color)"
                      ) +
                      " " +
                      Y(D.computedProgress) +
                      "%, " +
                      Y(D.bgColorBar || "var(--td-progress-track-bg-color)") +
                      " 0%);"
                  );
                if (C) O(N, "aria-role", "progressbar");
                if (C) O(N, "aria-valuemin", "0");
                if (C) O(N, "aria-valuemax", "100");
                if (C || K || U.computedProgress)
                  O(N, "aria-valuenow", D.computedProgress);
                var $B = D.isIOS;
                if (
                  C ||
                  K ||
                  !!(
                    U.ariaLabel ||
                    U.isIOS ||
                    ($B
                      ? !!(Z(undefined, "getIOSAriaLabel") || U.status) ||
                        undefined
                      : !!(Z(undefined, "getAndroidAriaLabel") || U.status) ||
                        undefined)
                  ) ||
                  undefined
                )
                  O(
                    N,
                    "aria-label",
                    D.ariaLabel ||
                      ($B
                        ? P(X(b).getIOSAriaLabel)(D.status)
                        : P(X(b).getAndroidAriaLabel)(D.status))
                  );
                if (C) O(N, "aria-live", "polite");
              },
              K0
            );
          },
          I0 = (C, T, E) => {
            if (H0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      U.classPrefix ||
                      U.status ||
                      U.computedStatus ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(D.classPrefix) +
                        "--status--" +
                        Y(D.status || D.computedStatus) +
                        " " +
                        Y(D.prefix) +
                        "-class"
                    );
                },
                J0
              );
            }
          },
          d = (C, T, E, B) => {
            e = D.theme === X(X(b).PRO_THEME).LINE ? 1 : 0;
            B(e, f);
            r = D.theme === X(X(b).PRO_THEME).PLUMP ? 1 : 0;
            B(r, s);
            H0 = D.theme === X(X(b).PRO_THEME).CIRCLE ? 1 : 0;
            B(H0, I0);
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + " class");
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/progress/progress";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/progress/progress.js";
define(
  "miniprogram_npm/tdesign-miniprogram/progress/progress.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0);
    var e = require("../../../@babel/runtime/helpers/typeof"),
      r = require("../../../@babel/runtime/helpers/classCallCheck"),
      t = require("../../../@babel/runtime/helpers/createClass"),
      s = require("../../../@babel/runtime/helpers/inherits"),
      a = require("../../../@babel/runtime/helpers/createSuper"),
      o = require("tslib"),
      i = require("../common/src/index"),
      u = p(require("../common/config")),
      c = p(require("./props")),
      n = require("./utils"),
      l = require("../common/utils");
    function p(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var h = u.default.prefix,
      f = "".concat(h, "-progress"),
      m = (function (o) {
        s(u, o);
        var i = a(u);
        function u() {
          var t;
          return (
            r(this, u),
            ((t = i.apply(this, arguments)).externalClasses = [
              "".concat(h, "-class"),
              "".concat(h, "-class-bar"),
              "".concat(h, "-class-label"),
            ]),
            (t.options = { multipleSlots: !0 }),
            (t.properties = c.default),
            (t.data = {
              prefix: h,
              classPrefix: f,
              colorBar: "",
              heightBar: "",
              computedStatus: "",
              computedProgress: 0,
              isIOS: !1,
            }),
            (t.observers = {
              percentage: function (e) {
                (e = Math.max(0, Math.min(e, 100))),
                  this.setData({
                    computedStatus: 100 === e ? "success" : "",
                    computedProgress: e,
                  });
              },
              color: function (r) {
                this.setData({
                  colorBar: (0, n.getBackgroundColor)(r),
                  colorCircle: "object" == e(r) ? "" : r,
                });
              },
              strokeWidth: function (e) {
                if (!e) return "";
                this.setData({ heightBar: (0, l.unitConvert)(e) });
              },
              trackColor: function (e) {
                this.setData({ bgColorBar: e });
              },
            }),
            t
          );
        }
        return (
          t(u, [
            {
              key: "attached",
              value: function () {
                var e = (0, l.isIOS)();
                this.setData({ isIOS: e });
              },
            },
          ]),
          u
        );
      })(i.SuperComponent),
      d = (m = (0, o.__decorate)([(0, i.wxComponent)()], m));
    exports.default = d;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile: "miniprogram_npm/tdesign-miniprogram/progress/progress.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/progress/progress.js");
