__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/checkbox-group/checkbox-group":
      (() => {
        var H = {};
        var S;
        var I = (P) => {
          if (!S) S = Object.assign({}, H);
          return S[P];
        };
        var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
        H[""] = (R, C, D, U) => {
          R.setFnFilter(Q.A, Q.B);
          if (typeof R.setEventListenerWrapper === "function")
            R.setEventListenerWrapper(Q.C);
          var L = R.c,
            M = R.m,
            O = R.r,
            A = {
              classPrefix: new Array(1),
              style: new Array(1),
              customStyle: new Array(1),
            },
            K = U === true,
            d = (C, e, f, g, h, i, T, E) => {
              var j = (C) => {};
              E(
                "t-checkbox",
                {},
                (N, C) => {
                  if (C || K || !!U.prefix || undefined)
                    L(N, Y(D.prefix) + "-checkbox-option");
                  if (C || K || !!(Z(g, "label") || Z(g, "text")) || undefined)
                    O(N, "label", X(e).label || X(e).text || "");
                  var $A = X(e).value == null;
                  if (
                    C ||
                    K ||
                    !!Z(g, "value") ||
                    ($A ? undefined : Z(g, "value"))
                  )
                    O(N, "value", $A ? "" : X(e).value);
                  if (C || K || !!Z(g, "block") || undefined)
                    O(N, "block", X(e).block || true);
                  if (C || K || !!Z(g, "checkAll") || undefined)
                    O(N, "check-all", X(e).checkAll || false);
                  if (C || K || !!Z(g, "checked") || undefined)
                    O(N, "checked", X(e).checked || false);
                  if (C || K || !!Z(g, "content") || undefined)
                    O(N, "content", X(e).content || "");
                  if (C || K || !!Z(g, "contentDisabled") || undefined)
                    O(N, "content-disabled", X(e).contentDisabled || false);
                  if (C || K || !!Z(g, "icon") || undefined)
                    O(N, "icon", X(e).icon || "circle");
                  if (C || K || !!Z(g, "indeterminate") || undefined)
                    O(N, "indeterminate", X(e).indeterminate || false);
                  var $B = X(e).disabled == null;
                  if (
                    C ||
                    K ||
                    !!Z(g, "disabled") ||
                    ($B ? U.disabled : Z(g, "disabled"))
                  )
                    O(N, "disabled", $B ? D.disabled : X(e).disabled);
                  if (C || K || !!Z(g, "maxContentRow") || undefined)
                    O(N, "max-content-row", X(e).maxContentRow || 5);
                  if (C || K || !!Z(g, "maxLabelRow") || undefined)
                    O(N, "max-label-row", X(e).maxLabelRow || 3);
                  if (C || K || !!Z(g, "name") || undefined)
                    O(N, "name", X(e).name || "");
                  if (C || K || U.borderless) O(N, "borderless", D.borderless);
                  if (C || K || !!Z(g, "readonly") || undefined)
                    O(N, "readonly", X(e).readonly || false);
                  if (C || K || !!Z(g, "placement") || undefined)
                    O(N, "placement", X(e).placement || "left");
                  if (C || K || g) R.d(N, "item", e);
                  if (C)
                    R.v(N, "change", "handleInnerChildChange", !1, !1, !1, !1);
                },
                j
              );
            },
            c = (C, T, E, B, F, S) => {
              S("");
              F(
                D.checkboxOptions,
                "value",
                U ? U.checkboxOptions : undefined,
                [0, "checkboxOptions"],
                d
              );
            },
            b = (C, T, E) => {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  A["classPrefix"][0] = (D, E, T) => {
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  };
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") || Q.a([U.style, U.customStyle])
                    ) ||
                    undefined
                  )
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                    R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                  };
                },
                c
              );
            };
          return { C: b, B: A };
        };
        return Object.assign(
          function (R) {
            return H[R];
          },
          { _: H }
        );
      })(),
    "miniprogram_npm/tdesign-miniprogram/checkbox/checkbox": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            maxContentRow: new Array(1),
            customStyle: new Array(1),
            tabindex: new Array(1),
            maxLabelRow: new Array(1),
            block: new Array(1),
            tId: new Array(1),
            style: new Array(1),
          },
          K = U === true,
          d,
          g,
          j = (C) => {},
          i = (C, T, E) => {
            E(
              "image",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__icon-image");
                var $A = D.checked;
                var $C = 2;
                var $B = D.indeterminate && X(D.icon)[$C];
                var $D = 2;
                var $E = 0;
                var $F = 1;
                if (
                  C ||
                  K ||
                  !!U.checked ||
                  ($A
                    ? !!(U.indeterminate || Z(U.icon, $C)) ||
                      ($B ? Z(U.icon, $D) : Z(U.icon, $E))
                    : Z(U.icon, $F))
                )
                  O(
                    N,
                    "src",
                    $A ? ($B ? X(D.icon)[$D] : X(D.icon)[$E]) : X(D.icon)[$F]
                  );
                if (C) O(N, "webp", true);
              },
              j
            );
          },
          k,
          m = (C) => {},
          l = (C, T, E) => {
            if (k === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "cls") || U.classPrefix || Q.a([])) ||
                    undefined
                  )
                    L(N, P(X(a).cls)(D.classPrefix + "__icon-wrapper", []));
                  var $A = D.indeterminate;
                  if (
                    C ||
                    K ||
                    !!U.indeterminate ||
                    ($A ? !!U.icon || undefined : !!U.icon || undefined)
                  )
                    O(
                      N,
                      "name",
                      $A
                        ? "minus-" + D.icon + "-filled"
                        : "check-" + D.icon + "-filled"
                    );
                },
                m
              );
            }
          },
          n,
          p = (C) => {},
          q = (C) => {},
          o = (C, T, E) => {
            if (n === 1) {
              E(
                "t-icon",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(Z(undefined, "cls") || U.classPrefix || Q.a([])) ||
                    undefined
                  )
                    L(N, P(X(a).cls)(D.classPrefix + "__icon-wrapper", []));
                  var $A = D.indeterminate;
                  if (
                    C ||
                    K ||
                    !!U.indeterminate ||
                    ($A ? !!U.icon || undefined : undefined)
                  )
                    O(N, "name", $A ? "minus-" + D.icon + "-filled" : "check");
                },
                p
              );
            } else if (n === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      U.icon ||
                      Q.a([Q.a([U._disabled])])
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      P(X(a).cls)(D.classPrefix + "__icon-" + D.icon, [
                        ["disabled", D._disabled],
                      ])
                    );
                },
                q
              );
            }
          },
          r,
          t = (C) => {},
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C) L(N, "placeholder");
                },
                t
              );
            }
          },
          h = (C, T, E, B, F, S) => {
            if (g === 1) {
              S("icon");
            } else if (g === 2) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__icon");
                },
                i
              );
            } else {
              k =
                D.checked && (D.icon == "circle" || D.icon == "rectangle")
                  ? 1
                  : 0;
              B(k, l);
              n =
                D.checked && D.icon == "line"
                  ? 1
                  : !D.checked && (D.icon == "circle" || D.icon == "rectangle")
                  ? 2
                  : 0;
              B(n, o);
              r = !D.checked && D.icon == "line" ? 1 : 0;
              B(r, s);
            }
          },
          f = (C, T, E, B) => {
            g = D.icon === "slot" ? 1 : P(X(a).isArray)(D.icon) ? 2 : 0;
            B(g, h);
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([
                        U.placement,
                        Q.a([U.checked]),
                        Q.a([U._disabled]),
                      ]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__icon", [
                          D.placement,
                          ["checked", D.checked],
                          ["disabled", D._disabled],
                        ])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-icon"
                    );
                },
                f
              );
            }
          },
          w,
          x = (C, T) => {
            if (w === 1) {
              C || K || U.label ? T(Y(D.label)) : T();
            }
          },
          v = (C, T, E, B, F, S) => {
            w = D.label ? 1 : 0;
            B(w, x);
            S("");
            S("label");
          },
          z,
          A0 = (C, T) => {
            if (z === 1) {
              C || K || U.content ? T(Y(D.content)) : T();
            }
          },
          y = (C, T, E, B, F, S) => {
            z = D.content ? 1 : 0;
            B(z, A0);
            S("content");
          },
          u = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U._disabled]), Q.a([U.checked])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__title", [
                        ["disabled", D._disabled],
                        ["checked", D.checked],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-label"
                  );
                if (C || K || !!U.maxLabelRow || undefined)
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxLabelRow));
                A["maxLabelRow"][0] = (D, E, T) => {
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxLabelRow));
                };
              },
              v
            );
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([Q.a([U._disabled])]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix + "__description", [
                        ["disabled", D._disabled],
                      ])
                    ) +
                      " " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C || K || !!U.maxContentRow || undefined)
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxContentRow));
                A["maxContentRow"][0] = (D, E, T) => {
                  R.y(N, "-webkit-line-clamp:" + Y(D.maxContentRow));
                };
              },
              y
            );
          },
          B0,
          D0 = (C) => {},
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "cls") ||
                      U.classPrefix ||
                      Q.a([U.placement]) ||
                      U.prefix
                    ) ||
                    undefined
                  )
                    L(
                      N,
                      Y(
                        P(X(a).cls)(D.classPrefix + "__border", [D.placement])
                      ) +
                        " " +
                        Y(D.prefix) +
                        "-class-border"
                    );
                },
                D0
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.theme == "default" ? 1 : 0;
            B(d, e);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__content");
                if (C) R.d(N, "target", "text");
                if (C) R.v(N, "tap", "handleTap", !1, !0, !1, !1);
              },
              u
            );
            B0 = D.theme == "default" && !D.borderless ? 1 : 0;
            B(B0, C0);
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([
                      U.placement,
                      U.theme,
                      Q.a([U.checked]),
                      Q.a([U.block]),
                    ]) ||
                    U.prefix
                  ) ||
                  undefined
                )
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D.placement,
                        D.theme,
                        ["checked", D.checked],
                        ["block", D.block],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                A["block"][0] = (D, E, T) => {
                  L(
                    N,
                    Y(
                      P(X(a).cls)(D.classPrefix, [
                        D.placement,
                        D.theme,
                        ["checked", D.checked],
                        ["block", D.block],
                      ])
                    ) +
                      " class " +
                      Y(D.prefix) +
                      "-class"
                  );
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) O(N, "aria-role", "checkbox");
                var $A = D.checked;
                var $B = D.indeterminate;
                if (
                  C ||
                  K ||
                  !!U.checked ||
                  ($A
                    ? !!U.indeterminate || ($B ? undefined : undefined)
                    : undefined)
                )
                  O(N, "aria-checked", $A ? ($B ? "mixed" : true) : false);
                var $C = D._disabled;
                if (C || K || !!U._disabled || ($C ? undefined : undefined))
                  O(N, "aria-disabled", $C ? true : false);
                if (C || K || U.tabindex) O(N, "tabindex", D.tabindex);
                A["tabindex"][0] = (D, E, T) => {
                  O(N, "tabindex", D.tabindex);
                  E(N);
                };
                if (C) R.v(N, "tap", "handleTap", !1, !0, !1, !1);
                if (C || K || U.tId) R.i(N, D.tId);
                A["tId"][0] = (D, E, T) => {
                  R.i(N, D.tId);
                };
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/checkbox-group/checkbox-group.wxss"
  ] = setCssToHead(
    [[2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"]],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/checkbox-group/checkbox-group.wxss",
    }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/checkbox/checkbox.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-checkbox{background:var(--td-checkbox-bg-color,var(--td-bg-color-container,var(--td-font-white-1,#fff)));display:-webkit-inline-flex;display:inline-flex;font-size:var(--td-checkbox-font-size,",
        [0, 32],
        ");position:relative;vertical-align:middle}\n.",
        [1],
        "t-checkbox:focus{outline:0}\n.",
        [1],
        "t-checkbox--block{display:-webkit-flex;display:flex;padding:var(--td-checkbox-vertical-padding,",
        [0, 32],
        ")}\n.",
        [1],
        "t-checkbox--right{-webkit-flex-direction:row-reverse;flex-direction:row-reverse}\n.",
        [1],
        "t-checkbox .",
        [1],
        "limit-title-row{-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}\n.",
        [1],
        "t-checkbox .",
        [1],
        "image-center{position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.",
        [1],
        "t-checkbox__icon-left{margin-right:",
        [0, 20],
        ";width:",
        [0, 40],
        "}\n.",
        [1],
        "t-checkbox__icon-right{display:contents;position:absolute;right:0;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n.",
        [1],
        "t-checkbox__icon-image{vertical-align:top}\n.",
        [1],
        "t-checkbox__icon,.",
        [1],
        "t-checkbox__icon-image{height:var(--td-checkbox-icon-size,",
        [0, 48],
        ");width:var(--td-checkbox-icon-size,",
        [0, 48],
        ")}\n.",
        [1],
        "t-checkbox__icon{color:var(--td-checkbox-icon-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));display:block;font-size:var(--td-checkbox-icon-size,",
        [0, 48],
        ");margin-top:calc((var(--td-checkbox-title-line-height,",
        [0, 48],
        ") - var(--td-checkbox-icon-size,",
        [0, 48],
        "))/ 2);position:relative}\n.",
        [1],
        "t-checkbox__icon:empty{display:none}\n.",
        [1],
        "t-checkbox__icon--checked{color:var(--td-checkbox-icon-checked-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-checkbox__icon--disabled{color:var(--td-checkbox-icon-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)));cursor:not-allowed}\n.",
        [1],
        "t-checkbox__icon--left{margin-right:",
        [0, 16],
        "}\n.",
        [1],
        "t-checkbox__icon-circle{border:calc(",
        [0, 4],
        " * 2) solid var(--td-checkbox-icon-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));border-radius:50%;box-sizing:border-box;height:calc((var(--td-checkbox-icon-size,",
        [0, 48],
        ") - ",
        [0, 4],
        ") * 2);left:50%;position:absolute;top:50%;-webkit-transform:translate(-50%,-50%) scale(.5);transform:translate(-50%,-50%) scale(.5);width:calc((var(--td-checkbox-icon-size,",
        [0, 48],
        ") - ",
        [0, 4],
        ") * 2)}\n.",
        [1],
        "t-checkbox__icon-circle--disabled{background:var(--td-checkbox-icon-disabled-bg-color,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)))}\n.",
        [1],
        "t-checkbox__icon-rectangle{border:calc(",
        [0, 4],
        " * 2) solid var(--td-checkbox-icon-color,var(--td-component-border,var(--td-gray-color-4,#dcdcdc)));box-sizing:border-box;height:calc((var(--td-checkbox-icon-size,",
        [0, 48],
        ") - ",
        [0, 4],
        " * 2) * 2);left:50%;position:absolute;top:50%;-webkit-transform:translate(-50%,-50%) scale(.5);transform:translate(-50%,-50%) scale(.5);width:calc((var(--td-checkbox-icon-size,",
        [0, 48],
        ") - ",
        [0, 4],
        " * 2) * 2)}\n.",
        [1],
        "t-checkbox__icon-rectangle--disabled{background:var(--td-checkbox-icon-disabled-bg-color,var(--td-bg-color-component-disabled,var(--td-gray-color-2,#eee)))}\n.",
        [1],
        "t-checkbox__icon-line::after,.",
        [1],
        "t-checkbox__icon-line::before{background:var(--td-checkbox-icon-checked-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)));border-radius:",
        [0, 2],
        ";content:\x22\x22;display:block;position:absolute;-webkit-transform-origin:top center;transform-origin:top center;width:",
        [0, 5],
        "}\n.",
        [1],
        "t-checkbox__icon-line::before{height:",
        [0, 16],
        ";left:",
        [0, 8],
        ";top:",
        [0, 22],
        ";-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}\n.",
        [1],
        "t-checkbox__icon-line::after{height:",
        [0, 26],
        ";right:",
        [0, 8],
        ";top:",
        [0, 14],
        ";-webkit-transform:rotate(45deg);transform:rotate(45deg)}\n.",
        [1],
        "t-checkbox__icon-line--disabled::after,.",
        [1],
        "t-checkbox__icon-line--disabled::before{background:var(--td-checkbox-icon-disabled-color,var(--td-brand-color-disabled,var(--td-primary-color-3,#b5c7ff)))}\n.",
        [1],
        "t-checkbox__content{-webkit-flex:1;flex:1}\n.",
        [1],
        "t-checkbox__title{-webkit-box-orient:vertical;color:var(--td-checkbox-title-color,var(--td-text-color-primary,var(--td-font-gray-1,rgba(0,0,0,.9))));display:-webkit-box;line-height:var(--td-checkbox-title-line-height,",
        [0, 48],
        ");overflow:hidden}\n.",
        [1],
        "t-checkbox__title--disabled{color:var(--td-checkbox-title-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-checkbox__description{-webkit-box-orient:vertical;color:var(--td-checkbox-description-color,var(--td-text-color-secondary,var(--td-font-gray-2,rgba(0,0,0,.6))));display:-webkit-box;font-size:",
        [0, 28],
        ";line-height:var(--td-checkbox-description-line-height,",
        [0, 44],
        ");overflow:hidden}\n.",
        [1],
        "t-checkbox__description--disabled{color:var(--td-checkbox-description-disabled-color,var(--td-text-color-disabled,var(--td-font-gray-4,rgba(0,0,0,.26))))}\n.",
        [1],
        "t-checkbox__title+.",
        [1],
        "t-checkbox__description:not(:empty){margin-top:",
        [0, 8],
        "}\n.",
        [1],
        "t-checkbox__border{background:var(--td-checkbox-border-color,var(--td-component-stroke,var(--td-gray-color-3,#e7e7e7)));bottom:0;height:1px;left:",
        [0, 96],
        ";position:absolute;right:0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}\n.",
        [1],
        "t-checkbox__border--right{left:",
        [0, 32],
        "}\n.",
        [1],
        "t-checkbox--tag{background-color:var(--td-bg-color-secondarycontainer,var(--td-gray-color-1,#f3f3f3));border-radius:",
        [0, 12],
        ";font-size:",
        [0, 28],
        ";padding-bottom:",
        [0, 16],
        ";padding-top:",
        [0, 16],
        ";text-align:center}\n.",
        [1],
        "t-checkbox--tag.",
        [1],
        "t-checkbox--checked{background-color:var(--td-checkbox-tag-active-bg-color,var(--td-brand-color-light,var(--td-primary-color-1,#f2f3ff)))}\n.",
        [1],
        "t-checkbox--tag .",
        [1],
        "t-checkbox__title--checked,.",
        [1],
        "t-checkbox--tag.",
        [1],
        "t-checkbox--checked{color:var(--td-checkbox-tag-active-color,var(--td-brand-color,var(--td-primary-color-7,#0052d9)))}\n.",
        [1],
        "t-checkbox--tag .",
        [1],
        "t-checkbox__content{margin-right:0}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/checkbox/checkbox.wxss" }
    );
}
