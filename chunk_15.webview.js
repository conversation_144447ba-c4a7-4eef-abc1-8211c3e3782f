__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/col/col": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/col/col"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            prefix: new Array(1),
            gutter: new Array(1),
            style: new Array(1),
            offset: new Array(2),
            customStyle: new Array(1),
            classPrefix: new Array(2),
            span: new Array(1),
          },
          K = U === true,
          d = (C, T, E, B, F, S) => {
            S("");
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.offset;
                if (
                  C ||
                  K ||
                  !!(
                    U.prefix ||
                    Z(undefined, "cls") ||
                    U.classPrefix ||
                    Q.a([U.span]) ||
                    U.offset ||
                    ($A
                      ? !!(U.classPrefix || U.offset) || undefined
                      : undefined)
                  ) ||
                  undefined
                )
                  L(
                    N,
                    "class " +
                      Y(D.prefix) +
                      "-class " +
                      Y(P(X(a).cls)(D.classPrefix, [D.span])) +
                      " " +
                      Y($A ? D.classPrefix + "--offset-" + D.offset : "")
                  );
                A["prefix"][0] =
                  A["classPrefix"][0] =
                  A["span"][0] =
                  A["offset"][0] =
                  A["classPrefix"][1] =
                  A["offset"][1] =
                    (D, E, T) => {
                      var $B = D.offset;
                      L(
                        N,
                        "class " +
                          Y(D.prefix) +
                          "-class " +
                          Y(P(X(a).cls)(D.classPrefix, [D.span])) +
                          " " +
                          Y($B ? D.classPrefix + "--offset-" + D.offset : "")
                      );
                    };
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "getColStyles") ||
                    U.gutter ||
                    U.style ||
                    U.customStyle
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(b).getColStyles)(D.gutter, D.style, D.customStyle)
                  );
                A["gutter"][0] =
                  A["style"][0] =
                  A["customStyle"][0] =
                    (D, E, T) => {
                      R.y(
                        N,
                        P(X(b).getColStyles)(D.gutter, D.style, D.customStyle)
                      );
                    };
              },
              d
            );
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/col/col.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-col{box-sizing:border-box;display:block;min-height:1px}\n.",
        [1],
        "t-col--1{width:4.16666667%}\n.",
        [1],
        "t-col--offset-1{margin-left:4.16666667%}\n.",
        [1],
        "t-col--2{width:8.33333333%}\n.",
        [1],
        "t-col--offset-2{margin-left:8.33333333%}\n.",
        [1],
        "t-col--3{width:12.5%}\n.",
        [1],
        "t-col--offset-3{margin-left:12.5%}\n.",
        [1],
        "t-col--4{width:16.66666667%}\n.",
        [1],
        "t-col--offset-4{margin-left:16.66666667%}\n.",
        [1],
        "t-col--5{width:20.83333333%}\n.",
        [1],
        "t-col--offset-5{margin-left:20.83333333%}\n.",
        [1],
        "t-col--6{width:25%}\n.",
        [1],
        "t-col--offset-6{margin-left:25%}\n.",
        [1],
        "t-col--7{width:29.16666667%}\n.",
        [1],
        "t-col--offset-7{margin-left:29.16666667%}\n.",
        [1],
        "t-col--8{width:33.33333333%}\n.",
        [1],
        "t-col--offset-8{margin-left:33.33333333%}\n.",
        [1],
        "t-col--9{width:37.5%}\n.",
        [1],
        "t-col--offset-9{margin-left:37.5%}\n.",
        [1],
        "t-col--10{width:41.66666667%}\n.",
        [1],
        "t-col--offset-10{margin-left:41.66666667%}\n.",
        [1],
        "t-col--11{width:45.83333333%}\n.",
        [1],
        "t-col--offset-11{margin-left:45.83333333%}\n.",
        [1],
        "t-col--12{width:50%}\n.",
        [1],
        "t-col--offset-12{margin-left:50%}\n.",
        [1],
        "t-col--13{width:54.16666667%}\n.",
        [1],
        "t-col--offset-13{margin-left:54.16666667%}\n.",
        [1],
        "t-col--14{width:58.33333333%}\n.",
        [1],
        "t-col--offset-14{margin-left:58.33333333%}\n.",
        [1],
        "t-col--15{width:62.5%}\n.",
        [1],
        "t-col--offset-15{margin-left:62.5%}\n.",
        [1],
        "t-col--16{width:66.66666667%}\n.",
        [1],
        "t-col--offset-16{margin-left:66.66666667%}\n.",
        [1],
        "t-col--17{width:70.83333333%}\n.",
        [1],
        "t-col--offset-17{margin-left:70.83333333%}\n.",
        [1],
        "t-col--18{width:75%}\n.",
        [1],
        "t-col--offset-18{margin-left:75%}\n.",
        [1],
        "t-col--19{width:79.16666667%}\n.",
        [1],
        "t-col--offset-19{margin-left:79.16666667%}\n.",
        [1],
        "t-col--20{width:83.33333333%}\n.",
        [1],
        "t-col--offset-20{margin-left:83.33333333%}\n.",
        [1],
        "t-col--21{width:87.5%}\n.",
        [1],
        "t-col--offset-21{margin-left:87.5%}\n.",
        [1],
        "t-col--22{width:91.66666667%}\n.",
        [1],
        "t-col--offset-22{margin-left:91.66666667%}\n.",
        [1],
        "t-col--23{width:95.83333333%}\n.",
        [1],
        "t-col--offset-23{margin-left:95.83333333%}\n.",
        [1],
        "t-col--24{width:100%}\n.",
        [1],
        "t-col--offset-24{margin-left:100%}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/col/col.wxss" }
    );
}
