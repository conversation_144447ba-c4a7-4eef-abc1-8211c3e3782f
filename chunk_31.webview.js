__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/image-viewer/image-viewer": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (
              G["miniprogram_npm/tdesign-miniprogram/common/template/icon"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          c,
          f = (C) => {},
          g,
          k = (C, l, m, n, o, p, T, E) => {
            var r = (C) => {},
              q = (C, T, E) => {
                E(
                  "t-image",
                  {},
                  (N, C) => {
                    if (C || K || !!U.classPrefix || undefined)
                      L(N, Y(D.classPrefix) + "__image");
                    var $A = m;
                    if (
                      C ||
                      K ||
                      !!(o || Z(Z(U.imagesStyle, $A), "style")) ||
                      undefined
                    )
                      R.y(N, X(X(D.imagesStyle)[$A]).style || "");
                    if (C) O(N, "t-class", "t-image--external");
                    if (C) O(N, "mode", "aspectFit");
                    if (C) O(N, "lazy", true);
                    if (C || K || n) O(N, "src", l);
                    if (C) O(N, "bindload", "onImageLoadSuccess");
                    if (C || K || o) R.d(N, "index", m);
                  },
                  r
                );
              };
            E(
              "swiper-item",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__preview-image");
              },
              q
            );
          },
          j = (C, T, E, B, F) => {
            F(D.images, "index", U ? U.images : undefined, [0, "images"], k);
          },
          i = (C, T, E) => {
            E(
              "swiper",
              {},
              (N, C) => {
                if (C) L(N, "swiper");
                var $A = D.currentSwiperIndex;
                if (
                  C ||
                  K ||
                  !!U.currentSwiperIndex ||
                  Z(Z(U.swiperStyle, $A), "style")
                )
                  R.y(N, X(X(D.swiperStyle)[$A]).style);
                if (C || K || undefined) O(N, "autoplay", false);
                if (C || K || U.currentSwiperIndex)
                  O(N, "current", D.currentSwiperIndex);
                if (C) O(N, "bindchange", "onSwiperChange");
                if (C) O(N, "bindtap", "onClose");
                if (C) O(N, "tabindex", "0");
              },
              j
            );
          },
          n,
          p,
          q = (C, T, E, B, F, S, J) => {
            var $A = I(p);
            if (p && $A)
              $A(
                R,
                C,
                Object.assign({}, X(D._closeBtn), {}),
                K ||
                  (U
                    ? U._closeBtn === true ||
                      Object.assign({}, X(U._closeBtn), {})
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          o = (C, T, E, B, F, S, J) => {
            if (n === 1) {
              p = "icon";
              B(p, q);
            }
          },
          m = (C, T, E, B, F, S) => {
            S("close-btn");
            n = D._closeBtn ? 1 : 0;
            B(n, o);
          },
          r,
          t = (C, T) => {
            C ||
            K ||
            !!(U.currentSwiperIndex || Z(U.images, "length")) ||
            undefined
              ? T(Y(Y(D.currentSwiperIndex + 1) + "/" + Y(X(D.images).length)))
              : T();
          },
          s = (C, T, E) => {
            if (r === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__nav-index");
                },
                t
              );
            }
          },
          v,
          w = (C, T, E, B, F, S, J) => {
            var $A = I(v);
            if (v && $A)
              $A(
                R,
                C,
                Object.assign({}, X(D._deleteBtn), {}),
                K ||
                  (U
                    ? U._deleteBtn === true ||
                      Object.assign({}, X(U._deleteBtn), {})
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          u = (C, T, E, B, F, S, J) => {
            S("delete-btn");
            v = "icon";
            B(v, w);
          },
          l = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__nav-close");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "关闭");
                if (C) R.v(N, "tap", "onClose", !0, !1, !1, !1);
              },
              m
            );
            r = D.showIndex ? 1 : 0;
            B(r, s);
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__nav-delete");
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "删除");
                if (C) R.v(N, "tap", "onDelete", !1, !1, !1, !1);
              },
              u
            );
          },
          h = (C, T, E) => {
            if (g === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__content");
                },
                i
              );
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__nav");
                },
                l
              );
            }
          },
          e = (C, T, E, B) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__mask");
                if (C || K || !!U.backgroundColor || undefined)
                  R.y(N, "background-color: " + D.backgroundColor);
                if (C) O(N, "aria-role", "button");
                if (C) O(N, "aria-label", "关闭");
                if (C) R.d(N, "source", "overlay");
                if (C) R.v(N, "tap", "onClose", !1, !1, !1, !1);
              },
              f
            );
            g = D.images && X(D.images).length ? 1 : 0;
            B(g, h);
          },
          d = (C, T, E) => {
            if (c === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([U.style, U.customStyle, !!U.maskTop || undefined])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(a)._style)([
                        D.style,
                        D.customStyle,
                        "--td-image-viewer-top: " + D.maskTop + "px",
                      ])
                    );
                  if (C || K || undefined) O(N, "aria-modal", true);
                  if (C) O(N, "aria-role", "dialog");
                  if (C) O(N, "aria-label", "图片查看器");
                  if (C) O(N, "catchtouchmove", "true");
                  if (C || K || U.classPrefix) R.i(N, D.classPrefix);
                },
                e
              );
            }
          },
          b = (C, T, E, B) => {
            c = D.visible ? 1 : 0;
            B(c, d);
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/image-viewer/image-viewer.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "t-image-viewer{bottom:0;left:0;overflow:hidden;position:fixed;right:0;top:var(--td-image-viewer-top,var(--td-position-fixed-top,0));-webkit-transform:translateZ(0);transform:translateZ(0);z-index:1001}\n.",
      [1],
      "t-image-viewer__mask{height:100%;left:0;position:absolute;top:0;width:100%;z-index:1000}\n.",
      [1],
      "t-image-viewer__content{display:inline-block;position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:100vw;z-index:1005}\n.",
      [1],
      "t-image-viewer .",
      [1],
      "swiper{outline:0}\n.",
      [1],
      "t-image-viewer__image{display:inline-block;position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);width:100%}\n.",
      [1],
      "t-image-viewer .",
      [1],
      "t-image--external{display:block;height:inherit;width:inherit}\n.",
      [1],
      "t-image-viewer__nav{-webkit-align-items:center;align-items:center;background-color:var(--td-image-viewer-nav-bg-color,var(--td-font-gray-3,rgba(0,0,0,.4)));color:var(--td-image-viewer-nav-color,var(--td-text-color-anti,var(--td-font-white-1,#fff)));display:-webkit-flex;display:flex;height:var(--td-image-viewer-nav-height,",
      [0, 96],
      ");-webkit-justify-content:space-between;justify-content:space-between;left:0;position:absolute;width:100%;z-index:1005}\n.",
      [1],
      "t-image-viewer__nav-close{margin-left:var(--td-image-viewer-close-margin-left,var(--td-spacer-1,",
      [0, 24],
      "))}\n.",
      [1],
      "t-image-viewer__nav-close:empty{display:none}\n.",
      [1],
      "t-image-viewer__nav-delete{margin-right:var(--td-image-viewer-delete-margin-right,var(--td-spacer-1,",
      [0, 24],
      "))}\n.",
      [1],
      "t-image-viewer__nav-delete:empty{display:none}\n.",
      [1],
      "t-image-viewer__nav-close,.",
      [1],
      "t-image-viewer__nav-delete{font-size:",
      [0, 48],
      "}\n.",
      [1],
      "t-image-viewer__nav-index{-webkit-flex:1;flex:1;font-size:var(--td-image-viewer-nav-index-font-size,var(--td-font-size-base,",
      [0, 28],
      "));text-align:center}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/image-viewer/image-viewer.wxss",
    }
  );
}
