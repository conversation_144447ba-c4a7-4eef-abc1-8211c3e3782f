__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/auth/auth": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = { formData: new Array(4), showBackButton: new Array(1) },
          K = U === true,
          c = (C) => {},
          b = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "填写认证信息");
                if (C || K || U.showBackButton) O(N, "back", D.showBackButton);
                A["showBackButton"][0] = (D, E, T) => {
                  O(N, "back", D.showBackButton);
                  E(N);
                };
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              c
            );
          },
          f = (C, T) => {
            C ? T("怎么认证") : T();
          },
          g = (C, T) => {
            C
              ? T(
                  "提交后管理员会在1个工作日内主动联系您进行认证\\n管理员微信：bcxtd001"
                )
              : T();
          },
          h = (C, T) => {
            C ? T("隐私保护") : T();
          },
          i = (C, T) => {
            C
              ? T(
                  "您的认证信息严格保密，仅在下单时给服务者，或发布求助时给您选定的接单人查看"
                )
              : T();
          },
          j = (C, T) => {
            C
              ? T("如果您填写认证信息，将默认您同意授权向服务者展示认证信息")
              : T();
          },
          e = (C, T, E) => {
            E("text", {}, (N, C) => {}, f);
            E("text", {}, (N, C) => {}, g);
            E("text", {}, (N, C) => {}, h);
            E("text", {}, (N, C) => {}, i);
            E("text", {}, (N, C) => {}, j);
          },
          m = (C, T) => {
            C ? T("手机号") : T();
          },
          n = (C) => {},
          l = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              m
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请填写手机号");
                if (C || K || Z(U.formData, "phone"))
                  O(N, "value", X(D.formData).phone);
                A["formData"][0] = (D, E, T) => {
                  O(N, "value", X(D.formData).phone);
                  E(N);
                };
                if (C) O(N, "bindinput", "onPhoneInput");
                if (C) O(N, "type", "number");
                if (C) O(N, "maxlength", "11");
              },
              n
            );
          },
          p = (C, T) => {
            C ? T("楼栋号") : T();
          },
          q = (C) => {},
          o = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              p
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请输入楼栋号");
                if (C || K || Z(U.formData, "building"))
                  O(N, "value", X(D.formData).building);
                A["formData"][1] = (D, E, T) => {
                  O(N, "value", X(D.formData).building);
                  E(N);
                };
                if (C) O(N, "bindinput", "onBuildingInput");
                if (C) O(N, "type", "number");
              },
              q
            );
          },
          s = (C, T) => {
            C ? T("单元号") : T();
          },
          t = (C) => {},
          r = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              s
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请输入单元号");
                if (C || K || Z(U.formData, "unit"))
                  O(N, "value", X(D.formData).unit);
                A["formData"][2] = (D, E, T) => {
                  O(N, "value", X(D.formData).unit);
                  E(N);
                };
                if (C) O(N, "bindinput", "onUnitInput");
                if (C) O(N, "type", "number");
              },
              t
            );
          },
          v = (C, T) => {
            C ? T("门牌号") : T();
          },
          w = (C) => {},
          u = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-label required");
              },
              v
            );
            E(
              "input",
              {},
              (N, C) => {
                if (C) L(N, "form-input");
                if (C) O(N, "placeholder", "请填写");
                if (C || K || Z(U.formData, "roomNumber"))
                  O(N, "value", X(D.formData).roomNumber);
                A["formData"][3] = (D, E, T) => {
                  O(N, "value", X(D.formData).roomNumber);
                  E(N);
                };
                if (C) O(N, "bindinput", "onRoomNumberInput");
                if (C) O(N, "type", "number");
              },
              w
            );
          },
          y,
          A0 = (C, T) => {
            C ? T("取消") : T();
          },
          z = (C, T, E) => {
            if (y === 1) {
              E(
                "button",
                {},
                (N, C) => {
                  if (C) L(N, "btn btn-cancel");
                  if (C) O(N, "bindtap", "onCancel");
                },
                A0
              );
            }
          },
          B0,
          D0 = (C, T) => {
            C ? T("跳过") : T();
          },
          C0 = (C, T, E) => {
            if (B0 === 1) {
              E(
                "button",
                {},
                (N, C) => {
                  if (C) L(N, "btn btn-cancel");
                  if (C) O(N, "bindtap", "onSkip");
                },
                D0
              );
            }
          },
          E0 = (C, T) => {
            C ? T("提交认证") : T();
          },
          x = (C, T, E, B) => {
            y = D.showCancelButton ? 1 : 0;
            B(y, z);
            B0 = D.showSkipButton ? 1 : 0;
            B(B0, C0);
            E(
              "button",
              {},
              (N, C) => {
                if (C) L(N, "btn btn-submit");
                if (C) O(N, "bindtap", "onSubmit");
              },
              E0
            );
          },
          k = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              l
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              o
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              r
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-item");
              },
              u
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "button-group");
              },
              x
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "shuoming_box");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "form-container");
              },
              k
            );
          },
          a = (C, T, E) => {
            E("page-meta", {}, (N, C) => {}, b);
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "container");
              },
              d
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/auth/auth.wxss"] = setCssToHead(
    [
      "body{overflow:hidden}\n.",
      [1],
      "container,body{background-color:#fff}\n.",
      [1],
      "container{box-sizing:border-box}\n.",
      [1],
      "shuoming_box{background-color:#f0f0f0;border-radius:",
      [0, 20],
      ";font-size:",
      [0, 24],
      ";margin:",
      [0, 20],
      " auto;padding:",
      [0, 20],
      ";width:90%}\n.",
      [1],
      "shuoming_box wx-text{color:#666;display:block;line-height:1.5;margin-bottom:",
      [0, 10],
      "}\n.",
      [1],
      "shuoming_box wx-text:nth-child(1),.",
      [1],
      "shuoming_box wx-text:nth-child(2){color:#f08c00;font-size:",
      [0, 28],
      ";font-weight:700;text-align:center}\n.",
      [1],
      "shuoming_box wx-text:nth-child(3),.",
      [1],
      "shuoming_box wx-text:nth-child(5),.",
      [1],
      "shuoming_box wx-text:nth-child(7){color:#333;font-weight:700;margin-top:",
      [0, 15],
      "}\n.",
      [1],
      "form-container{background-color:#fff;border-radius:",
      [0, 20],
      ";margin:",
      [0, 20],
      " auto;padding:",
      [0, 40],
      " ",
      [0, 30],
      ";width:95%}\n.",
      [1],
      "form-item{margin-bottom:",
      [0, 30],
      ";position:relative}\n.",
      [1],
      "form-label{-webkit-align-items:center;align-items:center;color:#333;display:-webkit-flex;display:flex;font-size:",
      [0, 28],
      ";margin-bottom:",
      [0, 15],
      "}\n.",
      [1],
      "form-label.",
      [1],
      "required::before{color:#ff4d4f;content:\x22*\x22;font-size:",
      [0, 28],
      ";margin-right:",
      [0, 8],
      "}\n.",
      [1],
      "form-input{background-color:#fff;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 10],
      ";box-sizing:border-box;color:#333;font-size:",
      [0, 26],
      ";height:",
      [0, 80],
      ";padding:0 ",
      [0, 20],
      ";width:100%}\n.",
      [1],
      "form-input:focus{border-color:#195abf}\n.",
      [1],
      "form-input::-webkit-input-placeholder{color:#999}\n.",
      [1],
      "form-input::placeholder{color:#999}\n.",
      [1],
      "picker-input{-webkit-align-items:center;align-items:center;height:",
      [0, 80],
      ";line-height:",
      [0, 80],
      "}\n.",
      [1],
      "button-group,.",
      [1],
      "picker-input{display:-webkit-flex;display:flex}\n.",
      [1],
      "button-group{gap:",
      [0, 20],
      ";-webkit-justify-content:space-between;justify-content:space-between;margin-top:",
      [0, 60],
      "}\n.",
      [1],
      "btn{-webkit-align-items:center;align-items:center;border:none;border-radius:",
      [0, 20],
      ";display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;font-size:",
      [0, 28],
      ";height:",
      [0, 80],
      ";-webkit-justify-content:center;justify-content:center}\n.",
      [1],
      "btn-cancel{background-color:#fff;border:",
      [0, 3],
      " solid #1e1e1e;color:#666}\n.",
      [1],
      "btn-submit{background-color:#a5d8ff;border:",
      [0, 3],
      " solid #1971c2;color:#1971c2}\n.",
      [1],
      "btn:active{opacity:.8}\n",
    ],
    "Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.(./pages/auth/auth.wxss:1:432)",
    { path: "./pages/auth/auth.wxss" }
  );
}
