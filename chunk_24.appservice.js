__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/dropdown-item/dropdown-item": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/dropdown-item/index"]();
      var b = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          d,
          g = (C) => {},
          j,
          n = (C, o, p, q, r, s, T, E) => {
            var u = (C) => {},
              t = (C, T, E) => {
                E(
                  "t-radio",
                  {},
                  (N, C) => {
                    if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__radio-item " +
                          Y(D.prefix) +
                          "-class-column-item"
                      );
                    if (C || K || U.placement) O(N, "placement", D.placement);
                    if (C) O(N, "tabindex", "0");
                    if (C) O(N, "icon", "line");
                    if (C) O(N, "t-class", "radio");
                    if (C || K || !!U.prefix || undefined)
                      O(
                        N,
                        "t-class-label",
                        Y(D.prefix) + "-class-column-item-label"
                      );
                    var $A = D.valueAlias;
                    if (C || K || !!U.valueAlias || Z(q, $A))
                      O(N, "value", X(o)[$A]);
                    var $B = D.labelAlias;
                    if (C || K || !!U.labelAlias || Z(q, $B))
                      O(N, "label", X(o)[$B]);
                    if (C || K || Z(q, "disabled"))
                      O(N, "disabled", X(o).disabled);
                  },
                  u
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.valueAlias;
                if (C || K || !!(U.valueAlias || Z(q, $A)) || undefined)
                  R.i(N, "id_" + Y(X(o)[$A]));
              },
              t
            );
          },
          m = (C, T, E, B, F) => {
            F(D.options, "index", U ? U.options : undefined, [0, "options"], n);
          },
          l = (C, T, E) => {
            E(
              "t-radio-group",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__radio " +
                      Y(D.prefix) +
                      "-class-column"
                  );
                if (C || K || !!U.optionsColumns || undefined)
                  R.y(
                    N,
                    "grid-template-columns:repeat(" +
                      Y(D.optionsColumns) +
                      ", 1fr)"
                  );
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class", Y(D.classPrefix) + "__radio-group");
                if (C || K || U.value) O(N, "value", D.value);
                if (C) R.v(N, "change", "handleRadioChange", !1, !1, !1, !1);
              },
              m
            );
          },
          k = (C, T, E) => {
            if (j === 1) {
              E(
                "scroll-view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__scroll");
                  if (C) O(N, "scroll-y", true);
                  if (C || K || !!U.value || undefined)
                    O(N, "scroll-into-view", "id_" + Y(D.value));
                },
                l
              );
            }
          },
          o,
          s = (C, t, u, v, w, x, T, E) => {
            var z = (C) => {},
              y = (C, T, E) => {
                E(
                  "t-checkbox",
                  {},
                  (N, C) => {
                    if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                      L(
                        N,
                        Y(D.classPrefix) +
                          "__checkbox-item " +
                          Y(D.prefix) +
                          "-class-column-item"
                      );
                    if (C) O(N, "tabindex", "0");
                    if (C) O(N, "theme", "tag");
                    var $A = D.valueAlias;
                    if (C || K || !!U.valueAlias || Z(v, $A))
                      O(N, "value", X(t)[$A]);
                    var $B = D.labelAlias;
                    if (C || K || !!U.labelAlias || Z(v, $B))
                      O(N, "label", X(t)[$B]);
                    if (C || K || Z(v, "disabled"))
                      O(N, "disabled", X(t).disabled);
                  },
                  z
                );
              };
            E(
              "view",
              {},
              (N, C) => {
                var $A = D.valueAlias;
                if (C || K || !!(U.valueAlias || Z(v, $A)) || undefined)
                  R.i(N, "id_" + Y(X(t)[$A]));
              },
              y
            );
          },
          r = (C, T, E, B, F) => {
            F(D.options, "index", U ? U.options : undefined, [0, "options"], s);
          },
          q = (C, T, E) => {
            E(
              "t-checkbox-group",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__checkbox " +
                      Y(D.prefix) +
                      "-class-column"
                  );
                if (C || K || !!U.optionsColumns || undefined)
                  R.y(
                    N,
                    "grid-template-columns:repeat(" +
                      Y(D.optionsColumns) +
                      ", 1fr)"
                  );
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class", Y(D.classPrefix) + "__checkbox-group");
                var $A = D.value;
                if (C || K || !!U.value || ($A ? U.value : Q.a([])))
                  O(N, "value", $A ? D.value : []);
                if (C) R.v(N, "change", "handleRadioChange", !1, !1, !1, !1);
              },
              r
            );
          },
          p = (C, T, E) => {
            if (o === 1) {
              E(
                "scroll-view",
                {},
                (N, C) => {
                  if (C || K || !!U.classPrefix || undefined)
                    L(N, Y(D.classPrefix) + "__scroll");
                  if (C) O(N, "scroll-y", true);
                  if (C || K || !!U.firstCheckedValue || undefined)
                    O(N, "scroll-into-view", "id_" + Y(D.firstCheckedValue));
                },
                q
              );
            }
          },
          i = (C, T, E, B, F, S) => {
            j = !D.multiple && D.options && X(D.options).length > 0 ? 1 : 0;
            B(j, k);
            o = D.multiple && D.options && X(D.options).length > 0 ? 1 : 0;
            B(o, p);
            S("");
          },
          u,
          w = (C) => {},
          x = (C) => {},
          v = (C, T, E) => {
            if (u === 1) {
              E(
                "t-button",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__footer-btn " +
                        Y(D.classPrefix) +
                        "__reset-btn"
                    );
                  if (C) O(N, "block", true);
                  if (C) O(N, "theme", "light");
                  if (C) O(N, "content", "重置");
                  if (C || K || !!Z(U.value, "length") || undefined)
                    O(N, "disabled", X(D.value).length == 0);
                  if (C) O(N, "bindtap", "handleReset");
                },
                w
              );
              E(
                "t-button",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.classPrefix) || undefined)
                    L(
                      N,
                      Y(D.classPrefix) +
                        "__footer-btn " +
                        Y(D.classPrefix) +
                        "__confirm-btn"
                    );
                  if (C) O(N, "block", true);
                  if (C) O(N, "theme", "primary");
                  if (C) O(N, "content", "确定");
                  if (C || K || !!Z(U.value, "length") || undefined)
                    O(N, "disabled", X(D.value).length == 0);
                  if (C) O(N, "bindtap", "handleConfirm");
                },
                x
              );
            }
          },
          t = (C, T, E, B, F, S) => {
            S("footer");
            u = D.multiple ? 1 : 0;
            B(u, v);
          },
          h = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__body");
              },
              i
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(
                    N,
                    Y(D.classPrefix) +
                      "__footer " +
                      Y(D.prefix) +
                      "-class-footer"
                  );
              },
              t
            );
          },
          f = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!U.classPrefix || undefined)
                  L(N, Y(D.classPrefix) + "__mask");
                if (
                  C ||
                  K ||
                  !!(
                    Z(undefined, "_style") ||
                    Q.a([!!U.maskHeight || undefined, U.style, U.customStyle])
                  ) ||
                  undefined
                )
                  R.y(
                    N,
                    P(X(b)._style)([
                      "height:" + D.maskHeight + "px",
                      D.style,
                      D.customStyle,
                    ])
                  );
                if (C || K || !!U.show || undefined) O(N, "hidden", !D.show);
                if (C) O(N, "catchtouchmove", "closeDropdown");
                if (C) R.v(N, "tap", "handleMaskClick", !1, !1, !1, !1);
              },
              g
            );
            E(
              "t-popup",
              {},
              (N, C) => {
                if (C || K || U.show) O(N, "visible", D.show);
                if (C || K || !!U.zIndex || undefined)
                  O(N, "z-index", D.zIndex + 1);
                if (C || K || U.duration) O(N, "duration", D.duration);
                if (C || K || U.showOverlay)
                  O(N, "show-overlay", D.showOverlay);
                if (C) O(N, "custom-style", "position: absolute");
                if (C || K || Q.b({}))
                  O(N, "overlay-props", { style: "position: absolute" });
                if (C || K || !!U.classPrefix || undefined)
                  O(N, "t-class", Y(D.classPrefix) + "__popup-host");
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  O(
                    N,
                    "t-class-content",
                    Y(D.classPrefix) +
                      "__content " +
                      Y(D.prefix) +
                      "-class-content"
                  );
                if (C) R.v(N, "leaved", "onLeaved", !1, !1, !1, !1);
                if (C)
                  R.v(N, "visible-change", "handleMaskClick", !1, !1, !1, !1);
              },
              h
            );
          },
          e = (C, T, E) => {
            if (d === 1) {
              E(
                "view",
                {},
                (N, C) => {
                  if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                    L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                  if (
                    C ||
                    K ||
                    !!(
                      Z(undefined, "_style") ||
                      Q.a([
                        !!(Z(undefined, "getStyles") || U.top || U.zIndex) ||
                          undefined,
                        U.style,
                        U.customStyle,
                      ])
                    ) ||
                    undefined
                  )
                    R.y(
                      N,
                      P(X(b)._style)([
                        P(X(a).getStyles)(D.top, D.zIndex),
                        D.style,
                        D.customStyle,
                      ])
                    );
                },
                f
              );
            }
          },
          c = (C, T, E, B) => {
            d = D.wrapperVisible ? 1 : 0;
            B(d, e);
          };
        return { C: c, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
__wxRoute = "miniprogram_npm/tdesign-miniprogram/dropdown-item/dropdown-item";
__wxRouteBegin = true;
__wxAppCurrentFile__ =
  "miniprogram_npm/tdesign-miniprogram/dropdown-item/dropdown-item.js";
define(
  "miniprogram_npm/tdesign-miniprogram/dropdown-item/dropdown-item.js",
  function (
    require,
    module,
    exports,
    window,
    document,
    frames,
    self,
    location,
    navigator,
    localStorage,
    history,
    Caches,
    screen,
    alert,
    confirm,
    prompt,
    XMLHttpRequest,
    WebSocket,
    Reporter,
    webkit,
    WeixinJSCore
  ) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: !0 }),
      (exports.default = void 0),
      require("../../../@babel/runtime/helpers/Arrayincludes");
    var e = require("../../../@babel/runtime/helpers/createClass"),
      t = require("../../../@babel/runtime/helpers/classCallCheck"),
      a = require("../../../@babel/runtime/helpers/inherits"),
      r = require("../../../@babel/runtime/helpers/createSuper"),
      i = require("tslib"),
      l = require("../common/src/index"),
      n = c(require("../common/config")),
      s = c(require("./props")),
      o = c(require("../dropdown-menu/props")),
      u = require("../common/utils");
    function c(e) {
      return e && e.__esModule ? e : { default: e };
    }
    var d = n.default.prefix,
      h = "".concat(d, "-dropdown-item"),
      p = (function (i) {
        a(n, i);
        var l = r(n);
        function n() {
          var e;
          return (
            t(this, n),
            ((e = l.apply(this, arguments)).options = { multipleSlots: !0 }),
            (e.externalClasses = [
              "".concat(d, "-class"),
              "".concat(d, "-class-content"),
              "".concat(d, "-class-column"),
              "".concat(d, "-class-column-item"),
              "".concat(d, "-class-column-item-label"),
              "".concat(d, "-class-footer"),
            ]),
            (e.properties = s.default),
            (e.data = {
              prefix: d,
              classPrefix: h,
              show: !1,
              top: 0,
              maskHeight: 0,
              initValue: null,
              hasChanged: !1,
              duration: o.default.duration.value,
              zIndex: o.default.zIndex.value,
              overlay: o.default.showOverlay.value,
              labelAlias: "label",
              valueAlias: "value",
              computedLabel: "",
              firstCheckedValue: "",
            }),
            (e.relations = {
              "../dropdown-menu/dropdown-menu": {
                type: "parent",
                linked: function (e) {
                  var t = e.properties,
                    a = t.zIndex,
                    r = t.duration,
                    i = t.showOverlay;
                  this.setData({ zIndex: a, duration: r, showOverlay: i });
                },
              },
            }),
            (e.controlledProps = [{ key: "value", event: "change" }]),
            (e.observers = {
              keys: function (e) {
                this.setData({
                  labelAlias: e.label || "label",
                  valueAlias: e.value || "value",
                });
              },
              value: function (e) {
                var t = this.data,
                  a = t.options,
                  r = t.labelAlias,
                  i = t.valueAlias;
                if (this.data.multiple && !Array.isArray(e))
                  throw TypeError("应传入数组类型的 value");
                var l = a.find(function (t) {
                  return t[i] === e;
                });
                l && this.setData({ computedLabel: l[r] });
              },
              "label, computedLabel, disabled": function () {
                var e;
                null === (e = this.$parent) || void 0 === e || e.getAllItems();
              },
              show: function (e) {
                var t = this;
                e &&
                  this.getParentBottom(function () {
                    t.setData({ wrapperVisible: !0 });
                  });
              },
            }),
            (e.methods = {
              closeDropdown: function () {
                var e;
                null === (e = this.$parent) ||
                  void 0 === e ||
                  e.setData({ activeIdx: -1 }),
                  this.setData({ show: !1 }),
                  this.triggerEvent("close");
              },
              getParentBottom: function (e) {
                var t = this;
                (0, u.getRect)(this.$parent, "#".concat(d, "-bar")).then(
                  function (a) {
                    t.setData({ top: a.bottom, maskHeight: a.top }, e);
                  }
                );
              },
              handleTreeClick: function (e) {
                var t = e.currentTarget.dataset,
                  a = t.level,
                  r = t.value,
                  i = this.data.value;
                (i[a] = r), this._trigger("change", { value: i });
              },
              handleRadioChange: function (e) {
                var t = e.detail.value;
                if (
                  (this._trigger("change", { value: t }), this.data.multiple)
                ) {
                  var a = this.data.options.find(function (e) {
                    return t.includes(e.value);
                  });
                  a && (this.data.firstCheckedValue = a.value);
                } else this.closeDropdown();
              },
              handleMaskClick: function () {
                var e;
                (null === (e = this.$parent) || void 0 === e
                  ? void 0
                  : e.properties.closeOnClickOverlay) && this.closeDropdown();
              },
              handleReset: function () {
                this._trigger("change", { value: [] }), this._trigger("reset");
              },
              handleConfirm: function () {
                this._trigger("confirm", { value: this.data.value }),
                  this.closeDropdown(),
                  this.setData({
                    firstCheckedValue: this.data.firstCheckedValue,
                  });
              },
              onLeaved: function () {
                this.setData({ wrapperVisible: !1 });
              },
            }),
            e
          );
        }
        return e(n);
      })(l.SuperComponent),
      v = (p = (0, i.__decorate)([(0, l.wxComponent)()], p));
    exports.default = v;
  },
  {
    isPage: false,
    isComponent: true,
    currentFile:
      "miniprogram_npm/tdesign-miniprogram/dropdown-item/dropdown-item.js",
  }
);
require("miniprogram_npm/tdesign-miniprogram/dropdown-item/dropdown-item.js");
