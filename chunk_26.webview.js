__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "miniprogram_npm/tdesign-miniprogram/fab/draggable/draggable": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      var a = R["miniprogram_npm/tdesign-miniprogram/common/utils"]();
      H[""] = (R, C, D, U) => {
        R.setFnFilter(Q.A, Q.B);
        if (typeof R.setEventListenerWrapper === "function")
          R.setEventListenerWrapper(Q.C);
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {
            prefix: new Array(1),
            style: new Array(1),
            customStyle: new Array(1),
            classPrefix: new Array(1),
          },
          K = U === true,
          c = (C, T, E, B, F, S) => {
            S("");
          },
          b = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C || K || !!(U.classPrefix || U.prefix) || undefined)
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                A["classPrefix"][0] = A["prefix"][0] = (D, E, T) => {
                  L(N, Y(D.classPrefix) + " class " + Y(D.prefix) + "-class");
                };
                if (
                  C ||
                  K ||
                  !!(Z(undefined, "_style") || Q.a([U.style, U.customStyle])) ||
                  undefined
                )
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                A["style"][0] = A["customStyle"][0] = (D, E, T) => {
                  R.y(N, P(X(a)._style)([D.style, D.customStyle]));
                };
                if (C) R.v(N, "touchstart", "onTouchStart", !1, !1, !1, !1);
                if (C) R.v(N, "touchmove", "onTouchMove", !0, !1, !1, !1);
                if (C) R.v(N, "touchend", "onTouchEnd", !1, !1, !1, !1);
              },
              c
            );
          };
        return { C: b, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
    "miniprogram_npm/tdesign-miniprogram/fab/fab": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S)
          S = Object.assign(
            {},
            (G["miniprogram_npm/tdesign-miniprogram/fab/template/view"] || {})
              ._,
            (
              G["miniprogram_npm/tdesign-miniprogram/fab/template/draggable"] ||
              {}
            )._,
            H
          );
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b,
          c = (C, T, E, B, F, S, J) => {
            var $A = I(b);
            if (b && $A)
              $A(
                R,
                C,
                {
                  prefix: D.prefix,
                  classPrefix: D.classPrefix,
                  style: D.style,
                  customStyle: D.customStyle,
                  moveStyle: D.moveStyle,
                  draggable: D.draggable,
                  buttonData: D.buttonData,
                },
                K ||
                  (U
                    ? {
                        prefix: U.prefix,
                        classPrefix: U.classPrefix,
                        style: U.style,
                        customStyle: U.customStyle,
                        moveStyle: U.moveStyle,
                        draggable: U.draggable,
                        buttonData: U.buttonData,
                      }
                    : Object.create(null))
              ).C(C, T, E, B, F, S, J);
          },
          a = (C, T, E, B, F, S, J) => {
            var $A = D.draggable;
            b = $A ? "draggable" : "view";
            B(b, c);
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__[
    "miniprogram_npm/tdesign-miniprogram/fab/draggable/draggable.wxss"
  ] = setCssToHead(
    [
      [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
      ".",
      [1],
      "hotspot-expanded.",
      [1],
      "relative{position:relative}\n.",
      [1],
      "hotspot-expanded::after{bottom:0;content:\x22\x22;display:block;left:0;position:absolute;right:0;top:0;-webkit-transform:scale(1.5);transform:scale(1.5)}\n.",
      [1],
      "t-draggable{position:fixed}\n",
    ],
    undefined,
    {
      path: "./miniprogram_npm/tdesign-miniprogram/fab/draggable/draggable.wxss",
    }
  );
  __wxAppCode__["miniprogram_npm/tdesign-miniprogram/fab/fab.wxss"] =
    setCssToHead(
      [
        [2, "./miniprogram_npm/tdesign-miniprogram/common/style/index.wxss"],
        ".",
        [1],
        "t-fab{position:fixed}\n.",
        [1],
        "t-fab__button{box-shadow:var(--td-fab-shadow,var(--td-shadow-2,0 3px 14px 2px rgba(0,0,0,.05),0 8px 10px 1px rgba(0,0,0,.06),0 5px 5px -3px rgba(0,0,0,.1)))}\n.",
        [1],
        "t-fab__draggable{position:fixed}\n",
      ],
      undefined,
      { path: "./miniprogram_npm/tdesign-miniprogram/fab/fab.wxss" }
    );
}
