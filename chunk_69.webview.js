__wxCodeSpace__.batchAddCompiledTemplate(function (
  G,
  R,
  D,
  Q,
  gdc,
  X,
  Y,
  Z,
  RG = {}
) {
  var P =
    RG.P ||
    function (a) {
      return typeof a === "function" ? a : () => {};
    };
  return {
    "pages/help-info/help-info": (() => {
      var H = {};
      var S;
      var I = (P) => {
        if (!S) S = Object.assign({}, H);
        return S[P];
      };
      H[""] = (R, C, D, U) => {
        var L = R.c,
          M = R.m,
          O = R.r,
          A = {},
          K = U === true,
          b = (C) => {},
          e = (C, T) => {
            C ? T("服务说明") : T();
          },
          g = (C, T) => {
            C ? T("这是是服务的文字说明") : T();
          },
          h = (C, T) => {
            C ? T("这是是服务的文字说明") : T();
          },
          f = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "description_text");
              },
              g
            );
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "description_text");
              },
              h
            );
          },
          d = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "section_title");
              },
              e
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "description_box");
              },
              f
            );
          },
          k = (C, T) => {
            C ? T("图片") : T();
          },
          j = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder_text");
              },
              k
            );
          },
          m = (C, T) => {
            C ? T("图片") : T();
          },
          l = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "placeholder_text");
              },
              m
            );
          },
          i = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_placeholder");
              },
              j
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_placeholder");
              },
              l
            );
          },
          p = (C, T) => {
            C ? T("联系服务者") : T();
          },
          o = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "contact_text");
              },
              p
            );
          },
          s = (C, T) => {
            C ? T("申请接单") : T();
          },
          r = (C, T, E) => {
            E(
              "text",
              {},
              (N, C) => {
                if (C) L(N, "order_button_text");
              },
              s
            );
          },
          q = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "custom_order_button");
                if (C) O(N, "bindtap", "onOrderClick");
              },
              r
            );
          },
          n = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "contact_section");
              },
              o
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "order_button_section");
              },
              q
            );
          },
          c = (C, T, E) => {
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service_description_section");
              },
              d
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "image_section");
              },
              i
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "bottom_actions");
              },
              n
            );
          },
          a = (C, T, E) => {
            E(
              "navigation-bar",
              {},
              (N, C) => {
                if (C) O(N, "title", "代买早餐");
                if (C || K || undefined) O(N, "back", true);
                if (C) O(N, "color", "black");
                if (C) O(N, "background", "#FFF");
              },
              b
            );
            E(
              "view",
              {},
              (N, C) => {
                if (C) L(N, "service_info_container");
              },
              c
            );
          };
        return { C: a, B: A };
      };
      return Object.assign(
        function (R) {
          return H[R];
        },
        { _: H }
      );
    })(),
  };
});
var noCss =
  typeof __vd_version_info__ !== "undefined" &&
  __vd_version_info__.noCss === true;
if (!noCss) {
  __wxAppCode__["pages/help-info/help-info.wxss"] = setCssToHead(
    [
      ".",
      [1],
      "service_info_container{background-color:#fff;display:-webkit-flex;display:flex;-webkit-flex-direction:column;flex-direction:column;min-height:100vh;padding:",
      [0, 30],
      "}\n.",
      [1],
      "service_description_section{margin-bottom:",
      [0, 40],
      "}\n.",
      [1],
      "section_title{color:#333;font-size:",
      [0, 32],
      ";font-weight:500;margin-bottom:",
      [0, 20],
      "}\n.",
      [1],
      "description_box{background-color:#f5f5f5;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 15],
      ";padding:",
      [0, 25],
      "}\n.",
      [1],
      "description_text{color:#666;display:block;font-size:",
      [0, 28],
      ";line-height:1.6;margin-bottom:",
      [0, 10],
      "}\n.",
      [1],
      "description_text:last-child{margin-bottom:0}\n.",
      [1],
      "image_section{-webkit-flex:1;flex:1;margin-bottom:",
      [0, 40],
      "}\n.",
      [1],
      "image_placeholder{-webkit-align-items:center;align-items:center;background-color:#f5f5f5;border:",
      [0, 2],
      " solid #e0e0e0;border-radius:",
      [0, 15],
      ";display:-webkit-flex;display:flex;height:",
      [0, 300],
      ";-webkit-justify-content:center;justify-content:center;margin-bottom:",
      [0, 20],
      ";width:100%}\n.",
      [1],
      "image_placeholder:last-child{margin-bottom:0}\n.",
      [1],
      "placeholder_text{color:#999;font-size:",
      [0, 28],
      "}\n.",
      [1],
      "bottom_actions{-webkit-align-items:center;align-items:center;background-color:#fff;border-top:",
      [0, 2],
      " solid #f0f0f0;bottom:0;box-shadow:0 ",
      [0, -2],
      " ",
      [0, 10],
      " rgba(0,0,0,.05);display:-webkit-flex;display:flex;left:0;padding:",
      [0, 20],
      " ",
      [0, 30],
      ";position:fixed;right:0}\n.",
      [1],
      "contact_section{-webkit-flex:1;flex:1;margin-right:",
      [0, 20],
      "}\n.",
      [1],
      "contact_text{color:#195abf;font-size:",
      [0, 28],
      "}\n.",
      [1],
      "order_button_section{display:-webkit-flex;display:flex;-webkit-flex:1;flex:1;-webkit-justify-content:flex-end;justify-content:flex-end}\n.",
      [1],
      "custom_order_button{-webkit-align-items:center;align-items:center;display:-webkit-flex;display:flex;height:",
      [0, 80],
      ";-webkit-justify-content:center;justify-content:center;padding:0 ",
      [0, 40],
      "}\n.",
      [1],
      "order_button_text{border:",
      [0, 5],
      " solid #ff4d4f;border-radius:",
      [0, 20],
      ";color:#ff4d4f;font-size:",
      [0, 24],
      ";padding:",
      [0, 8],
      " ",
      [0, 32],
      "}\n.",
      [1],
      "custom_order_button:active{background-color:rgba(255,77,79,.1)}\n.",
      [1],
      "service_info_container{padding-bottom:",
      [0, 120],
      "}\n",
    ],
    undefined,
    { path: "./pages/help-info/help-info.wxss" }
  );
}
